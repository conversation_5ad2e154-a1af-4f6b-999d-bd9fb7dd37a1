package com.xhgj.srm.common.enums.supplierorder;

import cn.hutool.core.util.StrUtil;
import com.xhiot.boot.core.common.exception.CheckException;
import com.xhiot.boot.core.common.util.dict.BootDictEnum;
import lombok.Getter;

/**
 * <AUTHOR>
 * @since 2022/11/28 14:17
 */
@Getter
public enum SupplierOrderState implements BootDictEnum<String, String> {
  /** 待履约 */
  WAIT("0", "待履约"),
  /** 履约中 */
  IN_PROGRESS("1", "履约中"),
  /** 已完成 */
  COMPLETE("2", "已完成"),
  /** 挂起 */
  PENDING("3", "挂起"),
  /** 暂存 */
  STAGING("4", "暂存"),
  /** 审核中 */
  UNAUDITED("5", "审核中"),
  /** 驳回 */
  REJECT("6", "驳回"),
  /** 未审核（包含暂存、审核中、驳回、挂起） */
  NOT_REVIEWED("7", "审核中"),
  /** 已审核（待履约、履约中） */
  REVIEWED("8","已审核");

  /** 订单状态 */
  private final String orderState;
  /** 订单描述 */
  private final String detail;

  SupplierOrderState(String orderState, String detail) {
    this.orderState = orderState;
    this.detail = detail;
  }

  /**
   * 根据 orderState 查找
   *
   * @param orderState 枚举code
   * @return 枚举对象
   */
  public static SupplierOrderState findValueByOrderState(String orderState) {
    for (SupplierOrderState statusEnum : SupplierOrderState.values()) {
      if (statusEnum.getKey().equals(orderState)) {
        // 如果需要直接返回name则更改返回类型为String,return statusEnum.name;
        return statusEnum;
      }
    }
    throw new CheckException("orderState is invalid");
  }

  /**
   * 根据 orderState 查找
   *
   * @param orderState 枚举code
   * @return 枚举对象
   */
  public static SupplierOrderState findValueByOrderStateWithoutError(String orderState) {
    for (SupplierOrderState statusEnum : SupplierOrderState.values()) {
      if (statusEnum.getKey().equals(orderState)) {
        // 如果需要直接返回name则更改返回类型为String,return statusEnum.name;
        return statusEnum;
      }
    }
    return null;
  }

  @Override
  public String getKey() {
    return orderState;
  }

  @Override
  public String getValue() {
    return detail;
  }

  /**
   * 根据中文找枚举名  模糊匹配
   */
  public static String findOrderStateByValue(String value) {
    if (StrUtil.isBlank(value)) {
      return null;
    }
    for (SupplierOrderState statusEnum : SupplierOrderState.values()) {
      if (StrUtil.contains(value, statusEnum.getValue())) {
        return statusEnum.name();
      }
    }
    return null;
  }

  public static void main(String[] args) {
    System.out.println(findOrderStateByValue("订单状态暂存"));
  }

}
