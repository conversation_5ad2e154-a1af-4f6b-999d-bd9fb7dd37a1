package com.xhgj.srm.common.dto.invoice;

import com.xhgj.srm.common.enums.InvoiceTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * Created by Geng Shy on 2023/9/8
 * 发票Ocr识别信息vo对象
 */
@Data
public class InvoiceIdentifyResultDTO {

  @ApiModelProperty(value = "发票号")
  private String invoiceNum;

  @ApiModelProperty(value = "订单号")
  private String dockingOrderNo;

  @ApiModelProperty(value = "发票代码")
  private String invoiceCode;

  @ApiModelProperty("合计金额")
  private String totalAmount;

  @ApiModelProperty("合计税额")
  private String totalTaxAmount;

  @ApiModelProperty("价税合计")
  private String totalAmountIncludingTax;

  @ApiModelProperty(value = "开票时间")
  private Long invoiceTime;

  @ApiModelProperty(value = "前缀路径")
  private String baseUrl;

  @ApiModelProperty(value = "附件路径")
  private String url;

  @ApiModelProperty(value = "发票类型")
  private InvoiceTypeEnum invoiceType;

  @ApiModelProperty(value = "发票备注")
  private String remarks;

  @ApiModelProperty(value = "校验码")
  private String checkCode;
  @ApiModelProperty(value = "销方名称")
  private String sellerName;
  @ApiModelProperty(value = "购方名称")
  private String payerName;


  public String getSellerName() {
    return sellerName.replace("（", "(").replace("）", ")");
  }
  public String getPayerName() {
    return payerName.replace("（", "(").replace("）", ")");
  }

}
