package com.xhgj.srm.common.utils;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.xhgj.mall.express.support.dto.LogisticsInfoDTO;
import com.xhgj.mall.express.support.param.OrcParam;
import com.xhgj.srm.common.Constants;
import com.xhgj.srm.common.config.SrmConfig;
import com.xhgj.srm.common.dto.ExpressCompanyDTO;
import com.xhgj.srm.common.dto.PaymentStatus;
import com.xhgj.srm.common.utils.dingding.DingUtils;
import com.xhiot.boot.core.common.exception.CheckException;
import com.xhiot.boot.core.common.util.StringUtils;
import com.xhiot.boot.core.config.BootConfig;
import com.xhiot.boot.mvc.base.ResultBean;
import java.io.BufferedReader;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import javax.annotation.Resource;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.HttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.DefaultHttpClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class HttpUtil {

    /**
     * 根据类目获取扩展属性
     */
    private final String field_getByCategoryId;

    /**
     * mpm一审自动审核
     */
    private final String product_automaticAudit;

    /**
     * 商品分頁
     */
    private final String productInfo_list;
    /**
     * 商品是否存在
     */
    private final String exists_product_code;
    /**
     * mpm唤醒机器人
     */
    private final String rouse_robot;
    /**
     * mpm查询机器人执行结果
     */
    private final String query_robot_execution_result;
    /**
     * mpm保存国网最低价
     */
    private final String add_gw_bottom_price;

    /**
     * 获取货主接口
     */
    private final String shipplier_list;
    /**
     * 撤回商品
     */
    private final String product_withdrawProductAssess;
    /**
     * 放弃商品
     */
    private final String product_giveUpProductAssess;
    /**
     * 验证物料重复
     */
    private final String valid_product;
    /**
     * 物流url（旧）
     */
    private final String express_url;
    /**
     * 物流url（OMS承运商物流）
     */
    private final String logistics_companies_url;
    /**
     * 政采云物流url
     */
    private final String express_dock_url;
    /**
     * 履约订单取消审核
     */
    private final String platform_check_order_cancel_url;
    /**
     * 履约订单退货取消审核
     */
    private final String platform_check_order_return_url;
    /**
     * 履约订单退货完结
     */
    private final String platform_order_return_end_url;
    /**
     * 履约订单发货物是否完结
     */
    private final String platform_delivery_update_url;
    /**
     * 履约订单状态更新
     */
    private final String platform_update_order_state_url;
    /**
     * oms订单取消审核
     */
    private final String oms_check_order_cancel_url;
    /**
     * oms订单退货取消审核
     */
    private final String oms_check_order_return_url;
    /**
     * oms订单退货完结
     */
    private final String oms_order_return_end_url;
    /**
     * oms 订单发货物是否完结
     */
    private final String oms_delivery_update_url;
    /**
     * oms 订单状态更新
     */
    private final String oms_update_order_state_url;

    /**
     * 通过订单编号获取订单的大票
     */
    private final String getOrderLargeTicketProjectNoUrl;

    /**
     * 获取mpm所有项目
     */
    private final String mpm_get_platform_and_status;


    private final String SHORT_NAME = "SRM";

  /** OA用户更新 */
    private final String oa_user_update;
  /** OA用户 */
    private final String oa_user_url;
  /**
   * 通过履约获取物流状态
   */
  private final String platform_logistics_status_url;

  /**
   * 履约获取订单回款信息
   */
  private final String platform_order_customer_return_progress_url;
  private final String oa_user_allow;

  /**
   * mdm，获取供应商区域库存列表
   */
  private final String mdm_product_inventory_management_page;

  /**
   * mdm 区域库存新增或编辑
   */
  private final String mdm_product_inventory_management_add;
  /**
   * mdm，获取供应商区域库存列表
   */
  private final String mdm_product_inventory_management_detail;
  /**
   * mdm，修改区域库存状态
   */
  private final String mdm_product_inventory_status_update;

  /**
   * mdm，获取区域地址信息
   */
  private final String get_mdm_platform_address_list;

  /**
   * mdm，根据平台编码查询地址信息
   */
  private final String get_mdm_platform_address_by_type;

  /**
   * mdm，区域库存新增或编辑(excel)
   */
  private final String mdm_product_inventory_management_import_add;
  /**
   * srm，通过审核
   */
  private final String srm_pass_assess;
  /**
   * srm，驳回审核
   */
  private final String srm_pass_un_assess;
  /**
   * oms，查询验收单模版信息
   */
  private final String get_oms_packing_template_by_customer_code;
  /**
   * oms，查询订单客户签收时间信息
   */
  private final String oms_get_order_info_url;

  /**
   * 快递100面单OCR识别
   */
  private final String kuaidi_100_orc_url;

  /**
   * 钉钉审批事件消费信息回调
   */
  private final String mdm_send_approval_event_back_url;

  /**
   * 查询MDM客户平台列表-含平台和招投标项目信息
   */
  private final String mdm_customer_platform_list_with_bid_page;

  /**
   * 获取MPM库存物料信息
   */
  private final String mpm_inventory_product_info;

  @Autowired
  private CommonHttpsUtil commonHttpsUtil;
  @Resource
  private BootConfig bootConfig;

    public HttpUtil(SrmConfig config) {
        String apiUrl = config.getApiUrl();
        String dockUrl = config.getDockUrl();
        String platformUrl = config.getPlatformUrl();
        String platformOrderUrl = config.getPlatformOrderUrl();
        String omsUrl = config.getOmsUrl();
        String srmManageUrl = config.getSrmManageUrl();
        String omsPlatformPortalUrl = config.getOmsPlatformPortalUrl();
        String mdmApprovalEventUrl = config.getMdmApprovalEventUrl();
        String mdmCustomerPlatformUrl = config.getMdmCustomerPlatformUrl();
        mpm_get_platform_and_status = apiUrl + "/platform/getPlatformAndDockingStatus";
        field_getByCategoryId = apiUrl + "/field/getByCategoryId";
        product_automaticAudit = apiUrl + "/product/automaticAudit";
        shipplier_list = apiUrl + "/organization/srmShipperList";
        product_withdrawProductAssess = apiUrl + "/product/withdrawProductAssess";
        product_giveUpProductAssess = apiUrl + "/product/giveUpProductAssess";
        productInfo_list = apiUrl + "/product/getSrmProductInfoList";
        exists_product_code = apiUrl + "/product/existsProductByCode";
        rouse_robot = apiUrl + "/product/rouseRobot";
        query_robot_execution_result = apiUrl + "/product/queryRobotExecutionResult";
        add_gw_bottom_price = apiUrl + "/product/addGwBottomPrice";
        valid_product =  apiUrl + "/product/validateRepeatAndGetSign";
        express_url =  dockUrl + "/express/company/getCompanyList";
        logistics_companies_url = omsUrl + "/dock/SrmOrder/getErpExpressList";
        express_dock_url =  dockUrl + "/dockingIns/getOrderLogisticByType";
        platform_check_order_cancel_url =  platformUrl + "/dock/SrmOrder/srmOrderCancelCheck";
        platform_check_order_return_url =  platformUrl + "/dock/SrmOrder/srmOrderReturnCheck";
        platform_order_return_end_url =  platformUrl + "/dock/SrmOrder/srmOrderReturnComplete";
        platform_delivery_update_url =  platformUrl + "/dock/SrmOrder/srmDeliveryOrderIsEnd";
        platform_update_order_state_url =  platformUrl + "/dock/SrmOrder/setSrmOrder";


        oms_check_order_cancel_url =  omsUrl + "/dock/SrmOrder/srmOrderCancelCheck";
        oms_check_order_return_url =  omsUrl + "/dock/SrmOrder/srmOrderReturnCheck";
        oms_order_return_end_url =  omsUrl + "/dock/SrmOrder/srmOrderReturnComplete";
        oms_delivery_update_url =  omsUrl + "/dock/SrmOrder/srmDeliveryOrderIsEnd";
        oms_update_order_state_url =  omsUrl + "/dock/SrmOrder/setSrmOrder";

        getOrderLargeTicketProjectNoUrl = platformOrderUrl + "/order/getOrderLargeTicketProjectNo";
        oa_user_update = apiUrl.replace("/srm", "") + "/oa/getOaPersonById";
        oa_user_url = apiUrl.replace("/srm", "") + "/oa/getOaPersonPage";
        platform_logistics_status_url = dockUrl + "/express/company/getTrajectory";
        kuaidi_100_orc_url = dockUrl + "/express/order/getLogisticsInfoByUrl";
      platform_order_customer_return_progress_url = platformUrl +
          "/open/getPaymentByOrderNo";
      oa_user_allow = apiUrl.replace("/srm", "") + "/oa/allocatePersons";
      mdm_product_inventory_management_page = apiUrl + "/product/getProductInventoryManagementPage";
      mdm_product_inventory_management_add = apiUrl + "/product/addOrUpdateProductInventoryManagement";
      mdm_product_inventory_management_detail = apiUrl + "/product/getProductInventoryManagementDetails";
      mdm_product_inventory_status_update = apiUrl + "/product/areaInventoryUpdateState";
      get_mdm_platform_address_list = apiUrl + "/product/getPlatformAddressList";
      mdm_product_inventory_management_import_add = apiUrl + "/product/importAddOrUpdateProductInventoryManagement";
      get_mdm_platform_address_by_type = apiUrl + "/product/getAddressByType";
      srm_pass_assess = srmManageUrl + "/assess/doPassHandle";
      srm_pass_un_assess = srmManageUrl + "/assess/doRejectHandle";
      get_oms_packing_template_by_customer_code = omsPlatformPortalUrl + "/dock/SrmOrder/getReceiptInfo";
      oms_get_order_info_url =  omsPlatformPortalUrl + "/dock/SrmOrder/getOrderInfo";
      mdm_send_approval_event_back_url =  mdmApprovalEventUrl + "/eventDetail/approvalEventCallback";
      mdm_customer_platform_list_with_bid_page= mdmCustomerPlatformUrl + "/platform/listWithBid";
      mpm_inventory_product_info= apiUrl + "/product/getInventoryProductInfo";
    }

  public JSONArray getPlatformAndDockingStatus(String platformCode) {
    JSONArray resData = new JSONArray();
    try {
      platformCode = StringUtils.isNullOrEmpty(platformCode) ? "" : URLEncoder.encode(platformCode,
          "utf-8");
      String outputStr = "?codes=" + platformCode;
      String jsonString = commonHttpsUtil.httpRequest(mpm_get_platform_and_status+outputStr, "GET", null);
      if (jsonString != null) {
        JSONObject jsonObject = JSONObject.parseObject(jsonString);
        if (jsonObject != null) {
          String code = jsonObject.containsKey("code") ? jsonObject.getString("code") : "";
          if ("0".equals(code) && jsonObject.containsKey("data")) {
            resData = jsonObject.getJSONArray("data");
          }
        }
      }
      return resData;
    } catch (Exception e) {
      log.error("https请求异常：{" + e.toString() + "}");
      return null;
    }
  }

    public String automaticAudit(String id) {
      String param = "id=" + id;
      return commonHttpsUtil.httpRequest(product_automaticAudit, "POST", param);
    }


    public String productBack(String param) {
        return commonHttpsUtil.httpRequest(product_withdrawProductAssess, "POST", param);
    }

    public String productGiveUp(String param) {
        return commonHttpsUtil.httpRequest(product_giveUpProductAssess, "POST", param);
    }

    public String validateRepeatAndGetSign(String param) {
        return commonHttpsUtil.httpRequest(valid_product, "POST", param);
    }


    public JSONObject getShipplierList() {
        JSONObject jo = null;
        //根据类目获取属性
        try {
            String str = commonHttpsUtil.httpRequest(shipplier_list, "GET", null);
            if (!StringUtils.isNullOrEmpty(str)) {
                jo = JSONObject.parseObject(str);
            }
        } catch (Exception e) {
            log.error("https请求异常：{" + e.toString() + "}");
        }
        return jo;
    }

    public JSONObject getExpandName(String categoryId) {
        JSONObject jo = null ;
        String outStr = "?categoryId=" + categoryId;
        //根据类目获取属性
      try {
        String str = commonHttpsUtil.httpRequest(field_getByCategoryId + outStr, "GET", null);
        if (!StringUtils.isNullOrEmpty(str)) {
          jo = JSONObject.parseObject(str);
        }
      } catch (Exception e) {
        jo = new JSONObject();
      }
        return jo;
    }


    /**
     * post请求工具类
     *
     * @param url 接口url（不包含网关），例：/oauth/token
     * @return
     * @throws Exception
     */
    public String post(String url, JSONObject json) {
        String result = "";
        HttpPost post = new HttpPost(url);
      try (DefaultHttpClient httpClient = new DefaultHttpClient();) {
        post.setHeader("Content-Type", "application/json;charset=utf-8");
        StringEntity postingString = new StringEntity(json.toString(), "utf-8");
        post.setEntity(postingString);
        HttpResponse response = httpClient.execute(post);
        try (InputStream in = response.getEntity().getContent();
            BufferedReader br = new BufferedReader(new InputStreamReader(in, "utf-8"));
        ) {
          StringBuilder strber = new StringBuilder();
          String line = null;
          while ((line = br.readLine()) != null) {
            strber.append(line + '\n');
          }
          result = strber.toString();
          return result;
        }
      } catch (Exception e) {
        log.error(e.toString());
        return null;
      }
    }


  @SneakyThrows
  public JSONObject getProductInventoryManagementPage(String supplierCode, String platformCode,
      String name, String productCode, String manuCode,
      Integer pageNo, Integer pageSize) {
    JSONObject jo = null ;
    supplierCode = !StringUtils.isNullOrEmpty(supplierCode)? URLEncoder.encode(supplierCode,"UTF-8"):StrUtil.EMPTY;
    platformCode = !StringUtils.isNullOrEmpty(platformCode)? URLEncoder.encode(platformCode,"UTF-8"):StrUtil.EMPTY;
    name = !StringUtils.isNullOrEmpty(name)? URLEncoder.encode(name,"UTF-8"):StrUtil.EMPTY;
    productCode = !StringUtils.isNullOrEmpty(productCode)? URLEncoder.encode(productCode,"UTF-8"):StrUtil.EMPTY;
    manuCode = !StringUtils.isNullOrEmpty(manuCode)? URLEncoder.encode(manuCode,"UTF-8"):StrUtil.EMPTY;
    String outStr = "?supplierCode=" + supplierCode + "&platformCode=" + platformCode + "&name=" + name
            + "&productCode=" + productCode + "&manuCode=" + manuCode + "&pageNo=" + pageNo
            + "&pageSize=" + pageSize;
    String str = commonHttpsUtil.httpRequest(mdm_product_inventory_management_page + outStr, "GET", null);
    if (!StringUtils.isNullOrEmpty(str)) {
      jo = JSONObject.parseObject(str);
    }
    return jo;
  }

  @SneakyThrows
  public JSONObject getMDMProductInventoryDetail(String platformCode, String productCode) {
    JSONObject jo = null ;
    platformCode = !StringUtils.isNullOrEmpty(platformCode)? URLEncoder.encode(platformCode,"UTF-8"):StrUtil.EMPTY;
    productCode = !StringUtils.isNullOrEmpty(productCode)? URLEncoder.encode(productCode,"UTF-8"):StrUtil.EMPTY;
    String outStr = "?platformCode=" + platformCode + "&productCode=" + productCode;
    String str = commonHttpsUtil.httpRequest(mdm_product_inventory_management_detail + outStr, "GET", null);
    if (!StringUtils.isNullOrEmpty(str)) {
      jo = JSONObject.parseObject(str);
    }
    return jo;
  }

  @SneakyThrows
  public JSONObject getMDMPlatformAddressList(String platformCode) {
    JSONObject jo = null ;
    platformCode = !StringUtils.isNullOrEmpty(platformCode)? URLEncoder.encode(platformCode,"UTF-8"):StrUtil.EMPTY;
    String outStr = "?platformCode=" + platformCode;
    String str = commonHttpsUtil.httpRequest(get_mdm_platform_address_list + outStr, "GET", null);
    if (!StringUtils.isNullOrEmpty(str)) {
      jo = JSONObject.parseObject(str);
    }
    return jo;
  }

  /**
   * 根据平台编码查询地址信息
   * @param platformCode
   */
  @SneakyThrows
  public JSONObject getMDMAddressByType(String platformCode) {
    JSONObject jo = null ;
    platformCode = !StringUtils.isNullOrEmpty(platformCode)? URLEncoder.encode(platformCode,"UTF-8"):StrUtil.EMPTY;
    String outStr = "?platformCode=" + platformCode;
    String str = commonHttpsUtil.httpRequest(get_mdm_platform_address_by_type + outStr, "GET", null);
    if (!StringUtils.isNullOrEmpty(str)) {
      jo = JSONObject.parseObject(str);
    }
    return jo;
  }



  /**
   * 修改区域库存状态
   * @param jsonStr
   */
  @SneakyThrows
  public JSONObject updateStockStatus(String jsonStr) {
    JSONObject result = null ;
    String str = commonHttpsUtil.httpRequestJson(mdm_product_inventory_status_update, "POST", jsonStr);
    if (!StringUtils.isNullOrEmpty(str)) {
      result = JSONObject.parseObject(str);
    }
    return result;
  }

  /**
   * 区域库存新增或编辑(页面)
   * @param jsonStr
   * @return
   */
  @SneakyThrows
  public JSONObject addOrUpdateProductInventoryManagement(String jsonStr) {
    JSONObject result = null ;
    String str = commonHttpsUtil.httpRequestJson(mdm_product_inventory_management_add, "POST", jsonStr);
    if (!StringUtils.isNullOrEmpty(str)) {
      result = JSONObject.parseObject(str);
    }
    return result;
  }

  /**
   * 导入区域库存新增或编辑(excel)
   * @param jsonStr
   * @return
   */
  @SneakyThrows
  public JSONObject importAddOrUpdateProductInventoryManagement(String jsonStr) {
    JSONObject result = null ;
    String str = commonHttpsUtil.httpRequestJson(mdm_product_inventory_management_import_add,
        "POST", jsonStr);
    if (!StringUtils.isNullOrEmpty(str)) {
      result = JSONObject.parseObject(str);
    }
    return result;
  }


  @SneakyThrows
  @Deprecated
  public JSONObject getSrmProductInfoList(List<String> codes,String useCode) {
    JSONObject jo = new JSONObject() ;
    JSONObject jsonResult = null ;
    jo.put("codes",codes);
    jo.put("useCode",useCode);
    //是否禁用：0不禁用，1禁用
    jo.put("isDisable","0");
    String str = commonHttpsUtil.httpRequestJson(productInfo_list, "POST", jo.toJSONString());
    if (!StringUtils.isNullOrEmpty(str)) {
      jsonResult = JSONObject.parseObject(str);
    }
    return jsonResult;
  }

  /**
   * mpm唤醒机器人
   * @return
   */
  @SneakyThrows
  public String rouseRobot(String gwProductSerialNumber) {
    String result = "";
    gwProductSerialNumber = !StringUtils.isNullOrEmpty(gwProductSerialNumber)? URLEncoder.encode(gwProductSerialNumber,"UTF-8"):"";
    String outStr = "?gwProductSerialNumber=" + gwProductSerialNumber;
    String str = commonHttpsUtil.httpRequest(rouse_robot + outStr, "GET", null);
    if (!StringUtils.isNullOrEmpty(str)) {
      JSONObject json = JSONObject.parseObject(str);
      if(json!=null){
        String resCode = json.containsKey("code") ? json.getString("code") : "";
        if(resCode.equals("0")){
          result = json.containsKey("data") ? json.getString("data") : "";
        } else {
          String msg = json.containsKey("msg") ? json.getString("msg") : "";
          throw new CheckException(msg);
        }
      } else {
        throw new CheckException("网络异常,请稍后重试");
      }
    }
    return result;
  }

  /**
   * mpm查询机器人执行结果
   * @param taskId 任务id
   * @return
   */
  @SneakyThrows
  public String queryRobotExecutionResult(String taskId,String gwProductSerialNumber) {
    String result = "";
    taskId = !StringUtils.isNullOrEmpty(taskId)? URLEncoder.encode(taskId,"UTF-8"):"";
    gwProductSerialNumber = !StringUtils.isNullOrEmpty(gwProductSerialNumber)? URLEncoder.encode(gwProductSerialNumber,"UTF-8"):"";
    String outStr = "?taskId="+taskId+"&gwProductSerialNumber="+gwProductSerialNumber;
    String str = commonHttpsUtil.httpRequest(query_robot_execution_result+outStr, "GET", null);
    if (StrUtil.isNotEmpty(str)) {
      JSONObject json = JSONObject.parseObject(str);
      if(json!=null){
        String resCode = json.containsKey("code") ? json.getString("code") : "";
        if(resCode.equals("0")){
          result = json.containsKey("data") ? json.getString("data") : "";
        } else {
          String msg = json.containsKey("msg") ? json.getString("msg") : "";
          throw new CheckException(msg);
        }
      } else {
        throw new CheckException("网络异常,请稍后重试");
      }
    }
    return result;
  }

  /**
   * 保存国网最低价
   * @param jsonStr (code 物料编码 price价格)
   */
  @SneakyThrows
  public void addGwBottomPrice(String jsonStr) {
    log.info("保存国网最低价入参:" + jsonStr);
    JSONObject result = null;
    String responseBody = commonHttpsUtil.httpRequestJson(add_gw_bottom_price, "POST", jsonStr);
    if (StrUtil.isNotBlank(responseBody)) {
      result = JSONObject.parseObject(responseBody);
    }
    log.info("保存国网最低价出参:" + responseBody);
    if (result == null || Objects.equals(Constants.HTTP_RESPONSE_CODE_NO,
        result.getInteger("code"))) {
      throw new CheckException(result != null ? "操作失败,mpm平台报错: " + result.getString(
          "msg") : "操作失败,网络异常");
    }
  }
  @SneakyThrows
  public Boolean existsProductByCode(String supplierId,String code) {
      Boolean result = false;
      JSONObject jo = null ;
      supplierId = !StringUtils.isNullOrEmpty(supplierId)? URLEncoder.encode(supplierId,"UTF-8"):"";
      code = !StringUtils.isNullOrEmpty(code)? URLEncoder.encode(code,"UTF-8"):"";
      String outStr = "?code="+code+"&srmSupplierId="+supplierId;
      String str = commonHttpsUtil.httpRequest(exists_product_code + outStr, "GET", null);
      if (!StringUtils.isNullOrEmpty(str)) {
        jo = JSONObject.parseObject(str);
        if(jo!=null){
          String resCode = jo.containsKey("code") ? jo.getString("code") : "";
          if(resCode.equals("0")){
            result = jo.containsKey("data") ? jo.getBooleanValue("data") : false;
          } else {
            String msg = jo.containsKey("msg") ? jo.getString("msg") : "";
            throw new CheckException(msg);
          }
        } else {
          throw new CheckException("网络异常,请稍后重试");
        }
      }
      return result;
  }

    /**
     * 获取物流公司
     * @param name
     * @return
     */
    public JSONObject getExpressCompany(String name) {
        JSONObject jo = null ;
        String outStr = "?name=" + name;
        //根据类目获取属性
        String str = commonHttpsUtil.httpRequest(express_url + outStr, "GET", null);
        if (!StringUtils.isNullOrEmpty(str)) {
            jo = JSONObject.parseObject(str);
        }
        return jo;
    }

  /**
   * 获取OMS承运商物料公司列表
   */
  public List<ExpressCompanyDTO> getLogisticsCompanies(String code) {
    JSONObject responseBody = null;
    String responseJson;
    String outStr = "";
    if(StrUtil.isNotEmpty(code)){
      outStr = "?expressCode=" + code;
    }
    try {
      responseJson = commonHttpsUtil.httpRequest(logistics_companies_url + outStr, "GET", null);
    } catch (Exception e) {
      throw new RuntimeException("调用OMS承运商物流公司列表接口异常");
    }
    if (StrUtil.isNotBlank(responseJson)) {
      responseBody = JSONObject.parseObject(responseJson);
    }
    if (responseBody == null || !Objects.equals(responseBody.getInteger("code"),
        Constants.HTTP_RESPONSE_CODE_YES)) {
      log.error("获取OMS承运商物流公司列表异常，OMS响应：{}", responseJson);
      throw new RuntimeException("获取OMS承运商物流公司列表异常");
    }
    List<ExpressCompanyDTO> expressCompanies;
    String data = responseBody.getString("data");
    expressCompanies = JSON.parseObject(data, new TypeReference<List<ExpressCompanyDTO>>() {});
    if (CollUtil.isEmpty(expressCompanies)) {
      log.error("获取OMS承运商物流公司列表异常，OMS响应：{}", responseJson);
      throw new RuntimeException("获取OMS承运商物流公司列表异常");
    }
    return expressCompanies;
  }

    /**
     * 获取政采云物流公司
     * @param type
     * @return
     */
    public JSONObject getDockingExpressCompany(String type) {
        JSONObject jo = null ;
        String outStr = "?type=" + type;
        //根据类目获取属性
        String str = commonHttpsUtil.httpRequest(express_dock_url + outStr, "GET", null);
        if (!StringUtils.isNullOrEmpty(str)) {
            jo = JSONObject.parseObject(str);
        }
        return jo;
    }

    /**
     * 验证商品重复
     * @Title:
     * @Description:
     * @param brandId
     * @param model
     * @param name
     * @param unit
     * @param desc
     * <AUTHOR>
     * @date 2021/8/16 10:18
     */
    public String validProduct(String brandId,String model,String name,String unit,String desc) {
        String pCode = "";
        try {
            String param = "brandId=" + brandId + "&description="+desc + "&manuCode="+model + "&name="+name + "&unit="+unit;
            String str = validateRepeatAndGetSign(param);
            if (!StringUtils.isNullOrEmpty(str)) {
                JSONObject resData = JSONObject.parseObject(str);
                if(resData!=null){
                    String code = resData.containsKey("code")?resData.getString("code"):"";
                    if(code.equals("0")){
                        pCode = resData.containsKey("data")?resData.getString("data"):"";
                    } else {
                        String msg = resData.containsKey("msg")?resData.getString("msg"):"";
                        throw new CheckException(msg);
                    }
                } else {
                    throw new CheckException("网络异常,请稍后重试");
                }
            } else {
                throw new CheckException("网络异常,请稍后重试");
            }
        } catch (Exception e) {
            log.error("https请求异常：{" + e.toString() + "}");
            throw new CheckException("https请求异常：{" + e.toString() + "}");
        }
        return pCode;
    }

    /**
     * 履约订单取消审核
     * @param param
     * @return
     */
    public JSONObject omsCheckOrderCancel(String param) {
        JSONObject result = null ;
        String str = commonHttpsUtil.httpRequest(oms_check_order_cancel_url, "POST", param);
        if (!StringUtils.isNullOrEmpty(str)) {
            result = JSONObject.parseObject(str);
        }
        return result;
    }

    /**
     * 履约订单取消审核
     * @param param
     * @return
     */
    public JSONObject platformCheckOrderCancel(String param) {
        JSONObject result = null ;
        String str = commonHttpsUtil.httpRequest(platform_check_order_cancel_url, "POST", param);
        if (!StringUtils.isNullOrEmpty(str)) {
            result = JSONObject.parseObject(str);
        }
        return result;
    }

    /**
     * 履约订单退货审核
     * @param param
     * @return
     */
    public JSONObject omsCheckOrderReturn(String param) {
        JSONObject result = null ;
        String str = commonHttpsUtil.httpRequest(oms_check_order_return_url, "POST", param);
        if (!StringUtils.isNullOrEmpty(str)) {
            result = JSONObject.parseObject(str);
        }
        return result;
    }

    /**
     * 履约订单退货审核
     * @param param
     * @return
     */
    public JSONObject platformCheckOrderReturn(String param) {
        JSONObject result = null ;
        String str = commonHttpsUtil.httpRequest(platform_check_order_return_url, "POST", param);
        if (!StringUtils.isNullOrEmpty(str)) {
            result = JSONObject.parseObject(str);
        }
        return result;
    }


    /**
     * 履约订单退货完结
     * @param param
     * @return
     */
    public JSONObject omsCheckOrderReturnEnd(String param) {
        JSONObject result = null ;
        String str = commonHttpsUtil.httpRequest(oms_order_return_end_url, "POST", param);
        if (!StringUtils.isNullOrEmpty(str)) {
            result = JSONObject.parseObject(str);
        }
        return result;
    }

    /**
     * 履约订单退货完结
     * @param param
     * @return
     */
    public JSONObject platformCheckOrderReturnEnd(String param) {
        JSONObject result = null ;
        String str = commonHttpsUtil.httpRequest(platform_order_return_end_url, "POST", param);
        if (!StringUtils.isNullOrEmpty(str)) {
            result = JSONObject.parseObject(str);
        }
        return result;
    }

    /**
     * 履约订单发货单是否完结
     * @return
     */
    public Boolean orderOmsDeliveryIsEnd(String deliveryNo,String type) {
        boolean isEnd = false;
        JSONObject result;
        String outStr = "?deliveryNo=" + deliveryNo+"&relationType="+type;
        String str = commonHttpsUtil.httpRequest(oms_delivery_update_url + outStr, "GET", null);
        if (!StringUtils.isNullOrEmpty(str)) {
            result = JSONObject.parseObject(str);
            if(result!=null){
                isEnd = result.containsKey("data")?result.getBoolean("data"):false;
            }
        }
        return isEnd;
    }

    /**
     * 履约订单发货单是否完结
     * @return
     */
    public Boolean orderDeliveryIsEnd(String deliveryNo,String type) {
        boolean isEnd = false;
        JSONObject result;
        String outStr = "?deliveryNo=" + deliveryNo+"&relationType="+type;
        String str = commonHttpsUtil.httpRequest(platform_delivery_update_url + outStr, "GET", null);
        if (!StringUtils.isNullOrEmpty(str)) {
            result = JSONObject.parseObject(str);
            if(result!=null){
                isEnd = result.containsKey("data")?result.getBoolean("data"):false;
            }
        }
        return isEnd;
    }

    /**
     * 修改履约平台订单状态
     * @return
     */
    public void updateOmsOrderState(String param) {
        String str = commonHttpsUtil.httpRequestJson(oms_update_order_state_url, "POST", param);
        log.info("【修改履约平台订单状态返参】"+str);
    }

    /**
     * 修改履约平台订单状态
     * @return
     */
    public void updatePlatformOrderState(String param) {
        String str = commonHttpsUtil.httpRequestJson(platform_update_order_state_url, "POST", param);
        log.info("【修改履约平台订单状态返参】"+str);
    }

  /**
   * 通过订单编号获取订单的大票
   *
   * @param orderNo 订单编号
   * @param dockingOrderType 订单类型
   */
  public JSONObject getOrderLargeTicketProjectNo(String orderNo, String dockingOrderType) {
    JSONObject jo = null;
    String outStr = "?dockingOrderNo=" + orderNo + "&dockingOrderType=" + dockingOrderType;
    log.info("【通过订单编号获取订单的大票】接口请求入参：" + getOrderLargeTicketProjectNoUrl + outStr);
    // 根据类目获取属性
    String str = commonHttpsUtil.httpRequest(getOrderLargeTicketProjectNoUrl + outStr, "GET", null);
    if (!StringUtils.isNullOrEmpty(str)) {
      log.info("【通过订单编号获取订单的大票】接口返参：" + str);
      jo = JSONObject.parseObject(str);
    }
    return jo;
  }


  /**
   * 根据mdmId获取OA用户详情
   * @param oaId
   * @return
   */
  public JSONObject getOAUserInfoById(String oaId) {
    JSONObject resData = new JSONObject();
    try {
      oaId = !StringUtils.isNullOrEmpty(oaId) ? oaId : "";
      String outputStr = "?personId=" + oaId + "&systemShortName=" + SHORT_NAME;
      String jsonString = commonHttpsUtil.httpRequest(oa_user_update + outputStr, "GET", null);
      if (jsonString != null) {
        JSONObject jsonObject = JSONObject.parseObject(jsonString);
        if (jsonObject != null) {
          String code = jsonObject.containsKey("code") ? jsonObject.getString("code") : "";
          if ("0".equals(code) && jsonObject.containsKey("data")) {
            resData = jsonObject.getJSONObject("data");
          }
        }
      }
      return resData;
    } catch (Exception e) {
      log.error("https请求异常：{" + e.toString() + "}");
      return null;
    }
  }
  public JSONObject getOAUserList(String name, String mobile, int pageNo, int pageSize) {
    JSONObject resData = new JSONObject();
    try {
      mobile = !StringUtils.isNullOrEmpty(mobile) ? mobile : "";
      name = !StringUtils.isNullOrEmpty(name) ? URLEncoder.encode(name, "utf-8") : "";
      String outputStr =
          "?name="
              + name
              + "&systemShortName="
              + SHORT_NAME
              + "&mobile="
              + mobile
              + "&pageNo="
              + pageNo
              + "&pageSize="
              + pageSize;
      String jsonString = commonHttpsUtil.httpRequest(oa_user_url + outputStr, "GET", null);
      if (jsonString != null) {
        JSONObject jsonObject = JSONObject.parseObject(jsonString);
        if (jsonObject != null) {
          String code = jsonObject.containsKey("code") ? jsonObject.getString("code") : "";
          if ("0".equals(code) && jsonObject.containsKey("data")) {
            resData = jsonObject.getJSONObject("data");
          }
        }
      }
      return resData;
    } catch (Exception e) {
      log.error("https请求异常：{" + e.toString() + "}");
      return null;
    }
  }

  /**
   * 履约订单发货单是否完结
   * @param expressCode 物流公司编码
   * @param expressNo 物流单号
   * @return
   */
  public JSONArray getLogisticsStatus(String expressNo,String expressCode,String mobile) {
    JSONObject result;
    String outStr =
        "?expressNo=" + expressNo +"&expressCode=" + expressCode+(StrUtil.isEmpty(mobile) ? "":
            "&phone="+mobile);
    String str = commonHttpsUtil.httpRequest(platform_logistics_status_url + outStr, "GET", null);
    if (StrUtil.isBlank(str)) {
      log.error("请求履约查询物流签收状态接口异常，请求参数：{}",
          "expressNo：" + expressNo + "expressCode：" + expressCode);
      throw new RuntimeException("请求履约查询物流签收状态接口异常");
    }
    result = JSONObject.parseObject(str);
    if (!Constants.HTTP_RESPONSE_CODE_YES.equals(result.getInteger("code"))) {
      log.error("请求履约查询物流签收状态接口异常，请求参数{}，响应：{}",
          "expressNo" + expressNo + "expressCode" + expressCode, result.toJSONString());
      throw new RuntimeException("请求履约查询物流签收状态接口异常");
    }
    String data = result.getString("data");
    return JSON.parseArray(data);
  }


  public PaymentStatus getOrderCustomerReturn(String orderNo, String platformCode,
      boolean sendWarningMessage) {
    JSONObject result;
    String outStr = "?dockingOrderNo=" + orderNo +"&dockingOrderType=" + platformCode;
    String response = null;
    try {
      response =
          commonHttpsUtil.httpRequest(platform_order_customer_return_progress_url + outStr,
              "GET", null);
    } catch (Exception e) {
      log.error("请求履约查询订单回款信息接口异常，请求参数：{}",
          outStr);
      if (sendWarningMessage) {
        sendOrderCustomerReturnStatusWarningMessage(outStr, JSON.toJSONString(response));
      }
      return null;
    }
    if (StrUtil.isBlank(response)) {
      log.error("请求履约查询订单回款信息接口异常，请求参数：{}",
          outStr);
      if (sendWarningMessage) {
        sendOrderCustomerReturnStatusWarningMessage(outStr, JSON.toJSONString(response));
      }
      return null;
    }
    result = JSONObject.parseObject(response);
    if (!Constants.HTTP_RESPONSE_CODE_YES.equals(result.getInteger("code"))) {
      log.error("请求履约查询订单回款信息接口异常，请求参数{}，响应：{}",
          outStr, result.toJSONString());
      if (sendWarningMessage) {
        sendOrderCustomerReturnStatusWarningMessage(outStr, JSON.toJSONString(response));
      }
      return null;
    }
    return JSON.parseObject(result.getString("data"), new TypeReference<PaymentStatus>() {});
  }

  private void sendOrderCustomerReturnStatusWarningMessage(String requestMessage,
      String responseMessage) {
    String env = bootConfig.getEnv();
    DingUtils.sendMsgByWarningRobot(
        "【" + env + "环境 " + bootConfig.getAppName() + "】调用履约平台查询订单回款状态接口异常："
            + "请求参数：【"+ requestMessage +"】"
            + "履约平台返回:" + responseMessage + "，请及时处理！", env);
  }

  public boolean allocatePersons(String mdmId) {
    boolean isSuccess = false;
    try {
      List<String> ids = new ArrayList<>();
      ids.add(mdmId);
      Map<String, Object> param = new HashMap<>();
      param.put("systemShortName","SRM");
      param.put("personIds", ids);
      String str = postDockingHttpUtils(oa_user_allow,param);
      System.out.println("mdm授权返回"+str);
      if (str != null) {
        JSONObject jsonObject = JSONObject.parseObject(str);
        if (jsonObject != null) {
          String code = jsonObject.containsKey("code") ? jsonObject.getString("code") : "";
          if ("0".equals(code)) {
            isSuccess = true;
          }
        }
      }
    } catch (Exception e) {
      log.error("https请求异常：{" + e + "}");
    }
    return isSuccess;
  }

  public  String postDockingHttpUtils(String url, Map<String, Object> map) {
    String getResult = "";
    try {
      if (!StringUtils.isNullOrEmpty(url)) {
        getResult = cn.hutool.http.HttpUtil
            .createPost(url)
            .form(map)
            .timeout(1000*60*2)
            .execute()
            .charset("UTF-8")
            .body();
      }
    } catch (Exception e) {
      log.error(e.toString());
      return null;
    }
    return getResult;
  }

  /**
   * 快递面单识别
   *
   * @param param 入参
   * @return
   */
  public LogisticsInfoDTO getLogisticsInfoByUrl(OrcParam param) {
    log.info("【getLogisticsInfoByUrl】请求地址：{} \n  入参：{}", kuaidi_100_orc_url, param);
    String result = cn.hutool.http.HttpUtil.post(kuaidi_100_orc_url, JSON.toJSONString(param));
    log.info("【getLogisticsInfoByUrl】返回：{} ", result);
    ResultBean<LogisticsInfoDTO> resultBean =
        JSON.parseObject(result, new TypeReference<ResultBean<LogisticsInfoDTO>>() {});
    if(ObjectUtil.notEqual(ResultBean.SUCCESS,resultBean.getCode())){
      throw new CheckException(resultBean.getMsg());
    }
    return resultBean.getData();
  }

  /**
   * @description: 钉钉供应商审批通过
   * @param: param
   **/
  public void handleAssess(String param) {
    JSONObject result;
    log.info("【钉钉供应商审批通过】请求地址：{} \n  入参：{}", srm_pass_assess, param);
    String resultStr = commonHttpsUtil.httpRequestJson(srm_pass_assess, "POST", param);
    if (StrUtil.isBlank(resultStr)) {
      log.info("请求钉钉通过审核接口异常，请求参数：{}",param);
      return;
    }
    result = JSONObject.parseObject(resultStr);
    String data = result.getString("data");
    if (StrUtil.isBlank(data) || !Constants.HTTP_RESPONSE_CODE_YES.equals(
        result.getInteger("code"))) {
      log.info("请求钉钉通过审核接口异常，请求参数{}，响应：{}", param,
          result.toJSONString());
    }
  }

  /**
   * @description: 钉钉供应商审批驳回
   * @param: param
   **/
  public void handleUnAssess(String param) {
    JSONObject result;
    log.info("【钉钉供应商审批驳回】请求地址：{} \n  入参：{}", srm_pass_un_assess, param);
    String resultStr = commonHttpsUtil.httpRequestJson(srm_pass_un_assess, "POST", param);
    if (StrUtil.isBlank(resultStr)) {
      log.info("请求钉钉驳回审核接口异常，请求参数：{}",param);
      return;
    }
    result = JSONObject.parseObject(resultStr);
    String data = result.getString("data");
    if (StrUtil.isBlank(data) || !Constants.HTTP_RESPONSE_CODE_YES.equals(
        result.getInteger("code"))) {
      log.info("请求钉钉驳回审核接口异常，请求参数{}，响应：{}", param, result.toJSONString());
    }
  }
  /**
   * @description: 查询OMS验收单模版信息
   * @param: customerOrderCodes 客户订单号
   * @return: com.alibaba.fastjson.JSONObject
   **/
  public JSONObject getOmsPackingTemplateByCustomerCode(String customerOrderCodes) {
    JSONObject jo = null;
    String outStr = "?dockingOrderNo=" + customerOrderCodes;
    log.info("【查询OMS验收单模版信息】接口请求入参：" + get_oms_packing_template_by_customer_code + outStr);
    String str =
        commonHttpsUtil.httpRequest(get_oms_packing_template_by_customer_code + outStr, "GET", null);
    if (!StringUtils.isNullOrEmpty(str)) {
      jo = JSONObject.parseObject(str);
      log.info("【查询OMS验收单模版信息】接口返参：" + str);
    }
    return jo;
  }
  /**
   * @description: 查询履约订单客户签收时间
   * @param: dockingOrderNo
   * @param: dockingOrderType
   * @param: supplierOrderId
   **/
  public String updateOrderCustomerAcceptTime(String dockingOrderNo, String dockingOrderType,
      String supplierOrderId) {
    String outStr = "?dockingOrderNo=" + dockingOrderNo + "&dockingOrderType=" + dockingOrderType
        + "&supplierOrderId=" + supplierOrderId;
    return commonHttpsUtil.httpRequest(oms_get_order_info_url + outStr, "GET", null);
  }

  /**
   * 发送审批事件回调信息
   */
  public void sendApprovalEventCallback(String param) {
    commonHttpsUtil.httpRequestJson(mdm_send_approval_event_back_url, "POST", param);
  }

  /**
   * @description: 查询MDM客户平台列表X-含平台和招投标项目信息
   **/
  @SneakyThrows
  public JSONObject getMdmCustomerPlatformListPage(String jsonStr) {
    JSONObject jo = null ;
    String str = commonHttpsUtil.httpRequestJson(mdm_customer_platform_list_with_bid_page, "POST",
        jsonStr);
    if (!StringUtils.isNullOrEmpty(str)) {
      jo = JSONObject.parseObject(str);
    }
    return jo;
  }

  @SneakyThrows
  public JSONObject getInventoryProductInfoByMPM(Set<String> codes) {
    JSONObject jo = new JSONObject();
    JSONObject jsonResult = null ;
    jo.put("codes", codes);
    String str =
        commonHttpsUtil.httpRequestJson(mpm_inventory_product_info, "POST", jo.toJSONString());
    if (!StringUtils.isNullOrEmpty(str)) {
      jsonResult = JSONObject.parseObject(str);
    }
    return jsonResult;
  }
}
