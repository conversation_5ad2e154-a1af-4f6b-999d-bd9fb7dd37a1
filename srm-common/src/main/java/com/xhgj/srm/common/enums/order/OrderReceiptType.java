package com.xhgj.srm.common.enums.order;/**
 * @since 2025/3/17 15:26
 */

/**
 *<AUTHOR>
 *@date 2025/3/17 15:26:30
 *@description 回款类型
 */
public enum OrderReceiptType {
  /**
   * A类型 - 正常回款记录 --- 需要生成需付账款
   */
  NORMAL("A", "正常回款记录"),
  /**
   * B类型 - 冲销回款记录 对应A类型 --- 不额外生成需付账款
   */
  OFFSET("B", "冲销回款记录"),
  /**
   * C类型 - 多模态回款记录--根据回款金额判断生成正负记录 --- 需要生成需付账款
   */
  MULTI_MODAL("C", "多模态回款记录"),

  ;
  /**
   * code
   */
  private String code;

  /**
   * 名称
   */
  private String desc;

  OrderReceiptType(String code, String desc) {
    this.code = code;
    this.desc = desc;
  }

  public String getCode() {
    return code;
  }

  public String getDesc() {
    return desc;
  }

  public static OrderReceiptType getByCode(String code) {
    for (OrderReceiptType type : OrderReceiptType.values()) {
      if (type.getCode().equals(code)) {
        return type;
      }
    }
    return NORMAL;
  }
}
