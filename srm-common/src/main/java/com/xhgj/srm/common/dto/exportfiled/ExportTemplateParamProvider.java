package com.xhgj.srm.common.dto.exportfiled;

import java.util.List;

/**
 * 导出模板参数提供者-字段名約束
 */
public interface ExportTemplateParamProvider {
  /**
   * 获取模板id
   * @return 模板id
   */
  String getTemplateId();
  /**
   * 获取模板名称
   * @return 模板名称
   */
  String getTemplateName();

  /**
   * 获取组织编码
   * @return 组织编码
   */
  String getUserGroup();
  /**
   * 获取是否保存
   * @return 是否保存
   */
  Boolean getSave();
  /**
   * 获取选择字段列表
   * @return 选择字段列表
   */
  List<ExportFiledSelectDTO> getSelectFieldList();
}
