package com.xhgj.srm.common.vo.asmDisOrder;/**
 * @since 2025/2/25 14:03
 */

import cn.hutool.core.util.StrUtil;
import lombok.Data;
import java.math.BigDecimal;

/**
 * 组装拆卸单列表VO - 明细传输类
 */
@Data
public class AsmDisOrderItemDTO {

  /**
   * 明细id
   */
  private String id;

  /**
   * rowId
   */
  private String rowId;

  /**
   * type
   */
  private Byte type;

  /**
   * 物料编码
   */
  private String productCode;

  /**
   * 品牌
   */
  private String brand;

  /**
   * 物料名称
   */
  private String productName;

  /**
   * 规格型号
   */
  private String model;

  /**
   * 单位
   */
  private String unit;

  /**
   * 单位编码
   */
  private String unitCode;

  /**
   * 数量
   */
  private BigDecimal num;

  /**
   * 仓库
   */
  private String warehouse;

  /**
   * 仓库名称
   */
  private String warehouseName;

  /**
   * 批次
   */
  private String batchNo;

  /**
   * 创建人 code
   */
  private String createManCode;

  /**
   * 创建人 name
   */
  private String createManName;

  /**
   * 创建人mix
   */
  private String createManMix;

  /**
   * 创建时间
   */
  private Long createTime;

  /**
   * 审核人
   */
  private String reviewer;

  /**
   * 审核时间
   */
  private Long reviewTime;

  /**
   * 仓库执行员
   */
  private String warehouseOperator;

  /**
   * 仓库执行时间
   */
  private Long warehouseTime;

  /**
   * sap物料凭证号
   */
  private String productVoucher;

  /**
   * sap物料凭证号年份
   */
  private String productVoucherYear;

  /**
   * 税率
   */
  private BigDecimal tax;

  /**
   * 未税单价
   */
  private BigDecimal originNetPrice;

  /**
   * 创建人mix
   * @return
   */
  public String getCreateManMix() {
    if (StrUtil.isNotBlank(createManCode)
        && createManCode.length() > 4
        && StrUtil.isNotBlank(createManName)
    ) {
      return createManCode.substring(createManCode.length() - 4) + createManName;
    }
    return null;
  }
}
