package com.xhgj.srm.common.enums.change.record;

import com.xhiot.boot.core.common.util.dict.BootDictEnum;
import lombok.Getter;

/**
 * <AUTHOR>
 * @since 2024/7/2 15:08
 */
@Getter
public enum OrderField implements BootDictEnum<String, String> {
  /** 验收凭证审核意见，目前仅驳回用 */
  ORDER_ACCEPT_AUDIT_COMMENT("OrderAccept#auditComment", "订单验收凭证审核意见"),
  ;
  private final String key;
  private final String value;

  OrderField(String key, String value) {
    this.key = key;
    this.value = value;
  }
}
