package com.xhgj.srm.common.enums.supplierorder;

import com.xhiot.boot.core.common.exception.CheckException;
import com.xhiot.boot.core.common.util.dict.BootDictEnum;
import lombok.Getter;

/**
 * <AUTHOR>
 * @since 2022/11/28 15:45
 */
@Getter
public enum SupplierOrderFormType implements BootDictEnum<String, String> {

  /** 订单明细 */
  DETAILED("1", "订单明细"),
  /** 发货单 (v2中已变成入库申请单)
   * */
  DELIVER("2", "入库申请单"),
  /** 退货单 */
  RETURN("3", "退货单"),
  /** 取消单 */
  CANCEL("4", "取消单"),
  /**
   * 入库单
   */
  WAREHOUSING("5", "入库单");

  /** 单据类型 */
  private final String type;

  /** 描述 */
  private final String desc;

  SupplierOrderFormType(String type, String desc) {
    this.type = type;
    this.desc = desc;
  }

  @Override
  public String getKey() {
    return type;
  }

  @Override
  public String getValue() {
    return desc;
  }

  /**
   * 根据 type 查找枚举
   *
   * @param type 枚举type
   * @return 枚举对象
   */
  public static SupplierOrderFormType findByType(String type) {
    for (SupplierOrderFormType typeEnum : SupplierOrderFormType.values()) {
      if (typeEnum.getKey().equals(type)) {
        // 如果需要直接返回name则更改返回类型为String,return statusEnum.name;
        return typeEnum;
      }
    }
    throw new CheckException("orderState is invalid");
  }
}
