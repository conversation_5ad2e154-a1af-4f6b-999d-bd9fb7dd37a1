package com.xhgj.srm.common.vo.order;/**
 * @since 2025/3/19 18:14
 */

import com.xhgj.srm.common.enums.order.OrderCancelStatus;
import lombok.Data;
import java.math.BigDecimal;
import java.util.List;

/**
 *<AUTHOR>
 *@date 2025/3/19 18:14:35
 *@description
 */
@Data
public class OrderCancelVO {
  /**
   * 主键id
   */
  private String id;

  /**
   * 关联订单id
   */
  private String orderId;

  /**
   * 取消单号
   */
  private String cancelNo;

  /**
   * 创建时间
   */
  private Long createTime;

  /**
   * 取消总金额
   */
  private BigDecimal price;

  /**
   * 取消总数量
   */
  private BigDecimal num;

  /**
   * 取消状态
   * 1取消失败  2已完成
   * @see com.xhgj.srm.common.enums.order.OrderCancelStatus
   */
  private Byte status;

  /**
   * 取消状态描述
   */
  private String statusValue;
  /**
   * 取消明细
   */
  private List<OrderCancelDetailVO> details;

  /**
   * 获取取消状态描述
   * @return
   */
  public String getStatusValue() {
    return OrderCancelStatus.getDescByCode(this.status);
  }
}
