package com.xhgj.srm.common.dto;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import java.util.List;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 审批流程定义
 */
@Data
public class Approval {

  /**
   * 发起人用户ID
   */
  private String originatorUserId;

  /**
   * 审批模板code
   */
  private String processCode;

  /**
   * 部门ID
   */
  private Long deptId;

  /**
   * 微应用Agent ID
   */
  private Long microappAgentId;

  /**
   * 审批动作和审批人列表
   */
  private List<Approver> approvers;

  /**
   * 被抄送人列表
   */
  private List<String> ccList;

  /**
   * 抄送时间节点
   */
  private String ccPosition;

  /**
   * 选择动作执行人列表
   */
  private List<TargetSelectActioner> targetSelectActioners;

  /**
   * 表单组件值列表
   */
  private List<FormComponentValue> formComponentValues;

  /**
   * 请求ID
   */
  private String RequestId;


  /**
   * 审批动作和审批人定义
   */
  @Data
  public static class Approver {

    /**
     * 动作类型
     */
    private String actionType;

    /**
     * 审批人列表
     */
    private List<String> userIds;
  }

  /**
   * 选择动作执行人定义
   */
  @Data
  public static class TargetSelectActioner {

    /**
     * 执行人Key
     */
    private String actionerKey;

    /**
     * 执行人列表
     */
    private List<String> actionerUserIds;
  }

  /**
   * 表单组件值定义
   */
  @Data
  @NoArgsConstructor
  public static class FormComponentValue {

    /**
     * 名称
     */
    private String name;

    /**
     * 值
     */
    private String value = "";

    public FormComponentValue(String name, String value) {
      this.setName(name);
      this.setValue(value);
      if (value == null) {
        this.setValue("");
      }
    }
  }

  public static Approval createInstance(final String originatorUserId,
      final List<String> approvalIds,
      final String approvalTemplateCode, final List<FormComponentValue> params,
      final List<String> ccList) {
    Approver approver = new Approver();
    approver.setUserIds(approvalIds);
    approver.setActionType("NONE");
    Approval approval = new Approval();
    approval.setOriginatorUserId(originatorUserId);
    approval.setApprovers(ListUtil.of(approver));
    approval.setProcessCode(approvalTemplateCode);
    approval.setFormComponentValues(params);
    approval.setCcList(ccList);
    approval.setCcPosition("FINISH");
    return approval;
  }

  public static Approval createInstance(final String originatorUserId,
      final String approvalTemplateCode, final List<FormComponentValue> params,
      final List<String> ccList, Long deptId) {
    Approval approval = new Approval();
    approval.setApprovers(null);
    approval.setOriginatorUserId(originatorUserId);
    approval.setProcessCode(approvalTemplateCode);
    approval.setFormComponentValues(params);
    if (CollUtil.isNotEmpty(ccList)) {
      approval.setCcList(ccList);
    }
    approval.setCcPosition("FINISH");
    approval.setDeptId(deptId);
    return approval;
  }
}




