package com.xhgj.srm.common.enums.product;/**
 * @since 2025/1/3 11:34
 */

import java.util.ArrayList;
import java.util.List;

/**
 *<AUTHOR>
 *@date 2025/1/3 11:34:48
 *@description
 */
public enum ProductExternalLinkType {
  /**
   * 匹配
   */
  MATCH("0", "匹配", "1"),
  /**
   * 类似
   */
  SIMILAR("1", "类似", "0"),
  ;

  private String code;

  private String name;

  private String MpmCode;

  ProductExternalLinkType(String code, String name, String MpmCode) {
    this.code = code;
    this.name = name;
    this.MpmCode = MpmCode;
  }

  public String getCode() {
    return code;
  }

  public String getName() {
    return name;
  }

  public static String getCodeByMpmCode(String mpmCode) {
    for (ProductExternalLinkType value : ProductExternalLinkType.values()) {
      if (value.getMpmCode().equals(mpmCode)) {
        return value.getCode();
      }
    }
    return null;
  }

  public static String getNameByCode(String code) {
    for (ProductExternalLinkType value : ProductExternalLinkType.values()) {
      if (value.getCode().equals(code)) {
        return value.getName();
      }
    }
    return null;
  }

  public static String getCodeByName(String name) {
    for (ProductExternalLinkType value : ProductExternalLinkType.values()) {
      if (value.getName().equals(name)) {
        return value.getCode();
      }
    }
    return null;
  }

  public static String getMpmCodeByCode(String code) {
    for (ProductExternalLinkType value : ProductExternalLinkType.values()) {
      if (value.getCode().equals(code)) {
        return value.getMpmCode();
      }
    }
    return null;
  }

  public static String getNameByMpmCode(String mpmCode) {
    for (ProductExternalLinkType value : ProductExternalLinkType.values()) {
      if (value.getMpmCode().equals(mpmCode)) {
        return value.getName();
      }
    }
    return null;
  }

  public String getMpmCode() {
    return MpmCode;
  }

  public static List<String> getAllNames() {
    ProductExternalLinkType[] values = ProductExternalLinkType.values();
    List<String> names = new ArrayList<>(values.length);
    for (ProductExternalLinkType value : values) {
      names.add(value.getName());
    }
    return names;
  }

}
