package com.xhgj.srm.common.enums;

import com.xhiot.boot.core.common.util.dict.BootDictEnum;

/**
 * 付款冻结状态枚举
 */
public enum PaymentFreezeStatusEnum implements BootDictEnum<String, String> {
  FROZEN("1", "冻结中"), THAWED("2", "已解冻");

  private final String type;
  private final String desc;

  PaymentFreezeStatusEnum(String type, String desc) {
    this.type = type;
    this.desc = desc;
  }

  @Override
  public String getKey() {
    return type;
  }

  @Override
  public String getValue() {
    return desc;
  }
  /**
   * 根据type获得desc
   */
  public static String getDescByType(String type) {
    for (PaymentFreezeStatusEnum value : PaymentFreezeStatusEnum.values()) {
      if (value.getKey().equals(type)) {
        return value.getValue();
      }
    }
    return null;
  }
}
