package com.xhgj.srm.common.dto;

import com.xhgj.srm.common.enums.DingTalkApproveEventResultEnum;
import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.NotBlank;
import lombok.Data;

/**
 * 审批结果数据
 *
 * @since v1.0
 */
@Data
public class ApprovalResult {
    /**
     * 审批结果
     * {@link DingTalkApproveEventResultEnum}
     */
    @ApiModelProperty(value = "审批结果", required = true)
    @NotBlank(message = "审批结果不能为空")
    private String result;

    /**
     * 流程实例ID
     */
    @ApiModelProperty(value = "流程实例ID", required = true)
    @NotBlank(message = "流程实例ID不能为空")
    private String processInstanceId;

    /**
     * 结束时间
     */
    private long finishTime;

    /**
     * 创建时间
     */
    private long createTime;

    /**
     * 流程代码
     */
    private String processCode;

    /**
     * 业务分类ID
     */
    private String bizCategoryId;

    /**
     * 业务ID
     */
    private String businessId;

    /**
     * 审批标题
     */
    private String title;

    /**
     * 事件类型（这里是finish，表示结束事件）
     */
    private String type;

    /**
     * 业务类型
     */
    private String businessType;

    /**
     * 链接地址
     */
    private String url;

    /**
     * 审批人ID
     */
    private String staffId;
    /**
     * 审批意见
     */
    private String remark;

    /**
     * 额外信息 json
     */
    private String extraJson;
}