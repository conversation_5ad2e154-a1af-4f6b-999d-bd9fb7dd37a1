package com.xhgj.srm.common.vo.order;/**
 * @since 2025/3/17 18:45
 */

import lombok.Data;
import java.math.BigDecimal;

/**
 *<AUTHOR>
 *@date 2025/3/17 18:45:23
 *@description 可付款履约单统计
 */
@Data
public class OrderNeedPaymentStatistics {
  /**
   * 实际订货金额总计
   */
  private BigDecimal totalOrderActualAmount;
  /**
   * 可提款金额总计
   */
  private BigDecimal totalAvailableAmount;
  /**
   * 已提款金额总计
   */
  private BigDecimal totalPaidAmount;
  /**
   * 已退款金额总计
   */
  private BigDecimal totalReturnAmount;
  /**
   * 剩余可提款金额总计
   */
  private BigDecimal totalRemainingAmount;
}
