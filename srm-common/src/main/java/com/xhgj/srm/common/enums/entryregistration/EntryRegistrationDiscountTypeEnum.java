package com.xhgj.srm.common.enums.entryregistration;

import java.util.Optional;
import lombok.Getter;

/**
 * 准入报备单折扣类型
 */
@Getter
public enum EntryRegistrationDiscountTypeEnum {

  STEP_DISCOUNT_RATIO("1", "阶梯折扣"), BRAND_DISCOUNT("2", "品牌折扣");

  private final String description;

  private final String key;

  EntryRegistrationDiscountTypeEnum(String key, String description) {
    this.key = key;
    this.description = description;
  }

  public static Optional<EntryRegistrationDiscountTypeEnum> fromKey(String key) {
    for (EntryRegistrationDiscountTypeEnum value : EntryRegistrationDiscountTypeEnum.values()) {
      if (value.getKey().equals(key)) {
        return Optional.of(value);
      }
    }
    return Optional.empty();
  }
}
