package com.xhgj.srm.common.enums.supplierorder;/**
 * @since 2025/5/6 13:33
 */

/**
 *<AUTHOR>
 *@date 2025/5/6 13:33:56
 *@description 供应商订单同步记录状态
 */
public enum SupplierOrderSyncStatus {

  /**
   * 同步中
   */
  SYNCING((byte) 1, "同步中"),

  /**
   * 同步成功
   */
  SUCCESS((byte) 2, "成功"),

  /**
   * 同步失败
   */
  FAIL((byte) -1, "失败");

  private final Byte code;
  private final String desc;

  SupplierOrderSyncStatus(Byte code, String desc) {
    this.code = code;
    this.desc = desc;
  }

  public Byte getCode() {
    return code;
  }

  public String getDesc() {
    return desc;
  }

  public static String getDescByCode(Byte code) {
    for (SupplierOrderSyncStatus status : SupplierOrderSyncStatus.values()) {
      if (status.getCode().equals(code)) {
        return status.getDesc();
      }
    }
    return null;
  }
}
