package com.xhgj.srm.common.enums;

public enum PurchaseApplicationTypeEnum {
  STANDARD("NB", "标准采购申请"), LEND("FB", "借出采购申请"),
  MANUALLY_ISSUED("Z010", "手工下达采购申请"), DIRECTIONAL("DB", "定向采购申请"),
  CC("CC", "生产类采购申请"),
  CM("CM", "研发类采购申请"),
  CN("CN", "工程类采购申请"),
  Z020("Z020", "低值易耗采购申请"),
  Z030("Z030", "固定资产采购申请"),
  Z040("Z040", "委外采购申请"),
  ;

  private final String code;
  private final String name;

  PurchaseApplicationTypeEnum(String code, String name) {
    this.code = code;
    this.name = name;
  }

  public String getCode() {
    return code;
  }

  public String getName() {
    return name;
  }

  public static PurchaseApplicationTypeEnum fromCode(String code) {
    for (PurchaseApplicationTypeEnum type : values()) {
      if (type.getCode().equals(code)) {
        return type;
      }
    }
    return null;
  }

  public static PurchaseApplicationTypeEnum fromName(String name) {
    for (PurchaseApplicationTypeEnum type : values()) {
      if (type.getName().equals(name)) {
        return type;
      }
    }
    return null;
  }
}
