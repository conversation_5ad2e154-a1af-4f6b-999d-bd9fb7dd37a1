package com.xhgj.srm.common.dto;

import com.alibaba.fastjson.annotation.JSONField;
import java.util.Map;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2022/10/7 19:21
 */
@NoArgsConstructor
@Data
public class UpdateSuperDingTaskRobotCarDTO {

  @JSONField(name = "outTrackId")
  private String outTrackId;
  @JSONField(name = "cardData")
  private CardDataDTO cardData;
  @JSONField(name = "privateData")
  private PrivateDataDTO privateData;
  @JSONField(name = "userIdType")
  private String userIdType;
  @JSONField(name = "cardOptions")
  private CardOptionsDTO cardOptions;

  @NoArgsConstructor
  @Data
  public static class CardDataDTO {
    @JSONField(name = "cardParamMap")
    private Map<String,Object> cardParamMap;
    @JSONField(name = "cardMediaIdParamMap")
    private Map<String,String> cardMediaIdParamMap;
  }

  @NoArgsConstructor
  @Data
  public static class PrivateDataDTO {
    @J<PERSON>NField(name = "key")
    private KeyDTO key;

    @NoArgsConstructor
    @Data
    public static class KeyDTO {
      @JSONField(name = "cardParamMap")
      private CardParamMapDTO cardParamMap;
      @JSONField(name = "cardMediaIdParamMap")
      private CardMediaIdParamMapDTO cardMediaIdParamMap;

      @NoArgsConstructor
      @Data
      public static class CardParamMapDTO {
        @JSONField(name = "key")
        private String key;
      }

      @NoArgsConstructor
      @Data
      public static class CardMediaIdParamMapDTO {
        @JSONField(name = "key")
        private String key;
      }
    }
  }

  @NoArgsConstructor
  @Data
  public static class CardOptionsDTO {
    @JSONField(name = "updateCardDataByKey")
    private String updateCardDataByKey;
    @JSONField(name = "updatePrivateDataByKey")
    private String updatePrivateDataByKey;
  }

}
