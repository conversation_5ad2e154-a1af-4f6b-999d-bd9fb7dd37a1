package com.xhgj.srm.request.dto.erp;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

/**
 * Created by <PERSON>g Shy on 2023/11/13
 */
@Data
public class ErpOrderRateResult {

  private UpdateErpOrderRateResult Result;

  @Data
  public class UpdateErpOrderRateResult {

    @JSONField(name = "ResponseStatus")
    private ResponseStatus responseStatus;
  }

  @Data
  public static class ResponseStatus {

    /**
     * 是否成功
     */
    @JSONField(name = "IsSuccess")
    private String isSuccess;

    /**
     * 错误原因
     */
    @JSONField(name = "Errors")
    private String errors;
  }
}
