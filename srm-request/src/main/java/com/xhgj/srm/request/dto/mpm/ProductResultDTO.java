package com.xhgj.srm.request.dto.mpm;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 物料详情传输对象
 */
@ApiModel(value = "MPM - 物料详情传输对象")
@Data
public class ProductResultDTO {

  @ApiModelProperty("类目名称")
  private String cateName;

  @ApiModelProperty("采购员编码")
  private String empCode;

  @ApiModelProperty("采购员名称")
  private String empName;

  @ApiModelProperty("产品经理名称")
  private String productManagerName;
}
