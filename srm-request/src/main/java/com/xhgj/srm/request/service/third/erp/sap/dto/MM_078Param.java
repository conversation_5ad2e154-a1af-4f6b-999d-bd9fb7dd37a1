package com.xhgj.srm.request.service.third.erp.sap.dto;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * MM_078Param
 */
@NoArgsConstructor
@Data
public class MM_078Param {

    /**
     *订单号-生产订单号（组装单98开头，拆卸单99开头）
     */
    @JSONField(name = "AUFNR")
    private String aufnr;

    /** 审批结果和作废标识二选一
     *审批结果（S为审批通过，E为审批驳回）
     */
    @JSONField(name = "ZSPJG")
    private String zspjg;

    /**
     *审批说明
     */
    @JSONField(name = "ZSPSM")
    private String zspsm;

    /**
     *作废标识（X为作废，空为不作废）
     */
    @JSONField(name = "ZFBS")
    private String zfbs;


}
