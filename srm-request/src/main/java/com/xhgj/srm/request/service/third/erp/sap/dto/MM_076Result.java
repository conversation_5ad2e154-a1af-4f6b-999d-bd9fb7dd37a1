package com.xhgj.srm.request.service.third.erp.sap.dto;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

/**
 * MM_076Result
 */
@Data
public class MM_076Result {

    /**
     * 物料凭证编码
     */
    @JSONField(name = "BELNR")
    private String belnr;
    /**
     * 物料凭证年度
     */
    @JSONField(name = "GJAHR")
    private String gjahr;
    /**
     * 消息内容
     */
    @JSONField(name = "MESSAGE")
    private String message;

    /**
     * 消息类型: S 成功,E 错误,W 警告,I 信息,A 中断
     */
    @JSONField(name = "TYPE")
    private String type;

}
