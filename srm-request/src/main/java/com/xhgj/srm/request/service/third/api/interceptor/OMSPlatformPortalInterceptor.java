package com.xhgj.srm.request.service.third.api.interceptor;
import com.xhgj.srm.common.config.SrmConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;


/**
 * <AUTHOR>
 * OMS API拦截器
 */
@Component
@Slf4j
public class OMSPlatformPortalInterceptor extends OMSInterceptor {

  /**
   * @see SrmConfig#getOmsPlatformPortalUrl()
   */
  @Value("${srm.oms-platform-portal-url:default}")
  private String omsPlatformPortalUrl;

  @Override
  protected String getBaseUrl() {
    return omsPlatformPortalUrl;
  }

}
