package com.xhgj.srm.request.service.third.erp.sap.dto;

import com.alibaba.fastjson.annotation.JSONField;
import java.util.List;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 */
@NoArgsConstructor
@Data
public class UpdatePurchaseOrderSapResult {

  @JSONField(name = "RETURN")
  private UpdatePurchaseOrderRETURNDTO returnSap;

  @NoArgsConstructor
  @Data
  public static class UpdatePurchaseOrderRETURNDTO {
    /** 采购订单编码 */
    @JSONField(name = "EBELN")
    private String ebeln;

    /** 消息类型 */
    @JSONField(name = "TYPE")
    private String type;

    /** 消息 */
    @JSONField(name = "MESSAGE")
    private String msg;
  }
}
