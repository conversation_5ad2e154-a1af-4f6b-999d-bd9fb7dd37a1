package com.xhgj.srm.request.service.third.mdm;

import com.alibaba.fastjson.TypeReference;
import com.xhgj.srm.request.config.AbstractXhgjConfig;
import com.xhgj.srm.request.config.XhgjPartnerConfig;
import com.xhgj.srm.request.dto.mdm.InternalPartnerPageDTO;
import com.xhgj.srm.request.service.third.xhgj.BaseXhgjRequest;
import com.xhiot.boot.mvc.base.ResultBean;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class MdmRequest implements BaseXhgjRequest {

  private final XhgjPartnerConfig config;

  /** 获取url-方法-地址 */
  private String mdm_partner_url = "/getInternalPartnerPage";

  /** 获取url-方法-参数 */
  private Integer pageNo = 1;
  /** 获取url-方法-参数 */
  private Integer pageSize = 10000;

  public MdmRequest(XhgjPartnerConfig config) {
    this.config = config;
  }

  @Override
  public AbstractXhgjConfig getConfig() {
    return config;
  }

  @Override
  public Logger getLogger() {
    return log;
  }

  /** mdm 查询内部供应商 */
  public Optional<InternalPartnerPageDTO> getInternalPartnerPage() {
    Map<String, Object> params = new HashMap<>(3);
    params.put("pageNo", pageNo);
    params.put("pageSize", pageSize);
    return resolveResult(
        get(mdm_partner_url, params), new TypeReference<ResultBean<InternalPartnerPageDTO>>() {});
  }
}
