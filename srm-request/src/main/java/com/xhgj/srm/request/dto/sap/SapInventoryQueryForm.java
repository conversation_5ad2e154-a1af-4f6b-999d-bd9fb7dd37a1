package com.xhgj.srm.request.dto.sap;/**
 * @since 2025/2/11 16:44
 */

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

/**
 *<AUTHOR>
 *@date 2025/2/11 16:44:26
 *@description
 */
@Data
public class SapInventoryQueryForm {
  /**
   * 用户所在组织-工厂
   */
  @JSONField(name = "WERKS")
  private String userGroup;

  /**
   * 物料编码
   */
  @JSONField(name = "MATNR")
  private String productCode;

  /**
   * 仓库编码-存储地点
   */
  @JSONField(name = "LGORT")
  private String warehouse;

  /**
   * 批次
   */
  @JSONField(name = "CHARG")
  private String batchNo;

  /**
   * 品牌-品牌描述
   */
  @JSONField(name = "ZPPMS")
  private String brand;

  /**
   * 名称-物料名称
   */
  @JSONField(name = "MAKTX")
  private String productName;

  /**
   * 规格型号
   */
  @JSONField(name = "ZZXH")
  private String manuCode;

  /**
   * 采购员
   */
  @JSONField(name = "AFNAM")
  private String purchaseMan;

  /**
   * 采购部门
   */
  @JSONField(name = "EKNAM")
  private String purchaseDept;
}
