package com.xhgj.srm.request.dto.ocr;

import com.alibaba.fastjson.annotation.JSONField;
import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2023-04-17 9:40
 */
@Data
public class AccurateDTO {
  @ApiModelProperty("唯一的log id，用于问题定位")
  @JSONField(name = "log_id")
  private Long logId;

  @ApiModelProperty("唯一的log id，用于问题定位\t图像方向，当 detect_direction=true 时返回该字段。\n"
      + "- - 1：未定义，\n" + "- 0：正向，\n" + "- 1：逆时针90度，\n" + "- 2：逆时针180度，\n"
      + "- 3：逆时针270度")
  @JSONField(name = "direction")
  private Integer direction;

  @ApiModelProperty("识别结果数，表示words_result的元素个数")
  @JSONField(name = "words_result_num")
  private Long wordsResultNum;

  @ApiModelProperty("识别结果数，表示words_result的元素个数")
  @JSONField(name = "words_result")
  private List<WordsResultDTO> wordsResultListDto;

  @ApiModelProperty("传入PDF文件的总页数，当 pdf_file 参数有效时返回该字段")
  @JSONField(name = "pdf_file_size")
  private String pdfFileSize;



}
