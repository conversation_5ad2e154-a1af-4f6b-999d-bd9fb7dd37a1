package com.xhgj.srm.request.dto.sms;

import io.swagger.annotations.ApiModelProperty;
import java.util.Map;
import javax.validation.constraints.NotBlank;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2022/12/16 16:34
 */
@Data
public class SendSmsParams {

  /**
   * 发送类型
   */
  @ApiModelProperty("发送类型")
  @NotBlank(message = "发送类型必传")
  private String sendType;
  @ApiModelProperty("发送参数")
  private Map<String, String> params;
  @ApiModelProperty("发送人手机号")
  @NotBlank(message = "发送人手机号必传")
  private String mobile;
}
