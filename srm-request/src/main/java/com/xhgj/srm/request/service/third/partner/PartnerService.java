package com.xhgj.srm.request.service.third.partner;/**
 * @since 2024/11/28 16:42
 */

import com.xhgj.srm.request.dto.partner.BusinessInfoDTO;
import com.xhgj.srm.request.dto.partner.PartnerDTO;
import com.xhgj.srm.request.dto.partner.PartnerIcpDTO;
import com.xhgj.srm.request.dto.partner.SrmSaveOrUpdateSupplier;
import com.xhiot.boot.mvc.base.PageResult;

import java.util.List;

/**
 *<AUTHOR>
 *@date 2024/11/28 16:42:22
 *@description
 */
public interface PartnerService {
  // todo 这些接口需要校验

  /**
   * 根据合作商编码获得合作商
   */
  PartnerDTO getPartnerByCode(String mdmCode);

  /**
   * srm 同步天眼查
   */
  BusinessInfoDTO srmSyncTianYanInfo(String syncName, String partnerCode);

  /**
   * 修改主数据
   */
  String SrmSaveOrUpdate(SrmSaveOrUpdateSupplier form);

  /**
   * 新增合作商数据（无需审核）
   */
  String srmSaveNoAssess(SrmSaveOrUpdateSupplier form);

  /**
   * 根据名称查找 MDM 中是否有该企业（含天眼查）
   */
  PartnerDTO getPartnerByName(String partnerName, String type);

  /**
   * srm 查询合作商
   */
  List<PartnerDTO> srmSearchPartner(String partnerName, String type);

  /**
   * srm 标记供应商
   */
  Boolean markSupplier(String mdmCode);

  /**
   * srm 通过名称获得天眼查详情
   */
  PartnerDTO getTianYanDetails(String partnerName);

  /**
   * 通过个人供应商名称和手机号获得合作商
   */
  PartnerDTO searchPersonPartner(String partnerName, String mobile);

  /**
   * 获得天眼查的工商信息
   */
  BusinessInfoDTO srmGetTianYanInfo(String partnerCode, String name);

  /**
   *  根据关键字查询合作商网络备案信息
   * @param keyWord
   * @return
   */
  List<PartnerIcpDTO> getPartnerIprByKeyWord(String keyWord);

  /**
   * 分页查询国内合作商
   */
  PageResult<PartnerDTO> getChinaPartnerPage(Integer pageNo, Integer pageSize,
      String cooperationType, String mdmCode, String partnerName);
}
