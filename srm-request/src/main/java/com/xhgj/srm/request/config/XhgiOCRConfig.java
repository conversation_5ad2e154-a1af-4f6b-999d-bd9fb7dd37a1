package com.xhgj.srm.request.config;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @since 2023-04-17 14:04
 */
@Configuration
@ConfigurationProperties(prefix = "third.xhgj.ocr")
@Data
@EqualsAndHashCode(callSuper = true)
public class XhgiOCRConfig extends AbstractXhgjConfig{}
