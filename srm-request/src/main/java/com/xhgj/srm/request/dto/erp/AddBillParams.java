package com.xhgj.srm.request.dto.erp;

import com.alibaba.fastjson.annotation.JSONField;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2022/12/21 15:43
 */
@NoArgsConstructor
@Data
@EqualsAndHashCode(callSuper = true)
public class AddBillParams extends BaseSupplierOrderERP {

  @JSONField(name = "Model")
  private ModelDTO data;

  @NoArgsConstructor
  @Data
  public static class ModelDTO {

    /** 订单id */
    @JSONField(name = "orderId")
    private String orderId;
    /** 订单编号 */
    @JSONField(name = "orderCode")
    private String orderCode;
    /** 物流单号 */
    @JSONField(name = "FLOGISTICSNO")
    private String logisticsNo;
    /** 承运商编码 */
    @JSONField(name = "FLOGISTICS")
    private String logistics;
    /** 员工 id */
    @JSONField(name = "creatorId")
    private String creatorId;
    @JSONField(name = "productDetail")
    private List<RowAndQty> productDetail;
  }
}
