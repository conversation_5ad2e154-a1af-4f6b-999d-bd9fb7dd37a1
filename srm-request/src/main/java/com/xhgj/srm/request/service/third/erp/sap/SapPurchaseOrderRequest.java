package com.xhgj.srm.request.service.third.erp.sap;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.exceptions.ExceptionUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.xhgj.srm.common.constants.Constants_Sap;
import com.xhgj.srm.common.utils.dingding.DingUtils;
import com.xhgj.srm.request.enums.SAPMethod;
import com.xhgj.srm.request.service.third.erp.sap.dto.ReceiptOrReturnReversalParams;
import com.xhgj.srm.request.service.third.erp.sap.dto.ReceiptOrReturnReversalParams.DATADTO;
import com.xhgj.srm.request.service.third.erp.sap.dto.ReceiptOrReturnReversalParams.DATADTO.HEADDTO;
import com.xhgj.srm.request.service.third.erp.sap.dto.ReceiptOrReturnReversalResult;
import com.xhgj.srm.request.service.third.erp.sap.dto.ReceiptOrReturnReversalResult.RETURNDTO;
import com.xhgj.srm.request.service.third.erp.sap.dto.UpdatePurchaseOrderSapParam;
import com.xhgj.srm.request.service.third.erp.sap.dto.UpdatePurchaseOrderSapResult;
import com.xhgj.srm.request.service.third.erp.sap.dto.UpdatePurchaseOrderSapResult.UpdatePurchaseOrderRETURNDTO;
import com.xhiot.boot.core.common.exception.CheckException;
import java.util.Date;
import java.util.HashSet;
import java.util.Set;
import com.xhiot.boot.core.config.BootConfig;
import org.springframework.stereotype.Component;
import javax.annotation.Resource;

/** Created by Geng Shy on 2023/12/14 */
@Component
public class SapPurchaseOrderRequest extends BaseSapRequest {
  @Resource
  private BootConfig bootConfig;

  public ReceiptOrReturnReversalResult.RETURNDTO receiptOrReturnReversal(
      String productVoucher,String date, boolean directShipment, Long createTime){
    ReceiptOrReturnReversalResult receiptOrReturnReversalResult;
    ReceiptOrReturnReversalParams receiptOrReturnReversalParams = null;
    String result = StrUtil.EMPTY;
    try {
      receiptOrReturnReversalParams = new ReceiptOrReturnReversalParams();
      DATADTO data = new DATADTO();
      HEADDTO head = new HEADDTO();
      head.setBelnr(productVoucher);
      head.setBudat(date);
      head.setBldat(date);
      //如果是直销库传空，如果不是传X
      head.setZxkbj(directShipment ? StrUtil.EMPTY : Constants_Sap.CONFIRM_IDENTIFICATION);
      // 默认102
      head.setBwart("102");
      int year = DateUtil.year(new Date());
      if (createTime != null) {
        year = DateUtil.year(new Date(createTime));
      }
      head.setGjahr(Convert.toStr(year));
      data.setHead(head);
      receiptOrReturnReversalParams.setData(data);
      result = postSap(SAPMethod.ZFM_MM_032, receiptOrReturnReversalParams);
      try {
        receiptOrReturnReversalResult =
            JSONObject.parseObject(result, new TypeReference<ReceiptOrReturnReversalResult>() {});
      } catch (Exception e) {
        throw new CheckException("SAP网络异常请求失败，请联系管理员处理。请求时间"+ DateUtil.format(new Date(),
            DatePattern.NORM_DATETIME_PATTERN));
      }
      RETURNDTO returnX = receiptOrReturnReversalResult.getReturnX();
      if (StrUtil.equals(returnX.getType(), ERROR_TYPE)) {
        throw new CheckException("调用 SAP 接口存在报错信息：" + returnX.getMsg());
      }
    } catch (Exception e) {
      String msg =
          "SAP 接口异常，入参："
              + JSON.toJSONString(receiptOrReturnReversalParams)
              + " ，出参："
              + result
              + " ，异常信息：";
      if (e instanceof CheckException) {
        msg += ExceptionUtil.getSimpleMessage(e);
      } else {
        msg += "未知异常，请联系管理员";
      }
      throw new CheckException(msg);
    }
    return receiptOrReturnReversalResult.getReturnX();
  }

  /**
   * SAP创建修改采购订单信息
   * @deprecated 请使用 {@link com.xhgj.srm.request.service.third.sap.impl.SAPServiceImpl#sapPurchaseOrderWithAlarm(UpdatePurchaseOrderSapParam, String)} )}
   */
  @Deprecated
  public UpdatePurchaseOrderRETURNDTO updatePurchaseOrderSap(
      UpdatePurchaseOrderSapParam updatePurchaseOrderSapParam, String orderNo) {
    UpdatePurchaseOrderSapResult updatePurchaseOrderSap;
    String result = StrUtil.EMPTY;
    try {
      result = postSap(SAPMethod.ZFM_MM_021, updatePurchaseOrderSapParam);
      try {
        updatePurchaseOrderSap =
            JSONObject.parseObject(result, new TypeReference<UpdatePurchaseOrderSapResult>() {});
      } catch (Exception e) {
        throw new CheckException("SAP网络异常请求失败，请联系管理员处理。请求时间"+ DateUtil.format(new Date(),
            DatePattern.NORM_DATETIME_PATTERN));
      }
      UpdatePurchaseOrderRETURNDTO returnSap = updatePurchaseOrderSap.getReturnSap();
      Set<String> errorMsg = new HashSet<>();
      if (StrUtil.equals(returnSap.getType(), ERROR_TYPE)) {
        errorMsg.add(returnSap.getMsg());
      }
      if (CollUtil.isNotEmpty(errorMsg)) {
        throw new CheckException("调用 SAP 接口存在报错行信息：" + CollUtil.join(errorMsg, StrUtil.COMMA));
      }
    } catch (Exception e) {
      String msg =
          "SAP 接口异常，入参："
              + JSON.toJSONString(updatePurchaseOrderSapParam)
              + " ，出参："
              + result
              + " ，异常信息：";
      if (e instanceof CheckException) {
        msg += ExceptionUtil.getSimpleMessage(e);
      } else {
        msg += "未知异常，请联系管理员";
      }
      //采购订单调用MM021接口失败的时候，不在群里报警，落地商订单调用MM021接口失败还是需要在群里报警
      if (StrUtil.isNotBlank(orderNo)) {
        String env = bootConfig.getEnv();
        DingUtils.sendMsgByWarningRobot(
            "【" + env + "环境 " + bootConfig.getAppName() + "】 【" + orderNo + "】调用SAP " + "新增采购订单失败：" + "请求参数：" + JSON.toJSONString(updatePurchaseOrderSapParam)
                + "请求结果：" + result + " ，请及时处理！", env);
      }
      throw new CheckException(msg);
    }
    return updatePurchaseOrderSap.getReturnSap();
  }

  /**
   *
   * @param updatePurchaseOrderSapParam
   * @return
   * @deprecated 请使用 {@link com.xhgj.srm.request.service.third.sap.impl.SAPServiceImpl#sapPurchaseOrderWithAlarm(UpdatePurchaseOrderSapParam, String)} )}
   */
  @Deprecated
  public UpdatePurchaseOrderRETURNDTO updatePurchaseOrderExcelSap(
      UpdatePurchaseOrderSapParam updatePurchaseOrderSapParam) {
    UpdatePurchaseOrderSapResult updatePurchaseOrderSap = new UpdatePurchaseOrderSapResult();
    String result = StrUtil.EMPTY;
    try {
      result = postSap(SAPMethod.ZFM_MM_021, updatePurchaseOrderSapParam);
      try {
        updatePurchaseOrderSap =
            JSONObject.parseObject(result, new TypeReference<UpdatePurchaseOrderSapResult>() {});
      } catch (Exception e) {
        throw new CheckException("SAP网络异常请求失败，请联系管理员处理。请求时间"+ DateUtil.format(new Date(),
            DatePattern.NORM_DATETIME_PATTERN));
      }
      UpdatePurchaseOrderRETURNDTO returnSap = updatePurchaseOrderSap.getReturnSap();
      Set<String> errorMsg = new HashSet<>();
      if (StrUtil.equals(returnSap.getType(), ERROR_TYPE)) {
        errorMsg.add(returnSap.getMsg());
      }
      if (CollUtil.isNotEmpty(errorMsg)) {
        return updatePurchaseOrderSap.getReturnSap();
      }
    } catch (Exception e) {
      String msg =
          "SAP 接口异常，入参："
              + JSON.toJSONString(updatePurchaseOrderSapParam)
              + " ，出参："
              + result
              + " ，异常信息：";
      if (e instanceof CheckException) {
        msg += ExceptionUtil.getSimpleMessage(e);
      } else {
        msg += "未知异常，请联系管理员";
      }
      return updatePurchaseOrderSap.getReturnSap();
    }
    return updatePurchaseOrderSap.getReturnSap();
  }
}
