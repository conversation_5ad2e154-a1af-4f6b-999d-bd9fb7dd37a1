package com.xhgj.srm.request.dto.hZero.process;

import com.xhgj.srm.request.ConstantHZero;
import com.xhgj.srm.request.config.HZeroProcessConfig;
import io.swagger.annotations.ApiModelProperty;
import java.util.Map;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Author: fanghuanxu
 * @Date: 2025/2/16 13:38
 * @Description: 启动H0流程入参
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class StartProcessParam {

  /**
   * #{@link HZeroProcessConfig}
   */
  @ApiModelProperty(value = "流程编码", required = true)
  private String flowKey;

  @ApiModelProperty(value = "业务主键", required = true)
  private String businessKey;

  /**
   * #{@link ConstantHZero#DIMENSION_ORG}
   */
  @ApiModelProperty(value = "流程启动维度", required = true)
  private String dimension;

  @ApiModelProperty(value = "流程启动人", required = true)
  private String starter;

  @ApiModelProperty("流程描述")
  private String description;

  @ApiModelProperty("启动参数")
  private Map<String, Object> variableMap;

  @ApiModelProperty("json报文信息")
  private Map<String, Object> docJsonMap;
}
