package com.xhgj.srm.request.vo.supplierCategory;

import com.xhgj.srm.request.dto.supplierCategory.TreeDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * Created by Geng Shy on 2023/11/19
 */
@Data
public class CategoryTreeResultVO extends TreeDTO<CategoryTreeResultVO> {

  /**
   * 类目前缀编码
   */
  @ApiModelProperty("类目前缀编码")
  private String prefixCode;
  /**
   * 该类目是否展示
   */
  @ApiModelProperty("该类目是否展示")
  private boolean canShow = true;
  /**
   * 该类目是否可以新增物料
   */
  @ApiModelProperty("该类目是否可以新增物料")
  private boolean canAddProduct = false;

  /**
   * 该类目是否被禁用
   */
  @ApiModelProperty("该类目是否被禁用")
  private boolean categoryDisabled = false;

  /**
   * 来源：1. 商城类（MPM）; 2:生产类（PLM）
   */
  @ApiModelProperty("来源：1. 商城类（MPM）; 2:生产类（PLM）")
  private String source;
}
