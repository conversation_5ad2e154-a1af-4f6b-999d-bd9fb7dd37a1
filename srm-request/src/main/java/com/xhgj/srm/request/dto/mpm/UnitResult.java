package com.xhgj.srm.request.dto.mpm;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@ApiModel(value = "MDM计量单位结果")
@Data
public class UnitResult {

  @ApiModelProperty("erpId")
  private String erpId;

  @ApiModelProperty("计量单位名称")
  private String name;

  @ApiModelProperty("计量单位编码（唯一）")
  private String unit;

  @ApiModelProperty("精度")
  private Integer precision;
}
