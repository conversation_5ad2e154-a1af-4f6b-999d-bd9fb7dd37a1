package com.xhgj.srm.request.service.third.erp.sap.dto;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.annotation.JSONField;
import com.xhgj.srm.common.constants.Constants_Sap;
import java.util.List;
import lombok.Data;

@Data
public class SupplierAddOrUpdateResult {

  @JSONField(name = "RETURN")
  private List<Message> messages;

  @Data
  public class Message {

    @JSONField(name = "PARTNER")
    private String partner;
    @J<PERSON>NField(name = "TYPE")
    private String type;
    @J<PERSON>NField(name = "MESSAGE")
    private String message;
    @J<PERSON><PERSON>ield(name = "SHEET")
    private String sheet;
  }

  public boolean isSuccess() {
    if (CollUtil.isEmpty(messages)) {
      return false;
    }
    for (Message message : messages) {
      if (Constants_Sap.ERROR_TYPE.equals(message.getType())) {
        return false;
      }
    }
    return true;
  }
}
