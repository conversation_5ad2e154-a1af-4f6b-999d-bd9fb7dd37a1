package com.xhgj.srm.request.service.third.erp.sap;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.xhgj.srm.common.constants.Constants_Sap;
import com.xhgj.srm.request.enums.SAPMethod;
import com.xhgj.srm.request.service.third.erp.sap.dto.SapPayStateParam;
import com.xhgj.srm.request.service.third.erp.sap.dto.SapPayStateResult;
import com.xhiot.boot.core.common.exception.CheckException;
import com.xhiot.boot.upload.util.OssUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import javax.annotation.Resource;
import java.io.InputStream;
import java.net.HttpURLConnection;
import java.net.URL;

/**
  *@ClassName SapPayStateRequest
  *<AUTHOR>
  *@Date 2024/1/11 8:36
*/

@Component
@Slf4j
public class SapPayStateRequest  extends BaseSapRequest{
  @Resource
  private OssUtil ossUtil;

  /**
   * 查询sap付款状态
   * @param param
   * @return
   * @throws RuntimeException
   * @deprecated 请使用 {@link com.xhgj.srm.request.service.third.sap.impl.SAPServiceImpl#sapPayState(SapPayStateParam)}
   */
  @Deprecated
  public SapPayStateResult sapPayState(SapPayStateParam param)
      throws RuntimeException {
    Assert.notNull(param);
    SapPayStateResult result;
    try {
      String responseBody = postSap(SAPMethod.ZFM_SRM_FI_018,param);
      result =
          JSON.parseObject(responseBody, new TypeReference<SapPayStateResult>() {});
      if(CollUtil.isNotEmpty(result.getReturnX()) && StrUtil.equals(result.getReturnX().get(0).getType(),
          Constants_Sap.SUCCESS_TYPE)){
        return result;
      }else {
        log.error("调用sap接口失败", responseBody);
        return new SapPayStateResult();
      }
    } catch (CheckException e){
      throw e;
    } catch (Exception e) {
      log.error("调用sap接口失败", e);
      throw new CheckException("调用sap接口失败", e);
    }
  }

  /**
   *
   * @param fileUrl
   * @return
   * @deprecated 请使用 {@link com.xhgj.srm.request.service.third.sap.impl.SAPServiceImpl#uploadBankReceipt(String)}
   */
  @Deprecated
  public String getUrl(String fileUrl) {
    try {
      String[] split = fileUrl.split("/");
      String fileName = split[split.length - 1];
      URL url = new URL("fileUrl");
      // 打开连接
      HttpURLConnection httpURLConnection = (HttpURLConnection) url.openConnection();
      httpURLConnection.setRequestMethod("GET");
      httpURLConnection.connect();
      // 检查HTTP响应码是否表示成功（例如200）
      int responseCode = httpURLConnection.getResponseCode();
      if (responseCode != HttpURLConnection.HTTP_OK) {
        throw new RuntimeException("Failed to connect with HTTP error code: " + responseCode);
      }
      // 获取输入流
      try(InputStream inputStream = httpURLConnection.getInputStream();) {
        String oss_path = "srm/sap";
        return ossUtil.putOneFile(inputStream, fileName, oss_path);
      }
    } catch (Exception e) {
      log.error("获取sap银行回单，上传oss报错！");
    }
    return null;
  }

}
