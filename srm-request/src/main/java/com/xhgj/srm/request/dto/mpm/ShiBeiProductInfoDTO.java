package com.xhgj.srm.request.dto.mpm;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 拾贝物料详情
 */
@Data
public class ShiBeiProductInfoDTO {

    @ApiModelProperty("主图")
    private List<ProductPicResult> picList;
    @ApiModelProperty("详情图 url 列表")
    private List<String> contentUrlList;
    @ApiModelProperty("名称")
    private String name;
    @ApiModelProperty("型号规格")
    private String manuCode;
    @ApiModelProperty("商城价")
    private String mallPrice;
    @ApiModelProperty("市场价")
    private String marketPrice;
    @ApiModelProperty("条形码")
    private String barCode;
    @ApiModelProperty("商城上下架")
    private String shelfState;
    @ApiModelProperty("拾贝品牌名称")
    private String shiBeiBrandName;
    @ApiModelProperty("类目")
    private String shiBeiCategoryName;

    @ApiModelProperty("类目id")
    private String categoryId;
    @ApiModelProperty("所属类目名称")
    private String categoryName;
    @ApiModelProperty("所属类目code")
    private String categoryCode;

    @ApiModelProperty("品牌Id")
    private String brandId;
    @ApiModelProperty("品牌名称")
    private String brandName;
    @ApiModelProperty("品牌code")
    private String brandCode;
    @ApiModelProperty("品牌是否可售")
    private String brandIsSale;
}
