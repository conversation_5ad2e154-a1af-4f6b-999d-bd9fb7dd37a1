package com.xhgj.srm.request.service.third.erp;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.exceptions.ExceptionUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.lang.Pair;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.xhgj.srm.common.Constants;
import com.xhgj.srm.common.constants.Constants_LockName;
import com.xhgj.srm.common.utils.dingding.DingUtils;
import com.xhgj.srm.request.config.ERPConfig;
import com.xhgj.srm.request.dto.erp.AddBillParams;
import com.xhgj.srm.request.dto.erp.AddBillParams.ModelDTO;
import com.xhgj.srm.request.dto.erp.AddBillResult;
import com.xhgj.srm.request.dto.erp.AddBillResult.ResultDTO;
import com.xhgj.srm.request.dto.erp.AddBillResult.ResultDTO.ResponseStatusDTO;
import com.xhgj.srm.request.dto.erp.AddBillResult.ResultDTO.ResponseStatusDTO.ErrorsDTO;
import com.xhgj.srm.request.dto.erp.AddPayableParams;
import com.xhgj.srm.request.dto.erp.AddPayableResult;
import com.xhgj.srm.request.dto.erp.AddPayableResult.Data;
import com.xhgj.srm.request.dto.erp.AddPoOrderParams;
import com.xhgj.srm.request.dto.erp.AddPoOrderParams.PoOrder;
import com.xhgj.srm.request.dto.erp.AddPoOrderResult;
import com.xhgj.srm.request.dto.erp.AddPoOrderResult.ResultDTO.ResponseStatusDTO.SuccessEntitysDTO.FPOOrderEntryDTO;
import com.xhgj.srm.request.dto.erp.BaseSupplierOrderERP;
import com.xhgj.srm.request.dto.erp.ERPOrderPayAmountDTO;
import com.xhgj.srm.request.dto.erp.ErpOrderRateResult;
import com.xhgj.srm.request.dto.erp.MessageDTO;
import com.xhgj.srm.request.dto.erp.OpenOrCloseOrderParams;
import com.xhgj.srm.request.dto.erp.OpenOrCloseOrderParams.OpenOrCloseOrderIdsDTO;
import com.xhgj.srm.request.dto.erp.QueryOrderPayAmountResult;
import com.xhgj.srm.request.dto.erp.ReceivableBillDTO;
import com.xhgj.srm.request.dto.erp.ReceivableQueryDTO;
import com.xhgj.srm.request.dto.erp.ReceivableReturnDTO;
import com.xhgj.srm.request.dto.erp.ReturnParams;
import com.xhgj.srm.request.dto.erp.ReturnResult;
import com.xhgj.srm.request.dto.erp.ReturnResult.OrderReturnData;
import com.xhgj.srm.request.dto.erp.RowAndNum;
import com.xhgj.srm.request.dto.erp.RowAndQty;
import com.xhgj.srm.request.dto.erp.UpdateErpOrderRateParams;
import com.xhgj.srm.request.dto.erp.UpdateLogisitcaDTO;
import com.xhgj.srm.request.dto.erp.UpdateReturnParams;
import com.xhgj.srm.request.enums.ERPUrl;
import com.xhgj.srm.request.enums.OpenOrCloseOrderType;
import com.xhgj.srm.request.service.third.oms.OmsRequest;
import com.xhiot.boot.core.common.exception.CheckException;
import com.xhiot.boot.core.config.BootConfig;
import com.xhiot.boot.mvc.base.ResultBean;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @since 2022/12/14 16:08
 */
@Component
@Slf4j
public class ERPRequest {

  @Autowired private ERPConfig erpConfig;
  @Autowired private OmsRequest omsRequest;
  @Autowired private BootConfig bootConfig;
  @Resource
  private RedissonClient redissonClient;
/**erp返回字段**/
  private static final String ERP_RETURN_SUCCESS_CODE = "IsSuccess";

  /**
   * ERP采购订单下推采购入库单
   *
   * @param orderId erp 订单 id 必传
   * @param orderCode 订单编码 必传
   * @param logisticsNo 物流单号 必传
   * @param rowAndQtyList 明细 必传
   */
  public Pair<String,String> addBill(
      String orderId, String orderCode, String logisticsNo, List<RowAndQty> rowAndQtyList,
      String logisticsCompany,boolean throwError,String orderNo,String creatorId,
      boolean isSyncExecute) {
    AddBillParams addBillParams = new AddBillParams();
    String resultJson = StrUtil.EMPTY;
    String addBillNo = StrUtil.EMPTY;
    try {
      Assert.notEmpty(orderId);
      Assert.notEmpty(orderCode);
      Assert.notEmpty(logisticsNo);
      Assert.notEmpty(rowAndQtyList);
      setDbIdAndNameAndPassword(addBillParams);
      ModelDTO data = new ModelDTO();
      data.setOrderId(orderId);
      data.setOrderCode(orderCode);
      data.setLogisticsNo(logisticsNo);
      String logistics = StrUtil.EMPTY;
      if (StrUtil.isNotBlank(logisticsCompany)) {
        logistics = omsRequest.getErpExpressCodeByName(logisticsCompany);
      }
      data.setLogistics(StrUtil.blankToDefault(logistics, Constants.DEFAULT_LOGISTICS));
      data.setCreatorId(StrUtil.emptyIfNull(creatorId));
      data.setProductDetail(rowAndQtyList);
      addBillParams.setData(data);
      RLock lock = null;
      try {
        lock = redissonClient.getLock(Constants_LockName.ADD_BILL_LOCK);
        lock.lock();
        //应产品要求不能在同一秒下推多个采购入库单
        Thread.sleep(1000);
        resultJson = post(ERPUrl.ADD_BILL, JSON.toJSONString(addBillParams));
      } catch (Exception e) {
        log.error(ExceptionUtil.stacktraceToString(e, -1));
        throw new CheckException("服务异常，请联系管理员！");
      } finally {
        if (lock != null) {
          lock.unlock();
        }
      }
      try {
        AddBillResult addBillResult =
            JSON.parseObject(resultJson, new TypeReference<AddBillResult>() {});
        ResultDTO result = addBillResult.getResult();
        if (result != null) {
          ResponseStatusDTO responseStatus = result.getResponseStatus();
          if (responseStatus != null) {
            List<ResponseStatusDTO.SuccessEntitysDTO> successEntitys =
                responseStatus.getSuccessEntitys();
            if (CollUtil.isNotEmpty(successEntitys)) {
              addBillNo = successEntitys.get(0).getNumber();
            }
            if (ObjectUtil.notEqual(Boolean.TRUE, responseStatus.getIsSuccess())) {
              String errorMessage = CollUtil.emptyIfNull(responseStatus.getErrors()).stream().map(ErrorsDTO::getMessage).collect(Collectors.joining(StrUtil.COMMA));
              // 20230202 ERP 存在入库成功单出库或发货失败的情况
              // 当 errorCode == 600 时特殊情况，入库成功，发货或出库失败，不抛出异常，将错误信息传递给前端
              Integer errorCode = responseStatus.getErrorCode();
              if (errorCode != null) {
                if (isSyncExecute && errorCode.equals(600)) {
                  return new Pair<>(addBillNo, errorMessage);
                }
              }
              throw new CheckException("ERP 接口报错返回：" + errorMessage);
            }
          } else {
            throw new CheckException("ERP 接口返回 ResponseStatus ，请联系管理员！：" + resultJson);
          }
        } else {
          throw new CheckException("ERP 接口返回 Result 异常，请联系管理员！：" + resultJson);
        }
      } catch (Exception e) {
        log.error(ExceptionUtil.stacktraceToString(e, -1));
        if (e instanceof CheckException) {
          throw e;
        }
        throw new CheckException("ERP 接口解析失败，请联系管理员！ERP 接口返回：" + resultJson);
      }
    } catch (Exception e) {
      String errorMsg ;
      if (e instanceof CheckException) {
        errorMsg = ExceptionUtil.getSimpleMessage(e);
      }else{
        errorMsg = "未知异常，请联系管理员！";
      }
      log.info(ExceptionUtil.stacktraceToString(e, -1));
      try {
        String env = bootConfig.getEnv();
        DingUtils.sendMsgByWarningRobot(
            "【" + env + "环境 " + bootConfig.getAppName() + "】 【"+orderNo +"】调用ERP "
                + "新增采购入库单报错："+
                errorMsg + "，请求参数："+ JSON.toJSONString(addBillParams)+"请求结果："+
                resultJson
                +" ，请及时处理！", env);
      } catch (Exception ex) {
        log.info(ExceptionUtil.stacktraceToString(ex, -1));
      }
      if (throwError) {
        throw new CheckException(errorMsg);
      }
    }
    return new Pair<>(addBillNo,StrUtil.EMPTY);
  }
  /**
   * @param params 采购订单反审核修改Params
   * @return
   */
  public ErpOrderRateResult updateErpOrderRate(UpdateErpOrderRateParams params) {
    params.setDbId(erpConfig.getDbId());
    params.setUserName(erpConfig.getUserName());
    params.setPassWord(erpConfig.getPassword());
    String result = StrUtil.EMPTY;
    ErpOrderRateResult updateErpOrderRateResult = null;
    try {
      result =
          HttpUtil.post(erpConfig.getServiceUrlNewPort() + ERPUrl.UPDATE_ERP_ORDER_RATE.getUrl(),
          JSON.toJSONString(params));
      updateErpOrderRateResult =
          JSON.parseObject(result, new TypeReference<ErpOrderRateResult>() {});
      if (Objects.equals(updateErpOrderRateResult.getResult().getResponseStatus().getIsSuccess(), "false")) {
        try {
          String env = bootConfig.getEnv();
          DingUtils.sendMsgByWarningRobot(
              "【" + env + "环境 " + bootConfig.getAppName() + "】调用ERP " + "采购订单反审核修改出现异常，请求参数："
                  + JSON.toJSONString(params) + "请求结果：" + result + " ，请及时处理！", env);
        } catch (Exception ex) {
          log.info(ExceptionUtil.stacktraceToString(ex, -1));
        }
      }
    } catch (Exception e) {
      log.error(ExceptionUtil.stacktraceToString(e, -1));
      try {
        String env = bootConfig.getEnv();
        DingUtils.sendMsgByWarningRobot(
            "【" + env + "环境 " + bootConfig.getAppName() + "】调用ERP " + "采购订单反审核修改出现异常，请求参数："
                + JSON.toJSONString(params) + "请求结果：" + result + " ，请及时处理！", env);
      } catch (Exception ex) {
        log.info(ExceptionUtil.stacktraceToString(ex, -1));
      }
    }
    return updateErpOrderRateResult;
  }
  /**
   * 新增采购单
   * @param orderCode 订单号 必传
   * @param saleOrderNo 销售订单 必传
   * @param mdmCode mdm 编码 必传
   */
  public Pair<Pair<String,String>, Map<String,String>> addPoOrder(String orderCode,
      String saleOrderNo, String mdmCode,String purNumber,String note){
    Assert.notBlank(orderCode);
    if (StrUtil.isBlank(saleOrderNo)) {
      throw new CheckException("【"+orderCode + "】销售单号不存在，无法新增采购单");
    }
    if (StrUtil.isBlank(mdmCode)) {
      throw new CheckException("【"+mdmCode + "】 MDM 编码不存在，无法新增采购单");
    }
    AddPoOrderParams addPoOrderParams = new AddPoOrderParams();
    String dbId = erpConfig.getDbId();
    if (StrUtil.isBlank(dbId)) {
      throw new CheckException("数据中心 id 未配置 ");
    }
    String userName = erpConfig.getUserName();
    if (StrUtil.isBlank(userName)) {
      throw new CheckException("登录名未配置 ");
    }
    String password = erpConfig.getPassword();
    if (StrUtil.isBlank(password)) {
      throw new CheckException("密码未配置 ");
    }
    addPoOrderParams.setDbId(dbId);
    addPoOrderParams.setPassWord(password);
    addPoOrderParams.setUserName(userName);
    List<PoOrder> poOrderList = new ArrayList<>();
    PoOrder poOrder = new PoOrder();
    poOrder.setBillNo(saleOrderNo);
    poOrder.setMdmNumber(mdmCode);
    poOrder.setNote(StrUtil.emptyIfNull(note));
    poOrder.setPurNumber(purNumber);
    poOrder.setDate(DateUtil.formatDate(DateTime.of(System.currentTimeMillis())));
    poOrderList.add(poOrder);
    addPoOrderParams.setPoOrderList(poOrderList);
    String result = StrUtil.EMPTY;
    try {
      result = postAshxSecondUrl(ERPUrl.ADD_POORDER, JSON.toJSONString(addPoOrderParams));
      AddPoOrderResult addPoOrderResult;
      try {
        addPoOrderResult =
            resolveResultNew(ERP_RETURN_SUCCESS_CODE, result, ERPUrl.ADD_POORDER,
                new TypeReference<AddPoOrderResult>() {});
        if (addPoOrderResult==null) {
          throw new CheckException("ERP 接口返回空");
        }
        AddPoOrderResult.ResultDTO result1 = addPoOrderResult.getResult();
        if (result1==null) {
          throw new CheckException("ERP 未解析出 Result 字段");
        }
        AddPoOrderResult.ResultDTO.ResponseStatusDTO responseStatus = result1.getResponseStatus();
        if (responseStatus==null) {
          throw new CheckException("ERP 未解析出 ResponseStatus 字段");
        }
        List<AddPoOrderResult.ResultDTO.ResponseStatusDTO.SuccessEntitysDTO> successEntitys =
            responseStatus.getSuccessEntitys();
        if (CollUtil.isEmpty(successEntitys)) {
          throw new CheckException("ERP 未解析出 SuccessEntitys 字段");
        }
        AddPoOrderResult.ResultDTO.ResponseStatusDTO.SuccessEntitysDTO successEntitysDTO =
            successEntitys.get(0);
        String id = successEntitysDTO.getId();
        String number = successEntitysDTO.getNumber();
        if (StrUtil.isBlank(id)) {
          throw new CheckException("ERP接口 采购单 id 返回未空");
        }
        if (StrUtil.isBlank(number)) {
          throw new CheckException("ERP接口 采购单单号返回未空");
        }
        Pair<String, String> idAndErpOrderNo = new Pair<>(id,number);
        Map<String,String> productCodeMapErpId = new HashMap<>();
        List<FPOOrderEntryDTO> fpoOrderEntry =
            CollUtil.emptyIfNull(successEntitysDTO.getFPOOrderEntry());
        for (FPOOrderEntryDTO fpoOrderEntryDTO : fpoOrderEntry) {
          productCodeMapErpId.put(fpoOrderEntryDTO.getFNumber(),fpoOrderEntryDTO.getFEntryId());
        }
        return new Pair<>(idAndErpOrderNo,productCodeMapErpId);
      } catch (Exception e) {
        throw new CheckException("JSON 解析失败");
      }
    }  catch (Exception e) {
      String errorMsg ;
      if (e instanceof CheckException) {
        errorMsg = ExceptionUtil.getSimpleMessage(e);
      }else{
        errorMsg = "未知异常，请联系管理员！";
      }
      log.info(ExceptionUtil.stacktraceToString(e, -1));
      try {
        String env = bootConfig.getEnv();
        DingUtils.sendMsgByWarningRobot(
            "【" + env + "环境 " + bootConfig.getAppName() + "】 【"+orderCode +"】调用ERP "
                + "新增采购订单报错："+
                errorMsg + "，请求参数："+ JSON.toJSONString(addPoOrderParams)+"请求结果："+
                result
                +" ，请及时处理！", env);
      } catch (Exception ex) {
        log.info(ExceptionUtil.stacktraceToString(ex, -1));
      }
    }
    return null;
  }

  /**
   * ERP采购订单退货（下推采购退货）
   *
   * @param orderId 采购订单单据ID 必传
   * @param productDetail 行ID/下推数量 集合 必传
   */
  public List<OrderReturnData> pushPurMrb(String orderCode,String orderId,
      List<RowAndNum> productDetail,
      String trackNum,String logisticsCompany,String remark,boolean isAutoAudit,
      boolean sendDingMsg) {
    List<OrderReturnData> orderReturnData = null;
    String result = StrUtil.EMPTY;
    ReturnParams returnParams = new ReturnParams();
    try {
      if (StrUtil.isBlank(orderId)) {
        throw new CheckException("采购订单单据ID 必传");
      }
      if (CollUtil.isEmpty(productDetail)) {
        throw new CheckException("行ID/下推数量 集合 必传");
      }
      setDbIdAndNameAndPassword(returnParams);
      returnParams.setOrderId(orderId);
      returnParams.setProductDetail(productDetail);
      String logistics = StrUtil.EMPTY;
      if (StrUtil.isNotBlank(logisticsCompany)) {
        logistics = omsRequest.getErpExpressCodeByName(logisticsCompany);
      }
      returnParams.setLogistics(StrUtil.blankToDefault(logistics,Constants.DEFAULT_LOGISTICS));
      returnParams.setLogisticsNo(StrUtil.blankToDefault(trackNum,"/"));
      returnParams.setReturnReason(StrUtil.blankToDefault(remark,"退货"));
      returnParams.setIsAutoAudit(isAutoAudit);
      result = post(ERPUrl.PUSH_PUR_MRB, JSON.toJSONString(returnParams));
      orderReturnData = Optional.ofNullable(
              resolveResult(result, ERPUrl.PUSH_PUR_MRB, new TypeReference<ReturnResult>() {}))
          .map(ReturnResult::getReturnData).orElse(ListUtil.empty());
    } catch (Exception e) {
      if (sendDingMsg) {
        String errorMsg ;
        if (e instanceof CheckException) {
          errorMsg = ExceptionUtil.getSimpleMessage(e);
        }else{
          errorMsg = "未知异常，请联系管理员！";
        }
        log.info(ExceptionUtil.stacktraceToString(e, -1));
        try {
          String env = bootConfig.getEnv();
          DingUtils.sendMsgByWarningRobot(
              "【" + env + "环境 " + bootConfig.getAppName() + "】 【"+orderCode +"】调用ERP "
                  + "下推采购退货："+
                  errorMsg + "，请求参数："+ JSON.toJSONString(returnParams)+"请求结果："+
                  result
                  +" ，请及时处理！", env);
        } catch (Exception ex) {
          log.info(ExceptionUtil.stacktraceToString(ex, -1));
        }
      }else{
        throw e;
      }
    }
    return orderReturnData;
  }

  /**
   * ERP采购退货单修改数量
   *
   * @param orderId 采购订单单据ID 必传
   * @param productDetail 行ID/下推数量 集合 必传
   */
  public void updatePurMrb(String orderId, List<RowAndNum> productDetail,String trackNum,
      String logisticsCompany,String remark) {
    if (StrUtil.isBlank(orderId)) {
      throw new CheckException("采购订单单据ID 必传");
    }
    if (CollUtil.isEmpty(productDetail)) {
      throw new CheckException("行ID/下推数量 集合 必传");
    }
    UpdateReturnParams returnParams = new UpdateReturnParams();
    setDbIdAndNameAndPassword(returnParams);
    returnParams.setReturnReason(StrUtil.emptyIfNull(remark));
    returnParams.setOrderId(orderId);
    String logistics = StrUtil.EMPTY;
    if (StrUtil.isNotBlank(logisticsCompany)) {
      logistics = omsRequest.getErpExpressCodeByName(logisticsCompany);
    }
    returnParams.setLogistics(StrUtil.blankToDefault(logistics,Constants.DEFAULT_LOGISTICS));
    returnParams.setProductDetail(productDetail);
    returnParams.setLogisticsNo(StrUtil.blankToDefault(trackNum,"/"));
    resolveResult(
        post(ERPUrl.UPDATE_PUR_MRB, JSON.toJSONString(returnParams)), ERPUrl.UPDATE_PUR_MRB, null);
  }

  /**
   * ERP采购订单物流信息链接修改
   *
   * @param updateLogisitcaDTO 请求参数 必传
   */
  public void updateLogistics(UpdateLogisitcaDTO updateLogisitcaDTO) {
    resolveResult(
        post(ERPUrl.UPDATE_LOGISTICS, JSON.toJSONString(updateLogisitcaDTO)),
        ERPUrl.UPDATE_LOGISTICS,
        null);
  }

  /**
   * 应收单查询对接
   *
   * @param queryDTO 查询条件
   */
  public List<ReceivableReturnDTO> receivableQuery(ReceivableQueryDTO queryDTO) {
    List<ReceivableReturnDTO> list = null;
    try {
      list =
          CollUtil.emptyIfNull(
              resolveResult(
                  ERP_RETURN_SUCCESS_CODE,
                  postAshxUrl(ERPUrl.RECEIVABLE_QUERY, JSON.toJSONString(queryDTO)),
                  ERPUrl.RECEIVABLE_QUERY,
                  new TypeReference<ResultBean<List<ReceivableReturnDTO>>>() {}));
    } catch (Exception e) {
      String errorMsg;
      if (e instanceof CheckException) {
        errorMsg = ExceptionUtil.getSimpleMessage(e);
      } else {
        errorMsg = "未知异常，请联系管理员！";
      }
      log.info(ExceptionUtil.stacktraceToString(e, -1));
      try {
        String env = bootConfig.getEnv();
        DingUtils.sendMsgByWarningRobot(
            "【"
                + env
                + "环境 "
                + bootConfig.getAppName()
                + "】 【"
                + queryDTO.getModel().getProjectNum()
                + "】调用ERP "
                + "同步应收款报错："
                + errorMsg
                + "，请求参数："
                + JSON.toJSONString(queryDTO)
                + " ，请及时处理！",
            env);
      } catch (Exception ex) {
        log.info(ExceptionUtil.stacktraceToString(ex, -1));
      }
    }
    return list;
  }

  /**
   * 应付金额查询
   * @param dto
   * @return
   */
  public BigDecimal getOrderPayAmount(ERPOrderPayAmountDTO dto) {
    QueryOrderPayAmountResult result = null;
    BigDecimal amount = BigDecimal.ZERO;
    try {
      result =resolveResultNew(
                  ERP_RETURN_SUCCESS_CODE,
          postAshxSecondUrl(ERPUrl.GET_ORDER_PAY_AMOUNT, JSON.toJSONString(dto)),
                  ERPUrl.GET_ORDER_PAY_AMOUNT,
                  new TypeReference<QueryOrderPayAmountResult>() {});
    } catch (Exception e) {
      String errorMsg;
      if (e instanceof CheckException) {
        errorMsg = ExceptionUtil.getSimpleMessage(e);
      } else {
        errorMsg = "未知异常，请联系管理员！";
      }
      log.info(ExceptionUtil.stacktraceToString(e, -1));
      try {
        String env = bootConfig.getEnv();
        DingUtils.sendMsgByWarningRobot(
            "【"
                + env
                + "环境 "
                + bootConfig.getAppName()
                + "】 【"
                + dto.getOrderNo()
                + "】调用ERP "
                + "应付金额查询："
                + errorMsg
                + "，请求参数："
                + JSON.toJSONString(dto)
                + " ，请及时处理！",
            env);
      } catch (Exception ex) {
        log.info(ExceptionUtil.stacktraceToString(ex, -1));
      }
    }
    if(result != null && result.getResult() !=null && result.getResult().getResponseStatus() != null
        && result.getResult().getResponseStatus().getReturnInfo() !=null){
      amount = result.getResult().getResponseStatus().getReturnInfo().getFPayAmount();
    }
    return amount;
  }

  /**
   * 回款单查询对接
   *
   * @param queryDTO 查询条件
   */
  public List<ReceivableBillDTO> receivableBillQuery(ReceivableQueryDTO queryDTO) {
    return CollUtil.emptyIfNull(
        resolveResult(
            ERP_RETURN_SUCCESS_CODE,
            postAshxUrl(ERPUrl.RECEIVABLE_BILL, JSON.toJSONString(queryDTO)),
            ERPUrl.RECEIVABLE_BILL,
            new TypeReference<ResultBean<List<ReceivableBillDTO>>>() {}));
  }

  /**
   * ERP采购订单关闭/反关闭
   *
   * @param type 操作类型 必传
   * @param ids 明细行 id （明细行终止，反终止时 必传）
   */
  public String openOrCloseOrder(OpenOrCloseOrderType type, OpenOrCloseOrderIdsDTO ids) {
    if (type == null) {
      throw new CheckException("操作类型必传");
    }
    String id = ids.getId();
    if (StrUtil.isBlank(id)) {
      throw new CheckException("单据 id 必传");
    }
    if (OpenOrCloseOrderType.DETAIL_CLOSE.equals(type) || OpenOrCloseOrderType.DETAIL_OPEN.equals(
        type)) {
      List<String> entryIds = ids.getEntryIds();
      if (CollUtil.isEmpty(entryIds)) {
        throw new CheckException("erp 明细 id 集合必传");
      }
    }
    OpenOrCloseOrderParams openOrCloseOrderParams = new OpenOrCloseOrderParams();
    setDbIdAndNameAndPassword(openOrCloseOrderParams);
    openOrCloseOrderParams.setType(type.getType());
    ArrayList<OpenOrCloseOrderIdsDTO> openOrCloseOrderIdsDTOS =
        new ArrayList<OpenOrCloseOrderIdsDTO>() {{
          add(ids);
        }};
    openOrCloseOrderParams.setIds(openOrCloseOrderIdsDTOS);
    String result = post(ERPUrl.OPEN_OR_CLOSE_ORDER, JSON.toJSONString(openOrCloseOrderParams));
    resolveResult(result, ERPUrl.OPEN_OR_CLOSE_ORDER, null);
    return result;
  }

  /**
   * ERP生成应付单
   */
  public Data addPayable(AddPayableParams params) {
    params.setDbId(erpConfig.getDbId());
    params.setUserName(erpConfig.getUserName());
    params.setPassWord(erpConfig.getPassword());
    AddPayableResult result = null;
    try {
      String response = post(ERPUrl.ADD_PAYABLE, JSON.toJSONString(params));
      result = JSON.parseObject(response, new TypeReference<AddPayableResult>() {});
    } catch (Exception e) {
      handleAddPayableFail(params, result);
      throw new CheckException("请求erp接口异常，请联系管理员");
    }
    if (result == null) {
      handleAddPayableFail(params, result);
      throw new CheckException("请求erp接口异常，请联系管理员");
    }
    if (BooleanUtil.isFalse(result.getIsSuccess())) {
      handleAddPayableFail(params, result);
      throw new CheckException("生成应付单失败，erp返回：" + JSON.toJSONString(result));
    }
    if (result.getData() == null) {
      handleAddPayableFail(params, result);
      throw new CheckException("生成应付单失败，erp返回：" + JSON.toJSONString(result));
    }
    log.info("生成应付单成功：{}",JSON.toJSONString(result));
    return result.getData();
  }

  /**
   * 处理生成erp应付单失败的情况
   * @param params
   * @param result
   */
  private void handleAddPayableFail(AddPayableParams params, AddPayableResult result) {
    log.error("新增erp应付单失败，对账单号：{}",params.getAccountNo());
    sendDingTalkMessageForAddPayable(params, result);
  }

  /**
   * 发送生成erp应付单失败钉钉消息
   * @param params
   * @param result
   */
  private void sendDingTalkMessageForAddPayable(AddPayableParams params, AddPayableResult result) {
    String env = bootConfig.getEnv();
    DingUtils.sendMsgByWarningRobot(
        "【" + env + "环境 " + bootConfig.getAppName() + "】 对账单号：【" + params.getAccountNo()
            + "】调用ERP" + "新增应付单报错，请求参数：" + JSON.toJSONString(params) + "响应参数："
            + JSON.toJSONString(result) + "，请及时处理！", env);
  }


  /**
   * 设置必要的 账套，用户名，密码等信息
   *
   * @param supplierOrderERP 参数必传
   */
  private void setDbIdAndNameAndPassword(BaseSupplierOrderERP supplierOrderERP) {
    Assert.notNull(supplierOrderERP);
    String dbId = erpConfig.getDbId();
    if (StrUtil.isBlank(dbId)) {
      throw new CheckException("数据中心 id 未配置 ");
    }
    String userName = erpConfig.getUserName();
    if (StrUtil.isBlank(userName)) {
      throw new CheckException("登录名未配置 ");
    }
    String password = erpConfig.getPassword();
    if (StrUtil.isBlank(password)) {
      throw new CheckException("密码未配置 ");
    }
    supplierOrderERP.setDbId(dbId);
    supplierOrderERP.setPassWord(password);
    supplierOrderERP.setUserName(userName);
  }

  /**
   * 解析 erp （ashx接口）接口返回数据
   *
   * @param json
   * @param typeReference
   */
  private <T> T resolveResult(
      String successKey, String json, ERPUrl erpUrl, TypeReference<ResultBean<T>> typeReference) {
    Boolean success;
    try {
      JSONObject jsonObject = JSON.parseObject(json);
      success = jsonObject.getBoolean(successKey);
      if (success == null) {
        throw new CheckException(
            "【" + erpUrl.getDesc() + "】 ERP 返参无 " + successKey + " 字段，接口返回：【" + json + "】，请联系管理员！");
      }
      if (success) {
        if (typeReference == null) {
          return null;
        } else {
          ResultBean<T> resultBean = JSON.parseObject(json, typeReference);
          log.info("erp【"+erpUrl.getDesc()+"】接口返参："+resultBean.getData());
          return resultBean.getData();
        }
      }
    } catch (Exception e) {
      if (e instanceof CheckException) {
        throw e;
      }
      log.error(ExceptionUtil.stacktraceToString(e, -1));
      throw new CheckException("【" + erpUrl.getDesc() + "】 JSON 解析失败，请联系管理员！");
    }
    return null;
  }

  /**
   * 解析 erp （ashx接口）接口返回数据
   *
   * @param json
   * @param typeReference
   */
  private <T> T resolveResultNew(
      String successKey, String json, ERPUrl erpUrl, TypeReference<T> typeReference) {
    Boolean success;
    try {
      JSONObject jsonObject = JSON.parseObject(json);
      if (jsonObject==null) {
        throw new CheckException("ERP 接口返回空");
      }
      JSONObject result1 = jsonObject.getJSONObject("Result");
      if (result1==null) {
        throw new CheckException("ERP 未解析出 Result 字段");
      }
      JSONObject responseStatus = result1.getJSONObject("ResponseStatus");
      if (responseStatus==null) {
        throw new CheckException("ERP 未解析出 ResponseStatus 字段");
      }
      success = responseStatus.getBoolean(successKey);
      if (success == null) {
        throw new CheckException(
            "【" + erpUrl.getDesc() + "】 ERP 返参无 " + successKey + " 字段，接口返回：【" + json + "】，请联系管理员！");
      }
      if (success) {
        if (typeReference == null) {
          return null;
        } else {
          T resultBean = JSON.parseObject(json, typeReference);
          log.info("erp【"+erpUrl.getDesc()+"】接口返参："+ json);
          return resultBean;
        }
      }
    } catch (Exception e) {
      if (e instanceof CheckException) {
        throw e;
      }
      log.error(ExceptionUtil.stacktraceToString(e, -1));
      throw new CheckException("【" + erpUrl.getDesc() + "】 JSON 解析失败，请联系管理员！");
    }
    return null;
  }

  /**
   * 解析 erp 接口返回数据
   *
   * @param json
   * @param typeReference
   */
  private <T> T resolveResult(String json, ERPUrl erpUrl, TypeReference<T> typeReference) {
    Boolean success;
    try {
      JSONObject jsonObject = JSON.parseObject(json);
      success = jsonObject.getBoolean("success");
      if (success == null) {
        throw new CheckException(
            "【" + erpUrl.getDesc() + "】 ERP 返参无 success 字段，接口返回：【" + json + "】，请联系管理员！");
      }
      if (success) {
        if (typeReference == null) {
          return null;
        } else {
          T t;
          try {
            t = jsonObject.toJavaObject(typeReference);
          } catch (Exception e) {
            throw new CheckException("JSON 解析失败请联系管理员！");
          }
          return t;
        }
      } else {
        try {
          String mes = jsonObject.getString("mes");
          throw new CheckException("ERP 接口提示：" + mes);
        } catch (Exception e) {
          if (e instanceof CheckException) {
            throw e;
          }
          JSONArray mes = jsonObject.getJSONArray("mes");
          if (mes == null) {
            throw new CheckException("【" + erpUrl.getDesc() + "】 ERP 返参无 mes 字段，请联系管理员！");
          }
          try {
            List<MessageDTO> messageDTOList =
                mes.toJavaObject(new TypeReference<List<MessageDTO>>() {});
            throw new CheckException(
                "ERP 接口提示："
                    + messageDTOList.stream()
                        .map(MessageDTO::getMessage)
                        .collect(Collectors.joining(StrUtil.COMMA)));
          } catch (Exception e1) {
            if (e1 instanceof CheckException) {
              throw e1;
            }
            log.error(ExceptionUtil.stacktraceToString(e1, -1));
            throw new CheckException("【" + erpUrl.getDesc() + "】 JSON 解析失败，请联系管理员！");
          }
        }
      }
    } catch (Exception e) {
      if (e instanceof CheckException) {
        throw e;
      }
      log.error(ExceptionUtil.stacktraceToString(e, -1));
      throw new CheckException("【" + erpUrl.getDesc() + "】 JSON 解析失败，请联系管理员！");
    }
  }

  private String post(ERPUrl erpUrl, String body) {
    String fullUrl = buildFullUrl(erpUrl.getUrl());
    log.info(fullUrl + "【" + erpUrl.getDesc() + "】入参：");
    log.info(body);
    String result;
    try {
      result = HttpUtil.post(fullUrl, body);
      log.info(fullUrl + "【" + erpUrl.getDesc() + "】出参：");
      log.info(result);
    } catch (Exception e) {
      log.error(ExceptionUtil.stacktraceToString(e, -1));
      throw new CheckException(fullUrl + "【" + erpUrl.getDesc() + "】接口请求失败，请联系管理员！");
    }
    return result;
  }

  private String postAshxUrl(ERPUrl erpUrl, String body) {
    String fullUrl = buildAxhxFullUrl(erpUrl.getUrl());
    log.info(fullUrl + "【" + erpUrl.getDesc() + "】入参：");
    log.info(body);
    String result;
    try {
      result = HttpUtil.post(fullUrl, body);
      log.info(fullUrl + "【" + erpUrl.getDesc() + "】出参：");
      log.info(result);
    } catch (Exception e) {
      log.error(ExceptionUtil.stacktraceToString(e, -1));
      throw new CheckException(fullUrl + "【" + erpUrl.getDesc() + "】接口请求失败，请联系管理员！");
    }
    return result;
  }

  private String postAshxSecondUrl(ERPUrl erpUrl, String body) {
    String fullUrl = buildAxhxSecondFullUrl(erpUrl.getUrl());
    log.info(fullUrl + "【" + erpUrl.getDesc() + "】入参：");
    log.info(body);
    String result;
    try {
      result = HttpUtil.post(fullUrl, body);
      log.info(fullUrl + "【" + erpUrl.getDesc() + "】出参：");
      log.info(result);
    } catch (Exception e) {
      log.error(ExceptionUtil.stacktraceToString(e, -1));
      throw new CheckException(fullUrl + "【" + erpUrl.getDesc() + "】接口请求失败，请联系管理员！");
    }
    return result;
  }

  private String buildFullUrl(String url) {
    String serviceUrl = erpConfig.getServiceUrl();
    if (StrUtil.isBlank(serviceUrl)) {
      throw new CheckException(
          StrUtil.emptyToDefault(erpConfig.getServiceName(), "存在外部系统") + "地址未配置，请联系管理员处理！");
    }
    return StrUtil.removeSuffix(serviceUrl, "/") + StrUtil.addPrefixIfNot(url, "/");
  }

  private String buildAxhxFullUrl(String url) {
    String serviceUrl = erpConfig.getAshxServiceUrl();
    if (StrUtil.isBlank(serviceUrl)) {
      throw new CheckException(
          StrUtil.emptyToDefault(erpConfig.getServiceName(), "存在外部系统") + "地址未配置，请联系管理员处理！");
    }
    return StrUtil.removeSuffix(serviceUrl, "/") + StrUtil.addPrefixIfNot(url, "/");
  }

  private String buildAxhxSecondFullUrl(String url) {
    String serviceUrl = erpConfig.getAshxSecondServiceUrl();
    if (StrUtil.isBlank(serviceUrl)) {
      throw new CheckException(
          StrUtil.emptyToDefault(erpConfig.getServiceName(), "存在外部系统") + "地址未配置，请联系管理员处理！");
    }
    return StrUtil.removeSuffix(serviceUrl, "/") + StrUtil.addPrefixIfNot(url, "/");
  }
}
