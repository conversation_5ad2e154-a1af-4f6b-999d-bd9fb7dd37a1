package com.xhgj.srm.request.dto.erp;

import com.alibaba.fastjson.annotation.JSONField;
import java.math.BigDecimal;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2022/12/20 14:20
 */
@Data
@NoArgsConstructor
public class RowAndQty {

  /**
   * erp 行 id
   */
  @JSO<PERSON>ield(name = "RowId")
  private String rowId;

  /**
   * 数量
   */
  @JSONField(name = "QTY")
  private BigDecimal qty;

}
