/**
 * @since 2025/4/21 17:49
 */

/**
 *<AUTHOR>
 *@date 2025/4/21 17:49:35
 *@description
 */

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.xhgj.srm.api.ApiPortalApplication;
import com.xhgj.srm.api.portal.factory.MapStructFactory;
import com.xhgj.srm.common.enums.VerifyConfigTypeEnum;
import com.xhgj.srm.jpa.entity.PurchaseApplyForOrder;
import com.xhgj.srm.jpa.entity.PurchaseApplyRecord;
import com.xhgj.srm.jpa.entity.PurchaseApplyRecord.PurchaseApplyRecordJson;
import com.xhgj.srm.jpa.entity.VerifyConfig;
import com.xhgj.srm.jpa.entity.v2.PurchaseApplyForOrderV2;
import com.xhgj.srm.jpa.enums.PurchaseApplyRecordFields;
import com.xhgj.srm.jpa.repository.PurchaseApplyForOrderRepository;
import com.xhgj.srm.jpa.repository.VerifyConfigRepository;
import com.xhgj.srm.request.dto.hZero.process.StartProcessParam;
import com.xhgj.srm.request.service.third.hZero.HZeroService;
import com.xhgj.srm.v2.factory.PurchaseApplyForOrderV2Factory;
import com.xhgj.srm.v2.repository.PurchaseApplyForOrderV2Repository;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

@RunWith(SpringJUnit4ClassRunner.class)
@SpringBootTest(classes = ApiPortalApplication.class)
public class PurchaseApplyForOrderV2Test {

  @Resource
  PurchaseApplyForOrderV2Repository purchaseApplyForOrderV2Repository;
  @Resource
  PurchaseApplyForOrderRepository purchaseApplyForOrderRepository;
  @Resource
  HZeroService hZeroService;
  @Resource
  PurchaseApplyForOrderV2Factory purchaseApplyForOrderV2Factory;
  @Resource
  VerifyConfigRepository verifyConfigRepository;

  @Test
  public void testVerifyConfig() {
    VerifyConfig verifyConfig = verifyConfigRepository.findFirstByConfigTypeAndEnable(
        VerifyConfigTypeEnum.WORKBENCH_TWO_ZERO_AVAILABLE_ORG.getCode(), Boolean.TRUE);
    System.out.println(JSON.toJSONString(verifyConfig));
  }

  /**
   * 复制一条数据到V2
   */
  @Test
  public void copyTest() {
    PurchaseApplyForOrder purchaseApplyForOrder =
        purchaseApplyForOrderRepository.findById("40289b289150352a01915076ca06000a").get();
    PurchaseApplyForOrderV2 purchaseApplyForOrderV2 =
        MapStructFactory.INSTANCE.toPurchaseApplyForOrderV2(purchaseApplyForOrder);
    purchaseApplyForOrderV2.setId(null);
    purchaseApplyForOrderV2Repository.save(purchaseApplyForOrderV2);
  }

  @Test
  public void testFind() {
    PurchaseApplyForOrderV2 purchaseApplyForOrderV2 =
        purchaseApplyForOrderV2Repository.findById("1914259609757962240e8f26e60ccbb4").get();
    System.out.println(purchaseApplyForOrderV2);
  }

  @Test
  public void testFeida() {
    List<PurchaseApplyForOrderV2> purchaseApplyForOrderV2List =
        purchaseApplyForOrderV2Repository.findAllById(CollUtil.toList("191448171670616883254afdc4e3b664"));
    List<PurchaseApplyRecord> records = new ArrayList<>();
    PurchaseApplyRecord purchaseApplyRecord = new PurchaseApplyRecord();
    purchaseApplyRecord.setPurchaseApplyId("191448171670616883254afdc4e3b664");
    List<PurchaseApplyRecordJson> jsonArray = new ArrayList<>();
    PurchaseApplyRecordJson json = new PurchaseApplyRecordJson();
    json.setKey(PurchaseApplyRecordFields.PURCHASING_DEPARTMENT.getKey());
    json.setDesc(PurchaseApplyRecordFields.PURCHASING_DEPARTMENT.getValue());
    json.setOldValue("123456");
    json.setNewValue("155694565");
    json.setFieldName(null);
    json.setShowFlag(true);
    PurchaseApplyRecordJson json2 = new PurchaseApplyRecordJson();
    json2.setKey(PurchaseApplyRecordFields.PURCHASING_DEPARTMENT.getKey());
    json2.setDesc(PurchaseApplyRecordFields.PURCHASING_DEPARTMENT.getValue());
    json2.setOldValue("253");
    json2.setNewValue("256");
    json2.setFieldName("purchaseDepartment");
    json2.setShowFlag(false);
    jsonArray.add(json);
    jsonArray.add(json2);
    PurchaseApplyRecordJson json3 = new PurchaseApplyRecordJson();
    json3.setKey(PurchaseApplyRecordFields.APPLY_QUANTITY.getKey());
    json3.setDesc(PurchaseApplyRecordFields.APPLY_QUANTITY.getValue());
    json3.setOldValue("6");
    json3.setNewValue("20");
    json3.setFieldName("applyForNumber");
    json3.setShowFlag(true);
    jsonArray.add(json3);
    purchaseApplyRecord.setChanges(JSON.toJSONString(jsonArray));
    records.add(purchaseApplyRecord);
    StartProcessParam feidaProcessParam =
        purchaseApplyForOrderV2Factory.createFeidaProcessParam(purchaseApplyForOrderV2List, records,
            true);
    hZeroService.startProcessWithoutFile(feidaProcessParam);
  }
}
