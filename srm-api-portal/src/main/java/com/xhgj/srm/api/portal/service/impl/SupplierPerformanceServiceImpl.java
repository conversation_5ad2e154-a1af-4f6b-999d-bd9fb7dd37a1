package com.xhgj.srm.api.portal.service.impl;

import com.xhgj.srm.api.portal.service.SupplierPerformanceService;
import com.xhgj.srm.common.Constants;
import com.xhgj.srm.jpa.dao.SupplierPerformanceDao;
import com.xhgj.srm.jpa.entity.SupplierPerformance;
import com.xhgj.srm.jpa.repository.SupplierPerformanceRepository;
import com.xhiot.boot.framework.jpa.repository.BootBaseRepository;
import java.util.List;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 */
@Service
public class SupplierPerformanceServiceImpl implements SupplierPerformanceService {

  @Resource
  private SupplierPerformanceRepository repository;
  @Resource
  private SupplierPerformanceDao supplierPerformanceDao;

  @Override
  public BootBaseRepository<SupplierPerformance, String> getRepository() {
    return repository;
  }

  @Override
  public SupplierPerformance getFirstBySupplierIdAndPlatformCode(String supplierId,
      String platformCode) {
    return repository.getFirstByPlatformCodeAndSupplierIdAndStateOrderByUpdateTimeDesc(platformCode, supplierId,
        Constants.STATE_OK);
  }
  @Override
  public List<String> getPerformCodeListBySupplierId(String supplierId){
   return  supplierPerformanceDao.getListBySupplierId(supplierId).stream()
        .map(SupplierPerformance::getPlatformCode).collect(Collectors.toList());
  }
}
