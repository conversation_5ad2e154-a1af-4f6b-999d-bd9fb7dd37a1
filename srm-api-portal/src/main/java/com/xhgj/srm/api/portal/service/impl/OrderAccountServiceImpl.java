package com.xhgj.srm.api.portal.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.PageUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.xhgj.srm.dto.account.AccountPageQuery;
import com.xhgj.srm.dto.account.AccountPageDTO;
import com.xhgj.srm.api.portal.service.OrderAccountService;
import com.xhgj.srm.common.Constants;
import com.xhgj.srm.common.config.SrmConfig;
import com.xhgj.srm.dto.account.OrderAccountInvoiceInfo;
import com.xhgj.srm.jpa.dao.FileDao;
import com.xhgj.srm.jpa.dao.GroupDao;
import com.xhgj.srm.jpa.dao.OrderAccountDao;
import com.xhgj.srm.jpa.dao.OrderAccountDetailDao;
import com.xhgj.srm.jpa.dao.SearchSchemeDao;
import com.xhgj.srm.jpa.dao.UserDao;
import com.xhgj.srm.jpa.entity.Group;
import com.xhgj.srm.jpa.entity.OrderAccount;
import com.xhgj.srm.jpa.entity.OrderAccountDetail;
import com.xhgj.srm.jpa.entity.SearchScheme;
import com.xhgj.srm.jpa.entity.User;
import com.xhgj.srm.jpa.repository.OrderAccountRepository;
import com.xhgj.srm.jpa.repository.UserRepository;
import com.xhgj.srm.service.ShareOrderAccountInvoiceService;
import com.xhiot.boot.core.common.exception.CheckException;
import com.xhiot.boot.core.common.util.StringUtils;
import com.xhiot.boot.mvc.base.PageResult;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
public class OrderAccountServiceImpl implements OrderAccountService {

  private final String url;

  public OrderAccountServiceImpl(SrmConfig config) {
    this.url = config.getUploadUrl();
  }

  @Resource
  UserRepository userRepository;
  @Resource
  OrderAccountDetailDao orderAccountDetailDao;
  @Resource
  SearchSchemeDao searchSchemeDao;
  @Resource
  OrderAccountDao orderAccountDao;
  @Resource
  OrderAccountRepository orderAccountRepository;
  @Resource
  GroupDao groupDao;
  @Resource
  UserDao userDao;
  @Resource
  FileDao fileDao;
  @Resource
  ShareOrderAccountInvoiceService shareOrderAccountInvoiceService;

  @Override
  public PageResult<AccountPageDTO> getAccountPage(AccountPageQuery accountPageQuery) {
    //查询方案
    String schemeId = !StringUtils.isNullOrEmpty(accountPageQuery.getSchemeId())?accountPageQuery.getSchemeId():"";
    String userId = !StringUtils.isNullOrEmpty(accountPageQuery.getUserId())?accountPageQuery.getUserId():"";
    if (StringUtils.isNullOrEmpty(schemeId)) {
      SearchScheme search = searchSchemeDao.getDefaultSearchScheme(userId, Constants.SEARCH_TYPE_SUPPLIER_ACCOUNT);
      if (search != null) {
        schemeId = search.getId();
      }
    }
    if (!StringUtils.isNullOrEmpty(schemeId)) {
      SearchScheme search = searchSchemeDao.get(schemeId);
      if (search != null && !StringUtils.isNullOrEmpty(search.getContent())) {
        JSONObject searchJo = JSONObject.parseObject(search.getContent());
        if (searchJo != null) {
          accountPageQuery.setSupplierName(
              StrUtil.blankToDefault(accountPageQuery.getSupplierName(),searchJo.containsKey(
                  "supplierName") ?
                  searchJo.getString("supplierName") : ""));
          accountPageQuery.setAccountNo(
              StrUtil.blankToDefault(accountPageQuery.getAccountNo(), searchJo.containsKey(
                  "accountNo") ? searchJo.getString("accountNo") : ""));
          accountPageQuery.setReturnAmount(
              StrUtil.blankToDefault(accountPageQuery.getReturnAmount(),searchJo.containsKey(
                  "returnAmount") ? searchJo.getString("returnAmount") : ""));
          accountPageQuery.setAccountStartTime(
              StrUtil.blankToDefault(accountPageQuery.getAccountStartTime(),searchJo.containsKey(
                  "accountStartTime") ? searchJo.getString("accountStartTime") : ""));
          accountPageQuery.setAccountEndTime(
              StrUtil.blankToDefault(accountPageQuery.getAccountEndTime(), searchJo.containsKey(
                  "accountEndTime") ? searchJo.getString("accountEndTime") : ""));
          accountPageQuery.setAccountPrice(
              StrUtil.blankToDefault(accountPageQuery.getAccountPrice(),searchJo.containsKey(
                  "accountPrice") ? searchJo.getString("accountPrice") : ""));
          accountPageQuery.setAccountState(
              StrUtil.blankToDefault(accountPageQuery.getAccountState(),searchJo.containsKey(
                  "accountState") ? searchJo.getString("accountState") : ""));
          accountPageQuery.setInvoicingState(
              StrUtil.blankToDefault(accountPageQuery.getInvoicingState(),searchJo.containsKey(
                  "invoicingState") ? searchJo.getString("invoicingState") : ""));
        }
      }
    }
    User u = null;
    String userIds = "";
    if (!StringUtils.isNullOrEmpty(userId)) {
      u = userRepository.findById(userId).orElseThrow(() -> CheckException.noFindException(User.class, userId));
    }
    //获取部门人员
    if (u != null && !u.getRoleList().contains(Constants.ROLEMAP_GENERALMANAGER) && !u.getRoleList().contains("4")) {
      userIds = getUserIdsByDepart(u, ",");
    }
    Page<String> page = orderAccountDao.getOrderAccountPageByPurchase(userIds, accountPageQuery.getSupplierName(), accountPageQuery.getAccountNo(), accountPageQuery.getReturnAmount(),
        accountPageQuery.getAccountPrice(), accountPageQuery.getAccountStartTime(), accountPageQuery.getAccountEndTime(),
        accountPageQuery.getAccountState(), accountPageQuery.getInvoicingState(), accountPageQuery.getPageNo(), accountPageQuery.getPageSize());
    List<AccountPageDTO> pageDataList = new ArrayList<>();
    int totalPages = page.getTotalPages();
    if (!(accountPageQuery.getPageNo() > totalPages)) {
      List<String> orderAccounts = page.getContent();
      PageUtil.setOneAsFirstPageNo();
      pageDataList = CollUtil.emptyIfNull(orderAccounts).stream()
          .map(orderAccountId -> {
            OrderAccount orderAccount = orderAccountRepository.findById(orderAccountId).orElseThrow(() -> CheckException.noFindException(OrderAccount.class, orderAccountId));
            AccountPageDTO data = new AccountPageDTO(orderAccount);
            List<OrderAccountInvoiceInfo> orderAccountInvoiceInfos = shareOrderAccountInvoiceService.getByAccountId(
                orderAccountId, url);
            if (CollUtil.isNotEmpty(orderAccountInvoiceInfos)) {
              data.setInvoiceCount(orderAccountInvoiceInfos.size());
            }
            data.setIsUpload(CollUtil.isNotEmpty(orderAccountInvoiceInfos) ? Constants.YES : Constants.NO);
            //订单数
            List<OrderAccountDetail> orderAccountDetails = orderAccountDetailDao.getOrderAccountDetailByAccount(orderAccount.getId());
            if (CollUtil.isNotEmpty(orderAccountDetails)) {
              data.setOrderCount(orderAccountDetails.size());
            }
            data.setReturnPrice(orderAccount.getReturnPrice());
            return data;
          }).collect(Collectors.toList());

    }
    return new PageResult<>(pageDataList, page.getTotalElements(), totalPages,accountPageQuery.getPageNo(), accountPageQuery.getPageSize());
  }


  public String getUserIdsByDepart(User u, String splitStr) {
    Assert.notNull(u, "用户为空");
    Assert.notEmpty(splitStr, "分隔符为空");
    StringBuilder users = new StringBuilder();
    Group userGroup = groupDao.getCurGroupByErpCode(u.getDepartCode());
    if (userGroup != null) {
      if (u.getRoleList().contains(Constants.SUPPLIER_USER_ROLE_ADMIN)) {
        List<Group> groups = groupDao.getGroupByParentIds("", userGroup.getId());
        groups.add(userGroup);
        for (Group group : groups) {
          List<User> userList = userDao.getUserByDepartId(group.getId());
          if (CollUtil.isNotEmpty(userList)) {
            for (User otherUser : userList) {
              users.append("'").append(otherUser.getId()).append("'").append(splitStr);
            }
          }
        }
      } else if (u.getRoleList().contains(Constants.SUPPLIER_USER_ROLE_ORDINARY)) {
        users.append("'").append(u.getId()).append("'").append(splitStr);
      }
    } else {
      users.append("'").append(u.getId()).append("'").append(splitStr);
    }
    if (users.length() > 0) {
      users = new StringBuilder(users.substring(0, users.length() - 1));
    }
    return users.toString();
  }
}

