package com.xhgj.srm.api.portal.service.impl;

import com.xhgj.srm.api.portal.service.NoticeCenterService;
import com.xhgj.srm.common.Constants;
import com.xhgj.srm.common.enums.NoticeCenterType;
import com.xhgj.srm.jpa.entity.NoticeCenter;
import com.xhgj.srm.jpa.repository.NoticeCenterRepository;
import com.xhiot.boot.framework.jpa.repository.BootBaseRepository;
import javax.annotation.Resource;
import org.springframework.stereotype.Service;

/**
 * Created by Geng Shy on 2023/10/13
 */
@Service
public class NoticeCenterServiceImpl implements NoticeCenterService {

  @Resource
  private NoticeCenterRepository repository;

  @Override
  public BootBaseRepository<NoticeCenter, String> getRepository() {
    return repository;
  }

  @Override
  public void save(NoticeCenterType type, String content, String supplierOrderId,
      String supplierId) {
    NoticeCenter noticeCenter = new NoticeCenter();
    noticeCenter.setContent(content);
    noticeCenter.setType(type.getKey());
    noticeCenter.setSupplierId(supplierId);
    noticeCenter.setSupplierOrderId(supplierOrderId);
    noticeCenter.setIsRead(false);
    noticeCenter.setState(Constants.STATE_OK);
    noticeCenter.setCreateTime(System.currentTimeMillis());
    save(noticeCenter);
  }
}
