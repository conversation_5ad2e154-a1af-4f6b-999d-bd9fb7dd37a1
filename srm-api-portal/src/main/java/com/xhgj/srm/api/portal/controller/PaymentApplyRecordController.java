package com.xhgj.srm.api.portal.controller;

import com.xhgj.srm.dto.ApprovalResultsParam;
import com.xhgj.srm.api.portal.service.PaymentApplyRecordService;
import com.xhiot.boot.mvc.base.ResultBean;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/paymentApplyRecord")
@Api(tags = {"付款申请记录相关api"})
@Slf4j
public class PaymentApplyRecordController {
  @Resource
  private PaymentApplyRecordService service;

  @ApiOperation(value = "同步审批结果", notes = "同步审批结果")
  @PostMapping(value = "/approvalResults")
  public ResultBean<Boolean> approvalResults(@RequestBody @Validated ApprovalResultsParam params) {
    service.approvalResults(params);
    return new ResultBean<>(true);
  }

}
