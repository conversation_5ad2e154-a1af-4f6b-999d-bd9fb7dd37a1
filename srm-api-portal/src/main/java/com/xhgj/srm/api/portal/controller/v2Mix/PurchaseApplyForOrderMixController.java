package com.xhgj.srm.api.portal.controller.v2Mix;/**
 * @since 2025/6/4 10:42
 */

import com.xhgj.srm.api.portal.service.PurchaseApplyForOrderService;
import com.xhgj.srm.request.service.third.erp.sap.dto.PuchaseOrderAuditParam;
import com.xhiot.boot.mvc.base.ResultBean;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import javax.annotation.Resource;

/**
 *<AUTHOR>
 *@date 2025/6/4 10:42:33
 *@description
 */
@RestController
@RequestMapping("/purchaseApplyForOrder")
@Validated
@Api("采购订单申请接口")
public class PurchaseApplyForOrderMixController {
  @Resource
  private PurchaseApplyForOrderService purchaseOrderService;

  @ApiOperation(value = "采购订单审核")
  @PostMapping(value = "/auditPurchaseOrder", consumes = {MediaType.APPLICATION_JSON_VALUE})
  public ResultBean<String> auditPurchaseOrder(@RequestBody @Validated PuchaseOrderAuditParam param) {
    return new ResultBean<>(purchaseOrderService.auditPurchaseOrder(param));
  }
}
