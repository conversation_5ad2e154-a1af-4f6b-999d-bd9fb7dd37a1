package com.xhgj.srm.api;

import com.dtflys.forest.springboot.annotation.ForestScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.domain.EntityScan;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.context.annotation.Import;
import org.springframework.data.jpa.repository.config.EnableJpaAuditing;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;

/**
 * Created by Geng Shy on 2023/10/8
 */
@SpringBootApplication(scanBasePackages = {"com.xhiot.boot", "com.xhgj.srm"}, exclude = {DataSourceAutoConfiguration.class})
@EnableJpaRepositories(basePackages = {"com.xhiot.boot", "com.xhgj.srm"})
//@EntityScan(basePackages = {"com.xhiot.boot", "com.xhgj.srm.jpa"})
@EntityScan(basePackages = {"com.xhiot.boot", "com.xhgj.srm"})
@EnableJpaAuditing
@Import(cn.hutool.extra.spring.SpringUtil.class)
public class ApiPortalApplication {

  public static void main(String[] args) {
    SpringApplication.run(ApiPortalApplication.class, args);
  }
}
