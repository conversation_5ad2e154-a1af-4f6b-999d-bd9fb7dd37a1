package com.xhgj.srm.api.portal.controller;

import cn.hutool.core.collection.CollUtil;
import com.xhgj.srm.api.portal.dto.AddWarehousingEntryParam;
import com.xhgj.srm.api.portal.dto.UpdateExecutionStatusParam;
import com.xhgj.srm.api.portal.dto.purchase.order.PurchaseApplyForOrderAddParam;
import com.xhgj.srm.api.portal.factory.MapStructFactory;
import com.xhgj.srm.api.portal.service.PurchaseApplyForOrderService;
import com.xhgj.srm.common.enums.VerifyConfigTypeEnum;
import com.xhgj.srm.jpa.entity.VerifyConfig;
import com.xhgj.srm.jpa.repository.VerifyConfigRepository;
import com.xhgj.srm.jpa.sharding.enums.VersionEnum;
import com.xhgj.srm.request.service.third.erp.sap.dto.PuchaseOrderAuditParam;
import com.xhgj.srm.v2.form.PurchaseApplyForOrderV2AddForm;
import com.xhgj.srm.v2.service.PurchaseApplyForOrderV2Service;
import com.xhiot.boot.core.common.exception.CheckException;
import com.xhiot.boot.mvc.base.ResultBean;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import java.util.List;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import org.springframework.http.MediaType;
import org.springframework.validation.BeanPropertyBindingResult;
import org.springframework.validation.FieldError;
import org.springframework.validation.Validator;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * Created by Geng Shy on 2023/12/10
 */
@RestController
@RequestMapping("/purchaseApplyForOrder")
@Validated
@Api("采购订单申请接口")
public class PurchaseApplyForOrderController {

  @Resource
  private PurchaseApplyForOrderService purchaseOrderService;
  @Resource
  private PurchaseApplyForOrderV2Service purchaseApplyForOrderV2Service;
  @Resource
  private Validator validator;
  @Resource
  VerifyConfigRepository verifyConfigRepository;

  @ApiOperation(value = "采购申请订单新增")
  @PostMapping(value = "/add", consumes = {MediaType.APPLICATION_JSON_VALUE})
  public ResultBean<Boolean> addPurchaseOrder(@RequestBody List<PurchaseApplyForOrderV2AddForm> purchaseOrderAddParams) {
    if (CollUtil.isEmpty(purchaseOrderAddParams)) {
      throw new CheckException("采购申请单不能为空");
    }
    String purchasingOrganization = purchaseOrderAddParams.get(0).getPurchasingOrganization();
    // 获取第一个的组织
    VerifyConfig verifyConfig = verifyConfigRepository.findFirstByConfigTypeAndEnable(
        VerifyConfigTypeEnum.WORKBENCH_TWO_ZERO_AVAILABLE_ORG.getCode(), Boolean.TRUE);
    List<String> organizationRoleList = verifyConfig.getOrganizationRoleList();
    VersionEnum version;
    if (organizationRoleList.contains(purchasingOrganization)) {
      // 组织在V2版本中
      version = VersionEnum.V2;
    } else {
      // 组织在V1版本中
      version = VersionEnum.V1;
    }
    //
    if (version == VersionEnum.V2) {
      // 手动校验v2的
      for (PurchaseApplyForOrderV2AddForm param : purchaseOrderAddParams) {
        BeanPropertyBindingResult errors = new BeanPropertyBindingResult(param, "param");
        validator.validate(param, errors);
        if (errors.hasErrors()) {
          // 获取第一个错误直接抛出
          FieldError firstError = errors.getFieldErrors().get(0);
          throw new CheckException(
              firstError.getField() + ": " + firstError.getDefaultMessage()
          );
        }
      }
      purchaseApplyForOrderV2Service.addPurchaseApplyForOrder(purchaseOrderAddParams);
    } else {
      // 需要转换一层
      List<PurchaseApplyForOrderAddParam> v1AddParams = purchaseOrderAddParams.stream()
          .map(MapStructFactory.INSTANCE::toPurchaseApplyForOrderAddParam)
          .collect(Collectors.toList());
      // 手动校验v1的
      for (PurchaseApplyForOrderAddParam param : v1AddParams) {
        BeanPropertyBindingResult errors = new BeanPropertyBindingResult(param, "param");
        validator.validate(param, errors);
        if (errors.hasErrors()) {
          // 获取第一个错误直接抛出
          FieldError firstError = errors.getFieldErrors().get(0);
          throw new CheckException(
              firstError.getField() + ": " + firstError.getDefaultMessage()
          );
        }
      }
      purchaseOrderService.addPurchaseApplyForOrder(v1AddParams);
    }
    return new ResultBean<>(true);
  }

  @ApiOperation(value = "入库单/退库单新增", tags = {"0.4.0", "0.4.0-sap"})
  @PostMapping(value = "/addWarehousingEntry", consumes = {MediaType.APPLICATION_JSON_VALUE})
  public ResultBean<Boolean> addWarehousingEntry(@RequestBody @Validated
  AddWarehousingEntryParam param) {
    purchaseOrderService.addWarehousingEntry(param);
    return new ResultBean<>(true);
  }

  @ApiOperation(value = "退库单更新仓库执行状态")
  @PostMapping(value = "/updateExecutionStatus", consumes = {MediaType.APPLICATION_JSON_VALUE})
  public ResultBean<Boolean> updateExecutionStatus(@RequestBody @Validated
  UpdateExecutionStatusParam param) {
    purchaseOrderService.updateExecutionStatus(param);
    return new ResultBean<>(true);
  }

}
