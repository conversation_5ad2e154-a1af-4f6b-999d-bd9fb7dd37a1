package com.xhgj.srm.api.portal.controller;

import com.xhgj.srm.api.portal.dto.InventoryAddParam;
import com.xhgj.srm.api.portal.service.InventoryService;
import com.xhiot.boot.mvc.base.ResultBean;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 *  库存管理推送接口
 */
@Api(tags = {"库存管理推送接口"})
@RestController
@Validated
@RequestMapping("/inventory")
public class InventoryController {

  @Resource
  private InventoryService inventoryService;

  @ApiOperation("批量修改库存")
  @PostMapping(value = "/batchUpdateInventory", consumes = {MediaType.APPLICATION_JSON_VALUE})
  public ResultBean<Boolean> batchUpdateInventory(@RequestBody @Valid List<InventoryAddParam> param) {
    inventoryService.batchUpdateInventory(param);
    return new ResultBean<>(Boolean.TRUE);
  }

}
