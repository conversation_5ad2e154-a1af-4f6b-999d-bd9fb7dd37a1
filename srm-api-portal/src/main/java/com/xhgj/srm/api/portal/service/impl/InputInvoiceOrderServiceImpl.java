package com.xhgj.srm.api.portal.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.xhgj.srm.api.portal.dto.supplier.invoice.OffsetInvoiceParam;
import com.xhgj.srm.api.portal.dto.supplier.invoice.OffsetInvoiceResult;
import com.xhgj.srm.api.portal.service.InputInvoiceOrderService;
import com.xhgj.srm.common.Constants;
import com.xhgj.srm.common.enums.PaymentAuditStateEnum;
import com.xhgj.srm.common.enums.VoucherTypeEnum;
import com.xhgj.srm.common.utils.TimeStampUtil;
import com.xhgj.srm.jpa.dao.OrderSupplierInvoiceDao;
import com.xhgj.srm.jpa.dao.PaymentApplyRecordDao;
import com.xhgj.srm.jpa.entity.FinancialVoucher;
import com.xhgj.srm.jpa.entity.InputInvoiceOrder;
import com.xhgj.srm.jpa.entity.Order;
import com.xhgj.srm.jpa.entity.OrderSupplierInvoice;
import com.xhgj.srm.jpa.entity.PaymentApplyRecord;
import com.xhgj.srm.jpa.entity.Supplier;
import com.xhgj.srm.jpa.entity.SupplierInvoiceToDetail;
import com.xhgj.srm.jpa.entity.SupplierOrder;
import com.xhgj.srm.jpa.entity.SupplierOrderDetail;
import com.xhgj.srm.jpa.repository.FinancialVoucherRepository;
import com.xhgj.srm.jpa.repository.OrderInvoiceRelationRepository;
import com.xhgj.srm.jpa.repository.OrderRepository;
import com.xhgj.srm.jpa.repository.OrderSupplierInvoiceRepository;
import com.xhgj.srm.jpa.repository.SupplierInvoiceToDetailRepository;
import com.xhgj.srm.jpa.repository.SupplierOrderDetailRepository;
import com.xhgj.srm.jpa.repository.SupplierOrderRepository;
import com.xhgj.srm.jpa.repository.SupplierRepository;
import com.xhgj.srm.service.ShareInputInvoiceService;
import com.xhgj.srm.service.SharePaymentApplyDetailService;
import com.xhgj.srm.service.SupplierInvoiceToDetailService;
import com.xhiot.boot.core.common.exception.CheckException;
import com.xhiot.boot.framework.jpa.repository.BootBaseRepository;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import com.xhiot.boot.mvc.base.ResultBean;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
@Slf4j
public class InputInvoiceOrderServiceImpl implements InputInvoiceOrderService {
  @Resource
  private OrderInvoiceRelationRepository repository;
  @Resource
  private SupplierOrderRepository supplierOrderRepository;
  @Resource
  private SupplierOrderDetailRepository supplierOrderDetailRepository;
  @Resource
  com.xhgj.srm.service.SupplierInvoiceService supplierInvoiceService;
  @Resource
  private OrderInvoiceRelationRepository orderInvoiceRelationRepository;
  @Resource
  private OrderSupplierInvoiceRepository orderSupplierInvoiceRepository;
  @Resource
  private SupplierRepository supplierRepository;
  @Resource
  private FinancialVoucherRepository financialVoucherRepository;
  @Autowired
  private OrderSupplierInvoiceDao orderSupplierInvoiceDao;
  @Autowired
  private SupplierInvoiceToDetailService supplierInvoiceToDetailService;
  @Resource
  PaymentApplyRecordDao paymentApplyRecordDao;
  @Resource
  OrderRepository orderRepository;
  @Resource
  SharePaymentApplyDetailService sharePaymentApplyDetailService;
  @Resource
  ShareInputInvoiceService shareInputInvoiceService;


  @Override
  public BootBaseRepository<InputInvoiceOrder, String> getRepository() {
    return repository;
  }

  @Override
  @Transactional
  public OffsetInvoiceResult offsetInvoice(OffsetInvoiceParam param) {
    // 1. 获取进项票数据
    InputInvoiceOrder orderInvoiceRelation =
        orderInvoiceRelationRepository.findFirstByInvoiceVoucherNumberAndAccountingYearAndStateOrderByCreateTimeDesc(
            param.getInvoiceVoucherNo(), param.getAccountingYear(), Constants.STATE_OK);
    if (orderInvoiceRelation == null) {
      throw new CheckException("数据异常，请联系管理员");
    }
    orderInvoiceRelation.setInvoiceState(Constants.ORDER_INVOICE_STATE_OFFSET);
    orderInvoiceRelation.setOffsetAccountingYear(param.getOffsetAccountingYear());
    long reversalDate = TimeStampUtil.convertYmdToMillis(param.getReversalDate(), "yyyy-MM-dd");
    orderInvoiceRelation.setReversalDate(reversalDate);
    orderInvoiceRelation.setReversalVoucherNo(param.getReversalVoucherNo());
    orderInvoiceRelation.setInvoiceOffsetVoucherNo(param.getInvoiceOffsetVoucherNo());
    orderInvoiceRelationRepository.save(orderInvoiceRelation);
    // 2. 处理不同源单类型冲销
    // 落地商
    if (Constants.COOPERATE_TYPE_LANDER.equals(orderInvoiceRelation.getOrderSource())) {
      this.handlerLander(orderInvoiceRelation);
    }
    // 供应商
    if (Constants.COOPERATE_TYPE_SUPPLIER.equals(orderInvoiceRelation.getOrderSource())) {
      this.handlerSupplier(orderInvoiceRelation);
    }
    // 3. 发票信息
    List<String> invoiceNumberList = orderInvoiceRelation.getInvoiceNumsList();
    invoiceNumberList.forEach(invoiceNumber -> {
      OrderSupplierInvoice orderSupplierInvoice =
          orderSupplierInvoiceDao.getValidInvoiceByNum(invoiceNumber).orElseThrow(
              // 这里应该是必定会找到发票的，所以如果没找到就抛错了
              () -> CheckException.noFindException(OrderSupplierInvoice.class, invoiceNumber));
      orderSupplierInvoice.setOffset(Constants.YES);
      orderSupplierInvoiceRepository.save(orderSupplierInvoice);
    });
    // 4.处理财务凭证
    //    deleteFinancialVoucher(orderInvoiceRelation.getInvoiceNums());
    deleteFinancialVoucherByFinancialVoucherNo(orderInvoiceRelation.getFinancialVouchers(),orderInvoiceRelation.getInvoiceNums());
    return OffsetInvoiceResult.builder().invoiceOffsetVoucherNo(param.getInvoiceOffsetVoucherNo())
        .offsetAccountingYear(param.getOffsetAccountingYear())
        .purchasingOrganization(param.getPurchasingOrganization()).build();
  }

  /**
   * 处理供应商冲销
   * @param inputInvoiceOrder
   */
  private void handlerSupplier(InputInvoiceOrder inputInvoiceOrder) {
    List<SupplierInvoiceToDetail> supplierInvoiceToDetails =
        // TODO 多发票情况，先上线，等测试空闲需要尽快在测试环境验证
        shareInputInvoiceService.getSupplierInvoiceToDetailDistinct(inputInvoiceOrder.getId());
    if (CollUtil.isEmpty(supplierInvoiceToDetails)) {
      return;
    }
    shareInputInvoiceService.batchGetSupplierInvoiceToDetailLink(supplierInvoiceToDetails,
        (id2SupplierOrderDetail , id2SupplierOrder)->{
      for (SupplierInvoiceToDetail supplierInvoiceToDetail : CollUtil.emptyIfNull(
          supplierInvoiceToDetails)) {
        String detailId = supplierInvoiceToDetail.getDetailId();
        SupplierOrderDetail supplierOrderDetail = id2SupplierOrderDetail.get(detailId);
        if (supplierOrderDetail == null) {
          throw new CheckException("数据异常，请联系管理员");
        }
        supplierOrderDetail.setInvoicedNum(NumberUtil.sub(supplierOrderDetail.getInvoicedNum(),
            supplierInvoiceToDetail.getInvoiceNum()));
        log.info("发票审核冲销已开票数量：{}", NumberUtil.add(
            NumberUtil.sub(supplierOrderDetail.getInvoicedNum(),
                supplierInvoiceToDetail.getInvoiceNum())));
        supplierOrderDetail.setInvoicableNum(NumberUtil.add(supplierOrderDetail.getInvoicableNum(),
            supplierInvoiceToDetail.getInvoiceNum()));
        SupplierOrder supplierOrder = id2SupplierOrder.get(supplierOrderDetail.getPurchaseOrderId());
        if (supplierOrder == null) {
          throw new CheckException("数据异常，请联系管理员");
        }
        // 发票冲销后置Null问题排查 --- 排查结果 -- 进票项详情不应该使用此字段查询
        supplierOrder.setOrderInvoiceRelationId(null);
      }
      // supplierOrderDetails
      List<SupplierOrderDetail> supplierOrderDetails = ListUtil.toList(id2SupplierOrderDetail.values());
      // supplierOrder
      List<SupplierOrder> supplierOrders = ListUtil.toList(id2SupplierOrder.values());
      // purchaseOrderIds
      List<String> purchaseOrderIds =
          supplierOrderDetails.stream().map(SupplierOrderDetail::getPurchaseOrderId)
              .collect(Collectors.toList()).stream().distinct().collect(Collectors.toList());
      supplierOrderDetailRepository.saveAll(supplierOrderDetails);
      supplierOrderDetailRepository.flush();
      supplierOrderRepository.saveAll(supplierOrders);
      supplierOrderRepository.flush();
      supplierInvoiceService.batchSetSupplierOpenInvoiceState(purchaseOrderIds);
    });
  }

  /**
   * 处理落地商冲销
   * @param inputInvoiceOrder
   */
  private void handlerLander(InputInvoiceOrder inputInvoiceOrder) {
    // 根据orderNums获取相应的订单
    List<String> orderIdsList = inputInvoiceOrder.getOrderIdsList();
    orderIdsList.add("-1");
    List<Order> findOrders = orderRepository.findAllById(orderIdsList);
    findOrders.forEach(item -> {
      item.setOrderInvoiceRelationId(null);
      item.setSupplierOpenInvoiceStatus(Constants.ORDER_INVOICE_STATE_NOT_DONE);
    });
    if (CollUtil.isNotEmpty(findOrders)) {
      orderRepository.saveAll(findOrders);
    }
  }


  private void deleteFinancialVoucherByFinancialVoucherNo(String financialVoucherNo,String invoiceOrderNo) {
    if (!StrUtil.isAllNotBlank(financialVoucherNo,invoiceOrderNo)) {
      return;
    }
    // 逻辑一 进项票对应的发票过账凭证的已提款金额-退款金额>0 不能冲销
    List<FinancialVoucher> financialVouchers =
        financialVoucherRepository.findAllByFinancialVoucherNoAndVoucherTypeAndInvoiceOrderNoAndState(
            financialVoucherNo, VoucherTypeEnum.INVOICE_POSTING.getKey(), invoiceOrderNo,
            Constants.STATE_OK).stream().map(financialVoucher -> {
          BigDecimal withdrawnAmount =
              Optional.ofNullable(financialVoucher.getWithdrawnAmount()).orElse(BigDecimal.ZERO);
          BigDecimal refundAmount =
              Optional.ofNullable(financialVoucher.getRefundAmount()).orElse(BigDecimal.ZERO);
          if (withdrawnAmount.subtract(refundAmount).compareTo(BigDecimal.ZERO) > 0) {
            throw new CheckException("发票已经有对应的提款记录，且没有完全退款，无法冲销");
          }
          financialVoucher.setState(Constants.STATE_DELETE);
          financialVoucherRepository.save(financialVoucher);
          return financialVoucher;
        }).collect(Collectors.toList());
    List<String> financialVoucherIds =
        financialVouchers.stream().map(FinancialVoucher::getId).collect(Collectors.toList());
    // 逻辑二 对应的发票过账凭证有审核中/驳回状态的付款申请 不能冲销
    List<PaymentApplyRecord> findOnes =
        paymentApplyRecordDao.findAllByVoucherIdInAndStateIn(financialVoucherIds, new ArrayList<>(
            Arrays.asList(PaymentAuditStateEnum.PROCESSING, PaymentAuditStateEnum.REJECTED)));
    if (CollUtil.isNotEmpty(findOnes)) {
      throw new CheckException("对应的发票过账凭证有审核中/驳回状态的付款申请，无法冲销");
    }
  }
}
