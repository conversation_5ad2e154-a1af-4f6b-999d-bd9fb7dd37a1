package com.xhgj.srm.api.portal.dto.supplier.invoice;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class OffsetInvoiceResult {

  /**
   * 发票冲销凭证号
   */
  @JSONField(name = "INVOICE_OFFSET_VOUCHER_NO")
  private String invoiceOffsetVoucherNo;
  /**
   * 采购组织
   */
  @JSONField(name = "PURCHASING_ORGANIZATION")
  private String purchasingOrganization;

  /**
   * 冲销会计年度
   */
  @JSONField(name = "OFFSET_ACCOUNTING_YEAR")
  private String offsetAccountingYear;

}
