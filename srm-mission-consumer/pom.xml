<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <groupId>com.xhgj</groupId>
    <artifactId>srm-boot</artifactId>
    <version>3.0.0-SNAPSHOT</version>
  </parent>

  <packaging>jar</packaging>
  <artifactId>srm-mission-consumer</artifactId>

  <properties>
    <maven.compiler.source>8</maven.compiler.source>
    <maven.compiler.target>8</maven.compiler.target>
    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
  </properties>
  <dependencies>
    <dependency>
      <groupId>com.xhiot.xhiot-boot</groupId>
      <artifactId>boot-mvc</artifactId>
    </dependency>
    <dependency>
      <groupId>com.xhgj</groupId>
      <artifactId>srm-jpa</artifactId>
    </dependency>
    <dependency>
      <groupId>com.xhgj</groupId>
      <artifactId>srm-mq</artifactId>
    </dependency>
    <dependency>
      <groupId>commons-fileupload</groupId>
      <artifactId>commons-fileupload</artifactId>
      <version>1.3.3</version>
      <exclusions>
        <exclusion>
          <artifactId>commons-io</artifactId>
          <groupId>commons-io</groupId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>com.xhgj</groupId>
      <artifactId>srm-request</artifactId>
      <exclusions>
        <exclusion>
          <artifactId>commons-io</artifactId>
          <groupId>commons-io</groupId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>com.xhgj</groupId>
      <artifactId>srm-mission-common</artifactId>
    </dependency>
    <dependency>
      <groupId>com.xhgj</groupId>
      <artifactId>srm-service</artifactId>
    </dependency>
    <dependency>
      <groupId>com.xhgj</groupId>
      <artifactId>mdm-customer-platform</artifactId>
      <version>3.0.0-SNAPSHOT</version>
    </dependency>
  </dependencies>

  <build>
    <finalName>srm-mission-consumer</finalName>
    <plugins>
      <plugin>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-maven-plugin</artifactId>
      </plugin>
    </plugins>
  </build>
</project>