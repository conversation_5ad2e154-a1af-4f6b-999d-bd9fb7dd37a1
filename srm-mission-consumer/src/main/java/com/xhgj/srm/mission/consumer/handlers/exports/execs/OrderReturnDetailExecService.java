package com.xhgj.srm.mission.consumer.handlers.exports.execs;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.xhgj.srm.common.Constants_Excel;
import com.xhgj.srm.common.utils.ExportUtil;
import com.xhgj.srm.dto.order.ExportOrderParams;
import com.xhgj.srm.dto.order.OrderReturnDetailExportDTO;
import com.xhgj.srm.jpa.dao.OrderDao;
import com.xhgj.srm.jpa.entity.Order;
import com.xhgj.srm.jpa.entity.OrderReturn;
import com.xhgj.srm.jpa.entity.OrderReturnDetail;
import com.xhgj.srm.jpa.repository.OrderRepository;
import com.xhgj.srm.mission.common.MissionDispatchParam;
import com.xhgj.srm.mission.consumer.framework.service.FcMissionService;
import com.xhgj.srm.mission.consumer.handlers.ExecService;
import com.xhgj.srm.mission.consumer.handlers.exports.ExportMissionCompleteResult;
import com.xhgj.srm.service.OrderReturnDetailTempService;
import com.xhgj.srm.service.OrderReturnTempService;
import com.xhiot.boot.core.common.exception.CheckException;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.FillPatternType;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.stereotype.Component;
import javax.annotation.Resource;
import java.io.ByteArrayOutputStream;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.stream.IntStream;
import java.util.stream.Stream;

/**
 * OrderReturnDetailExportExec
 */
@Slf4j
@Component
public class OrderReturnDetailExecService implements ExecService {

  @Resource
  private OrderRepository orderRepository;
  @Resource
  private OrderDao orderDao;
  @Resource
  private ExportUtil ex;
  @Resource
  private FcMissionService fcMissionService;
  @Resource
  private OrderReturnTempService orderReturnTempService;
  @Resource
  private OrderReturnDetailTempService orderReturnDetailTempService;

  @SneakyThrows
  @Override
  public ExportMissionCompleteResult exec(Collection<?> collection,
      MissionDispatchParam dispatchParam) {
    try (XSSFWorkbook book = new XSSFWorkbook();) {
    CellStyle baseStyle = ex.getBaseStyle(book);
    CellStyle titleStyle = ex.getTitleStyle(book);
    titleStyle.setFillForegroundColor(IndexedColors.WHITE.getIndex());
    titleStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
    List<Integer> title =
        ListUtil.toList(30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30);
    List<String> titles = new ArrayList<>(Constants_Excel.EXPORT_ORDER_RETURN_DETAIL_TITLE_LIST);
    Sheet sheet = ex.createSheet(book, "落地商订单退货明细导出", title);
    Row rowTitle = sheet.createRow(0);
    ex.createTitle(titles, titleStyle, rowTitle);
    List<OrderReturnDetailExportDTO> detailExportDTOList = (List<OrderReturnDetailExportDTO>) collection;
    int count = 0;
    // 写入数据
    int index = 1; // 假设从第二行开始写入数据
    for (OrderReturnDetailExportDTO detailExportDTO : detailExportDTOList) {
      Row row = sheet.createRow(index);
      int rowNum = index + 1;
      ex.createCell(row, 0, detailExportDTO.getOrderNo(), baseStyle);
      ex.createCell(row, 1, detailExportDTO.getOrderState(), baseStyle);
      ex.createCell(row, 2, detailExportDTO.getPlatform(), baseStyle);
      ex.createCell(row, 3, detailExportDTO.getEnterpriseName(), baseStyle);
      ex.createCell(row, 4, detailExportDTO.getReturnNo(), baseStyle);
      ex.createCell(row, 5, detailExportDTO.getReturnTime(), baseStyle);
      ex.createCell(row, 6, detailExportDTO.getReturnPrice(), baseStyle);
      ex.createCell(row, 7, detailExportDTO.getStateStr(), baseStyle);
      ex.createCell(row, 8, detailExportDTO.getErpNo(), baseStyle);
      ex.createCell(row, 9, detailExportDTO.getPurchaseOrderNo(), baseStyle);
      ex.createCell(row, 10,detailExportDTO.getRowNum(), baseStyle);
      ex.createCell(row, 11, detailExportDTO.getProductCode(), baseStyle);
      ex.createCell(row, 12, detailExportDTO.getBrand(), baseStyle);
      ex.createCell(row, 13, detailExportDTO.getProductName(), baseStyle);
      ex.createCell(row, 14, detailExportDTO.getManuCode(), baseStyle);
      ex.createCell(row, 15, detailExportDTO.getReturnNum(), baseStyle);
      ex.createCell(row, 16, detailExportDTO.getUnit(), baseStyle);
      ex.createCell(row, 17, detailExportDTO.getProductPrice(), baseStyle);
      ex.createCell(row, 18, detailExportDTO.getDeliveryDetailId(), baseStyle);
      count++;
      index++;
      fcMissionService.createMissionDetail(
          dispatchParam.getMissionId(), "第【" + rowNum + "】行", StrUtil.EMPTY);
    }
    String now = String.valueOf(System.currentTimeMillis());
    String fileNewName = "落地商订单退货明细" + now + ".xlsx";
    ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
    book.write(outputStream);
    return ExportMissionCompleteResult.builder()
        .successCount(count)
        .fileName(fileNewName)
        .bytes(outputStream.toByteArray())
        .build();
    }
  }

  @Override
  public Collection<?> prepare(MissionDispatchParam dispatchParam) {
    JSONObject jsonObject = JSON.parseObject(dispatchParam.getParams());
    String userId = jsonObject.getString("userId");
    if (StrUtil.isBlank(userId)) {
      throw new CheckException("userId参数缺失");
    }
    String queryJson = jsonObject.getString("query");
    ExportOrderParams exportOrderParams =
        JSONObject.parseObject(queryJson, new TypeReference<ExportOrderParams>() {});
    Map<String, Object> queryMap = exportOrderParams.toQueryMap();
    List<Order> orderList;
    if (CollUtil.isNotEmpty(exportOrderParams.getOrderIds())) {
      orderList = orderRepository.findAllByIdInOrderByCreateTimeDesc(exportOrderParams.getOrderIds());
    } else {
      orderList = orderDao.getOrderPageRef(queryMap).getContent();
    }
    // 使用 Stream API 处理订单列表，生成导出详情 DTO 列表
    List<OrderReturnDetailExportDTO> detailExportDTOS = orderList.stream()
        .flatMap(order -> processOrder(order).stream())
        .collect(Collectors.toList());
    return detailExportDTOS;
  }


  private List<OrderReturnDetailExportDTO> processOrder(Order order) {
    // 获取订单退货信息列表
    List<OrderReturn> orderReturnList = orderReturnTempService.getOrderReturnByOrderId(order.getId());
    // 如果存在订单退货信息
    if (CollUtil.isNotEmpty(orderReturnList)) {
      return orderReturnList.stream()
          .flatMap(orderReturn -> {
            // 获取退货明细信息列表
            List<OrderReturnDetail> returnDetailList = orderReturnDetailTempService.getReturnDetailByReturnId(orderReturn.getId());
            // 如果存在退货明细信息
            if (CollUtil.isNotEmpty(returnDetailList)) {
              return IntStream.range(0, returnDetailList.size())
                  .mapToObj(i -> new OrderReturnDetailExportDTO(order, orderReturn, returnDetailList.get(i), i + 1))
                  .collect(Collectors.toList()).stream();
            } else {
              // 如果没有退货明细信息，创建并返回导出详情 DTO
              return Stream.of(new OrderReturnDetailExportDTO(order, orderReturn));
            }
          }).collect(Collectors.toList());
    } else {
      // 如果没有订单退货信息，创建并返回导出详情 DTO
      return ListUtil.toList(new OrderReturnDetailExportDTO(order));
    }
  }

}
