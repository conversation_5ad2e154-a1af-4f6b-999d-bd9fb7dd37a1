package com.xhgj.srm.mission.consumer.handlers.imports.execs;/**
 * @since 2025/6/3 16:39
 */

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.xhgj.srm.common.Constants;
import com.xhgj.srm.jpa.entity.SupplierOrder;
import com.xhgj.srm.jpa.repository.SupplierOrderRepository;
import com.xhgj.srm.migration.service.SupplierOrderMigrationCoordinator;
import com.xhgj.srm.mission.common.MissionDispatchParam;
import com.xhgj.srm.mission.consumer.framework.MissionCompleteResult;
import com.xhgj.srm.mission.consumer.framework.service.FcMissionService;
import com.xhgj.srm.mission.consumer.handlers.ExecService;
import com.xhgj.srm.mission.consumer.handlers.imports.ImportMissionCompleteResult;
import com.xhgj.srm.request.utils.DownloadThenUpUtil;
import com.xhiot.boot.core.common.exception.CheckException;
import com.xhiot.boot.core.common.util.ExcelUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.stereotype.Component;
import javax.annotation.Resource;
import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

/**
 *<AUTHOR>
 *@date 2025/6/3 16:39:39
 *@description
 */
@Component
@Slf4j
public class PurchaseOrderMigrationExecService implements ExecService {

  @Resource
  DownloadThenUpUtil downloadThenUpUtil;
  @Resource
  private FcMissionService fcMissionService;
  @Resource
  private SupplierOrderMigrationCoordinator supplierOrderMigrationCoordinator;
  @Resource
  private SupplierOrderRepository supplierOrderRepository;

  @Override
  public MissionCompleteResult exec(Collection<?> collection, MissionDispatchParam dispatchParam) {
    // 转换为JSONObject
    JSONObject jsonObject = JSONObject.parseObject(dispatchParam.getParams());
    // 获取filePath
    String filePath = jsonObject.getString("filePath");
    // 获取fileName
    String fileName = jsonObject.getString("fileName");
    List<String> supplierOrderCodes = (List<String>) collection;
    int successCount = 0;
    int rowIndex = 1;
    for (String code : supplierOrderCodes) {
      try {
        SupplierOrder supplierOrder = supplierOrderRepository.findFirstByCodeAndState(code, Constants.STATE_OK);
        if (supplierOrder == null) {
          throw new CheckException("订单不存在");
        }
        supplierOrderMigrationCoordinator.migrateSupplierOrder(supplierOrder.getId());
        fcMissionService.createMissionDetail(dispatchParam.getMissionId(),
            String.format("第【%s】行", rowIndex), StrUtil.EMPTY);
        successCount++;
      } catch (Exception e) {
        log.error("采购订单迁移信息异常,第【{}】行数据写入失败,异常信息为:", rowIndex, e);
        fcMissionService.createMissionDetail(dispatchParam.getMissionId(), String.format("第【%s】行,", rowIndex), e.getMessage());
      }
      rowIndex++;
    }
    return ImportMissionCompleteResult.builder()
        .successCount(successCount)
        .fileName(fileName)
        .filePath(filePath)
        .build();
  }

  @Override
  public Collection<?> prepare(MissionDispatchParam dispatchParam) {
    // 转换为JSONObject
    JSONObject jsonObject = JSONObject.parseObject(dispatchParam.getParams());
    // 获取filePath
    String filePath = jsonObject.getString("filePath");
    // 获取fileName
    String fileName = jsonObject.getString("fileName");
    // oss下载文件
    List<String> result = new ArrayList<>();
    try ( InputStream inputStream = downloadThenUpUtil.getInputStreamFromOSS(filePath);
        Workbook book = ExcelUtil.buildByFile(fileName, inputStream);) {
      if (book == null) {
        throw new CheckException("文件为空");
      }
      // 获取sheet
      Sheet sheet = book.getSheetAt(0);
      // 获取总行数
      int lastRowNum = sheet.getPhysicalNumberOfRows();
      // 如果总行数小于1则抛出异常
      if (lastRowNum < 1) {
        throw new CheckException("导入数据为空");
      }
      // 从第二行开始读取
      int startRow = 1;
      // 读取数据
      for (int i = startRow; i <= lastRowNum; i++) {
        // 获取当前行
        Row row = sheet.getRow(i);
        if (row == null) {
          continue;
        }
        int cellNum = 0;
        // 获取固定列
        // 订单号
        String supplierOrderCode = ExcelUtil.getCellStringValue(row.getCell(cellNum++));
        // 物料行号
        String groupCode = ExcelUtil.getCellStringValue(row.getCell(cellNum++));
        result.add(supplierOrderCode);
      }
    } catch (IOException e) {
      throw new RuntimeException(e);
    }
    if (CollUtil.isEmpty(result)) {
      throw new CheckException("导入数据为空");
    }
    return result;
  }
}
