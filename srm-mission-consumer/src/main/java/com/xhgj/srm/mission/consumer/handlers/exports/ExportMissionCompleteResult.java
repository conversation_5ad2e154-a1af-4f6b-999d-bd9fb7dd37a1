package com.xhgj.srm.mission.consumer.handlers.exports;

import com.xhgj.srm.mission.consumer.framework.MissionCompleteResult;
import lombok.Builder;
import lombok.Getter;
import lombok.ToString;
import java.io.File;

/**
 * <AUTHOR>
 * @since 2024/8/8 19:56
 */
@Getter
@Builder(toBuilder = true)
@ToString
public class ExportMissionCompleteResult implements MissionCompleteResult {
  /**
   * 附件名
   */
  private String fileName;

  /**
   * 附件地址
   */
  private String filePath;

  /**
   * 成功行数
   */
  private long successCount;

  /**
   * 强制设置为成功
   */
  private Boolean forceSuccess;

  /**
   * 失败原因
   */
  private String reason;

  /**
   * 导出文件字节数组
   * fix 需要及时释放内存
   */
  private byte[] bytes;

  /**
   * 文件保存路径，若为空则使用默认路径
   *
   * @see com.xhgj.srm.mission.consumer.handlers.ExportMissionHandler
   */
  private String savePath;

  /**
   * 临时上传文件
   */
  private File tempFile;
}
