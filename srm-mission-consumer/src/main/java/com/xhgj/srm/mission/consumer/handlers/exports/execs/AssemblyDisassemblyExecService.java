package com.xhgj.srm.mission.consumer.handlers.exports.execs;/**
 * @since 2025/2/20 10:22
 */

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.xhgj.srm.common.Constants_Excel;
import com.xhgj.srm.common.enums.asmDisOrder.AsmDisOrderItemType;
import com.xhgj.srm.common.utils.ExportUtil;
import com.xhgj.srm.common.vo.asmDisOrder.AsmDisOrderItemDTO;
import com.xhgj.srm.common.vo.asmDisOrder.AsmDisOrderListVO;
import com.xhgj.srm.factory.AsmDisOrderFactory;
import com.xhgj.srm.jpa.dao.AsmDisOrderDao;
import com.xhgj.srm.jpa.entity.AsmDisOrder;
import com.xhgj.srm.mission.common.MissionDispatchParam;
import com.xhgj.srm.mission.consumer.framework.MissionCompleteResult;
import com.xhgj.srm.mission.consumer.framework.service.FcMissionService;
import com.xhgj.srm.mission.consumer.handlers.ExecService;
import com.xhgj.srm.mission.consumer.handlers.exports.ExportMissionCompleteResult;
import com.xhiot.boot.core.common.util.DateUtils;
import java.io.File;
import java.io.FileOutputStream;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicInteger;
import javax.annotation.Resource;
import lombok.SneakyThrows;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.FillPatternType;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Component;

/**
 *<AUTHOR>
 *@date 2025/2/20 10:22:45
 *@description
 */
@Component
public class AssemblyDisassemblyExecService implements ExecService {

  @Resource
  private FcMissionService fcMissionService;
  @Resource
  private AsmDisOrderDao asmDisOrderDao;
  @Resource
  private ExportUtil ex;
  @Resource
  private AsmDisOrderFactory asmDisOrderFactory;

  @Override
  @SneakyThrows
  public MissionCompleteResult exec(Collection<?> collection, MissionDispatchParam dispatchParam) {
    try (SXSSFWorkbook book = new SXSSFWorkbook()) {
      CellStyle baseStyle = ex.getBaseStyle(book);
      CellStyle titleStyle = ex.getTitleStyle(book);
      titleStyle.setFillForegroundColor(IndexedColors.WHITE.getIndex());
      titleStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
      List<String> titles = new ArrayList<>(Constants_Excel.EXPORT_ASSEMBLY_DISASSEMBLY_LIST_TITLE_LIST);
      List<Integer> titleSize = new ArrayList<>();
      for (String t : Constants_Excel.EXPORT_ASSEMBLY_DISASSEMBLY_LIST_TITLE_LIST) {
        titleSize.add(20);
      }
      Sheet sheet = ex.createSheet(book, "组装拆卸单", titleSize);
      Row rowTitle = sheet.createRow(0);
      ex.createTitle(titles, titleStyle, rowTitle);
      List<AsmDisOrderListVO> vos = (List<AsmDisOrderListVO>) collection;
      // 使用累加安全类
      AtomicInteger index = new AtomicInteger(1);
      long count = 0;
      for (int i = 0; i < vos.size(); i++) {
        AsmDisOrderListVO vo = vos.get(i);
        AsmDisOrderItemDTO fp = vo.getFp();
        handlerOneRow(vo, fp, baseStyle, index, sheet);
        List<AsmDisOrderItemDTO> sub = vo.getSub();
        for (AsmDisOrderItemDTO dto : sub) {
          handlerOneRow(vo, dto, baseStyle, index, sheet);
        }
        count++;
        fcMissionService.createMissionDetail(dispatchParam.getMissionId(), "第【" + count + "】行",
            StrUtil.EMPTY);
      }
      String now = String.valueOf(System.currentTimeMillis());
      String fileNewName = "【后台】组装拆卸单" + now + ".xlsx";
      // 创建临时文件
      File tempFile = File.createTempFile("excel_", ".xlsx");
      try (FileOutputStream fileOutputStream = new FileOutputStream(tempFile)) {
        // 将 XSSFWorkbook 内容写入临时文件
        book.write(fileOutputStream);
        book.dispose();
        book.close();
      }
      return ExportMissionCompleteResult.builder().successCount(count).fileName(fileNewName)
          .tempFile(tempFile).build();
    }
  }

  private void handlerOneRow(AsmDisOrderListVO vo, AsmDisOrderItemDTO item, CellStyle baseStyle,
      AtomicInteger index, Sheet sheet) {
    int col = 0;
    Row row = sheet.createRow(index.get());
    // 订单号
    ex.createCell(row, col++, vo.getCode(), baseStyle);
    // 单据类型
    ex.createCell(row, col++, vo.getTypeValue(), baseStyle);
    // 订单状态
    ex.createCell(row, col++, vo.getStatusValue(), baseStyle);
    // 序号
    String rowId = item.getRowId();
    if (AsmDisOrderItemType.FP.getCode() == item.getType()) {
      rowId = "1";
    }
    ex.createCell(row, col++, rowId, baseStyle);
    // 物料编码
    ex.createCell(row, col++, item.getProductCode(), baseStyle);
    // 品牌
    ex.createCell(row, col++, item.getBrand(), baseStyle);
    // 物料名称
    ex.createCell(row, col++, item.getProductName(), baseStyle);
    // 规格型号
    ex.createCell(row, col++, item.getModel(), baseStyle);
    // 单位
    ex.createCell(row, col++, item.getUnit(), baseStyle);
    // 数量
    String numStr = item.getNum() != null ? item.getNum().toPlainString() : StrUtil.EMPTY;
    ex.createCell(row, col++, numStr, baseStyle);
    // 仓库
    ex.createCell(row, col++, item.getWarehouseName(), baseStyle);
    // 批次
    ex.createCell(row, col++, StrUtil.emptyIfNull(item.getBatchNo()), baseStyle);
    // 创建人
    ex.createCell(row, col++, StrUtil.emptyIfNull(item.getCreateManMix()), baseStyle);
    // 创建时间
    Long createTime = item.getCreateTime();
    String createTimeStr = createTime == null ? StrUtil.EMPTY : DateUtils.formatTimeStampToNormalDateTime(createTime);
    ex.createCell(row, col++, createTimeStr, baseStyle);
    // 审核人
    ex.createCell(row, col++, StrUtil.emptyIfNull(item.getReviewer()), baseStyle);
    // 审核时间
    Long reviewTime = item.getReviewTime();
    String reviewTimeStr = reviewTime == null ? StrUtil.EMPTY : DateUtils.formatTimeStampToNormalDateTime(reviewTime);
    ex.createCell(row, col++, reviewTimeStr, baseStyle);
    // 仓库执行员
    ex.createCell(row, col++, StrUtil.emptyIfNull(item.getWarehouseOperator()), baseStyle);
    // 仓库执行时间
    Long warehouseTime = item.getWarehouseTime();
    String warehouseTimeStr = warehouseTime == null ? StrUtil.EMPTY : DateUtils.formatTimeStampToNormalDateTime(warehouseTime);
    ex.createCell(row, col++, warehouseTimeStr, baseStyle);
    // sap物料凭证号
    ex.createCell(row, col++, StrUtil.emptyIfNull(item.getProductVoucher()), baseStyle);
    // sap物料凭证号年份
    ex.createCell(row, col++, StrUtil.emptyIfNull(item.getProductVoucherYear()), baseStyle);
    index.incrementAndGet();
  }

  @Override
  public Collection<?> prepare(MissionDispatchParam dispatchParam) {
    Map<String, Object> queryMap =
        JSON.parseObject(dispatchParam.getParams(), new TypeReference<Map<String, Object>>() {});
    Page<AsmDisOrder> page = asmDisOrderDao.getPage(queryMap);
    List<AsmDisOrder> content = page.getContent();
    List<AsmDisOrderListVO> asmDisOrderListVOS = asmDisOrderFactory.buildListVos(content);
    return asmDisOrderListVOS;
  }
}
