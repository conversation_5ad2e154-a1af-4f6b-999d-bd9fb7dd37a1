package com.xhgj.srm.mission.consumer.handlers.imports.execs;
import com.xhgj.srm.common.enums.PurchaseOrderTypeEnum;
import com.xhgj.srm.jpa.entity.SupplierOrderProduct;/**
 * @since 2025/2/20 11:18
 */

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.lang.Pair;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.xhgj.srm.common.Constants;
import com.xhgj.srm.common.enums.SimpleBooleanEnum;
import com.xhgj.srm.common.enums.WarehouseEnum;
import com.xhgj.srm.common.enums.supplierorder.SupplierOrderFormType;
import com.xhgj.srm.common.enums.supplierorder.SupplierOrderState;
import com.xhgj.srm.jpa.entity.Group;
import com.xhgj.srm.jpa.entity.InventoryLocation;
import com.xhgj.srm.jpa.entity.Supplier;
import com.xhgj.srm.jpa.entity.SupplierOrder;
import com.xhgj.srm.jpa.entity.SupplierOrderDetail;
import com.xhgj.srm.jpa.entity.SupplierOrderToForm;
import com.xhgj.srm.jpa.entity.User;
import com.xhgj.srm.jpa.repository.GroupRepository;
import com.xhgj.srm.jpa.repository.InventoryLocationRepository;
import com.xhgj.srm.jpa.repository.SupplierOrderDetailRepository;
import com.xhgj.srm.jpa.repository.SupplierOrderProductRepository;
import com.xhgj.srm.jpa.repository.SupplierOrderRepository;
import com.xhgj.srm.jpa.repository.SupplierOrderToFormRepository;
import com.xhgj.srm.jpa.repository.SupplierRepository;
import com.xhgj.srm.jpa.repository.UserRepository;
import com.xhgj.srm.mission.common.MissionDispatchParam;
import com.xhgj.srm.mission.consumer.framework.MissionCompleteResult;
import com.xhgj.srm.mission.consumer.framework.service.FcMissionService;
import com.xhgj.srm.mission.consumer.handlers.ExecService;
import com.xhgj.srm.mission.consumer.handlers.imports.ImportMissionCompleteResult;
import com.xhgj.srm.mission.consumer.handlers.imports.execs.params.purchaseOrder.InitialPurchaseOrderImportDTO;
import com.xhgj.srm.mission.consumer.handlers.imports.execs.params.purchaseOrder.SrmProductInfoDTO;
import com.xhgj.srm.mission.consumer.utils.ConsistencyCheck;
import com.xhgj.srm.request.service.third.mpm.MPMService;
import com.xhgj.srm.request.utils.DownloadThenUpUtil;
import com.xhiot.boot.core.common.exception.CheckException;
import com.xhiot.boot.core.common.util.ExcelUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import javax.annotation.Resource;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR> 期初采购订单导入执行类
 */
@Component
@Slf4j
public class InitialPurchaseOrderImportExecService implements ExecService {

  private static final String LOCK_FOR_SUPPLIER_ORDER = "srm:lock_for_supplier_order:{}";
  private static final int LOCK_WAIT_TIME = 30; // 等待锁的最长时间（秒）
  private static final int LOCK_LEASE_TIME = 120; // 锁自动释放时间（秒）
  private static final Map<String, String> INVOICE_TYPE_INPUT = MapUtil.of(
      new Pair<>("1", "增值税专用发票"),
      new Pair<>("2", "普通发票"),
      new Pair<>("3", "其他")
  );
  private static final Map<String, String> PROJECT_TYPE;
  private static final List<String> TAX_INPUT = CollUtil.toList(
      "0.01",
      "0",
      "0.03",
      "0.06",
      "0.09",
      "0.1",
      "0.11",
      "0.12",
      "0.13",
      "0.16",
      "0.17"
  );

  static {
    Map<String, String> map = new HashMap<>();
    map.put("", "标准");
    map.put("K", "寄售");
    PROJECT_TYPE = Collections.unmodifiableMap(map);
  }
  /**
   * 特殊物料编码
   */
  private static final List<String> SPECIAL_PRODUCT_CODE = CollUtil.toList("F9999999998");

  /**
   * 特殊物料赋值
   */
  private static final String SPECIAL_PRODUCT_DESC = "期初应付余额用物料";

  @Resource
  DownloadThenUpUtil downloadThenUpUtil;
  @Resource
  FcMissionService fcMissionService;
  @Resource
  GroupRepository groupRepository;
  @Resource
  SupplierRepository supplierRepository;
  @Resource
  UserRepository userRepository;
  @Resource
  SupplierOrderRepository supplierOrderRepository;
  @Resource
  private RedissonClient redissonClient;
  @Resource
  private SupplierOrderToFormRepository supplierOrderToFormRepository;
  @Resource
  private MPMService mpmService;
  @Resource
  private SupplierOrderProductRepository supplierOrderProductRepository;
  @Resource
  private InventoryLocationRepository inventoryLocationRepository;
  @Resource
  private SupplierOrderDetailRepository supplierOrderDetailRepository;

  @Resource
  private ApplicationContext applicationContext;

  /**
   * #check 校验物料信息
   * @param list
   */
  private Map<String, SrmProductInfoDTO> checkProduct(List<InitialPurchaseOrderImportDTO> list, String groupCode) {
    // 过滤掉失败的
    list = list.stream().filter(dto -> !Boolean.TRUE.equals(dto.getIsFailed()))
        .collect(Collectors.toList());
    if (CollUtil.isEmpty(list)) {
      return new HashMap<>();
    }
    // 获取物料编码
    List<String> productCodes = list.stream()
        .map(InitialPurchaseOrderImportDTO::getProductCode)
        .filter(item -> !SPECIAL_PRODUCT_CODE.contains(item))
        .distinct()
        .collect(Collectors.toList());

    List<SrmProductInfoDTO> srmProductInfoDTOList =
        mpmService.getProductInfoListByCodes(productCodes, groupCode,
            new TypeReference<List<SrmProductInfoDTO>>() {});
    Map<String, SrmProductInfoDTO> srmProductInfoDTOMap = srmProductInfoDTOList.stream().collect(
        Collectors.toMap(SrmProductInfoDTO::getCode, item -> item,
            (oldValue, newValue) -> oldValue));
    for (InitialPurchaseOrderImportDTO dto : list) {
      // 物料编码
      String productCode = dto.getProductCode();
      if (SPECIAL_PRODUCT_CODE.contains(productCode)) {
        continue;
      }
      SrmProductInfoDTO srmProductInfoDTO = srmProductInfoDTOMap.get(productCode);
      if (srmProductInfoDTO == null) {
        dto.setIsFailed(true);
        dto.setFailedReason("物料编码不存在");
      }
    }
    return srmProductInfoDTOMap;
  }

  /**
   * #check 单行数据校验
   * @param dto
   */
  private void check(InitialPurchaseOrderImportDTO dto) {
    // 必填校验
    if (StrUtil.isBlank(dto.getCode())) {
      throw new CheckException("订单号不能为空");
    }
    // 订单创建时间
    if (StrUtil.isBlank(dto.getCreateTimeStr())) {
      throw new CheckException("订单创建时间不能为空");
    }
    // 创建时间时间戳
    if (dto.getCreateTime() == null) {
      throw new CheckException("订单创建时间格式不正确");
    }
    // 采购订单类型
    if (StrUtil.isBlank(dto.getOrderTypeStr())) {
      throw new CheckException("采购订单类型不能为空");
    }
    // 采购订单类型
    if (StrUtil.isBlank(dto.getOrderType())) {
      throw new CheckException("采购订单类型输入错误");
    }
    if (!PurchaseOrderTypeEnum.INITIAL_PURCHASE.getKey().equals(dto.getOrderType())) {
      throw new CheckException("采购订单类型输入错误");
    }
    // 采购组织编码
    if (StrUtil.isBlank(dto.getGroupCode())) {
      throw new CheckException("采购组织编码不能为空");
    }
    // 采购部门编码
    if (StrUtil.isBlank(dto.getDepartmentCode())) {
      throw new CheckException("采购部门编码不能为空");
    }
    // 采购员
    if (StrUtil.isBlank(dto.getPurchaser())) {
      throw new CheckException("采购员不能为空");
    }
    // 供应商主数据编码
    if (StrUtil.isBlank(dto.getMdmCode())) {
      throw new CheckException("供应商主数据编码不能为空");
    }
    // 开票方主数据编码
    if (StrUtil.isBlank(dto.getInvoiceMdmCode())) {
      throw new CheckException("开票方主数据编码不能为空");
    }
    // 货币码
    if (StrUtil.isBlank(dto.getCurrency())) {
      throw new CheckException("货币码不正确");
    }
    // 货币码
    if (StrUtil.isBlank(dto.getCurrencyCode())) {
      throw new CheckException("货币码不能为空");
    }
    // 发票类型
    if (StrUtil.isBlank(dto.getInvoiceType())) {
      throw new CheckException("发票类型不能为空");
    }
    // 发票类型code
    boolean invoiceTypeFlag = INVOICE_TYPE_INPUT.containsValue(dto.getInvoiceType());
    if (!invoiceTypeFlag) {
      throw new CheckException("发票类型不正确");
    }
    // 物料编码
    if (StrUtil.isBlank(dto.getProductCode())) {
      throw new CheckException("物料编码不能为空");
    }
    // 物料名称
    if (StrUtil.isBlank(dto.getProductName())) {
      throw new CheckException("物料名称不能为空");
    }
    // 物料单位
    if (StrUtil.isBlank(dto.getProductUnit())) {
      throw new CheckException("物料单位不能为空");
    }
    // 仓库编码
    if (StrUtil.isBlank(dto.getWarehouseCode())) {
      throw new CheckException("仓库编码不能为空");
    }
    // 含税单价
    if (StrUtil.isBlank(dto.getTaxPrice())) {
      throw new CheckException("含税单价不能为空");
    }
    // 税率
    if (StrUtil.isBlank(dto.getTaxRate())) {
      throw new CheckException("税率不能为空");
    }
    // 填写 1%、0%、3%、6%、9%、10%、11%、12%、13%、16%、17%
    boolean taxRateFlag = TAX_INPUT.contains(dto.getTaxRate());
    if (!taxRateFlag) {
      throw new CheckException("税率输入错误");
    }
    // 是否免费
    if (StrUtil.isBlank(dto.getIsFree())) {
      throw new CheckException("是否免费不能为空");
    }
    // 是否免费校验
    String match = SimpleBooleanEnum.getKeyFromValue(dto.getIsFree());
    if (StrUtil.isBlank(match)) {
      throw new CheckException("是否免费填写不正确");
    }
    // 项目类别
    if (StrUtil.isBlank(dto.getProjectType())) {
      throw new CheckException("项目类别不能为空");
    }
    // 项目类别校验
    boolean projectTypeFlag = PROJECT_TYPE.containsValue(dto.getProjectType());
    if (!projectTypeFlag) {
      throw new CheckException("项目类别填写不正确");
    }
    // 是否金蝶已入库
    if (StrUtil.isBlank(dto.getJinDieInStock())) {
      throw new CheckException("是否金蝶已入库不能为空");
    }
    // 是否金蝶已入库校验
    String match1 = SimpleBooleanEnum.getKeyFromValue(dto.getJinDieInStock());
    if (StrUtil.isBlank(match1)) {
      throw new CheckException("是否金蝶已入库填写不正确");
    }
  }

  /**
   * # check 分组数据一致性校验
   */
  private void consistencyCheck(List<InitialPurchaseOrderImportDTO> list) {
    List<Function<InitialPurchaseOrderImportDTO, Object>> extractors = CollUtil.toList(
        InitialPurchaseOrderImportDTO::getCode,
        InitialPurchaseOrderImportDTO::getCreateTimeStr,
        InitialPurchaseOrderImportDTO::getOrderTypeStr,
        InitialPurchaseOrderImportDTO::getGroupCode,
        InitialPurchaseOrderImportDTO::getDepartmentCode,
        InitialPurchaseOrderImportDTO::getPurchaser,
        InitialPurchaseOrderImportDTO::getMdmCode,
        InitialPurchaseOrderImportDTO::getInvoiceMdmCode,
        InitialPurchaseOrderImportDTO::getCurrency,
        InitialPurchaseOrderImportDTO::getInvoiceType,
        InitialPurchaseOrderImportDTO::getSupplierContact,
        InitialPurchaseOrderImportDTO::getSupplierContactPhone,
        InitialPurchaseOrderImportDTO::getSupplierContactEmail,
        InitialPurchaseOrderImportDTO::getSupplierContactFax,
        InitialPurchaseOrderImportDTO::getRecipientName,
        InitialPurchaseOrderImportDTO::getRecipientPhone,
        InitialPurchaseOrderImportDTO::getRecipientAddress,
        InitialPurchaseOrderImportDTO::getOrderAmount
    );
    ConsistencyCheck.validateFieldsConsistencyByMD5(list, extractors, "数据不一致");
  }

  /**
   * 执行操作
   * @param collection
   * @param dispatchParam
   * @return
   */
  @Override
  public MissionCompleteResult exec(Collection<?> collection, MissionDispatchParam dispatchParam) {
    // 转换为JSONObject
    JSONObject jsonObject = JSONObject.parseObject(dispatchParam.getParams());
    // 获取filePath
    String filePath = jsonObject.getString("filePath");
    // 获取fileName
    String fileName = jsonObject.getString("fileName");
    // collection转换为SupplierImportParam
    List<InitialPurchaseOrderImportDTO> initialPurchaseOrderImportDTOS = (List<InitialPurchaseOrderImportDTO>) collection;
    int rowIndex = 1;
    String currentCode = null;
    AtomicInteger successCount = new AtomicInteger(0);
    List<InitialPurchaseOrderImportDTO> groupList = new ArrayList<>();
    for (InitialPurchaseOrderImportDTO dto : initialPurchaseOrderImportDTOS) {
      dto.setRowIndex(rowIndex++);
      // 如果是新的code组
      if (currentCode == null || !currentCode.equals(dto.getCode())) {
        // 保存上一组数据(如果存在)
        if (!groupList.isEmpty()) {
          this.saveOneOrder(groupList, dispatchParam.getMissionId(), successCount);
          groupList.clear();
        }
        currentCode = dto.getCode();
      }
      groupList.add(dto);
    }
    // 保存最后一组数据
    if (!groupList.isEmpty()) {
      this.saveOneOrder(groupList, dispatchParam.getMissionId(), successCount);
    }
    return ImportMissionCompleteResult.builder()
        .successCount(successCount.get())
        .fileName(fileName)
        .filePath(filePath)
        .build();
  }

  /**
   * 准备数据
   * @param dispatchParam
   * @return
   */
  @Override
  public Collection<?> prepare(MissionDispatchParam dispatchParam) {
    // 转换为JSONObject
    JSONObject jsonObject = JSONObject.parseObject(dispatchParam.getParams());
    // 获取filePath
    String filePath = jsonObject.getString("filePath");
    // 获取fileName
    String fileName = jsonObject.getString("fileName");
    // oss下载文件
    List<InitialPurchaseOrderImportDTO> result = new ArrayList<>();
    try ( InputStream inputStream = downloadThenUpUtil.getInputStreamFromOSS(filePath);
        Workbook book = ExcelUtil.buildByFile(fileName, inputStream);) {
      if (book == null) {
        throw new CheckException("文件为空");
      }
      // 获取sheet
      Sheet sheet = book.getSheetAt(0);
      // 获取总行数
      int lastRowNum = sheet.getPhysicalNumberOfRows();
      // 如果总行数小于1则抛出异常
      if (lastRowNum < 1) {
        throw new CheckException("导入数据为空");
      }
      // 从第四行开始读取
      int startRow = 3;
      // 读取数据
      for (int i = startRow; i <= lastRowNum; i++) {
        // 获取当前行
        Row row = sheet.getRow(i);
        if (row == null) {
          continue;
        }
        int cellNum = 0;
        // 获取固定列
        // 订单号
        String code = ExcelUtil.getCellStringValue(row.getCell(cellNum++));
        // 订单创建时间
        String createTimeStr = ExcelUtil.getCellStringValue(row.getCell(cellNum++));
        // 采购订单类型
        String orderTypeStr = ExcelUtil.getCellStringValue(row.getCell(cellNum++));
        // 采购组织编码
        String groupCode = ExcelUtil.getCellStringValue(row.getCell(cellNum++));
        // 采购部门编码
        String departmentCode = ExcelUtil.getCellStringValue(row.getCell(cellNum++));
        // 采购员
        String purchaser = ExcelUtil.getCellStringValue(row.getCell(cellNum++));
        // 供应商主数据编码
        String mdmCode = ExcelUtil.getCellStringValue(row.getCell(cellNum++));
        // 开票方主数据编码
        String invoiceMdmCode = ExcelUtil.getCellStringValue(row.getCell(cellNum++));
        // 货币码
        String currency = ExcelUtil.getCellStringValue(row.getCell(cellNum++));
        // 发票类型
        String invoiceType = ExcelUtil.getCellStringValue(row.getCell(cellNum++));
        // 订单备注
        String orderRemark = ExcelUtil.getCellStringValue(row.getCell(cellNum++));
        // 供方联系人
        String supplierContact = ExcelUtil.getCellStringValue(row.getCell(cellNum++));
        // 供方联系人电话
        String supplierContactPhone = ExcelUtil.getCellStringValue(row.getCell(cellNum++));
        // 供方联系人邮箱
        String supplierContactEmail = ExcelUtil.getCellStringValue(row.getCell(cellNum++));
        // 供方联系人传真
        String supplierContactFax = ExcelUtil.getCellStringValue(row.getCell(cellNum++));
        // 收件信息 收件人
        String recipientName = ExcelUtil.getCellStringValue(row.getCell(cellNum++));
        // 收件信息 收件人电话
        String recipientPhone = ExcelUtil.getCellStringValue(row.getCell(cellNum++));
        // 收件信息 收件人地址
        String recipientAddress = ExcelUtil.getCellStringValue(row.getCell(cellNum++));
        // 订货金额
        String orderAmount = ExcelUtil.getCellStringValue(row.getCell(cellNum++));
        // 已开票数量
        String invoiceQty = ExcelUtil.getCellStringValue(row.getCell(cellNum++));
        // 物料编码
        String productCode = ExcelUtil.getCellStringValue(row.getCell(cellNum++));
        // 物料名称
        String productName = ExcelUtil.getCellStringValue(row.getCell(cellNum++));
        // 物料单位
        String productUnit = ExcelUtil.getCellStringValue(row.getCell(cellNum++));
        // 订货数量
        String orderQty = ExcelUtil.getCellStringValue(row.getCell(cellNum++));
        // 仓库编码
        String warehouseCode = ExcelUtil.getCellStringValue(row.getCell(cellNum++));
        // 含税单价
        String taxPrice = ExcelUtil.getCellStringValue(row.getCell(cellNum++));
        // 税率
        String taxRate = ExcelUtil.getCellStringValue(row.getCell(cellNum++));
        // 结算单价
        String settlementPrice = ExcelUtil.getCellStringValue(row.getCell(cellNum++));
        // 约定交货日期
        String deliveryDateStr = ExcelUtil.getCellStringValue(row.getCell(cellNum++));
        // 实际交货日期
        String actualDeliveryDateStr = ExcelUtil.getCellStringValue(row.getCell(cellNum++));
        // 待发数量
        String pendingQty = ExcelUtil.getCellStringValue(row.getCell(cellNum++));
        // 已发数量
        String deliveredQty = ExcelUtil.getCellStringValue(row.getCell(cellNum++));
        // 取消订货数量
        String cancelOrderQty = ExcelUtil.getCellStringValue(row.getCell(cellNum++));
        // 入库数量
        String inStockQty = ExcelUtil.getCellStringValue(row.getCell(cellNum++));
        // 退库数量
        String returnStockQty = ExcelUtil.getCellStringValue(row.getCell(cellNum++));
        // 剩余入库数量
        String remainingInStockQty = ExcelUtil.getCellStringValue(row.getCell(cellNum++));
        // 实际交货数量
        String actualDeliveryQty = ExcelUtil.getCellStringValue(row.getCell(cellNum++));
        // 备注
        String remark = ExcelUtil.getCellStringValue(row.getCell(cellNum++));
        // 是否免费
        String isFree = ExcelUtil.getCellStringValue(row.getCell(cellNum++));
        // 项目类型
        String projectType = ExcelUtil.getCellStringValue(row.getCell(cellNum++));
        // 销售订单号
        String salesOrderNo = ExcelUtil.getCellStringValue(row.getCell(cellNum++));
        // 销售订单行id
        String salesOrderLineId = ExcelUtil.getCellStringValue(row.getCell(cellNum++));
        // 是否金蝶已入库
        String jinDieInStock = ExcelUtil.getCellStringValue(row.getCell(cellNum++));
        InitialPurchaseOrderImportDTO dto = InitialPurchaseOrderImportDTO
            .builder()
            .code(code)
            .createTimeStr(createTimeStr)
            .orderTypeStr(orderTypeStr)
            .groupCode(groupCode)
            .departmentCode(departmentCode)
            .purchaser(purchaser)
            .mdmCode(mdmCode)
            .invoiceMdmCode(invoiceMdmCode)
            .currency(currency)
            .invoiceType(invoiceType)
            .orderRemark(orderRemark)
            .supplierContact(supplierContact)
            .supplierContactPhone(supplierContactPhone)
            .supplierContactEmail(supplierContactEmail)
            .supplierContactFax(supplierContactFax)
            .recipientName(recipientName)
            .recipientPhone(recipientPhone)
            .recipientAddress(recipientAddress)
            .orderAmount(orderAmount)
            .invoiceQty(invoiceQty)
            .productCode(productCode)
            .productName(productName)
            .productUnit(productUnit)
            .orderQty(orderQty)
            .warehouseCode(warehouseCode)
            .taxPrice(taxPrice)
            .taxRate(taxRate)
            .settlementPrice(settlementPrice)
            .deliveryDateStr(deliveryDateStr)
            .actualDeliveryDateStr(actualDeliveryDateStr)
            .pendingQty(pendingQty)
            .deliveredQty(deliveredQty)
            .cancelOrderQty(cancelOrderQty)
            .inStockQty(inStockQty)
            .outStockQty(returnStockQty)
            .remainingInStockQty(remainingInStockQty)
            .actualDeliveryQty(actualDeliveryQty)
            .remark(remark)
            .isFree(isFree)
            .projectType(projectType)
            .salesOrderNo(salesOrderNo)
            .salesOrderLineId(salesOrderLineId)
            .jinDieInStock(jinDieInStock)
            .build();
        result.add(dto);
      }
    } catch (IOException e) {
      throw new RuntimeException(e);
    }
    if (CollUtil.isEmpty(result)) {
      throw new CheckException("导入数据为空");
    }
    return result;
  }

  /**
   * 保存分组数据
   * @param list
   */
  public void saveOneOrder(List<InitialPurchaseOrderImportDTO> list,
      String missionId, AtomicInteger successCount) {
    // 获取锁，防止同一时间多个线程操作同一数据
    RLock lock =
        redissonClient.getLock(StrUtil.format(LOCK_FOR_SUPPLIER_ORDER, list.get(0).getCode()));
    try {
      boolean locked = lock.tryLock(LOCK_WAIT_TIME, LOCK_LEASE_TIME, TimeUnit.SECONDS);
      try {
        InitialPurchaseOrderImportExecService proxy = applicationContext.getBean(InitialPurchaseOrderImportExecService.class);
        // 保存采购订单
        proxy.saveOneOrderTransactional(list);
      } catch (Exception e) {
        // 处理异常
        log.error("导入采购订单历史订单失败,第【{}】行数据写入失败,异常信息为:", list.get(0).getRowIndex(), e);
        allFail(list, e.getMessage());
      }
      // 创建采购订单入库单
      saveLogs(list, missionId, successCount);
    } catch (Exception e) {
      log.error("导入采购订单历史订单失败,第【}】行数据写入失败,异常信息为:", list.get(0).getRowIndex(), e);
      allFail(list, "导入采购订单历史订单失败");
      saveLogs(list, missionId, successCount);
    } finally {
      if (lock.isHeldByCurrentThread()) {
        lock.unlock();
      }
    }
  }

  /**
   * 保存采购订单等数据，开启事务，有异常则回退处理
   * @param list
   */
  @Transactional(rollbackFor = Exception.class)
  public void saveOneOrderTransactional(List<InitialPurchaseOrderImportDTO> list) {
    // 订单状态
    AtomicReference<String> orderState = new AtomicReference<>(SupplierOrderState.WAIT.getKey());
    // 0.校验
    // #check 分组数据一致性校验
    this.consistencyCheck(list);
    // 分行数据校验
    for (InitialPurchaseOrderImportDTO dto : list) {
      try {
        // #check 单行数据基础校验
        this.check(dto);
      } catch (Exception e) {
        dto.setIsFailed(true);
        dto.setFailedReason(e.getMessage());
      }
    }
    // #check 仓库校验
    InventoryLocation location = inventoryLocationRepository.findFirstByGroupCodeAndWarehouseAndState(
                list.get(0).getGroupCode(), list.get(0).getWarehouseCode(), Constants.STATE_OK)
            .orElse(null);
    if (location == null) {
      throw new CheckException("仓库不存在");
    }
    // #check 物料信息校验
    Map<String, SrmProductInfoDTO> productInfoDTOMap = this.checkProduct(list, list.get(0).getGroupCode());
    // 1.创建采购订单
    SupplierOrder supplierOrder = this.createSupplierOrder(list, orderState);
    if (supplierOrder == null) {
      throw new CheckException("采购订单创建失败");
    }
    supplierOrderRepository.saveAndFlush(supplierOrder);
    // 2.创建采购订单详情form
    SupplierOrderToForm supplierOrderDetailForm = this.createSupplierOrderDetailForm(list, supplierOrder);
    if (supplierOrderDetailForm == null) {
      throw new CheckException("采购订单详情form创建失败");
    }
    supplierOrderToFormRepository.saveAndFlush(supplierOrderDetailForm);
    // 3.创建采购订单物料列表
    List<SupplierOrderProduct> supplierOrderProducts = this.createSupplierOrderProduct(list,
        productInfoDTOMap);
    if (CollUtil.isEmpty(supplierOrderProducts)) {
      throw new CheckException("采购订单详情物料创建失败");
    }
    supplierOrderProductRepository.saveAll(supplierOrderProducts);
    supplierOrderProductRepository.flush();
    // 4.创建采购订单详情
    List<SupplierOrderDetail> supplierOrderDetails =
        this.createSupplierOrderDetail(list, productInfoDTOMap, supplierOrderDetailForm,
            supplierOrderProducts);
    if (CollUtil.isEmpty(supplierOrderDetails)) {
      throw new CheckException("采购订单详情创建失败");
    }
    supplierOrderDetailRepository.saveAll(supplierOrderDetails);
    supplierOrderDetailRepository.flush();

    // 判断是否已经完全入库，是则修改为已完成
    supplierOrder.setOrderState(orderState.get());
    supplierOrderRepository.saveAndFlush(supplierOrder);
    // 自动创建入库单
    // 5.创建采购订单入库单form
    SupplierOrderToForm supplierOrderInForm = this.createSupplierOrderInForm(list, supplierOrder);
    if (supplierOrderInForm == null) {
      return;
    }
    supplierOrderToFormRepository.saveAndFlush(supplierOrderInForm);
    // 6.创建采购入库单订单详情
    List<SupplierOrderDetail> supplierOrderInDetails = this.createSupplierOrderInDetail(list, supplierOrderInForm, supplierOrderProducts, supplierOrderDetails);
    supplierOrderDetailRepository.saveAll(supplierOrderInDetails);
    supplierOrderDetailRepository.flush();
  }

  /**
   * 1.创建采购订单
   * @param list
   * @return
   */
  private SupplierOrder createSupplierOrder(List<InitialPurchaseOrderImportDTO> originList,
      AtomicReference<String> orderState) {
    // 过滤掉失败的
    List<InitialPurchaseOrderImportDTO> list = originList.stream()
        .filter(dto -> !Boolean.TRUE.equals(dto.getIsFailed()))
        .collect(Collectors.toList());
    if (CollUtil.isEmpty(list)) {
      return null;
    }
    // 判断 initialPurchaseOrderImportDTOS的数据一致性
    // 获取第一条数据的code
    String code = list.get(0).getCode();
    SupplierOrder findSupplierOrder =
        supplierOrderRepository.findFirstByCodeAndState(code, Constants.STATE_OK);
    if (findSupplierOrder != null) {
      allFail(list, "订单号已存在，可能存在脏数据");
      return null;
    }
    // 获取第一条数据的采购组织编码
    String groupCode = list.get(0).getGroupCode();
    Group group = groupRepository.findFirstByErpCodeAndState(groupCode, Constants.STATE_OK);
    if (group == null) {
      allFail(list, "未找到采购组织，可能存在脏数据");
      return null;
    }
    // 获取第一条数据的采购部门编码
    String departmentCode = list.get(0).getDepartmentCode();
    Group dept = groupRepository.findFirstByErpCodeAndState(departmentCode, Constants.STATE_OK);
    if (dept == null) {
      allFail(list, "未找到采购部门，可能存在脏数据");
      return null;
    }
    // 获取第一条数据的采购员
    String purchaser = list.get(0).getPurchaser();
    // 假设工号是“xhgj002672”，提取数字部分
    String numericPart = purchaser.replaceAll("[^0-9]", ""); // 提取纯数字部分
    if (StrUtil.isBlank(numericPart)) {
      allFail(list, "未找到人员信息，可能存在脏数据");
      return null;
    }
    // 动态补充工号，确保长度符合要求
    String jobNumber = String.format("xhgj%06d", Integer.parseInt(numericPart)); // 根据需要调整填充长度
    String name = purchaser.replaceAll("[0-9]", ""); // 数字部分去除
    if (StrUtil.isBlank(name)) {
      allFail(list, "未找到人员信息，可能存在脏数据");
      return null;
    }
    User user = userRepository.findFirstByCodeAndState(jobNumber, Constants.STATE_OK).orElse(null);
    if (user == null) {
      allFail(list, "未找到人员信息，可能存在脏数据");
      return null;
    }
    // 获取第一条数据的供应商主数据编码
    String mdmCode = list.get(0).getMdmCode();
    Supplier supplier = supplierRepository.getFirstByMdmCodeAndState(mdmCode, Constants.STATE_OK);
    if (supplier == null) {
      allFail(list, "未找到供应商信息，可能存在脏数据");
      return null;
    }
    // 获取第一条数据的开票方主数据编码
    String invoiceMdmCode = list.get(0).getInvoiceMdmCode();
    Supplier invoiceSupplier =
        supplierRepository.getFirstByMdmCodeAndState(invoiceMdmCode, Constants.STATE_OK);
    if (invoiceSupplier == null) {
      allFail(list, "未找到开票方信息，可能存在脏数据");
      return null;
    }
    // 获取第一条数据的货币码
    String currencyCode = list.get(0).getCurrencyCode();
    // 获取第一条数据的发票类型
    String invoiceType = list.get(0).getInvoiceType();
    Set<Entry<String, String>> entries = INVOICE_TYPE_INPUT.entrySet();
    String invoiceTypeCode =
        entries.stream().filter(e -> e.getValue().equals(invoiceType)).findFirst()
            .map(Entry::getKey).orElse(null);
    // 获取第一条数据的供方联系人
    String supplierContact = list.get(0).getSupplierContact();
    // 获取第一条数据的供方联系人电话
    String supplierContactPhone = list.get(0).getSupplierContactPhone();
    // 获取第一条数据的供方联系人邮箱
    String supplierContactEmail = list.get(0).getSupplierContactEmail();
    // 获取第一条数据的供方联系人传真
    String supplierContactFax = list.get(0).getSupplierContactFax();
    // 获取第一条数据的收件人
    String recipientName = list.get(0).getRecipientName();
    // 获取第一条数据的收件人电话
    String recipientPhone = list.get(0).getRecipientPhone();
    // 获取第一条数据的收件人地址
    String recipientAddress = list.get(0).getRecipientAddress();
    // 获取第一条数据的订货金额
    String orderAmount = list.get(0).getOrderAmount();
    // 获取第一条数据的已开票数量
    String invoiceQty = list.get(0).getInvoiceQty();
    // 获取第一条创建时间
    Long createTime = list.get(0).getCreateTime();
    // 获取第一条仓库编码
    String warehouseCode = list.get(0).getWarehouseCode();
    SupplierOrder supplierOrder = new SupplierOrder();
    supplierOrder.setPrice(Convert.toBigDecimal(orderAmount));
    supplierOrder.setSupplierId(supplier.getId());
    supplierOrder.setSupplierName(supplier.getEnterpriseName());
    supplierOrder.setCode(code);
    //    supplierOrder.setErpId();
    supplierOrder.setOrderCreateTime(createTime);
    supplierOrder.setCreateTime(System.currentTimeMillis());
    supplierOrder.setGroupCode(group.getErpCode());
    supplierOrder.setGroupName(group.getName());
    supplierOrder.setPurchaseTime(System.currentTimeMillis());
    supplierOrder.setUpdateTime(System.currentTimeMillis());
    Boolean direct = WarehouseEnum.HAI_NING_DIRECT_SALES.getCode().equals(warehouseCode);
    supplierOrder.setDirectShipment(direct);
    supplierOrder.setReceiveMobile(recipientPhone);
    supplierOrder.setReceiveMan(recipientName);
    supplierOrder.setPurchaseMan(purchaser);
    supplierOrder.setPurchaseCode(user.getCode());
    supplierOrder.setPurchaseId(user.getId());
    supplierOrder.setReceiveAddress(recipientAddress);
    supplierOrder.setOrderConfirmState(false);
    supplierOrder.setOrderCancelState(false);
    supplierOrder.setOrderReturnState(false);
    supplierOrder.setOrderShipWaitStockState(false);
    BigDecimal totalStockInputQty = list.stream()
        .map(item -> Convert.toBigDecimal(item.getInStockQty()))
        .filter(Objects::nonNull)
        .reduce(BigDecimal.ZERO, BigDecimal::add);
    supplierOrder.setTotalStockInputQty(totalStockInputQty);
    BigDecimal totalNum = list.stream()
        .map(item -> Convert.toBigDecimal(item.getOrderQty()))
        .filter(Objects::nonNull)
        .reduce(BigDecimal.ZERO, BigDecimal::add);
    supplierOrder.setTotalNum(totalNum);
    // 总入库数量/ 总数量 - 取消数量
    BigDecimal cancelQty = list.stream()
        .map(item -> Convert.toBigDecimal(item.getCancelOrderQty()))
        .filter(Objects::nonNull)
        .reduce(BigDecimal.ZERO, BigDecimal::add);
    supplierOrder.setStockProgress(StrUtil.format("{}/{}", totalStockInputQty, totalNum.subtract(cancelQty)));
    supplierOrder.setOrderState(SupplierOrderState.WAIT.getOrderState());
    // 判断是否已经完全入库，是则修改为已完成
    if (totalStockInputQty.compareTo(totalNum.subtract(cancelQty)) == 0) {
      orderState.set(SupplierOrderState.COMPLETE.getKey());
    } else if (totalStockInputQty.compareTo(BigDecimal.ZERO) == 0) {
      orderState.set(SupplierOrderState.WAIT.getKey());
    } else if (totalStockInputQty.compareTo(BigDecimal.ZERO) > 0) {
      orderState.set(SupplierOrderState.IN_PROGRESS.getKey());
    }
    // 需要计算
    BigDecimal totalPrice = list.stream()
        .map(item -> {
          // 入库数量
          BigDecimal inStockQty = Optional.ofNullable(Convert.toBigDecimal(item.getInStockQty()))
              .orElse(BigDecimal.ZERO);
          // 金额
          BigDecimal taxPrice =
              Optional.ofNullable(Convert.toBigDecimal(item.getTaxPrice())).orElse(BigDecimal.ZERO).setScale(6, RoundingMode.HALF_UP);
          // 入库数量 * 金额
          return inStockQty.multiply(taxPrice);
        })
        .reduce(BigDecimal.ZERO, BigDecimal::add);
    supplierOrder.setFinalPrice(totalPrice);
    BigDecimal cancelReturnPrice = list.stream()
        .map(item -> {
          // 入库数量
          BigDecimal cancelQty1 =
              Optional.ofNullable(Convert.toBigDecimal(item.getCancelOrderQty()))
                  .orElse(BigDecimal.ZERO);
          BigDecimal returnQty = Optional.ofNullable(Convert.toBigDecimal(item.getOutStockQty()))
              .orElse(BigDecimal.ZERO);
          // 金额
          BigDecimal taxPrice =
              Optional.ofNullable(Convert.toBigDecimal(item.getTaxPrice())).orElse(BigDecimal.ZERO).setScale(6, RoundingMode.HALF_UP);
          // 入库数量 * 金额
          return cancelQty1.add(returnQty).multiply(taxPrice);
        }).reduce(BigDecimal.ZERO, BigDecimal::add);

    supplierOrder.setCancelReturnPrice(cancelReturnPrice);
    supplierOrder.setState(Constants.STATE_OK);
    //    supplierOrder.setPaymentKey();
    //    supplierOrder.setPaymentValue();
    supplierOrder.setConfirmTime(null);
    supplierOrder.setFirstShipTime(null);
    supplierOrder.setCompleteShipTime(null);
    supplierOrder.setCompleteOrderTime(null);
    supplierOrder.setSendReminderCount(null);
    supplierOrder.setCustomerOrderCode(null);
    supplierOrder.setRefuseState(null);
    supplierOrder.setRefuseReason(null);
    supplierOrder.setRefuseOperatorId(null);
    supplierOrder.setSupplierOpenInvoiceState(Constants.ORDER_INVOICE_STATE_NOT_DONE);
    supplierOrder.setOrderInvoiceRelationId(null);
    supplierOrder.setConfirmOpenInvoiceTime(null);
    supplierOrder.setOrderType(list.get(0).getOrderType());
    supplierOrder.setPurchaseDept(dept.getName());
    supplierOrder.setPurchaseDeptCode(dept.getErpCode());
    supplierOrder.setInvoicingParty(invoiceSupplier.getEnterpriseName());
    supplierOrder.setMoneyCode(currencyCode);
    supplierOrder.setOrderRate(null);
    supplierOrder.setPayCondition(null);
    supplierOrder.setAccountPeriod(null);
    supplierOrder.setCustomerInvoicingState(null);
    supplierOrder.setCustomerPaymentCollectionState(null);
    supplierOrder.setRejectReason(null);
    supplierOrder.setCreateMan(user.getId());
    supplierOrder.setUpdateMan(user.getId());
    supplierOrder.setSupContacts(supplierContact);
    supplierOrder.setSupMobile(supplierContactPhone);
    supplierOrder.setSupEmail(supplierContactEmail);
    supplierOrder.setSupFax(supplierContactFax);
    supplierOrder.setProjectName(null);
    supplierOrder.setProjectNo(null);
    supplierOrder.setSaleOrderNo(null);
    supplierOrder.setSoldToParty(null);
    supplierOrder.setSalesman(null);
    supplierOrder.setInvoiceType(invoiceTypeCode);
    supplierOrder.setFreight(BigDecimal.ZERO);
    supplierOrder.setFreeState(null);
    supplierOrder.setSelfState(false);
    supplierOrder.setAuditTime(null);
    supplierOrder.setPaymentTermsStr(null);
    supplierOrder.setLoss(null);
    supplierOrder.setCauseOfLoss(null);
    supplierOrder.setScp(false);
    supplierOrder.setConsignmentToOwnedCode(null);
    supplierOrder.setInternalRemark(null);
    supplierOrder.setInternalRemarkCreator(null);
    supplierOrder.setInternalRemarkUpdateTime(null);
    supplierOrder.setMark(list.get(0).getOrderRemark());
    supplierOrder.setOrderRate(BigDecimal.ONE.toPlainString());
    return supplierOrder;
  }


  /**
   * 2.创建采购订单详情form
   */
  private SupplierOrderToForm createSupplierOrderDetailForm(List<InitialPurchaseOrderImportDTO> originList
      , SupplierOrder supplierOrder) {
    // 过滤掉失败的
    List<InitialPurchaseOrderImportDTO> list =
        originList.stream().filter(dto -> !Boolean.TRUE.equals(dto.getIsFailed()))
        .collect(Collectors.toList());
    if (CollUtil.isEmpty(list)) {
      return null;
    }
    BigDecimal num = list.stream().map(item -> Convert.toBigDecimal(item.getOrderQty()))
        .filter(Objects::nonNull)
        .reduce(BigDecimal.ZERO, BigDecimal::add);
    SupplierOrderToForm supplierOrderToForm = new SupplierOrderToForm();
    supplierOrderToForm.setSupplierOrderId(supplierOrder.getId());
    supplierOrderToForm.setType(SupplierOrderFormType.DETAILED.getType());
    supplierOrderToForm.setCreateTime(System.currentTimeMillis());
    supplierOrderToForm.setState(Constants.STATE_OK);
    supplierOrderToForm.setUpdateTime(System.currentTimeMillis());
    supplierOrderToForm.setReturnPrice(BigDecimal.ZERO);
    supplierOrderToForm.setNum(num);
    supplierOrderToForm.setStockOutput(null);
    supplierOrderToForm.setReturnStock(null);
    supplierOrderToForm.setBatchNumber(null);
    supplierOrderToForm.setReturnId(null);
    supplierOrderToForm.setWarehousing(null);
    supplierOrderToForm.setRemark(null);
    supplierOrderToForm.setNoticeReceipt(null);
    supplierOrderToForm.setSendSms(null);
    supplierOrderToForm.setLogisticsInformation(null);
    supplierOrderToForm.setProductVoucher(null);
    supplierOrderToForm.setProductVoucherYear(null);
    supplierOrderToForm.setSapReversalNo(null);
    supplierOrderToForm.setDeliverFormId(null);
    supplierOrderToForm.setReturnReason(null);
    supplierOrderToForm.setSource("SRM");
    supplierOrderToForm.setExecutionStatus(null);
    supplierOrderToForm.setReturnWarehouse(null);
    supplierOrderToForm.setConsignee(null);
    supplierOrderToForm.setReceiveAddress(null);
    supplierOrderToForm.setNeedRedTicket(null);
    supplierOrderToForm.setSapReturnNumber(null);
    supplierOrderToForm.setNumber(null);
    supplierOrderToForm.setTime(supplierOrder.getCreateTime());
    return supplierOrderToForm;
  }


  /**
   * 3.创建采购订单物料列表
   */
  private List<SupplierOrderProduct> createSupplierOrderProduct(List<InitialPurchaseOrderImportDTO> originList, Map<String, SrmProductInfoDTO> productInfoDTOMap) {
    // 过滤掉失败的
    List<InitialPurchaseOrderImportDTO> list =
        originList.stream().filter(dto -> !Boolean.TRUE.equals(dto.getIsFailed()))
        .collect(Collectors.toList());
    if (CollUtil.isEmpty(list)) {
      return new ArrayList<>();
    }
    List<SupplierOrderProduct> supplierOrderProducts = new ArrayList<>();
    for (InitialPurchaseOrderImportDTO dto : list) {
      SupplierOrderProduct supplierOrderProduct = new SupplierOrderProduct();
      // 特殊物料处理
      if(SPECIAL_PRODUCT_CODE.contains(dto.getProductCode())) {
        supplierOrderProduct.setCode(dto.getProductCode());
        supplierOrderProduct.setBrand(SPECIAL_PRODUCT_DESC);
        supplierOrderProduct.setName(SPECIAL_PRODUCT_DESC);
        supplierOrderProduct.setManuCode(SPECIAL_PRODUCT_DESC);
        supplierOrderProduct.setUnit(SPECIAL_PRODUCT_DESC);
        supplierOrderProduct.setUnitCode(SPECIAL_PRODUCT_DESC);
        supplierOrderProducts.add(supplierOrderProduct);
        continue;
      }
      SrmProductInfoDTO findOne = productInfoDTOMap.get(dto.getProductCode());
      supplierOrderProduct.setCode(dto.getProductCode());
      supplierOrderProduct.setBrand(findOne.getBrandName());
      supplierOrderProduct.setName(findOne.getName());
      supplierOrderProduct.setManuCode(findOne.getManuCode());
      supplierOrderProduct.setUnit(findOne.getUnitName());
      supplierOrderProduct.setUnitCode(findOne.getUnit());
      supplierOrderProducts.add(supplierOrderProduct);
    }
    return supplierOrderProducts;
  }


  /**
   * 4.创建采购订单详情
   */
  private List<SupplierOrderDetail> createSupplierOrderDetail(
      List<InitialPurchaseOrderImportDTO> originList,
      Map<String, SrmProductInfoDTO> productInfoDTOMap,
      SupplierOrderToForm supplierOrderToForm,
      List<SupplierOrderProduct> supplierOrderProducts) {

    // 过滤掉失败的
    List<InitialPurchaseOrderImportDTO> list =
        originList.stream().filter(dto -> !Boolean.TRUE.equals(dto.getIsFailed()))
        .collect(Collectors.toList());
    if (CollUtil.isEmpty(list)) {
      return new ArrayList<>();
    }
    InventoryLocation location =
        inventoryLocationRepository.findFirstByGroupCodeAndWarehouseAndState(
                list.get(0).getGroupCode(), list.get(0).getWarehouseCode(), Constants.STATE_OK)
            .orElse(new InventoryLocation());
    List<SupplierOrderDetail> supplierOrderDetails = new ArrayList<>();
    int index = 0;
    for (InitialPurchaseOrderImportDTO dto : list) {
      SupplierOrderProduct supplierOrderProduct = supplierOrderProducts.get(index);
      SrmProductInfoDTO srmProductInfoDTO = productInfoDTOMap.get(dto.getProductCode());
      SupplierOrderDetail supplierOrderDetail = new SupplierOrderDetail();
      dto.setProductId(supplierOrderProduct.getId());
      supplierOrderDetail.setOrderProductId(supplierOrderProduct.getId());
      supplierOrderDetail.setOrderToFormId(supplierOrderToForm.getId());
      BigDecimal num =
          Optional.ofNullable(Convert.toBigDecimal(dto.getOrderQty())).orElse(BigDecimal.ZERO);
      supplierOrderDetail.setNum(num);
      BigDecimal waitQty =
          Optional.ofNullable(Convert.toBigDecimal(dto.getPendingQty())).orElse(BigDecimal.ZERO);
      BigDecimal shipQty =
          Optional.ofNullable(Convert.toBigDecimal(dto.getDeliveredQty())).orElse(BigDecimal.ZERO);
      BigDecimal cancelQty =
          Optional.ofNullable(Convert.toBigDecimal(dto.getCancelOrderQty())).orElse(BigDecimal.ZERO);
      BigDecimal stockInputQty =
          Optional.ofNullable(Convert.toBigDecimal(dto.getInStockQty())).orElse(BigDecimal.ZERO);
      BigDecimal stockOutputQty =
          Optional.ofNullable(Convert.toBigDecimal(dto.getOutStockQty())).orElse(BigDecimal.ZERO);
      BigDecimal remainQty =
          Optional.ofNullable(Convert.toBigDecimal(dto.getRemainingInStockQty())).orElse(BigDecimal.ZERO);
      BigDecimal SettleQty =
          Optional.ofNullable(Convert.toBigDecimal(dto.getActualDeliveryQty())).orElse(BigDecimal.ZERO);
      supplierOrderDetail.setWaitQty(waitQty);
      supplierOrderDetail.setShipQty(shipQty);
      supplierOrderDetail.setReturnQty(stockOutputQty);
      supplierOrderDetail.setCancelQty(cancelQty);
      supplierOrderDetail.setStockInputQty(stockInputQty);
      supplierOrderDetail.setWaitStockInputQty(new BigDecimal("0"));
      supplierOrderDetail.setRemainQty(remainQty);
      supplierOrderDetail.setStockOutputQty(stockOutputQty);
      supplierOrderDetail.setSettleQty(SettleQty);
      supplierOrderDetail.setMark(dto.getRemark());
      supplierOrderDetail.setSalesOrderNo(dto.getSalesOrderNo());
      supplierOrderDetail.setCreateTime(System.currentTimeMillis());
      supplierOrderDetail.setUpdateTime(System.currentTimeMillis());
      supplierOrderDetail.setState(Constants.STATE_OK);
      supplierOrderDetail.setSortNum(index + 1);
      supplierOrderDetail.setDetailedErpId(null);
      supplierOrderDetail.setReturnRowAndNumJson("{}");
      BigDecimal price =
          Optional.ofNullable(Convert.toBigDecimal(dto.getTaxPrice())).orElse(BigDecimal.ZERO).setScale(6, RoundingMode.HALF_UP);
      supplierOrderDetail.setPrice(price);
      supplierOrderDetail.setTotalPrice(price.multiply(num));
      supplierOrderDetail.setOpenRedInvoice(null);
      supplierOrderDetail.setErpClose(null);
      supplierOrderDetail.setDescription("");
      supplierOrderDetail.setPurchaseApplyForOrderId(null);
      supplierOrderDetail.setWarehouse(location.getWarehouse());
      supplierOrderDetail.setWarehouseName(location.getWarehouseName());
      // 百分号转换为小数
      BigDecimal taxRate = Convert.toBigDecimal(dto.getTaxRate());
      supplierOrderDetail.setTaxRate(taxRate);
      supplierOrderDetail.setTotalAmountIncludingTax(price.multiply(num));
      supplierOrderDetail.setFreeState(SimpleBooleanEnum.getKeyFromValue(dto.getIsFree()));
      String projectTypeKey = null;
      Set<Entry<String, String>> entries = PROJECT_TYPE.entrySet();
      for (Entry<String, String> entry : entries) {
        if (entry.getValue().equals(dto.getProjectType())) {
          projectTypeKey = entry.getKey().toString();
          break;
        }
      }
      supplierOrderDetail.setProjectType(projectTypeKey);
      supplierOrderDetail.setEntrustDetailId(null);
      supplierOrderDetail.setSapRowId(null);
      // 实际交货日期
      supplierOrderDetail.setPurchaseDeliverTime(dto.getActualDeliveryDate());
      supplierOrderDetail.setDeliverTime(dto.getDeliveryDate());
      supplierOrderDetail.setInWareHouseId(null);
      supplierOrderDetail.setInWareHouseName(null);
      // 批号问题
      supplierOrderDetail.setBatchNo("");
      supplierOrderDetail.setSapReversalRowNo(null);
      supplierOrderDetail.setOpenInvoiceState(null);
      supplierOrderDetail.setInvoicableNum(BigDecimal.ZERO);
      supplierOrderDetail.setPurchaseOrderId(supplierOrderToForm.getSupplierOrderId());
      supplierOrderDetail.setInvoicedNum(BigDecimal.ZERO);
      supplierOrderDetail.setProductRate(srmProductInfoDTO == null ? BigDecimal.ZERO : srmProductInfoDTO.getProductTaxRate());
      supplierOrderDetail.setMarkupCoefficient(BigDecimal.ZERO);
      supplierOrderDetail.setTransferPrice(BigDecimal.ZERO);
      supplierOrderDetail.setSurcharge(BigDecimal.ZERO);
      BigDecimal settlementPrice =
          Optional.ofNullable(Convert.toBigDecimal(dto.getSettlementPrice())).orElse(BigDecimal.ZERO);
      supplierOrderDetail.setSettlementPrice(settlementPrice);
      supplierOrderDetail.setTotalSettlementPrice(settlementPrice.multiply(supplierOrderDetail.getNum()));
      supplierOrderDetail.setTariff(BigDecimal.ZERO);
      supplierOrderDetail.setTariffAmount(BigDecimal.ZERO);
      supplierOrderDetail.setPaymentAmount(BigDecimal.ZERO);
      supplierOrderDetail.setFreight(BigDecimal.ZERO);
      supplierOrderDetail.setProjectNo(null);
      supplierOrderDetail.setProjectName(null);
      supplierOrderDetail.setFreightSupplierId(null);
      supplierOrderDetail.setTariffSupplierId(null);
      supplierOrderDetail.setFreightSupplierName(null);
      supplierOrderDetail.setTariffSupplierName(null);
      supplierOrderDetail.setReturnPrice(BigDecimal.ZERO);
      supplierOrderDetail.setReturnAmount(BigDecimal.ZERO);
      supplierOrderDetail.setIncidentalAmount(null);
      supplierOrderDetail.setIncidentalSupplierId(null);
      supplierOrderDetail.setIncidentalSupplierName(null);
      supplierOrderDetail.setOriginalPrice(BigDecimal.ZERO);
      supplierOrderDetail.setOriginalTotalPrice(BigDecimal.ZERO);
      supplierOrderDetail.setReturnFlag(false);
      supplierOrderDetails.add(supplierOrderDetail);
      index++;
    }
    return supplierOrderDetails;
  }

  /**
   * 5.创建采购订单入库单form
   */
  private SupplierOrderToForm createSupplierOrderInForm(List<InitialPurchaseOrderImportDTO> originList, SupplierOrder supplierOrder) {

    // 过滤掉失败的 + 需要入库的
    List<InitialPurchaseOrderImportDTO> list =
        originList.stream().filter(dto -> !Boolean.TRUE.equals(dto.getIsFailed()))
        .filter(item -> item.getJinDieInStock().equals(SimpleBooleanEnum.YES.getValue()))
        .collect(Collectors.toList());
    if (CollUtil.isEmpty(list)) {
      return null;
    }
    BigDecimal num = list.stream().map(item -> Convert.toBigDecimal(item.getInStockQty()))
        .filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
    SupplierOrderToForm supplierOrderToForm = new SupplierOrderToForm();
    supplierOrderToForm.setSupplierOrderId(supplierOrder.getId());
    supplierOrderToForm.setType(SupplierOrderFormType.WAREHOUSING.getType());
    supplierOrderToForm.setCreateTime(supplierOrder.getOrderCreateTime());
    supplierOrderToForm.setState(Constants.STATE_OK);
    supplierOrderToForm.setUpdateTime(System.currentTimeMillis());
    supplierOrderToForm.setReturnPrice(BigDecimal.ZERO);
    supplierOrderToForm.setNum(num);
    supplierOrderToForm.setStockOutput(null);
    supplierOrderToForm.setReturnStock(null);
    supplierOrderToForm.setBatchNumber(null);
    supplierOrderToForm.setReturnId(null);
    supplierOrderToForm.setWarehousing(null);
    supplierOrderToForm.setRemark(null);
    supplierOrderToForm.setNoticeReceipt(null);
    supplierOrderToForm.setSendSms(null);
    supplierOrderToForm.setLogisticsInformation(null);
    supplierOrderToForm.setProductVoucher("金蝶已入库");
    supplierOrderToForm.setProductVoucherYear(null);
    supplierOrderToForm.setSapReversalNo(null);
    supplierOrderToForm.setDeliverFormId(null);
    supplierOrderToForm.setReturnReason(null);
    supplierOrderToForm.setSource("SRM");
    supplierOrderToForm.setTime(supplierOrder.getOrderCreateTime());
    return supplierOrderToForm;
  }


  /**
   * 6.创建采购入库单订单详情
   */
  private List<SupplierOrderDetail> createSupplierOrderInDetail(List<InitialPurchaseOrderImportDTO> originList,
      SupplierOrderToForm supplierOrderInForm,
      List<SupplierOrderProduct> supplierOrderProducts,
      List<SupplierOrderDetail> supplierOrderDetails) {
    // 过滤掉失败的
    List<InitialPurchaseOrderImportDTO> list =
        originList.stream().filter(dto -> !Boolean.TRUE.equals(dto.getIsFailed()))
        .filter(item -> item.getJinDieInStock().equals(SimpleBooleanEnum.YES.getValue()))
        .collect(Collectors.toList());
    if (CollUtil.isEmpty(list)) {
      return new ArrayList<>();
    }
    List<SupplierOrderDetail> res = new ArrayList<>();
    int index = 0;
    Map<String, SupplierOrderProduct> supplierOrderProductMap = supplierOrderProducts.stream()
        .collect(Collectors.toMap(SupplierOrderProduct::getId, Function.identity(),
            (item1, item2) -> item1));
    Map<String, SupplierOrderDetail> supplierOrderDetailMap = supplierOrderDetails.stream()
        .collect(Collectors.toMap(SupplierOrderDetail::getOrderProductId, Function.identity(),
            (item1, item2) -> item1));
    for (InitialPurchaseOrderImportDTO dto : list) {
      SupplierOrderProduct supplierOrderProduct = supplierOrderProductMap.get(dto.getProductId());
      SupplierOrderDetail supplierOrderDetail = supplierOrderDetailMap.get(supplierOrderProduct.getId());
      SupplierOrderDetail inDetail = new SupplierOrderDetail();
      inDetail.setOrderProductId(supplierOrderProduct.getId());
      inDetail.setOrderToFormId(supplierOrderInForm.getId());
      inDetail.setNum(supplierOrderDetail.getStockInputQty());
      inDetail.setWaitQty(supplierOrderDetail.getWaitQty());
      inDetail.setShipQty(supplierOrderDetail.getShipQty());
      inDetail.setReturnQty(supplierOrderDetail.getReturnQty());
      inDetail.setCancelQty(supplierOrderDetail.getCancelQty());
      inDetail.setStockInputQty(supplierOrderDetail.getStockInputQty());
      inDetail.setWaitStockInputQty(supplierOrderDetail.getWaitStockInputQty());
      inDetail.setRemainQty(supplierOrderDetail.getRemainQty());
      inDetail.setStockOutputQty(supplierOrderDetail.getStockOutputQty());
      inDetail.setSettleQty(supplierOrderDetail.getSettleQty());
      inDetail.setMark(supplierOrderDetail.getMark());
      inDetail.setSalesOrderNo(supplierOrderDetail.getSalesOrderNo());
      inDetail.setCreateTime(System.currentTimeMillis());
      inDetail.setUpdateTime(System.currentTimeMillis());
      inDetail.setState(Constants.STATE_OK);
      inDetail.setSortNum(index + 1);
      inDetail.setDetailedErpId(null);
      inDetail.setReturnRowAndNumJson("{}");
      inDetail.setPrice(supplierOrderDetail.getPrice());
      inDetail.setTotalPrice(inDetail.getPrice().multiply(inDetail.getNum()));
      inDetail.setOpenRedInvoice(null);
      inDetail.setErpClose(null);
      inDetail.setDescription("");
      inDetail.setPurchaseApplyForOrderId(null);
      inDetail.setWarehouse(supplierOrderDetail.getWarehouse());
      inDetail.setWarehouseName(supplierOrderDetail.getWarehouseName());
      inDetail.setTaxRate(supplierOrderDetail.getTaxRate());
      inDetail.setTotalAmountIncludingTax(inDetail.getPrice().multiply(inDetail.getNum()));
      inDetail.setFreeState(supplierOrderDetail.getFreeState());
      inDetail.setProjectType(supplierOrderDetail.getProjectType());
      inDetail.setEntrustDetailId(null);
      inDetail.setSapRowId(null);
      inDetail.setPurchaseDeliverTime(supplierOrderDetail.getDeliverTime());
      inDetail.setDeliverTime(supplierOrderDetail.getDeliverTime());
      inDetail.setInWareHouseId(null);
      inDetail.setInWareHouseName(null);
      // 批号问题
      inDetail.setBatchNo("");
      inDetail.setSapReversalRowNo(null);
      inDetail.setOpenInvoiceState("0");
      BigDecimal invoiceQty =
          Optional.ofNullable(Convert.toBigDecimal(dto.getInvoiceQty())).orElse(BigDecimal.ZERO);
      inDetail.setInvoicedNum(invoiceQty);
      if (invoiceQty.compareTo(BigDecimal.ZERO) > 0) {
        inDetail.setOpenInvoiceState("1");
      }
      inDetail.setInvoicableNum(supplierOrderDetail.getStockInputQty().subtract(invoiceQty));
      inDetail.setPurchaseOrderId(supplierOrderInForm.getSupplierOrderId());
      inDetail.setProductRate(supplierOrderDetail.getProductRate());
      inDetail.setMarkupCoefficient(BigDecimal.ZERO);
      inDetail.setTransferPrice(BigDecimal.ZERO);
      inDetail.setSurcharge(BigDecimal.ZERO);
      inDetail.setSettlementPrice(supplierOrderDetail.getSettlementPrice());
      inDetail.setTotalSettlementPrice(supplierOrderDetail.getTotalSettlementPrice());
      inDetail.setTariff(BigDecimal.ZERO);
      inDetail.setTariffAmount(BigDecimal.ZERO);
      inDetail.setPaymentAmount(BigDecimal.ZERO);
      inDetail.setFreight(BigDecimal.ZERO);
      inDetail.setProjectNo(null);
      inDetail.setProjectName(null);
      inDetail.setFreightSupplierId(null);
      inDetail.setTariffSupplierId(null);
      inDetail.setFreightSupplierName(null);
      inDetail.setTariffSupplierName(null);
      inDetail.setReturnPrice(BigDecimal.ZERO);
      inDetail.setReturnAmount(BigDecimal.ZERO);
      inDetail.setIncidentalAmount(null);
      inDetail.setIncidentalSupplierId(null);
      inDetail.setIncidentalSupplierName(null);
      inDetail.setOriginalPrice(BigDecimal.ZERO);
      inDetail.setOriginalTotalPrice(BigDecimal.ZERO);
      inDetail.setReturnFlag(false);
      inDetail.setDetailedId(supplierOrderDetail.getId());
      res.add(inDetail);
      index++;
    }
    return res;
  }


  /**
   * 所有未失败的行失败处理
   */
  private void allFail(List<InitialPurchaseOrderImportDTO> list, String msg) {
    for (InitialPurchaseOrderImportDTO dto : list) {
      if (Boolean.TRUE.equals(dto.getIsFailed())) {
        continue;
      }
      dto.setIsFailed(true);
      dto.setFailedReason(msg);
    }
  }

  /**
   * 保存任务执行日志
   * @param list
   * @param missionId
   */
  private void saveLogs(List<InitialPurchaseOrderImportDTO> list, String missionId,
      AtomicInteger successCount) {
    for (InitialPurchaseOrderImportDTO dto : list) {
      if (Boolean.TRUE.equals(dto.getIsFailed())) {
        fcMissionService.createMissionDetail(missionId,
            String.format("第【%s】行,", dto.getRowIndex()), dto.getFailedReason());
      } else {
        fcMissionService.createMissionDetail(missionId,
            String.format("第【%s】行,", dto.getRowIndex()), StrUtil.EMPTY);
        successCount.incrementAndGet();
      }
    }
  }
}

