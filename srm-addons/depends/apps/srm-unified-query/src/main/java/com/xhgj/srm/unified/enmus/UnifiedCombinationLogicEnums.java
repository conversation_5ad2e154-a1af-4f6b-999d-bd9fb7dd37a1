package com.xhgj.srm.unified.enmus;

/**
 * <AUTHOR>
 * 统一查询方案组合逻辑
 */
public enum UnifiedCombinationLogicEnums {
  /**
   * 与
   */
  AND("and", "与"),
  /**
   * 或
   */
  OR("or", "或")
  ;

  private String key;

  private String value;

  UnifiedCombinationLogicEnums(String key, String value) {
    this.key = key;
    this.value = value;
  }

  public String getKey() {
    return key;
  }

  public String getValue() {
    return value;
  }
}
