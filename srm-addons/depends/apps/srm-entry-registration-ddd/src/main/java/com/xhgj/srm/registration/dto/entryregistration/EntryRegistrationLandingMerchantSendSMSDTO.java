package com.xhgj.srm.registration.dto.entryregistration;

import com.xhgj.srm.jpa.entity.EntryRegistrationLandingMerchant;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 报备单落地商发送短信DTO
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
public class EntryRegistrationLandingMerchantSendSMSDTO {

  /**
   * 报备单落地商 （数据库中的值）
   */
  private EntryRegistrationLandingMerchant currentEntryRegistrationLandingMerchant;
  /**
   * 报备单落地商 （页面传入的值）
   */
  private EntryRegistrationLandingMerchant landingMerchantEntity;
  /**
   * 营业执照
   */
  private String license;
  /**
   * 身份证照片 多张
   */
  private List<String> idCardPhoto;
  /**
   * 产品资质书
   */
  private List<String> productQualification;
}
