package com.xhgj.srm.registration.domain;

import lombok.Data;

/**
 * <AUTHOR>
 * 操作用户
 */
@Data
public class OperatingUser {

  /**
   * 来源：api_manage
   */
  public static final String SOURCE_API_MANAGE = "api_manage";

  /**
   * 来源：api_portal
   */
  public static final String SOURCE_API_PORTAL = "api_portal";

  /**
   * 来源 api_mobile
   */
  public static final String SOURCE_API_MOBILE = "api_mobile";

  /**
   * 来源 api_mobile
   */
  public static final String SOURCE_API_SUPPLIER = "api_supplier";

  /**
   * 操作用户Id
   */
  private String userId;

  /**
   * 操作用户code
   */
  private String userCode;

  /**
   * 操作用户真实名字
   */
  private String realName;

  /**
   * 来源
   */
  private String source;

  /**
   * 供应商Id
   */
  private String supplierId;

  public static OperatingUser createDefault(String source) {
    OperatingUser operatingUser = new OperatingUser();
    operatingUser.setUserId("-1");
    operatingUser.setRealName("未登录用户");
    operatingUser.setSource(source);
    operatingUser.setSupplierId("-1");
    operatingUser.setUserCode("-1");
    return operatingUser;
  }

}
