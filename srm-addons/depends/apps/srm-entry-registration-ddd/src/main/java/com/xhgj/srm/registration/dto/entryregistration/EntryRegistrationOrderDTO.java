package com.xhgj.srm.registration.dto.entryregistration;

import cn.hutool.core.util.StrUtil;
import com.xhgj.srm.common.Constants;
import com.xhgj.srm.common.enums.entryregistration.EntryRegistrationCooperationTypeEnum;
import com.xhgj.srm.common.enums.entryregistration.EntryRegistrationStatusEnum;
import com.xhgj.srm.jpa.entity.EntryRegistrationOrder;
import com.xhgj.srm.jpa.entity.Group;
import com.xhgj.srm.jpa.entity.User;
import com.xhiot.boot.core.common.exception.CheckException;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.util.List;
import javax.persistence.Column;
import javax.validation.Valid;
import javax.validation.constraints.DecimalMax;
import javax.validation.constraints.DecimalMin;
import javax.validation.constraints.Digits;
import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class EntryRegistrationOrderDTO {

  @ApiModelProperty("id")
  private String id;

  /**
   * 报备单号。
   */
  @ApiModelProperty("报备单号。新增后端自动生成，修改传入")
  private String registrationNumber;

  @ApiModelProperty("平台(传code),可多条数据,逗号隔开")
  @NotBlank(message = "平台不能为空")
  private String platformCode;

  @ApiModelProperty("项目大类")
  @NotBlank(message = "项目大类不能为空")
  private String projectCategory;

  @ApiModelProperty("项目名称,由platformCode查询而来")
  @NotBlank
  private String projectName;

  /**
   * 合作类型。
   * {@link EntryRegistrationCooperationTypeEnum}
   */
  @ApiModelProperty("合作类型。")
  @NotBlank(message = "合作类型不能为空")
  private String typeOfCooperation;

//  /**
//   * 常规供货 合作类型选项目报备时必填
//   * {@link Constants#REGULAR_SUPPLY_YES}
//   */
//  @ApiModelProperty("常规供货 合作类型选项目报备时必填")
//  private String regularSupply;

  @ApiModelProperty("合作单位名称。")
  @NotBlank(message = "合作单位名称不能为空")
  @Size(max = 50, message = "合作单位名称超长")
  private String partnerName;

  /**
   * 合作开始时间。
   */
  @ApiModelProperty("合作开始时间。")
  @NotNull(message = "合作开始时间不能为空")
  private Long cooperationStartTime;

  /**
   * 合作结束时间。
   */
  @ApiModelProperty("合作结束时间。")
  @NotNull(message = "合作结束时间不能为空")
  private Long cooperationEndTime;

  /**
   * 合作联系人姓名。
   */
  @ApiModelProperty("合作联系人姓名。")
  @NotBlank(message = "合作联系人姓名不能为空")
  @Size(max = 10, message = "合作联系人姓名长度不能超过10个字符")
  private String cooperationContactName;

  /**
   * 合作联系人电话。
   */
  @ApiModelProperty("合作联系人电话。")
  @NotBlank(message = "合作联系人电话不能为空")
  private String cooperationContactPhone;
  /**
   * 职务
   */
  @ApiModelProperty("职务")
  @Size(max = 10, message = "职务超长")
  private String position;
//  /**
//   * 邮箱
//   * @deprecated 6.5.1版本去除 使用merchant的emailAddress
//   */
//  @ApiModelProperty("邮箱")
//  @NotBlank(message = "邮箱不能为空")
//  @Size(max = 50, message = "邮箱超长")
//  private String email;
  /**
   * 联系地址
   */
  @ApiModelProperty("联系地址")
  @Size(max = 100, message = "联系地址超长")
  private String contactAddress;
  /**
   * 合作品牌
   */
  @ApiModelProperty("合作品牌。")
  @NotBlank(message = "合作品牌不能为空")
  @Size(max = 50, message = "合作品牌超长")
  private String cooperationBrand;

  @ApiModelProperty("合作区域。")
  @NotBlank(message = "合作区域不能为空")
  @Size(max = 50, message = "合作区域超长")
  private String cooperationRegion;

  @ApiModelProperty("合作比例/折扣信息。")
  @Valid
  private List<EntryRegistrationDiscountDTO> entryRegistrationDiscountInfo;

  /**
   * 保证金，精度为20位，小数点后10位。
   */
  @NotNull(message = "保证金不能为空")
  @DecimalMin(value = "0.00", inclusive = true, message = "保证金不能小于0")
  @DecimalMax(value = "9999999999.9999999999", inclusive = true, message = "保证金超出最大值")
  private BigDecimal deposit;

  /**
   * 是否有仓储。值为1表示有，0表示无。
   */
  @ApiModelProperty("是否有仓储。值为1表示有，0表示无。")
  @NotBlank(message = "是否有仓储不能为空")
  private String storage;
  /**
   * 仓库地址 仓储为有时必填
   */
  @ApiModelProperty("仓库地址")
  @Size(max = 100, message = "仓库地址超长")
  private String storageAddress;
  /**
   * 仓库面积 仓储为有时必填 平方米
   */
  @ApiModelProperty("仓库面积")
  @Min(value = 0, message = "仓库面积不能小于0")
  @Max(value = 99999,message = "仓库面积不能超过五位数")
  private BigDecimal storageArea;
  /**
   * 保底金额 仅数字
   */
  @ApiModelProperty("保底金额")
  @Digits(integer = 10, fraction = 10, message = "保底金额格式错误")
  private BigDecimal guaranteedAmount;

  /**
   * 违约金-实际订单金额: 两个值用,分开
   */
  @ApiModelProperty("违约金")
  private String penalty;
  /**
   * 付款方式。
   * {@link  com.xhgj.srm.common.enums.PayTypeSAPEnums}
   */
  @ApiModelProperty("付款方式。")
  @NotBlank(message = "付款方式不能为空")
  private String paymentType;
  /**
   * 付款方式后输入框 (月份、其他)
   */
  @ApiModelProperty("付款方式后输入框(月份、其他)")
  private String paymentTypeInput;
  /** 账期（是否为背靠背） */
  @Column(name = "c_back_to_back")
  private Boolean backToBack;
  /**
   * 账期天数。
   */
  @ApiModelProperty("账期天数。")
  @NotNull(message = "账期天数不能为空")
  @Min(value = 0, message = "账期天数不能小于0")
  @Max(value = 365, message = "账期天数不能超过一年")
  private Integer accountPeriod;

  /**
   * 付款比例 账期选择"账期"时出现
   */
  @ApiModelProperty("付款比例")
  private String paymentRatio;
  /**
   * 付款条件，逗号隔开。（前后需要加一个逗号，如：,对方开票,客户回款,）
   * {@link com.xhgj.srm.common.enums.entryregistration.EntryRegistrationPaymentConditionEnum}
   */
  @ApiModelProperty("付款条件，逗号隔开。")
  @NotBlank(message = "付款条件不能为空")
  private String paymentTerms;

  /**
   * 报备状态/提交类型。
   * {@link EntryRegistrationStatusEnum}
   */
  @ApiModelProperty("报备状态/提交类型。")
  @NotBlank(message = "报备状态不能为空")
  private String registrationStatus;

  /**
   * 初始折扣比例。
   */
  @ApiModelProperty("初始折扣比例。")
  @NotNull(message = "初始折扣比例不能为空")
  @DecimalMin(value = "0.00", inclusive = true, message = "初始折扣比例不能小于0")
  private BigDecimal initialDiscountRatio;

  /**
   * rename准入说明 v6.2.2
   */
  @Size(max = 500, message = "准入说明不能超过500个字符")
  private String notes;

  /**
   * 其他备注
   */
  @ApiModelProperty("其他备注")
  @Size(max = 50, message = "其他备注不能超过50个字符")
  private String otherRemarks;

  @ApiModelProperty("补充附件")
  @Size(max = 5,message = "最多可上传5个附件")
  private List<String> supplyFile;

  public EntryRegistrationOrder toEntity(Long createTime, User user, Group group,
      String registrationNumber, Long linkGenerationTime) {
    EntryRegistrationOrder entity = new EntryRegistrationOrder();
    if (StrUtil.isBlank(id)) {
      // entity.setId(UUID.randomUUID().toString(true));
      entity.setRegistrationNumber(registrationNumber);
    }else {
      //修改时报备单号不能为空
      if (StrUtil.isBlank(this.registrationNumber)){ throw new CheckException("报备单号不能为空");}
      entity.setId(this.id);
      entity.setRegistrationNumber(this.registrationNumber);
      entity.setUpdateTime(System.currentTimeMillis());
    }
    entity.setCreateTime(createTime);
    entity.setPartnerName(this.partnerName);
    entity.setSalesmanId(user.getId());
    entity.setSalesmanName(user.getRealName());
    entity.setBusinessCompany(group.getName());
    entity.setRegistrationTime(createTime);
    entity.setPlatform(this.platformCode);
    entity.setProjectName(this.projectName);
    entity.setTypeOfCooperation(this.typeOfCooperation);
    // 6.5.1版本去除
//    entity.setRegularSupply(this.regularSupply);
    entity.setCooperationStartTime(this.cooperationStartTime);
    entity.setCooperationEndTime(this.cooperationEndTime);
    entity.setCooperationContactName(this.cooperationContactName);
    entity.setCooperationContactPhone(this.cooperationContactPhone);
    entity.setPosition(this.position);
//    entity.setEmail(this.email);
    entity.setContactAddress(this.contactAddress);
    entity.setCooperationBrand(this.cooperationBrand);
    entity.setCooperationRegion(this.cooperationRegion);
    entity.setNotes(this.notes);
    entity.setDeposit(this.deposit);
    entity.setStorage(this.storage);
    entity.setStorageAddress(this.storageAddress);
    entity.setStorageArea(this.storageArea);
    entity.setGuaranteedAmount(this.guaranteedAmount);
    entity.setPenalty(this.penalty);
    entity.setPaymentType(this.paymentType);
    entity.setPaymentTypeInput(this.paymentTypeInput);
    entity.setAccountPeriod(this.accountPeriod);
    entity.setPaymentTerms(this.paymentTerms);
    entity.setOtherRemarks(this.otherRemarks);
    entity.setInitialDiscountRatio(this.initialDiscountRatio);
    entity.setCreateMan(user.getId());
    entity.setState(Constants.STATE_OK);
    entity.setRegistrationStatus(EntryRegistrationStatusEnum.PENDING_CONFIRMATION.getKey());
    entity.setLinkGenerationTime(linkGenerationTime);
    return entity;
}
}