package com.xhgj.srm.open.utils;


import com.xhgj.srm.jpa.entity.SupplierUser;
import com.xhgj.srm.jpa.repository.SupplierUserRepository;
import com.xhgj.srm.open.domain.OpenUserDetails;
import io.jsonwebtoken.Claims;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.SignatureAlgorithm;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.stereotype.Component;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2022/7/11 19:09
 */
@Component
@Slf4j
public class OpenSecurityUtil {

  @Value("${xhiot.boot.security.jwt.secret:default}")
  private String secret;

  @Value("${xhiot.boot.security.jwt.expiration:86400}")
  private Long expiration;

  @Value("${xhiot.boot.security.jwt.tokenHeader:Authorization}")
  private String tokenHeader;

  @Value("${xhiot.boot.security.jwt.tokenHead:Bearer}")
  private String tokenHead;

  @Resource
  private SupplierUserRepository supplierUserRepository;

  public OpenUserDetails getOpenUserDetails() {
    try {
      return (OpenUserDetails)this.getAuth().getPrincipal();
    }catch (Exception e) {
      log.info("获取用户信息失败", e);
      return null;
    }
  }

  Authentication getAuth() {
    return SecurityContextHolder.getContext().getAuthentication();
  }

  public String getToken(HttpServletRequest httpServletRequest) {
    String token = httpServletRequest.getHeader(this.tokenHeader);
    if (token != null && token.startsWith(this.tokenHead)) {
      token = token.substring(this.tokenHead.length());
      // 去除token前后空格
      token = token.trim();
    }
    return token;
  }

  /**
   * 验证token是否有效
   * @param token
   * @return
   */
  public boolean validateToken(String token) {
    try {
      return !this.isTokenExpired(token);
    }catch (Exception e) {
      log.info("token验证失败", e);
      return false;
    }
  }

  /**
   * 判断token是否过期
   * @param token
   * @return
   */
  private boolean isTokenExpired(String token) {
    Date expiredDate = this.getExpiredDateFromToken(token);
    return expiredDate != null && expiredDate.before(new Date());
  }

  /**
   * 从token中获取过期时间
   * @param token
   * @return
   */
  private Date getExpiredDateFromToken(String token) {
    Claims claims = this.parseToken(token);
    return claims.getExpiration();
  }

  /**
   * 根据userId获取用户信息
   * @param userId
   * @return
   */
  public SupplierUser getSupplierUserById(String userId) {
    return this.supplierUserRepository.findById(userId).orElse(null);
  }


  public String getTokenUserId(String token) {
    try {
      Claims claims = this.parseToken(token);
      return claims.get("userId").toString();
    } catch (Exception e) {
      log.info("获取token中的userId失败", e);
      return null;
    }
  }

  /**
   * 解析token
   * @param
   */
  private Claims parseToken(String token) {
    return Jwts.parser().setSigningKey(this.secret + "open").parseClaimsJws(token).getBody();
  }

  /**
   * 生成token
   * @param userDetails
   * @param userId
   * @return
   */
  public String generateToken(UserDetails userDetails, String userId) {
    Map<String, Object> claims = new HashMap();
    claims.put("userId", userId);
    claims.put("created", new Date());
    return this.generateToken(claims);
  }

  /**
   * 生成token
   * @param claims
   * @return
   */
  private String generateToken(Map<String, Object> claims) {
    // 差异化 生成token
    return Jwts.builder().setClaims(claims).setExpiration(this.generateExpirationDate()).signWith(
        SignatureAlgorithm.HS512, this.secret + "open").compact();
  }

  /**
   * 生成过期时间
   * @return
   */
  private Date generateExpirationDate() {
    return this.expiration <= 0L ? null : new Date(System.currentTimeMillis() + this.expiration * 1000L);
  }
}
