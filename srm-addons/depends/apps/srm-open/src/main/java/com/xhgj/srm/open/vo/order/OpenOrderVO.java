package com.xhgj.srm.open.vo.order;

import cn.hutool.core.util.StrUtil;
import com.xhgj.srm.common.Constants;
import com.xhgj.srm.common.Constants_order;
import com.xhgj.srm.common.dto.FileDTO;
import com.xhgj.srm.common.enums.TitleOfTheContractEnum;
import com.xhgj.srm.jpa.entity.OrderAccept;
import com.xhgj.srm.open.entity.OrderEntity;
import com.xhgj.srm.open.factory.MapStructFactory;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.math.BigDecimal;
import java.util.Comparator;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * 订单详情VO对象
 */
@Data
@NoArgsConstructor
public class OpenOrderVO {

  @ApiModelProperty("订单id")
  private String id;

  @ApiModelProperty("客户订单号")
  private String orderNo;

  @ApiModelProperty("下单时间")
  private Long createTime;

  @ApiModelProperty("下单平台Code")
  private String platformCode;

  @ApiModelProperty("下单平台")
  private String platform;

  @ApiModelProperty("下单金额")
  private BigDecimal price;

  @ApiModelProperty("退货金额")
  private BigDecimal refundPrice;

  @ApiModelProperty("结算金额")
  private BigDecimal settlePrice;

  @ApiModelProperty("发货进度")
  private String progress;

  @ApiModelProperty("客户名称")
  private String customer;

  @ApiModelProperty("收件人")
  private String consignee;

  @ApiModelProperty("联系方式")
  private String mobile;

  @ApiModelProperty("收件地址")
  private String address;

  @ApiModelProperty("开票状态")
  private String invoicingState;

  @ApiModelProperty("开票状态")
  private String invoicingStateStr;

  public String getInvoicingStateStr() {
    return Constants_order.INVOICE_STATE_MAP.getOrDefault(this.invoicingState,
        Constants_order.INVOICE_STATE_MAP.get(Constants_order.INVOICE_STATE_HAND));
  }

  @ApiModelProperty("订单状态")
  private String orderState;

  @ApiModelProperty("订单状态")
  private String orderStateStr;

  public String getOrderStateStr() {
    return Constants_order.ORDER_STATE_MAP.getOrDefault(this.orderState,
        Constants_order.ORDER_STATE_MAP.get(Constants_order.ORDER_STATE_WAIT));
  }

//  @ApiModelProperty("审核状态(1-取消审核中,2-退货审核中,空或其他为正常)")
//  private String checkState;

  @ApiModelProperty("报备单号")
  private String filingNo;

  @ApiModelProperty("第一次发货时间")
  private Long firstShipTime;

  @ApiModelProperty("全部发货完成时间")
  private Long allShipTime;

  @ApiModelProperty("订单完成时间")
  private Long confirmVoucherTime;

  @ApiModelProperty("回款状态")
  private String customerReturnProgress;

  @ApiModelProperty("回款状态")
  private String customerReturnProgressStr;

  public String getCustomerReturnProgressStr() {
    return Constants_order.CUSTOMER_RECEIVABLE_RETURN_PROGRESS.getOrDefault(this.customerReturnProgress,
        Constants_order.CUSTOMER_RECEIVABLE_RETURN_PROGRESS.get(Constants_order.RETURN_PROGRESS_NO));
  }

  @ApiModelProperty("对账状态")
  private String accountStatus;

  @ApiModelProperty("对账状态")
  private String accountStatusStr;

  public String getAccountStatusStr() {
    return Constants_order.ORDER_ACCOUNT_STATUS_MAP.getOrDefault(this.accountStatus,
        Constants_order.ORDER_ACCOUNT_STATUS_MAP.get(Constants_order.ORDER_ACCOUNT_STATUS_NOT_ALLOW));
  }

  @ApiModelProperty("对账单开票状态")
  private String accountOpenInvoiceStatus;

  @ApiModelProperty("对账单开票状态")
  private String accountOpenInvoiceStatusStr;

  public String getAccountOpenInvoiceStatusStr() {
    return Constants.ORDER_SUPPLIER_INVOICE_STATE_TYPE_NEW.getOrDefault(this.accountOpenInvoiceStatus,
        Constants.ORDER_SUPPLIER_INVOICE_STATE_TYPE_NEW.get(Constants.ORDER_INVOICE_STATE_NOT_DONE));
  }

  @ApiModelProperty("对账 id")
  private String accountId;

  @ApiModelProperty("备注")
  private String remark;

  @ApiModelProperty("付款状态")
  private String paymentStatus;

  @ApiModelProperty("付款状态")
  private String paymentStatusStr;

  public String getPaymentStatusStr() {
    return Constants_order.ORDER_PAYMENT_STATUS_MAP.getOrDefault(this.paymentStatus,
        Constants_order.ORDER_PAYMENT_STATUS_MAP.get(Constants_order.CAN_NOT_PAYMENT_TYPE));
  }

  @ApiModelProperty("付款单 id")
  private String orderPaymentId;

//  @ApiModelProperty("签收凭证")
//  private String signVoucherState;
//
//  @ApiModelProperty("签收凭证")
//  private String signVoucherStateStr;

//  public String getSignVoucherStateStr() {
//    String st = Optional.ofNullable(this.signVoucherState)
//        .orElse(Constants_order.ORDER_ACCEPT_PENDING_UPLOADING);
//    return Constants_order.SIGN_VOUCHER_MAP.get(st);
//  }

  @ApiModelProperty("开票申请单号")
  private String invoiceApplicationNum;

  @ApiModelProperty("发票信息id")
  private String orderInvoiceId;

  @ApiModelProperty("销售订单号")
  private String saleOrderNo;

  @ApiModelProperty("禁止付款状态")
  private Boolean prohibitionPaymentState;

  @ApiModelProperty("派单时间")
  private Long dispatchTime;

  @ApiModelProperty("erp类型")
  private String erpType;

  @ApiModelProperty("签约抬头")
  private String titleOfTheContract;

  @ApiModelProperty("签约抬头")
  private String titleOfTheContractStr;

  public String getTitleOfTheContractStr() {
    TitleOfTheContractEnum titleOfTheContractEnum =
        TitleOfTheContractEnum.getEnumByCode(this.getTitleOfTheContract());
    return titleOfTheContractEnum == null ? StrUtil.EMPTY : titleOfTheContractEnum.getName();
  }

  @ApiModelProperty("签收信息-审核人")
  private String acceptAuditMan;

  @ApiModelProperty("签收信息-驳回理由")
  private String acceptGroundsForRejection;

  @ApiModelProperty("签收信息-审核状态")
  private String acceptAuditState;

  @ApiModelProperty("签收信息-审核状态")
  private String acceptAuditStateStr;

  public String getAcceptAuditStateStr() {
    return Constants_order.SIGN_VOUCHER_MAP.getOrDefault(this.acceptAuditState, Constants_order.SIGN_VOUCHER_MAP.get(Constants_order.ORDER_ACCEPT_PENDING_UPLOADING));
  }

  /**
   * 采购单合同附件
   */
  @ApiModelProperty("采购单合同附件")
  private FileDTO purchaseOrderContract;

  /**
   * 客户信息附件
   */
  @ApiModelProperty("客户信息附件")
  private List<FileDTO> customerInfoAttachments;

  /**
   * 订单明细列表
   */
  private List<OpenOrderDetailVO> orderDetails;

  /**
   * 订单发票信息模版
   */
  private OpenOrderInvoiceInfoVO invoiceTemplate;

  /**
   * 订单发货列表
   */
  private List<OpenOrderShipVO> orderShipments;

  /**
   * 订单退货列表
   */
  private List<OpenOrderReturnVO> orderReturns;

  /**
   * 订单取消列表
   */
  private List<OpenOrderCancelVO> orderCancels;

  /**
   * 订单签收单
   */
  private List<OpenOrderReceiptVO> orderAccepts;

  public OpenOrderVO(OrderEntity orderEntity) {
    MapStructFactory.INSTANCE.updateOpenOrderVO(orderEntity, this);
    this.createTime = orderEntity.getOrderTime();
    this.settlePrice = orderEntity.getPrice().subtract(orderEntity.getRefundPrice());
//    this.checkState = orderEntity.getReturnState();
    this.invoicingState = StrUtil.blankToDefault(orderEntity.getInvoicingState(),
        Constants_order.INVOICE_STATE_HAND);
    this.orderState = StrUtil.blankToDefault(orderEntity.getOrderState(), Constants_order.ORDER_STATE_WAIT);
    this.accountOpenInvoiceStatus = orderEntity.getSupplierOpenInvoiceStatus();
    this.dispatchTime = orderEntity.getCreateTime();
    this.customerReturnProgress = orderEntity.getCustomerReturnProgress();
    this.accountStatus = StrUtil.blankToDefault(
        orderEntity.getAccountStatus(),Constants_order.ORDER_ACCOUNT_STATUS_NOT_ALLOW);
    this.accountOpenInvoiceStatus = StrUtil.blankToDefault(
        orderEntity.getSupplierOpenInvoiceStatus(), Constants.ORDER_INVOICE_STATE_NOT_DONE);
    this.remark = StrUtil.emptyIfNull(orderEntity.getRemark());
    this.paymentStatus = StrUtil.blankToDefault(orderEntity.getPaymentStatus(),Constants_order.CAN_NOT_PAYMENT_TYPE);
    this.acceptAuditState = orderEntity.getConfirmVoucherAuditStatus();

    // 复杂对象
    Optional.ofNullable(orderEntity.getPlatform()).ifPresent(platform -> {
      this.platform = platform.getName();
      this.platformCode = platform.getCode();
    });
    Optional.ofNullable(orderEntity.getPurchaseOrderContract())
        .ifPresent(one -> this.purchaseOrderContract = new FileDTO(one.getUrl(), one.getName()));
    Optional.ofNullable(orderEntity.getCustomerInformation()).ifPresent(
        files -> this.customerInfoAttachments =
            files.stream().map(file -> new FileDTO(file.getUrl(), file.getName()))
                .collect(Collectors.toList()));
    this.orderDetails = orderEntity.getOrderDetails().stream()
        .map(item -> new OpenOrderDetailVO(item, orderEntity)).collect(Collectors.toList());
    this.invoiceTemplate = new OpenOrderInvoiceInfoVO(orderEntity.getInvoiceTemplate());
    this.orderShipments = orderEntity.getOrderShipments().stream()
        .map(item -> new OpenOrderShipVO(item, orderEntity.getOrderEntityRepository())).collect(Collectors.toList());
    this.orderReturns = orderEntity.getOrderReturns().stream()
        .map(OpenOrderReturnVO::new).collect(Collectors.toList());
    this.orderCancels = orderEntity.getOrderCancels().stream()
        .map(OpenOrderCancelVO::new).collect(Collectors.toList());
    this.orderAccepts = orderEntity.getOrderAccepts().stream()
        .map(item -> new OpenOrderReceiptVO(item, orderEntity.getOrderEntityRepository())).collect(Collectors.toList());
    // 获取最新的一条签收信息 --- 根据id正序，获取最后一条
    orderEntity.getOrderAccepts().stream().max(Comparator.comparing(OrderAccept::getId))
        .ifPresent(accept -> {
          this.acceptAuditMan = accept.getAuditMan();
          this.acceptGroundsForRejection = accept.getGroundsForRejection();
        });
  }
}
