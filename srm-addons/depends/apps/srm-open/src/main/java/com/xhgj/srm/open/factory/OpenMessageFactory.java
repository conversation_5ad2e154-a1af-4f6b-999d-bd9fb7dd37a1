package com.xhgj.srm.open.factory;

import com.alibaba.fastjson.JSON;
import com.xhgj.srm.common.Constants;
import com.xhgj.srm.open.entity.OpenMessage;
import com.xhgj.srm.open.enums.OpenMessageStatusEnum;
import com.xhgj.srm.open.enums.OpenMessageTypeEnum;
import com.xhgj.srm.open.form.message.OpenMessageSaveForm;
import com.xhgj.srm.open.repository.OpenMessageRepository;
import com.xhgj.srm.open.vo.message.BaseOpenMessageVO;
import com.xhiot.boot.core.common.exception.CheckException;
import org.springframework.stereotype.Component;
import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Component
public class OpenMessageFactory {

  @Resource
  private OpenMessageRepository openMessageRepository;

  public BaseOpenMessageVO buildVo(OpenMessage message) {
    // 根据message不同的类型构建不同的vo
    BaseOpenMessageVO vo = null;
    switch (OpenMessageTypeEnum.getEnum(message.getMessageType())) {
      case ORDER_CREATE:
      case ORDER_RECALL:
      case ORDER_SIGN_REJECT:
        vo = MapStructFactory.INSTANCE.toOrderOpenMessageVO(message);
        break;
      case ORDER_RETURN:
        vo = MapStructFactory.INSTANCE.toReturnOrderOpenMessageVO(message);
        break;
      case ORDER_CANCEL:
        vo = MapStructFactory.INSTANCE.toCancelOrderOpenMessageVO(message);
        break;
      case PRODUCT_AUDIT:
      case BRAND_AUDIT:
        vo = MapStructFactory.INSTANCE.toAuditOpenMessageVO(message);
        break;
      default:
        throw new CheckException("未知的消息类型");
    }
    return vo;
  }

  public OpenMessage create(OpenMessageSaveForm openMessageSaveForm) {
    OpenMessage openMessage = MapStructFactory.INSTANCE.toOpenMessage(openMessageSaveForm);
    openMessage.setCreateTime(System.currentTimeMillis());
    openMessage.setUpdateTime(System.currentTimeMillis());
    openMessage.setState(Constants.STATE_OK);
    openMessage.setMessageStatus(OpenMessageStatusEnum.DEFAULT.getCode());
    Map<String, Object> extraMap = openMessageSaveForm.getExtraMap();
    if (extraMap == null) {
      extraMap = new HashMap<>();
    }
    openMessage.setExtra(JSON.toJSONString(extraMap));
    return openMessage;
  }

  public OpenMessage update(OpenMessageSaveForm openMessageSaveForm) {
    OpenMessage openMessage = openMessageRepository.findById(openMessageSaveForm.getId())
        .orElseThrow(() -> new CheckException("消息不存在"));
    MapStructFactory.INSTANCE.updateOpenMessage(openMessageSaveForm, openMessage);
    openMessage.setUpdateTime(System.currentTimeMillis());
    Map<String, Object> extraMap = openMessage.getExtraMap();
    Map<String, Object> formExtraMap = openMessageSaveForm.getExtraMap();
    if (formExtraMap == null) {
      formExtraMap = new HashMap<>();
    }
    extraMap.putAll(formExtraMap);
    openMessage.setExtra(JSON.toJSONString(extraMap));
    return openMessage;
  }

}
