package com.xhgj.srm.forest.config;/**
 * @since 2025/2/28 9:26
 */

import com.xhiot.boot.forest.config.HandConfig;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

/**
 *<AUTHOR>
 *@date 2025/2/28 09:26:35
 *@description
 */
@Configuration
@RefreshScope
@ConfigurationProperties(prefix = "third.hand")
@Data
public class HandConfigExt extends HandConfig {

}
