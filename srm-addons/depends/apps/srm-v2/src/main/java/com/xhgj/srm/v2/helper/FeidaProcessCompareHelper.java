package com.xhgj.srm.v2.helper;

import lombok.extern.slf4j.Slf4j;

import java.lang.reflect.Field;
import java.util.*;

/**
 * 对象比较工具类 - 用于比较两个对象的差异，生成修改字段描述
 * <AUTHOR>
 */
@Slf4j
public class FeidaProcessCompareHelper {
  /**
   * 检查字段路径是否匹配指定的模式
   */
  private static boolean matchesPattern(String fieldPath, String pattern) {
    // 直接相等
    if (pattern.equals(fieldPath)) {
      return true;
    }

    // 处理 "abc.*" 模式
    if (pattern.endsWith(".*")) {
      String prefix = pattern.substring(0, pattern.length() - 2);
      return fieldPath.startsWith(prefix + ".");
    }

    // 处理包含 "[*]" 的模式
    if (pattern.contains("[*]")) {
      // 转换模式为正则表达式
      String regex = pattern
          .replace(".", "\\.")       // 转义点号
          .replace("[*]", "\\[\\d+\\]");  // [*] 替换为 [\d+]
      return fieldPath.matches(regex);
    }

    return false;
  }

  /**
   * 增强版的字段忽略检查
   */
  private static boolean isFieldIgnored(String fieldPath, Set<String> ignoredPatterns) {
    if (ignoredPatterns == null || ignoredPatterns.isEmpty()) {
      return false;
    }

    for (String pattern : ignoredPatterns) {
      if (matchesPattern(fieldPath, pattern)) {
        return true;
      }
    }

    return false;
  }

  /**
   * 动态配置的对象比较方法
   * @param newObj 新对象
   * @param originObj 原对象
   * @param ignoredPatterns 忽略的字段路径模式
   * @param fieldNameMapping 字段路径到友好名称的映射
   * @return 修改字段描述字符串
   */
  public <T> String compareProcessForms(T newObj, T originObj,
      Set<String> ignoredPatterns,
      Map<String, String> fieldNameMapping) {
    List<String> modifiedPaths = new ArrayList<>();
    compareRecursively(newObj, originObj, "", ignoredPatterns, modifiedPaths);

    // 转换字段路径为友好名称
    List<String> friendlyNames = new ArrayList<>();
    for (String path : modifiedPaths) {
      String friendlyName = getFieldFriendlyName(path, fieldNameMapping);
      friendlyNames.add(friendlyName);
    }

    // 去重并排序
    Set<String> uniqueNames = new LinkedHashSet<>(friendlyNames);
    return String.join(",", uniqueNames);
  }

  /**
   * 获取字段的友好名称，支持通配符匹配
   */
  private String getFieldFriendlyName(String path, Map<String, String> fieldNameMapping) {
    // 1. 精确匹配
    if (fieldNameMapping.containsKey(path)) {
      return fieldNameMapping.get(path);
    }

    // 2. 通配符匹配
    for (Map.Entry<String, String> entry : fieldNameMapping.entrySet()) {
      String pattern = entry.getKey();
      if (pattern.contains("[*]") && matchesPattern(path, pattern)) {
        return entry.getValue();
      }
    }

    // 3. 没有匹配返回原路径
    return path;
  }

  /**
   * 递归比较两个对象
   * @param newObj 新对象
   * @param originObj 原对象
   * @param currentPath 当前字段路径
   * @param ignoredPatterns 忽略的字段路径模式
   * @param modifiedFields 收集修改的字段列表
   */
  @SuppressWarnings("unchecked")
  private <T> void compareRecursively(T newObj, T originObj, String currentPath,
      Set<String> ignoredPatterns, List<String> modifiedFields) {
    if (newObj == null && originObj == null) {
      return;
    }

    // 处理一个为NULL，一个为空字符串或零值BigDecimal的情况
    if (newObj == null || originObj == null) {
      // 如果其中一个是null，另一个是空字符串，也认为相等
      boolean isNullAndEmptyString =
          (newObj == null && originObj instanceof String && "".equals(originObj)) ||
              (originObj == null && newObj instanceof String && "".equals(newObj));

      // 如果其中一个是null，另一个是BigDecimal且值为0，也认为相等
      boolean isNullAndZeroBigDecimal =
          (newObj == null && originObj instanceof java.math.BigDecimal &&
              ((java.math.BigDecimal) originObj).stripTrailingZeros().compareTo(java.math.BigDecimal.ZERO) == 0) ||
              (originObj == null && newObj instanceof java.math.BigDecimal &&
                  ((java.math.BigDecimal) newObj).stripTrailingZeros().compareTo(java.math.BigDecimal.ZERO) == 0);

      if (isNullAndEmptyString || isNullAndZeroBigDecimal) {
        return; // 认为没有修改
      }

      // 其他情况认为有修改
      addModifiedField(currentPath, modifiedFields);
      return;
    }

    Class<?> clazz = newObj.getClass();

    // 处理集合类型
    if (newObj instanceof List) {
      List<Object> newList = (List<Object>) newObj;
      List<Object> origList = (List<Object>) originObj;

      if (newList.size() != origList.size()) {
        addModifiedField(currentPath, modifiedFields);
        return;
      }

      for (int i = 0; i < newList.size(); i++) {
        compareRecursively(newList.get(i), origList.get(i),
            currentPath + "[" + i + "]", ignoredPatterns, modifiedFields);
      }
      return;
    }

    // 处理Map类型
    if (newObj instanceof Map) {
      Map<Object, Object> newMap = (Map<Object, Object>) newObj;
      Map<Object, Object> origMap = (Map<Object, Object>) originObj;

      Set<Object> allKeys = new HashSet<>();
      allKeys.addAll(newMap.keySet());
      allKeys.addAll(origMap.keySet());

      for (Object key : allKeys) {
        Object newValue = newMap.get(key);
        Object origValue = origMap.get(key);

        String keyPath = currentPath.isEmpty() ? key.toString() : currentPath + "." + key;
        compareRecursively(newValue, origValue, keyPath, ignoredPatterns, modifiedFields);
      }
      return;
    }

    // 处理基本类型或String
    if (isPrimitiveOrString(clazz)) {
      // 特殊处理BigDecimal类型
      if (newObj instanceof java.math.BigDecimal && originObj instanceof java.math.BigDecimal) {
        java.math.BigDecimal newBd = ((java.math.BigDecimal) newObj).stripTrailingZeros();
        java.math.BigDecimal origBd = ((java.math.BigDecimal) originObj).stripTrailingZeros();

        if (newBd.compareTo(origBd) != 0) {
          addModifiedField(currentPath, modifiedFields);
        }
        return;
      }
      if (!Objects.equals(newObj, originObj)) {
        addModifiedField(currentPath, modifiedFields);
      }
      return;
    }

    // 处理普通对象类型
    Field[] fields = clazz.getDeclaredFields();
    for (Field field : fields) {
      String fieldPath = currentPath.isEmpty() ? field.getName() : currentPath + "." + field.getName();

      // 检查是否忽略该字段
      if (isFieldIgnored(fieldPath, ignoredPatterns)) {
        continue;
      }

      field.setAccessible(true);
      try {
        Object newValue = field.get(newObj);
        Object origValue = field.get(originObj);

        // 递归比较
        compareRecursively(newValue, origValue, fieldPath, ignoredPatterns, modifiedFields);

      } catch (IllegalAccessException e) {
        log.error("比较字段值出错: {}", fieldPath, e);
      }
    }
  }

  /**
   * 添加修改的字段到结果列表
   */
  private void addModifiedField(String fieldPath, List<String> modifiedFields) {
    // 可以在这里实现字段路径到友好名称的映射
    // 例如将"data.head.item[0].matnr"映射为"物料编号"

    modifiedFields.add(fieldPath);
  }

  /**
   * 判断类型是否为基本类型或String
   */
  private boolean isPrimitiveOrString(Class<?> clazz) {
    return clazz.isPrimitive() ||
        clazz == String.class ||
        clazz == Boolean.class ||
        clazz == Character.class ||
        Number.class.isAssignableFrom(clazz);
  }
}
