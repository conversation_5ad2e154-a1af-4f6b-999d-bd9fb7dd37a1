package com.xhgj.srm.v2.vo.purchaseOrder;

import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.xhgj.srm.common.Constants;
import com.xhgj.srm.common.constants.ConstantsFinance;
import com.xhgj.srm.common.enums.PurchaseOrderTypeEnum;
import com.xhgj.srm.common.utils.supplierorder.BigDecimalUtil;
import com.xhgj.srm.jpa.entity.File;
import com.xhgj.srm.jpa.entity.SupplierOrder;
import com.xhgj.srm.jpa.entity.SupplierOrderDetail;
import com.xhgj.srm.jpa.entity.SupplierOrderProduct;
import com.xhgj.srm.jpa.entity.v2.SupplierOrderDetailV2;
import com.xhgj.srm.jpa.entity.v2.SupplierOrderProductV2;
import com.xhgj.srm.jpa.entity.v2.SupplierOrderV2;
import com.xhgj.srm.v2.dto.FileDTO;
import com.xhgj.srm.v2.form.purchaseOrder.BaseSupplierOrderV2DTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class PurchaseOrderProductV2ListVO extends BaseSupplierOrderV2DTO {

  @ApiModelProperty("供应商订单 id")
  private String id;

  @ApiModelProperty("创建时间")
  private Long createTime;

  @ApiModelProperty("订货件数")
  private BigDecimal num;

  @ApiModelProperty("金额")
  private BigDecimal totalPrice;

  @ApiModelProperty("入库进度")
  private String stockProgress;

  @ApiModelProperty("是否有未签收")
  private Boolean unReceipt;

  @ApiModelProperty("订单类型 1 金蝶采购订单 NB 标准采购（SAP） Z040 寄售 Z010 委外加工")
  private String orderType;

  @ApiModelProperty("采购部门")
  private String purchaseDept;

  @ApiModelProperty("采购员")
  private String purchaseMan;

  /** 物料编码 */
  @ApiModelProperty("物料编码")
  private String code;
  /** 品牌 */
  @ApiModelProperty("品牌")
  private String brand;

  /** 商品名称 */
  @ApiModelProperty("物料名称")
  private String name;
  /** 规格型号 */
  @ApiModelProperty("规格型号")
  private String manuCode;
  /**
   * 规格
   */
  @ApiModelProperty("规格")
  private String specification;

  /**
   * 型号
   */
  @ApiModelProperty("型号")
  private String model;

  /** 物料单位 */
  @ApiModelProperty("物料单位")
  private String unit;
  /** 交货时间 */
  @ApiModelProperty("交货时间")
  private Long deliverTime;
  /**
   * 采购订单交货时间
   */
  @ApiModelProperty("实际交货时间")
  private Long purchaseDeliverTime;

  /** 已发数量 */
  @ApiModelProperty("已发数量")
  private BigDecimal shipQty;
  /** 采购入库数量 */
  @ApiModelProperty("采购入库数量")
  private BigDecimal stockInputQty;
  /** 待入库数量 */
  @ApiModelProperty("待入库数量")
  private BigDecimal waitStockInputQty;
  /** 剩余入库数量 */
  @ApiModelProperty("剩余入库数量")
  private BigDecimal remainQty;
  /** 物料单价 */
  @ApiModelProperty("物料单价")
  private BigDecimal productPrice;
  @ApiModelProperty("税率")
  private String rate;
  @ApiModelProperty("仓库")
  private String warehouse;
  @ApiModelProperty("物料描述")
  private String description;

  /**
   * 去税单价
   */
  @ApiModelProperty("去税单价")
  private BigDecimal  taxFreeCbPrice;
  @ApiModelProperty("采购申请单号")
  private String purchaseApplyCode;
  @ApiModelProperty("业务员")
  private String salesman;
  @ApiModelProperty("跟单员")
  private String followUpPersonName;
  @ApiModelProperty("采购申请类型")
  private String applyForType;
  @ApiModelProperty("业务员所在公司名称")
  private String businessCompanyName;
  @ApiModelProperty("制单员名称")
  private String makeManName;
  /**
   * 售达方
   */
  @ApiModelProperty("售达方")
  private String soldToParty;

  @ApiModelProperty("是否预付款")
  private Boolean prePay = false;
  @ApiModelProperty("是否上传合同")
  private Boolean uploadContract = false;
  @ApiModelProperty("供应商订单详情id")
  private String supplierOrderDetailId;
  @ApiModelProperty("订单合同")
  private List<FileDTO> contractFiles;
  @ApiModelProperty("开票方")
  private String invoicingParty;
  @ApiModelProperty(value = "是否有冲销")
  private Boolean counteractState;

  @ApiModelProperty("开票状态")
  private String supplierOpenInvoiceState;
  @ApiModelProperty("退货数量")
  private BigDecimal returnQty;

  @ApiModelProperty("取消数量")
  private BigDecimal cancelQty;

  @ApiModelProperty("是否有内部备注")
  private Boolean internalRemarkFlag;

  @ApiModelProperty("内部备注内容")
  private String internalRemark;

  @ApiModelProperty("内部备注提交人")
  private String internalRemarkCreator;

  @ApiModelProperty("内部备注提交/更新时间")
  private Long internalRemarkUpdateTime;

  @ApiModelProperty("是否亏本订单")
  private Boolean loss;

  @ApiModelProperty("亏本原因")
  private String causeOfLoss;

  @ApiModelProperty("仓库编码")
  private String warehouseCode;

  @ApiModelProperty("采购组织名称")
  private String purchaseGroupName;

  public PurchaseOrderProductV2ListVO(
      SupplierOrderV2 supplierOrder,
      SupplierOrderProductV2 supplierOrderProduct,
      SupplierOrderDetailV2 supplierOrderDetail) {
    super(supplierOrder);
    loss = supplierOrder.getLoss();
    causeOfLoss = ObjectUtil.defaultIfNull(supplierOrder.getCauseOfLoss(), "-");
    this.id = StrUtil.emptyIfNull(supplierOrder.getId());
    //原逻辑为为空默认金蝶订单，此处不修改逻辑，只做替换
    this.orderType = StrUtil.emptyToDefault(supplierOrder.getOrderType(),
        PurchaseOrderTypeEnum.JIN_DIE.getKey());
    this.purchaseDept = supplierOrder.getPurchaseDept();
    this.purchaseMan = supplierOrder.getPurchaseMan();
    this.stockProgress = StrUtil.emptyIfNull(supplierOrder.getStockProgress());
    this.code = supplierOrderProduct.getCode();
    this.brand = supplierOrderProduct.getBrand();
    this.name = supplierOrderProduct.getName();
    this.manuCode = supplierOrderProduct.getManuCode();
    this.specification = supplierOrderProduct.getSpecification();
    this.model = supplierOrderProduct.getModel();
    this.unit = supplierOrderProduct.getUnit();
    this.createTime = supplierOrder.getOrderCreateTime();
    this.num = supplierOrderDetail.getNum();
    // todo 设置几位小数展示
    this.totalPrice =
        BigDecimalUtil.setScaleBigDecimalHalfUp(supplierOrderDetail.getPrice().multiply(supplierOrderDetail.getNum()), 2);
    this.deliverTime = supplierOrderDetail.getDeliverTime();
    this.purchaseDeliverTime = supplierOrderDetail.getPurchaseDeliverTime();
    this.shipQty = supplierOrderDetail.getShipQty();
    this.waitStockInputQty = supplierOrderDetail.getWaitStockInputQty();
    this.remainQty = supplierOrderDetail.getRemainQty();
    this.stockInputQty = supplierOrderDetail.getStockInputQty();
    this.productPrice = supplierOrderDetail.getPrice();
    this.rate =
        Optional.ofNullable(supplierOrderDetail.getTaxRate()).map(BigDecimal::toPlainString).orElse("-");
    this.description = supplierOrderDetail.getDescription();
    if (supplierOrderDetail.getPrice() != null) {
      BigDecimal rateDecimal =
          Optional.ofNullable(supplierOrderDetail.getTaxRate()).orElse(BigDecimal.ZERO);
      // 计算去税单价
      BigDecimal onePlusRate = BigDecimal.ONE.add(rateDecimal);
      this.taxFreeCbPrice = productPrice.divide(onePlusRate, 2, RoundingMode.HALF_UP);
    }
    this.supplierOrderDetailId = supplierOrderDetail.getId();
    this.invoicingParty = StrUtil.emptyIfNull(supplierOrder.getInvoicingParty());
    this.supplierOpenInvoiceState =
        StrUtil.emptyIfNull(Constants.ORDER_SUPPLIER_INVOICE_STATE_TYPE_OLD.get(supplierOrder.getSupplierOpenInvoiceState()));
    this.returnQty =
        BigDecimalUtil.setScaleBigDecimalHalfUp(supplierOrderDetail.getReturnQty(),
            supplierOrderProduct.getUnitDigit());
    this.cancelQty =
        BigDecimalUtil.setScaleBigDecimalHalfUp(supplierOrderDetail.getCancelQty(),
            supplierOrderProduct.getUnitDigit());
    // 内部备注
    this.internalRemarkFlag = StrUtil.isNotBlank(supplierOrder.getInternalRemark());
    this.internalRemark = supplierOrder.getInternalRemark();
    this.internalRemarkCreator = supplierOrder.getInternalRemarkCreator();
    this.internalRemarkUpdateTime = supplierOrder.getInternalRemarkUpdateTime();
    this.warehouseCode = supplierOrderDetail.getWarehouse();
    this.purchaseGroupName = supplierOrder.getGroupName();
  }

  public void makeContractFiles(List<File> files, String uploadPath) {
    ArrayList<FileDTO> fileDTOS = new ArrayList<>();
    for (File file : files) {
      FileDTO fileDTO = new FileDTO();
      fileDTO.setId(file.getId());
      fileDTO.setUrl(file.getUrl());
      fileDTO.setName(file.getName());
      fileDTO.setBaseUrl(uploadPath);
      if (StrUtil.isNotBlank(file.getDescription())) {
        String[] split = StrUtil.split(file.getDescription(), ".");
        String fileType = ArrayUtil.get(split, -1);
        fileDTO.setType(fileType);
      }
      fileDTOS.add(fileDTO);
    }
    this.setContractFiles(fileDTOS);
  }
}
