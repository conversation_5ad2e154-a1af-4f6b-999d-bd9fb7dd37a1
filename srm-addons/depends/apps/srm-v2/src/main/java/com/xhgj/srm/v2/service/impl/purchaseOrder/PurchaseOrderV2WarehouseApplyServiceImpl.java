package com.xhgj.srm.v2.service.impl.purchaseOrder;
/**
 * @since 2025/4/28 9:40
 */

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.NumberUtil;
import com.xhgj.srm.common.Constants;
import com.xhgj.srm.common.component.LockUtils;
import com.xhgj.srm.common.enums.supplierorder.SupplierOrderFormCallStatus;
import com.xhgj.srm.common.enums.supplierorder.SupplierOrderFormType;
import com.xhgj.srm.common.utils.supplierOrderForm.SupplierOrderFormCodeGenerator;
import com.xhgj.srm.jpa.entity.BaseSupplierOrderDetail;
import com.xhgj.srm.jpa.entity.User;
import com.xhgj.srm.jpa.entity.v2.SupplierOrderDetailV2;
import com.xhgj.srm.jpa.entity.v2.SupplierOrderProductV2;
import com.xhgj.srm.jpa.entity.v2.SupplierOrderToFormV2;
import com.xhgj.srm.jpa.entity.v2.SupplierOrderV2;
import com.xhgj.srm.jpa.repository.UserRepository;
import com.xhgj.srm.jpa.util.LazyLoaderContext;
import com.xhgj.srm.request.dto.hZero.process.SupplierOrderWarehouseApplyForm;
import com.xhgj.srm.request.service.third.hZero.HZeroService;
import com.xhgj.srm.v2.constants.PurchaseOrderV2Lock;
import com.xhgj.srm.v2.constants.SupplierOrderFormSource;
import com.xhgj.srm.v2.factory.PurchaseOrderWarehouseApplyV2Factory;
import com.xhgj.srm.v2.form.PurchaseOrderWarehouseApplyV2AddForm;
import com.xhgj.srm.v2.form.PurchaseOrderWarehouseApplyV2AddForm.PurchaseOrderWarehouseApplyAddFormDetail;
import com.xhgj.srm.v2.form.PurchaseOrderWarehouseV2AddForm;
import com.xhgj.srm.v2.form.PurchaseOrderWarehouseV2AddForm.PurchaseOrderWarehouseAddFormDetail;
import com.xhgj.srm.v2.form.WarehouseApplyQualityCheckCallback;
import com.xhgj.srm.v2.repository.SupplierOrderDetailV2Repository;
import com.xhgj.srm.v2.repository.SupplierOrderProductV2Repository;
import com.xhgj.srm.v2.repository.SupplierOrderToFormV2Repository;
import com.xhgj.srm.v2.repository.SupplierOrderV2Repository;
import com.xhgj.srm.v2.service.purchaseOrder.PurchaseOrderV2WarehouseApplyService;
import com.xhgj.srm.v2.service.purchaseOrder.PurchaseOrderV2WarehouseService;
import com.xhgj.srm.v2.vo.purchaseOrder.form.WarehouseApplyFormDetailVO;
import com.xhiot.boot.core.common.exception.CheckException;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.slf4j.MDC;
import org.springframework.stereotype.Service;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.support.TransactionSynchronizationAdapter;
import org.springframework.transaction.support.TransactionSynchronizationManager;
import org.springframework.transaction.support.TransactionTemplate;
import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class PurchaseOrderV2WarehouseApplyServiceImpl implements PurchaseOrderV2WarehouseApplyService {

  @Resource
  LockUtils lockUtils;
  @Resource
  PurchaseOrderWarehouseApplyV2Factory purchaseOrderWarehouseApplyV2Factory;
  @Resource
  SupplierOrderDetailV2Repository supplierOrderDetailV2Repository;
  @Resource
  SupplierOrderToFormV2Repository supplierOrderToFormV2Repository;
  @Resource
  SupplierOrderV2Repository supplierOrderV2Repository;
  @Resource
  private PlatformTransactionManager transactionManager;
  @Resource
  PurchaseOrderV2WarehouseService purchaseOrderV2WarehouseService;
  @Resource
  SupplierOrderProductV2Repository supplierOrderProductV2Repository;
  @Resource
  HZeroService hZeroService;
  @Resource
  UserRepository userRepository;

  @Override
  public void warehouseApply(PurchaseOrderWarehouseApplyV2AddForm form, User user) {
    // 采购订单id
    String purchaserOrderId = form.getPurchaserOrderId();
    List<RLock> rLocks = lockUtils.lockAll(new HashSet<>(Collections.singleton(purchaserOrderId)),
        PurchaseOrderV2Lock.PURCHASE_ORDER_WAREHOUSE_APPLY_LOCK);
    // 开启事务
    TransactionTemplate transactionTemplate = new TransactionTemplate(transactionManager);
    try {
      transactionTemplate.execute(status -> {
        TransactionSynchronizationManager.registerSynchronization(
            new TransactionSynchronizationAdapter(){
              @Override
              public void afterCompletion(int status) {
                SupplierOrderFormCodeGenerator.clear();
              }
            }
        );
        // 根据物料id查询明细
        List<String> detailIds = form.getProductDetailList().stream()
            .map(PurchaseOrderWarehouseApplyAddFormDetail::getId).collect(Collectors.toList());
        // 创建ID到索引的映射，用于排序
        Map<String, Integer> idToIndexMap = new HashMap<>();
        for (int i = 0; i < detailIds.size(); i++) {
          idToIndexMap.put(detailIds.get(i), i);
        }
        List<SupplierOrderDetailV2> details = supplierOrderDetailV2Repository.findAllById(detailIds);
        // 对details进行排序，使其与表单中的顺序一致
        details.sort(
            Comparator.comparing(detail -> idToIndexMap.getOrDefault(detail.getId(), Integer.MAX_VALUE)));
        // ----------基础校验----------
          // #check 判断采购订单是否存在
        SupplierOrderV2 supplierOrder =
            supplierOrderV2Repository.findById(purchaserOrderId).orElseThrow(() -> new CheckException(
                "采购订单不存在"));
        // #check 基础校验，判断是否为空 + 是否本次申请数量超过待申请入库数量
        purchaseOrderWarehouseApplyV2Factory.checkChooseProductDetails(form, details);
        // #check 入库仓库是直销库时，快递公司和物流单号不能为空
        purchaseOrderWarehouseApplyV2Factory.checkLogisticsInfo(form);
        // -----------数据生成----------
          // 0.根据是否需要质检区分不同的明细
          // 无需质检
        List<SupplierOrderDetailV2> noQualityCheckList =
            details.stream().filter(item -> !Boolean.TRUE.equals(item.getSupplierOrderProduct().getQualityCheck()))
                .collect(Collectors.toList());
        // 需要质检
        List<SupplierOrderDetailV2> qualityCheckList =
            details.stream().filter(item -> Boolean.TRUE.equals(item.getSupplierOrderProduct().getQualityCheck()))
                .collect(Collectors.toList());
        // !!! 特殊情况来源SAP则不需要质检
        if (form.getSource().equals(SupplierOrderFormSource.SAP)) {
          noQualityCheckList = details;
          qualityCheckList.clear();
        }
        // #check校验，判断是否行数量超过400条
        purchaseOrderWarehouseApplyV2Factory.checkChooseProductDetails(noQualityCheckList);
        purchaseOrderWarehouseApplyV2Factory.checkChooseProductDetails(qualityCheckList);
          // 1.生成无需质检的入库单
        SupplierOrderToFormV2 warehouseApplyNoQualityCheckForm =
            purchaseOrderWarehouseApplyV2Factory.createWarehouseApplyNoQualityCheck(form,
                noQualityCheckList, user);
        if (warehouseApplyNoQualityCheckForm != null) {
          supplierOrderToFormV2Repository.saveAndFlush(warehouseApplyNoQualityCheckForm);
          // copy物料明细至入库单明细
          List<SupplierOrderDetailV2> inApplyDetails =
              purchaseOrderWarehouseApplyV2Factory.createWarehouseApplyDetails(form,
                  warehouseApplyNoQualityCheckForm, noQualityCheckList);
          // !!! 特殊情况来源SAP刷新质检数量
          if (form.getSource().equals(SupplierOrderFormSource.SAP)) {
            AtomicInteger index = new AtomicInteger();
            inApplyDetails.forEach(item -> item.setInspectQty(form.getProductDetailList().get(index.getAndIncrement()).getQualityNum()));
          }
          supplierOrderDetailV2Repository.saveAll(inApplyDetails);
          // 更新物料明细
          supplierOrderDetailV2Repository.saveAll(noQualityCheckList);
          supplierOrderDetailV2Repository.flush();
          // -----------调用入库单创建----------
          PurchaseOrderWarehouseV2AddForm warehouseV2AddForm = purchaseOrderWarehouseApplyV2Factory.createWarehouseAddForm(
              form,
              warehouseApplyNoQualityCheckForm,
              inApplyDetails
          );
          purchaseOrderV2WarehouseService.warehouseAdd(warehouseV2AddForm, user);
        }
        // 2.生成需质检的入库单
        SupplierOrderToFormV2 warehouseApplyQualityCheckForm =
            purchaseOrderWarehouseApplyV2Factory.createWarehouseApplyQualityCheck(form,
                qualityCheckList, user);
        if (warehouseApplyQualityCheckForm != null) {
          supplierOrderToFormV2Repository.saveAndFlush(warehouseApplyQualityCheckForm);
          // copy物料明细至入库单明细
          List<SupplierOrderDetailV2> inApplyDetails =
              purchaseOrderWarehouseApplyV2Factory.createWarehouseApplyDetails(form,
                  warehouseApplyQualityCheckForm, qualityCheckList);
          supplierOrderDetailV2Repository.saveAll(inApplyDetails);
          // 更新物料明细
          supplierOrderDetailV2Repository.saveAll(qualityCheckList);
          supplierOrderDetailV2Repository.flush();
          // -----------调用飞搭接口创建数据----------
          String traceId = MDC.get("TRACE_ID");
          TransactionSynchronizationManager.registerSynchronization(
              new TransactionSynchronizationAdapter(){
                @Override
                public void afterCommit() {
                  // 事务提交后执行异步任务
                  CompletableFuture.runAsync(() -> {
                    MDC.put("TRACE_ID", traceId);
                    try {
                      syncFeida(supplierOrder, warehouseApplyQualityCheckForm, inApplyDetails);
                    } catch (Exception e) {
                      log.error("入库申请单飞搭调用创建失败", e);
                    }
                  });
                }
              }
          );
          // 3.更新supplierOrder
          if (supplierOrder.getFirstShipTime() == null) {
            supplierOrder.setFirstShipTime(System.currentTimeMillis());
          }
          supplierOrder.setOrderConfirmState(false);
          if (supplierOrder.getConfirmTime() == null) {
            supplierOrder.setConfirmTime(System.currentTimeMillis());
          }
          // 查看所有明细行的待申请入库数量是否为0
          boolean allDetailsZero = true;
          for (SupplierOrderDetailV2 detail : details) {
            if (detail.getWaitQty().compareTo(BigDecimal.ZERO) > 0) {
              allDetailsZero = false;
              break;
            }
          }
          if (allDetailsZero) {
            supplierOrder.setCompleteOrderTime(System.currentTimeMillis());
          }
          supplierOrderV2Repository.saveAndFlush(supplierOrder);
        }
        return null;
      });

    } catch (Exception e) {
      SupplierOrderFormCodeGenerator.INSTANCE.rollbackOrderNumber();
      throw e;
    } finally {
      lockUtils.unlockAllLocks(rLocks);
    }
  }

  @Override
  public List<WarehouseApplyFormDetailVO> getWarehouseApplyDetail(String supplierOrderId) {
    // 根据采购订单id查询入库申请单
    List<SupplierOrderToFormV2> warehouseApplyList = supplierOrderToFormV2Repository.findBySupplierOrderIdAndTypeAndState(supplierOrderId, SupplierOrderFormType.DELIVER.getType(), Constants.STATE_OK);
    if (CollUtil.isEmpty(warehouseApplyList)) {
      return Collections.emptyList();
    }
    // 查询入库申请单明细
    List<String> warehouseApplyIds = warehouseApplyList.stream().map(SupplierOrderToFormV2::getId).collect(Collectors.toList());
    List<SupplierOrderDetailV2> warehouseApplyDetails = supplierOrderDetailV2Repository.findAllByOrderToFormIdInAndState(warehouseApplyIds, Constants.STATE_OK);
    Map<String, List<SupplierOrderDetailV2>> warehouseApplyDetailsMap = warehouseApplyDetails.stream()
        .collect(Collectors.groupingBy(SupplierOrderDetailV2::getOrderToFormId));
    return purchaseOrderWarehouseApplyV2Factory.buildDetailVOS(warehouseApplyList, warehouseApplyDetailsMap);
  }

  @Override
  public void warehouseApplyQualityCheckCallback(List<WarehouseApplyQualityCheckCallback> callBackList) {
    // 对于同一个入库申请单处理
    // 1.根据入库申请单ID分组
    Map<String, List<WarehouseApplyQualityCheckCallback>> map = callBackList.stream()
        .collect(Collectors.groupingBy(WarehouseApplyQualityCheckCallback::getWarehouseApplyCode));
    for (String warehouseApplyCode : map.keySet()) {
      SupplierOrderToFormV2 inApplyForm =
          supplierOrderToFormV2Repository.findFirstByFormCodeAndState(warehouseApplyCode,
                  Constants.STATE_OK)
          .orElseThrow(() -> new CheckException("入库申请单不存在"));
      // 锁定入库申请单
      List<RLock> rLocks =
          lockUtils.lockAll(new HashSet<>(Collections.singleton(inApplyForm.getSupplierOrderId())),
              PurchaseOrderV2Lock.PURCHASE_ORDER_WAREHOUSE_APPLY_LOCK);
      // 开启事务
      TransactionTemplate transactionTemplate = new TransactionTemplate(transactionManager);
      try {
        transactionTemplate.execute(status -> {
          this.handlerWarehouseApplyQualityCheckCallback(inApplyForm,
              map.getOrDefault(warehouseApplyCode, new ArrayList<>()));
          return null;
        });
      }finally {
        lockUtils.unlockAllLocks(rLocks);
      }
    }
  }

  private void handlerWarehouseApplyQualityCheckCallback(SupplierOrderToFormV2 inApplyForm, List<WarehouseApplyQualityCheckCallback> callBackList) {
    if (CollUtil.isEmpty(callBackList)) {
      throw new CheckException("质检回调数据不能为空");
    }
    List<SupplierOrderDetailV2> warehouseApplyDetailList = LazyLoaderContext.lazyLoad(
        () -> supplierOrderDetailV2Repository.findAllByOrderToFormIdInAndState(
            Collections.singletonList(inApplyForm.getId()), Constants.STATE_OK));
    // 通过入库申请单id查询入库申请单明细
    List<String> indexes =
        callBackList.stream().map(WarehouseApplyQualityCheckCallback::getWarehouseApplyIndex)
            .collect(Collectors.toList());
    warehouseApplyDetailList = warehouseApplyDetailList.stream().filter(item -> indexes.contains(item.getIndex()))
        .collect(Collectors.toList());
    if (CollUtil.isEmpty(warehouseApplyDetailList) || indexes.size() != warehouseApplyDetailList.size()) {
      throw new CheckException("入库申请单明细不存在");
    }
    List<String> detailIds =
        warehouseApplyDetailList.stream().map(BaseSupplierOrderDetail::getDetailedId).distinct()
            .collect(Collectors.toList());
    detailIds.add("-1");
    // 查询采购订单明细
    List<SupplierOrderDetailV2> detailList =
        LazyLoaderContext.lazyLoad(() -> supplierOrderDetailV2Repository.findAllById(detailIds));
    if (CollUtil.isEmpty(detailList)) {
      throw new CheckException("采购订单明细不存在");
    }
    // 查询物料明细
    List<String> productIds = detailList.stream().map(BaseSupplierOrderDetail::getOrderProductId)
        .collect(Collectors.toList());
    productIds.add("-1");
    List<SupplierOrderProductV2> supplierOrderProductV2List =
        supplierOrderProductV2Repository.findAllById(productIds);
    Map<String, SupplierOrderProductV2> productMap =
        supplierOrderProductV2List.stream().collect(Collectors.toMap(SupplierOrderProductV2::getId,
            item -> item));
    // 相关detail填充productMap
    detailList.forEach(item -> {
      SupplierOrderProductV2 product = productMap.get(item.getOrderProductId());
      if (product != null) {
        item.setSupplierOrderProduct(product);
      }
    });
    warehouseApplyDetailList.forEach(item -> {
      SupplierOrderProductV2 product = productMap.get(item.getOrderProductId());
      if (product != null) {
        item.setSupplierOrderProduct(product);
      }
    });
    Map<String, SupplierOrderDetailV2> warehouseApplyDetailMap =
        warehouseApplyDetailList.stream().collect(Collectors.toMap(SupplierOrderDetailV2::getIndex,
            item -> item));
    Map<String, SupplierOrderDetailV2> detailMap =
        detailList.stream().collect(Collectors.toMap(SupplierOrderDetailV2::getId, item -> item));
    List<PurchaseOrderWarehouseAddFormDetail> add2InFormDetails = new ArrayList<>();
    for (WarehouseApplyQualityCheckCallback callbackDetail : callBackList) {
      SupplierOrderDetailV2 warehouseApplyDetail = warehouseApplyDetailMap.get(callbackDetail.getWarehouseApplyIndex());
      // 查询采购订单明细
      SupplierOrderDetailV2 detail = detailMap.get(warehouseApplyDetail.getDetailedId());
      // 获取回调参数
      BigDecimal inspectQty =
          Optional.ofNullable(callbackDetail.getInspectQty()).orElse(BigDecimal.ZERO); // 本次质检数量
      BigDecimal confirmQty =
          Optional.ofNullable(callbackDetail.getConfirmQty()).orElse(BigDecimal.ZERO); // 确认入库数量
      BigDecimal cancelQty =
          Optional.ofNullable(callbackDetail.getCancelQty()).orElse(BigDecimal.ZERO); // 拒绝入库数量
      // 更新入库申请单明细
      // @入库申请单 已质检数量 ++
      warehouseApplyDetail.setInspectQty(NumberUtil.add(warehouseApplyDetail.getInspectQty(), inspectQty));
      // @入库申请单 取消入库数量 ++
      warehouseApplyDetail.setCancelQty(NumberUtil.add(warehouseApplyDetail.getCancelQty(), cancelQty));
      // 更新物料明细
      // @物料明细 待申请入库数量 ++
      detail.setWaitQty(NumberUtil.add(detail.getWaitQty(), cancelQty));
      // @物料明细 已申请入库数量 --
      detail.setShipQty(NumberUtil.sub(detail.getShipQty(), cancelQty));
      //  校验质检数量不超过申请入库数量
      BigDecimal applyNum = warehouseApplyDetail.getNum();
      if (NumberUtil.isGreater(inspectQty, applyNum)) {
        throw new CheckException("质检数量不能大于申请入库数量");
      }
      // 确认入库数量生成入库单
      if (confirmQty.compareTo(BigDecimal.ZERO) > 0) {
        PurchaseOrderWarehouseAddFormDetail add2InFormDetail = new PurchaseOrderWarehouseAddFormDetail();
        add2InFormDetail.setId(warehouseApplyDetail.getId());
        add2InFormDetail.setApplyNum(confirmQty);
        add2InFormDetails.add(add2InFormDetail);
      }
    }
    // 如果确认入库数量大于0，生成入库单
    if (CollUtil.isNotEmpty(add2InFormDetails)) {
      PurchaseOrderWarehouseV2AddForm addForm = new PurchaseOrderWarehouseV2AddForm();
      addForm.setPurchaseOrderId(inApplyForm.getSupplierOrderId());
      addForm.setWarehouseApplyId(inApplyForm.getId());
      addForm.setProductDetailList(add2InFormDetails);
      // 获取inApplyForm的创建用户
      User user = userRepository.findById(inApplyForm.getCreateUser())
          .orElseThrow(() -> new CheckException("入库申请单创建用户不存在"));
      purchaseOrderV2WarehouseService.warehouseAdd(addForm, user);
    }
    // 保存入库申请单明细
    // 保存物料明细
    supplierOrderDetailV2Repository.saveAll(warehouseApplyDetailList);
    supplierOrderDetailV2Repository.saveAll(detailList);
    supplierOrderDetailV2Repository.flush();
  }

  @Override
  public void syncFeida(String supplierOrderFormId) {
    // 通过formId查询入库申请单
    SupplierOrderToFormV2 inApplyForm =
        supplierOrderToFormV2Repository.findById(supplierOrderFormId)
            .orElseThrow(() -> new CheckException("入库申请单不存在"));
    // 判断是否调用失败
    if (!SupplierOrderFormCallStatus.CALL_FAIL.getStatus().equals(inApplyForm.getCallStatus())) {
      throw new CheckException("推送三方失败的入库申请单才可以重试");
    }
    // 查询采购订单
    SupplierOrderV2 supplierOrder =
        supplierOrderV2Repository.findById(inApplyForm.getSupplierOrderId())
            .orElseThrow(() -> new CheckException("采购订单不存在"));
    // 通过formId查询入库申请单明细
    List<SupplierOrderDetailV2> inApplyDetails =
        supplierOrderDetailV2Repository.findAllByOrderToFormIdInAndState(
            Collections.singletonList(inApplyForm.getId()), Constants.STATE_OK);
    this.syncFeida(supplierOrder, inApplyForm, inApplyDetails);
  }

  /**
   * 调用飞搭接口创建数据
   */
  private void syncFeida(SupplierOrderV2 supplierOrder, SupplierOrderToFormV2 inApplyForm, List<SupplierOrderDetailV2> inApplyDetails) {
    if (CollUtil.isEmpty(inApplyDetails)) {
      return;
    }
    try {
      List<SupplierOrderWarehouseApplyForm> forms =
          purchaseOrderWarehouseApplyV2Factory.createFeidaData(supplierOrder, inApplyForm, inApplyDetails);
      hZeroService.createWarehouseApplyQualityCheck(forms);
      inApplyForm.setCallStatus(SupplierOrderFormCallStatus.CALL_SUCCESS.getStatus());
    } catch (Exception e) {
      inApplyForm.setCallStatus(SupplierOrderFormCallStatus.CALL_FAIL.getStatus());
      throw e;
    }finally {
      inApplyForm.setCallTime(System.currentTimeMillis());
      inApplyForm.setUpdateTime(System.currentTimeMillis());
      supplierOrderToFormV2Repository.saveAndFlush(inApplyForm);
    }

  }
}
