package com.xhgj.srm.v2.helper;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.xhgj.srm.common.Constants;
import com.xhgj.srm.common.enums.purchase.order.PurchaseApplyOperationPermissionsEnum;
import com.xhgj.srm.jpa.entity.Group;
import com.xhgj.srm.jpa.entity.User;
import com.xhgj.srm.jpa.entity.UserToGroup;
import com.xhgj.srm.jpa.repository.GroupRepository;
import com.xhgj.srm.jpa.repository.UserRepository;
import com.xhgj.srm.jpa.repository.UserToGroupRepository;
import com.xhiot.boot.core.common.exception.CheckException;

import java.util.List;
import java.util.Objects;

/**
 * 采购权限校验辅助类
 * 提供采购申请相关权限验证的方法
 */
public class PurchaseApplyPermissionChecker {

    private final UserRepository userRepository;
    private final GroupRepository groupRepository;
    private final UserToGroupRepository userToGroupRepository;

    public PurchaseApplyPermissionChecker(
            UserRepository userRepository,
            GroupRepository groupRepository,
            UserToGroupRepository userToGroupRepository) {
        this.userRepository = userRepository;
        this.groupRepository = groupRepository;
        this.userToGroupRepository = userToGroupRepository;
    }

    /**
     * 检查用户是否为管理员
     */
    public boolean isAdminUser(User user) {
        return Objects.equals(user.getRealName(), Constants.ADMIN_REAL_NAME)
                && Objects.equals(user.getName(), Constants.USERNAME);
    }

    /**
     * 验证权限编码是否有效
     */
    public void validatePermissionCode(String permissionCode) {
        if (permissionCode == null) {
            throw new CheckException("用户权限配置异常，请联系管理员");
        }
    }

    /**
     * 获取用户所属部门/组织
     */
    public List<UserToGroup> getUserGroups(String userId) {
        List<UserToGroup> userToGroups =
                userToGroupRepository.findAllByUserIdAndState(userId, Constants.STATE_OK);
        if (CollUtil.isEmpty(userToGroups)) {
            throw new CheckException("当前用户没有部门/组织信息");
        }
        return userToGroups;
    }

    /**
     * 获取采购员用户信息
     */
    public User getPurchaserUser(String purchaseManNumber) {
        return userRepository.findFirstByCodeAndState(purchaseManNumber, Constants.STATE_OK)
                .orElseThrow(() -> new CheckException("采购申请单采购员信息异常"));
    }

    /**
     * 获取采购部门信息
     */
    public Group getProcurementDepartment(String departmentCode) {
        if (StrUtil.isBlank(departmentCode)) {
            throw new CheckException("采购部门编码不能为空");
        }
        Group department = groupRepository.findFirstByErpCodeAndState(departmentCode, Constants.STATE_OK);
        if (department == null) {
            throw new CheckException("采购订单采购部门信息异常");
        }
        return department;
    }

    /**
     * 验证采购员权限
     */
    public void validatePurchaserPermission(User currentUser, User purchaserUser) {
        boolean isPurchaser = currentUser.equals(purchaserUser);
        throwCheckPurchaserException(isPurchaser,
                PurchaseApplyOperationPermissionsEnum.PURCHASER.getDescription());
    }

    /**
     * 验证部门权限
     */
    public void validateDepartmentPermission(List<UserToGroup> userToGroups, Group department) {
        boolean hasDepartmentPermission = checkPurchaserByDepartment(userToGroups, department);
        throwCheckPurchaserException(hasDepartmentPermission,
                PurchaseApplyOperationPermissionsEnum.DEPARTMENT.getDescription());
    }

    /**
     * 验证部门及其下属部门权限
     */
    public void validateDepartmentAndSubordinatesPermission(List<UserToGroup> userToGroups, Group department) {
        // 首先检查是否在当前部门
        if (checkPurchaserByDepartment(userToGroups, department)) {
            return;
        }

        // 检查是否在子部门
        List<Group> branches = groupRepository.findAllByParentIdsAndTypeAndState(
                department.getId(), Constants.GROUPTYPE_MAP_DEPT, Constants.STATE_OK);

        boolean inSubDepartment = false;
        for (Group branch : CollUtil.emptyIfNull(branches)) {
            for (UserToGroup userToGroup : userToGroups) {
                if (Objects.equals(branch.getId(), userToGroup.getDeptId())) {
                    inSubDepartment = true;
                    break;
                }
            }
            if (inSubDepartment) {
                break;
            }
        }

        throwCheckPurchaserException(inSubDepartment,
                PurchaseApplyOperationPermissionsEnum.DEPARTMENT_AND_SUBORDINATES.getDescription());
    }

    /**
     * 验证组织权限
     */
    public void validateOrganizationPermission(List<UserToGroup> userToGroups, String organizationCode) {
        Group organization = groupRepository.findFirstByErpCodeAndState(
                organizationCode, Constants.STATE_OK);
        if (organization == null) {
            throw new CheckException("采购申请单没有采购组织信息，请联系管理员");
        }

        boolean inOrganization = false;
        for (UserToGroup userToGroup : userToGroups) {
            if (Objects.equals(organization.getId(), userToGroup.getGroupId())) {
                inOrganization = true;
                break;
            }
        }

        throwCheckPurchaserException(inOrganization,
                PurchaseApplyOperationPermissionsEnum.ORGANIZATION.getDescription());
    }

    /**
     * 用户在指定部门中检查
     */
    public boolean checkPurchaserByDepartment(List<UserToGroup> userToGroups, Group department) {
        for (UserToGroup userToGroup : userToGroups) {
            if (Objects.equals(department.getId(), userToGroup.getDeptId())) {
                return true;
            }
        }
        return false;
    }

    /**
     * 如果验证未通过则抛出异常
     */
    public void throwCheckPurchaserException(boolean isAuthorized, String placeholder) {
        if (!isAuthorized) {
            throw new CheckException(
                    StrUtil.format("您只能修改您{}内的采购申请，如有疑问或要调整权限范围请联系管理员", placeholder));
        }
    }
}