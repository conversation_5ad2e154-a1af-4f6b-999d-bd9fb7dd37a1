package com.xhgj.srm.v2.vo.purchaseOrder.form;

import com.alibaba.fastjson.annotation.JSONField;
import com.alibaba.fastjson.annotation.JSONType;
import com.alibaba.fastjson.serializer.SerializerFeature;
import lombok.Data;
import java.math.BigDecimal;
import java.util.List;

/**
 *<AUTHOR>
 *@date 2025/5/15 10:25:14
 *@description 采购入库申请单VO对象
 */
@Data
public class WarehouseApplyFormDetailVO {

  /**
   * 入库申请单id
   */
  private String id;

  /**
   * 入库申请单号
   */
  private String formCode;

  /**
   * 来源
   */
  private String source;

  /**
   * 是否质检 -- 如果为NULL 则显示未知
   */
  @JSONField(serialzeFeatures = SerializerFeature.WriteMapNullValue)
  private Boolean qualityCheck;

  /**
   * 创建时间
   */
  private Long createTime;

  /**
   * 过账日期
   */
  private Long postingDate;

  /**
   * 物流公司
   */
  private String logisticsCompany;

  /**
   * 物流编码
   */
  private String logisticsCode;

  /**
   * 快递单号
   */
  private String trackNum;

  /**
   * 物流状态
   * @see com.xhgj.srm.common.enums.supplierorder.SupplierOrderFormStatus
   */
  private String status;

  /**
   * 审核状态
   * @see com.xhgj.srm.common.enums.supplierorder.SupplierOrderFormReviewStatus
   */
  private Byte reviewStatus;

  /**
   * 三方接口调用状态
   * @see com.xhgj.srm.common.enums.supplierorder.SupplierOrderFormCallStatus
   */
  private Byte callStatus;

  /**
   * 三方接口调用时间
   */
  private Long callTime;

  /**
   * 仓库
   */
  private String warehouseName;

  /**
   * 仓库编码
   */
  private String warehouseCode;

  /**
   * 入库申请单明细
   */
  private List<WarehouseApplyDetailVO> details;



  @Data
  public static class WarehouseApplyDetailVO{
    /**
     * id
     */
    private String id;

    /**
     * index
     */
    private String index;

    /**
     * 物料编码
     */
    private String productCode;

    /**
     * 品牌
     */
    private String brand;

    /**
     * 物料名称
     */
    private String productName;

    /**
     * 规格
     */
    private String specification;

    /**
     * 型号
     */
    private String model;

    /**
     * 申请入库数量
     */
    private BigDecimal num;

    /**
     * 仓库
     */
    private String warehouseName;

    /**
     * 仓库编码
     */
    private String warehouseCode;

    /**
     * 是否质检
     */
    private Boolean qualityCheck;

    /**
     * 已质检数量
     */
    private BigDecimal inspectQty;

    /**
     * 已入库数量
     */
    private BigDecimal stockInputQty;

    /**
     * 取消入库数量
     */
    private BigDecimal cancelQty;

    /**
     * 单位
     */
    private String unit;

    /**
     * 单价
     */
    private BigDecimal price;

    /**
     * 单位编码
     */
    private String unitCode;

    /**
     * 采购订单物料行id
     */
    private Integer sortNum;
  }


}
