package com.xhgj.srm.v2.vo;/**
 * @since 2025/4/17 18:07
 */

import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import com.xhgj.srm.jpa.entity.PurchaseApplyForOrder;
import com.xhgj.srm.jpa.entity.v2.PurchaseApplyForOrderV2;
import com.xhgj.srm.v2.dto.FileDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.math.BigDecimal;
import java.util.List;

/**
 *<AUTHOR>
 *@date 2025/4/17 18:07:09
 *@description
 */
@Data
@NoArgsConstructor
public class PurchaseApplyForOrderV2ListVO {
  @ApiModelProperty("id")
  private String id;
  @ApiModelProperty("申请单号")
  private String applyForOrderNo;
  @ApiModelProperty("申请类型")
  private String applyForType;
  /** 订货状态
   * {@link com.xhgj.srm.common.enums.OrderGoodsStateV2Enum}
   **/
  @ApiModelProperty("订货状态：1.可订货，2.订货完成，4.锁定")
  private String orderGoodsState;
  @ApiModelProperty("创建日期")
  private Long createTime;
  @ApiModelProperty("物料编码")
  private String productCode;
  @ApiModelProperty("仓库")
  private String warehouse;
  @ApiModelProperty("品牌")
  private String brand;
  @ApiModelProperty("物料名称")
  private String productName;
  @ApiModelProperty("型号")
  private String model;
  @ApiModelProperty("规格")
  private String specification;
  @ApiModelProperty("单位名称")
  private String unit;
  @ApiModelProperty("单位编码")
  private String unitCode;
  @ApiModelProperty("申请数量")
  private BigDecimal applyForNumber;
  @ApiModelProperty("已订货数量")
  private BigDecimal orderGoodsNumber;
  @ApiModelProperty("未订货数量")
  private String unorderedQuantity;
  @ApiModelProperty("计划需求日期")
  private Long planDemandDate;
  @ApiModelProperty("采购员")
  private String purchaseMan;
  @ApiModelProperty("售达方")
  private String soldToParty;
  @ApiModelProperty("销售订单号")
  private String saleOrderNo;
  @ApiModelProperty("采购部门")
  private String purchaseDepartment;
  @ApiModelProperty("取消状态")
  private String cancellationState;
  @ApiModelProperty("下推状态")
  private String pushDownState;
  @ApiModelProperty("物料序号")
  private Integer serialNumber;
  @ApiModelProperty("物料描述")
  private String materialDescription;
  @ApiModelProperty("销售单价")
  private BigDecimal salesUnitPrice;
  @ApiModelProperty("销售需求数量")
  private BigDecimal salesDemandQuantity;
  @ApiModelProperty("MPM参考结算价")
  private BigDecimal mpmReferenceSettlementPrice;
  @ApiModelProperty("业务员")
  private String salesman;
  @ApiModelProperty("发货方式")
  private String deliveryType;
  @ApiModelProperty("客户订单号")
  private String orderNo;
  @ApiModelProperty("跟单员")
  private String merchandiser;
  @ApiModelProperty("项目编码")
  private String projectNo;
  @ApiModelProperty("大票项目名称")
  private String largeTicketProjectName;
  @ApiModelProperty("物料备注")
  private String materialLineRemarks;
  @ApiModelProperty("收件地址")
  private String deliveryAddress;
  @ApiModelProperty("收件人联系方式")
  private String contactInformation;

  @ApiModelProperty("收件人")
  private String consignee;

  @ApiModelProperty("申请单备注")
  private String applicationFormRemarks;


  @ApiModelProperty("打印次数")
  private Integer printNumber;

  @ApiModelProperty("销售订单行项目")
  private String saleOrderProductRowId;

  @ApiModelProperty("附件客户资料")
  private List<FileDTO> customerFileList;

  @ApiModelProperty("是否急单:Y/N")
  private String isWorryOrder;

  @ApiModelProperty("是否直发：1是，0否")
  private String directShipment;

  @ApiModelProperty("资料卡片:编码+名称")
  private String profileCard;

  @ApiModelProperty("资料卡片:编码")
  private String profileCardCode;


  /**
   * 科目分配类别:编码+名称
   */
  @ApiModelProperty("科目分配类别:编码+名称")
  private String assignmentCategory;

  /**
   * 科目分配类别编码
   */
  @ApiModelProperty("科目分配类别编码")
  private String assignmentCategoryCode;

  @ApiModelProperty("科目分配类别名称")
  private String assignmentCategoryName;

  /**
   * 项目类别:编码+名称
   */
  @ApiModelProperty("项目类别:编码+名称")
  private String projectCategory;

  /**
   * 项目类别编码
   */
  @ApiModelProperty("项目类别编码")
  private String projectCategoryCode;

  @ApiModelProperty("项目类别名称")
  private String projectCategoryName;

  /**
   * 总账科目:编码+名称
   */
  @ApiModelProperty("总账科目:编码+名称")
  private String ledgerSubject;

  @ApiModelProperty("总账科目编码")
  private String ledgerSubjectCode;

  @ApiModelProperty("总账科目名称")
  private String ledgerSubjectName;


  /**
   * 成本中心:编码+名称
   */
  @ApiModelProperty("成本中心:编码+名称")
  private String costCenter;

  @ApiModelProperty("成本中心编码")
  private String costCenterCode;

  @ApiModelProperty("成本中心名称")
  private String costCenterName;


  /**
   * 订单:编码+名称
   */
  @ApiModelProperty("订单:编码+名称")
  private String order;

  @ApiModelProperty("订单编码")
  private String orderCode;

  @ApiModelProperty("订单名称")
  private String orderName;

  /**
   * 交货日期
   */
  @ApiModelProperty("交货日期")
  private Long deliverTime;


  /**
   * 物料组:编码+名称
   */
  @ApiModelProperty("物料组:编码+名称")
  private String itemGroup;

  /**
   * 物料组编码
   */
  @ApiModelProperty("物料组编码")
  private String itemGroupCode;

  @ApiModelProperty("物料组名称")
  private String itemGroupName;

  /**
   * 固定的供应商
   */
  @ApiModelProperty("固定的供应商名称")
  private String fixedVendor;

  /**
   * 采购信息记录
   */
  @ApiModelProperty("采购信息记录")
  private String procurementRecord;

  @ApiModelProperty("是否有修改记录: 0 否 1是")
  private String haveModifyRecord;

  @ApiModelProperty("是否有组件清单: 0 否 1是")
  private String haveComponentManifest;

  @ApiModelProperty("申请人")
  private String applicant;

  @ApiModelProperty("销售组织")
  private String salesOrganization;

  @ApiModelProperty("采购组织")
  private String purchasingOrganization;

  @ApiModelProperty("客户订单号")
  private String customerOrderNumber;
  public static PurchaseApplyForOrderV2ListVO getInstance(PurchaseApplyForOrderV2 entity) {
    PurchaseApplyForOrderV2ListVO vo = new PurchaseApplyForOrderV2ListVO();
    vo.id = entity.getId();
    vo.applyForOrderNo = entity.getApplyForOrderNo();
    vo.applyForType = entity.getApplyForType();
    vo.orderGoodsState = entity.getOrderGoodsState();
    vo.productCode = entity.getProductCode();
    vo.createTime = entity.getCreateTime();
    vo.warehouse = entity.getWarehouse();
    vo.brand = entity.getBrand();
    vo.productName = entity.getProductName();
    vo.model = entity.getModel();
    vo.specification = entity.getSpecification();
    vo.unit = entity.getUnitName();
    vo.unitCode = entity.getUnit();
    vo.applyForNumber = entity.getApplyForNumber();
    vo.planDemandDate = entity.getPlanDemandDate();
    vo.purchaseMan = entity.getPurchaseManMix();
    vo.orderGoodsNumber = entity.getOrderGoodsNumber();
    vo.unorderedQuantity = (entity.getApplyForNumber() == null
        || entity.getOrderGoodsNumber() == null) ? StrUtil.EMPTY
        : NumberUtil.sub(entity.getApplyForNumber(),
            entity.getOrderGoodsNumber()).stripTrailingZeros().toPlainString();
    vo.soldToParty = entity.getSoldToParty();
    vo.saleOrderNo = entity.getSaleOrderNo();
    vo.cancellationState = entity.getCancellationState();
    vo.pushDownState = entity.getPushDownState();
    vo.serialNumber = entity.getSerialNumber();
    vo.materialDescription = StrUtil.emptyIfNull(entity.getMaterialDescription());
    vo.salesUnitPrice = entity.getSalesUnitPrice();
    vo.salesDemandQuantity = entity.getSalesDemandQuantity();
    vo.mpmReferenceSettlementPrice = entity.getMpmReferenceSettlementPrice();
    vo.salesman = entity.getSalesman();
    vo.deliveryType = entity.getDeliveryType();
    vo.orderNo = entity.getCustomerOrderNumber();
    vo.merchandiser = entity.getFollowUpPersonName();
    vo.largeTicketProjectName = entity.getProjectName();
    vo.materialLineRemarks = StrUtil.emptyIfNull(entity.getMaterialLineRemarks());
    vo.deliveryAddress = entity.getDeliveryAddress();
    vo.contactInformation = entity.getContactInformation();
    vo.consignee = entity.getConsignee();
    vo.applicationFormRemarks = entity.getApplicationFormRemarks();
    vo.projectNo = entity.getProjectNo();
    vo.printNumber =
        entity.getPrintNumber() == null ? 0 : entity.getPrintNumber();
    vo.saleOrderProductRowId = entity.getSaleOrderProductRowId();
    vo.isWorryOrder=entity.getIsWorryOrder();
    vo.directShipment=entity.getDirectShipment();
    vo.profileCard=entity.getProfileCard();
    vo.profileCardCode=entity.getProfileCardCode();
    vo.assignmentCategory=entity.getAssignmentCategory();
    vo.assignmentCategoryCode=entity.getAssignmentCategoryCode();
    vo.assignmentCategoryName=entity.getAssignmentCategoryName();
    vo.projectCategory=entity.getProjectCategory();
    vo.projectCategoryCode=entity.getProjectCategoryCode();
    vo.projectCategoryName=entity.getProjectCategoryName();
    vo.ledgerSubject=entity.getLedgerSubject();
    vo.ledgerSubjectCode=entity.getLedgerSubjectCode();
    vo.ledgerSubjectName=entity.getLedgerSubjectName();
    vo.costCenter=entity.getCostCenter();
    vo.costCenterCode=entity.getCostCenterCode();
    vo.costCenterName=entity.getCostCenterName();
    vo.order=entity.getOrder();
    vo.orderCode=entity.getOrderCode();
    vo.orderName=entity.getOrderName();
    vo.deliverTime=entity.getDeliverTime();
    vo.itemGroup=entity.getItemGroup();
    vo.itemGroupCode=entity.getItemGroupCode();
    vo.itemGroupName=entity.getItemGroupName();
    vo.fixedVendor=entity.getFixedVendorName();
    vo.procurementRecord=entity.getProcurementRecord();
    vo.salesOrganization=entity.getSalesOrganization();
    vo.purchasingOrganization=entity.getPurchasingOrganization();
    vo.customerOrderNumber=entity.getCustomerOrderNumber();
    return vo;
  }

}
