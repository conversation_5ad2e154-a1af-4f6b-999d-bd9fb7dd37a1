package com.xhgj.srm.v2.form;/**
 * @since 2025/4/17 18:45
 */

import com.xhgj.srm.common.dto.exportfiled.ExportFiledSelectDTO;
import com.xhgj.srm.common.dto.exportfiled.ExportTemplateParamProvider;
import com.xhgj.srm.jpa.dto.permission.MergeUserPermission;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Data
public class PurchaseApplyForOrderV2ExportForm extends PurchaseApplyForOrderV2QueryForm implements
    ExportTemplateParamProvider {
  /**
   * 勾选的ids
   */
  @ApiModelProperty("勾选的ids")
  private List<String> ids;

  @ApiModelProperty("采购部门编码")
  private List<String> purchaseDepartmentCode;

  @ApiModelProperty("模板 id")
  private String templateId;

  @ApiModelProperty("模板名称")
  private String templateName;

  @ApiModelProperty("是否保存")
  private Boolean save;

  @ApiModelProperty("选中字段集合")
  private List<ExportFiledSelectDTO> selectFieldList;

  public Map<String,Object> toQueryMap(MergeUserPermission searchPermission) {
    Map<String, Object> map = super.toQueryMap(searchPermission);
    map.put("ids",ids);
    map.put("purchaseDepartmentCode",purchaseDepartmentCode);
    map.put("exportField",selectFieldList);
    return map;
  }
}

