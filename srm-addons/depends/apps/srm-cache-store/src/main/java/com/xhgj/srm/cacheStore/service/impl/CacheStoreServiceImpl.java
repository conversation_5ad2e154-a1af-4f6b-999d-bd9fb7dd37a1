package com.xhgj.srm.cacheStore.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.xhgj.srm.cacheStore.factory.MapStructFactory;
import com.xhgj.srm.cacheStore.form.CacheStoreSaveForm;
import com.xhgj.srm.cacheStore.service.CacheStoreService;
import com.xhgj.srm.cacheStore.vo.CacheStoreVO;
import com.xhgj.srm.common.Constants;
import com.xhgj.srm.jpa.entity.CacheStore;
import com.xhgj.srm.jpa.repository.CacheStoreRepository;
import com.xhiot.boot.core.common.exception.CheckException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class CacheStoreServiceImpl implements CacheStoreService {

  @Resource
  CacheStoreRepository cacheStoreRepository;


  @Override
  public void saveCache(CacheStoreSaveForm storeSaveForm) {
    CacheStore cacheStore = new CacheStore();
    if (StrUtil.isNotBlank(storeSaveForm.getId())) {
      cacheStore = cacheStoreRepository.findById(storeSaveForm.getId()).orElseThrow(
          () -> CheckException.noFindException(CacheStore.class, storeSaveForm.getId()));
      MapStructFactory.INSTANCE.updateCacheStore(storeSaveForm, cacheStore);
      cacheStore.setUpdateTime(System.currentTimeMillis());
    } else {
      cacheStore.setUserId(storeSaveForm.getUserId());
      cacheStore.setCacheType(storeSaveForm.getCacheType());
      cacheStore.setContent(storeSaveForm.getContent());
      cacheStore.setCreateTime(System.currentTimeMillis());
      cacheStore.setUpdateTime(System.currentTimeMillis());
      cacheStore.setState(Constants.STATE_OK);
    }
    cacheStoreRepository.save(cacheStore);
  }

  @Override
  @Transactional(rollbackFor = Exception.class)
  public void saveCacheAndDeleteOld(CacheStoreSaveForm storeSaveForm) {
    // 删除旧的
    deleteCache(storeSaveForm.getUserId(), storeSaveForm.getCacheType());
    CacheStore cacheStore = new CacheStore();
    cacheStore.setUserId(storeSaveForm.getUserId());
    cacheStore.setCacheType(storeSaveForm.getCacheType());
    cacheStore.setContent(storeSaveForm.getContent());
    cacheStore.setCreateTime(System.currentTimeMillis());
    cacheStore.setUpdateTime(System.currentTimeMillis());
    cacheStore.setState(Constants.STATE_OK);
    cacheStoreRepository.save(cacheStore);
  }

  @Override
  public void deleteCache(String userId, String cacheType) {
    List<CacheStore> findOnes =
        cacheStoreRepository.findAllByUserIdAndCacheTypeAndState(userId, cacheType, Constants.STATE_OK);
    if (CollUtil.isEmpty(findOnes)) {
      return;
    }
    findOnes.forEach(item -> {
      item.setUpdateTime(System.currentTimeMillis());
      item.setState(Constants.STATE_DELETE);
    });
    cacheStoreRepository.saveAll(findOnes);
  }

  @Override
  public CacheStoreVO getCache(String userId, String cacheType) {
    List<CacheStore> findOnes =
        cacheStoreRepository.findAllByUserIdAndCacheTypeAndState(userId, cacheType, Constants.STATE_OK);
    // 返回最新的一条
    if (CollUtil.isEmpty(findOnes)) {
      return null;
    }
    // 最后一条
    CacheStore cacheStore = findOnes.get(findOnes.size() - 1);
    return CacheStoreVO.toVO(cacheStore);
  }
}

