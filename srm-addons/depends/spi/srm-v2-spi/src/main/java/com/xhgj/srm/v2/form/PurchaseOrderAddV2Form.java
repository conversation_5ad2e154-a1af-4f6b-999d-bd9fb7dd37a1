package com.xhgj.srm.v2.form;/**
 * @since 2025/4/29 14:32
 */

import cn.hutool.core.collection.CollUtil;
import com.xhgj.srm.common.Constants;
import com.xhgj.srm.jpa.entity.File;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;
import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;
import java.util.Set;

/**
 *<AUTHOR>
 *@date 2025/4/29 14:32:44
 *@description
 */
@Data
public class PurchaseOrderAddV2Form {
  /**
   * 订单id
   */
  private String id;

  /**
   * 订单号
   */
  @ApiModelProperty(hidden = true)
  private String orderCode;

  /**
   * 订单类型
   * @see com.xhgj.srm.common.enums.PurchaseOrderTypeEnum
   */
  private String orderType;

  /**
   * 采购组织
   */
  private String userGroup;

  /**
   * 采购部门
   */
  private String purchaseDeptCode;

  /**
   * 采购员
   */
  private String purchaseManCode;

  /**
   * 是否走SCP
   */
  private Boolean scp;

  /**
   * 是否亏本订单
   */
  private Boolean loss;

  /**
   * 亏本理由
   */
  @Length(max = 50,message = "亏本理由最大长度为 50 字")
  private String causeOfLoss;


//  /**
//   * 单子是否赠品订单 -- 2.0版本去除，由采购订单类型自行判断
//   */
//  private Boolean freeState;

//  /**
//   * 单子是否自采 -- 2.0版本去除，由采购订单类型自行判断
//   */
//  private Boolean selfState;

  /**
   * 供应商id
   */
  private String supplierId;

  /**
   * 开票方
   */
  private String invoicingParty;


  /**
   * 货币码
   */
  private String moneyCode;

  /**
   * 采购用途
   */
  private String purchaseUse;

  /**
   * 发票类型 1 增值税专用 2 增值税普通 3其他
   */
  private String invoiceType;

  /**
   * 附件
   */
  private List<AnnexParam> fileList;

  /**
   * 备注
   */
  private String mark;

  /**
   * 供方联系人
   */
  private String supContacts;

  /**
   * 联系方式
   */
  private String supMobile;

  /**
   * 电子邮件
   */
  private String supEmail;

  /**
   * 传真
   */
  private String supFax;

  /**
   * 收件人
   */
  private String receiveMan;

  /**
   * 联系方式
   */
  private String receiveMobile;

  /**
   * 收件地址
   */
  private String receiveAddress;

  /**
   * 保存类型 1 保存 2 提交
   */
  private Integer saveType;

  /**
   * 原寄售转自有订单号
   */
  private String consignmentToOwnedCode;

  /**
   * 订单运费
   */
  private BigDecimal freight;

  /**
   * 订单汇率
   */
  private String orderRate;

  /**
   * 付款条件信息
   */
  private List<PurchaseOrderPaymentTermsAddParam> paymentTerms;

  /**
   * 付款条件信息
   */
  @Length(max = 500,message = "付款条件信息最大长度为 500 字")
  private String paymentTermsStr;

  /**
   * 是否是sap085接口推送
   */
  private Boolean sap085Flag;

  /**
   * 物料信息
   */
  @Valid
  private List<ProductDetailAdd> productList;

  /**
   * 获取总订货数量
   * @return
   */
  public BigDecimal getTotalNum() {
    if (CollUtil.isEmpty(productList)) {
      return BigDecimal.ZERO;
    }
    return productList.stream().map(ProductDetailAdd::getNum).reduce(BigDecimal.ZERO, BigDecimal::add);
  }

  /**
   * 获取总的价税合计
   */
  public BigDecimal getTotalAmountIncludingTax() {
    if (CollUtil.isEmpty(productList)) {
      return BigDecimal.ZERO;
    }
    BigDecimal reduce = productList.stream().map(ProductDetailAdd::getTotalAmountIncludingTax).reduce(BigDecimal.ZERO, BigDecimal::add);
    if (reduce.compareTo(new BigDecimal("*********.99")) > 0) {
      throw new IllegalArgumentException("物料行的总价（原币）最大只支持*********.99，超出请拆成多行做单或做多个单子");
    } else {
      return reduce;
    }
  }



  @Data
  public static class AnnexParam {


    private String baseUrl;

    private String fileRes;

    private String filename;

    private String newFileName;

    private String newFilePath;

    private String type;

    private String relationType;

    public File buildFile(String relationId) {
      File file = new File();
      file.setUrl(newFilePath);
      file.setState(Constants.STATE_OK);
      file.setDescription(filename);
      file.setName(filename);
      file.setRelationType(relationType);
      file.setRelationId(relationId);
      file.setCreateTime(System.currentTimeMillis());
      return file;
    }
  }

  @Data
  public static class ProductDetailAdd {
    /**
     * 行id
     */
    private Integer indexNum;

    /**
     * 物料编码
     */
    private String productCode;

    /**
     * 物料组code
     */
    private String itemGroupCode;

    /**
     * 物料组名称
     */
    private String itemGroupCodeName;

    /**
     * 品牌
     */
    private String brand;

    /**
     * 物料名称
     */
    private String productName;

    /**
     * 规格
     */
    private String specification;

    /**
     * 型号
     */
    private String model;

    /**
     * 单位
     */
    private String unit;

    /**
     * 单位编码
     */
    private String unitCode;

    /**
     * 描述
     */
    private String description;

    /**
     * 订货数量
     */
//    @NumberLengthAndScale(length = 11,fraction = 3,message = "订货数量最大长度为11位，小数位数最大为3")
    private BigDecimal num;

    /**
     * 仓库
     */
    private String warehouse;

    /**
     * 仓库名称
     */
    private String warehouseName;

    /**
     * 含税单价
     */
//    @NumberLengthAndScale(length = 11,fraction = 6,message = "含税单价最大长度为11位，小数位数最大为6")
    private BigDecimal price;

    /**
     * 税率
     */
    private BigDecimal taxRate;

    /**
     * 总价 - 价税合计
     */
//    @NumberLengthAndScale(length = 11,fraction = 2,message = "价税合计最大长度为11位，小数位数最大为2")
    private BigDecimal totalAmountIncludingTax;

    /**
     * MPM结算价
     */
    private BigDecimal transferPrice;

    /**
     * 物料税率
     */
    private BigDecimal productRate;

    /**
     * 加价系数
     */
    private BigDecimal markupCoefficient;

    /**
     * 附加费
     */
    private BigDecimal surcharge;

    /**
     * 结算单价
     */
    private BigDecimal settlementPrice;

    /**
     * 结算总价
     */
    private BigDecimal totalSettlementPrice;

    /**
     * 约定交货日期
     */
    private Long supplierDeliverTime;

    /**
     * 实际交货日期
     */
    private Long purchaseDeliverTime;

    /**
     * 备注
     */
    private String mark;

    /**
     * 是否免费0 否 1是
     */
    private String freeState;

    /**
     * 项目类别
     */
    private String projectType;

    /**
     * 批次
     */
    private String batchNo;

    /**
     * 科目分配类别Code
     */
    private String assignmentCategoryCode;

    /**
     * 科目分配类别Name
     */
    private String assignmentCategoryName;

    /**
     * 总账科目code
     */
    private String ledgerSubjectCode;

    /**
     * 总账科目名称
     */
    private String ledgerSubjectName;

    /**
     * 成本中心code
     */
    private String costCenterCode;

    /**
     * 成本中心名称
     */
    private String costCenterName;

    /**
     * 订单号code
     */
    private String orderCode;

    /**
     * 订单号名称
     */
    private String orderName;

    /**
     * 资产卡片
     */
    private String profileCardCode;

    /**
     * 资产卡片名称
     */
    private String profileCardName;

    /**
     * 是否质检
     */
    private Boolean qualityCheck;

    /**
     * 关联采购申请id
     */
    private String purchaseApplyForOrderId;

    /**
     * 销售订单号
     */
    private String salesOrderNo;

    /**
     * 大票项目号
     */
    private String projectNo;

    /**
     * 大票项名称
     */
    private String projectName;

    /**
     * 业务员
     */
    private String salesman;

    /**
     * 跟单员
     */
    private String followUpPersonName;

    /**
     * 业务员所在公司名称
     */
    private String businessCompanyName;

    /**
     * 制单员名称
     */
    private String makeManName;

    /**
     * 售达方
     */
    private String soldToParty;

    /**
     * 杂费
     */
    private BigDecimal incidentalAmount;

    /**
     * 杂费供应商 id
     */
    private String incidentalSupplierId;

    /**
     * 关税
     */
    private BigDecimal tariff;

    /**
     * 关税金额
     */
    private BigDecimal tariffAmount;

    /**
     * 付汇金额
     */
    private BigDecimal paymentAmount;

    /**
     * 物料运费
     */
    private BigDecimal freight;

    /**
     * 运费供应商 id
     */
    private String freightSupplierId;

    /**
     * 关税供应商 id
     */
    private String tariffSupplierId;

    /**
     * 委外物料信息
     */
    private List<EntrustProduct> entrustProductList;
  }

  /**
   * 委外物料
   */
  @Data
  public static class EntrustProduct {

    /**
     * 物料编码
     */
    private String productCode;

    /**
     * 品牌
     */
    private String brand;

    /**
     * 物料名称
     */
    private String productName;

    /**
     * 规格
     */
    private String specification;

    /**
     * 型号
     */
    private String model;

    /**
     * 单位
     */
    private String unit;

    /**
     * 描述
     */
    private String description;

    /**
     * 需求数量
     */
    private BigDecimal num;

    /**
     * 组件单位
     */
    private String componentUnit;

    /**
     * 组件单位名称
     */
    private String componentUnitName;

    /**
     * 需求日期
     */
    private Long componentDemandDate;

    /**
     * 行项目类别
     */
    private String lineItemCategory;

    /**
     * MRP类型
     */
    private String mrpType;
  }

  @Data
  public static class PurchaseOrderPaymentTermsAddParam {

    /**
     * 满足条件
     */
    private Set<String> condition;

    /**
     * 账期
     */
    @NotBlank
    private String accountPeriod;

    /**
     * 预付比例
     */
    @NotBlank
    private String advanceRatio;

    /**
     * 预付金额
     */
    @NotNull
    private BigDecimal prepaidAmount;
  }


}
