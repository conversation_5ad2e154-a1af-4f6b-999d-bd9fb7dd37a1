package com.xhgj.srm.v2.dao.impl;/**
 * @since 2025/4/28 11:33
 */

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.StrUtil;
import com.xhgj.srm.common.Constants;
import com.xhgj.srm.common.enums.supplierorder.SupplierOrderFormStatus;
import com.xhgj.srm.common.enums.supplierorder.SupplierOrderFormType;
import com.xhgj.srm.common.utils.SQLUtils;
import com.xhgj.srm.jpa.dao.impl.AbstractExtDao;
import com.xhgj.srm.jpa.dto.purchase.order.PurchaseOrderOutBoundDeliveryStatistics;
import com.xhgj.srm.jpa.dto.purchase.order.PurchaseOrderWarehousingStatistics;
import com.xhgj.srm.jpa.entity.v2.SupplierOrderToFormV2;
import com.xhgj.srm.v2.dao.SupplierOrderToFormV2Dao;
import com.xhgj.srm.v2.dto.RetreatWarehousePageV2DTO;
import com.xhgj.srm.v2.dto.WarehousingV2DTO;
import com.xhiot.boot.core.common.util.ObjectUtils;
import com.xhiot.boot.framework.jpa.util.HqlUtil;
import com.xhiot.boot.mvc.base.PageResult;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Repository;

/**
 *<AUTHOR>
 *@date 2025/4/28 11:33:41
 *@description
 */
@Repository
public class SupplierOrderToFormV2DaoImpl extends AbstractExtDao<SupplierOrderToFormV2> implements SupplierOrderToFormV2Dao {

  @Override
  public List<SupplierOrderToFormV2> getSupplierOrderFormByTypeAndStatus(
      String type, List<String> statusList, String supplierOrderId) {
    Assert.notEmpty(supplierOrderId);
    StringBuilder hql =
        new StringBuilder("from SupplierOrderToFormV2  where supplierOrderId = ? and state = ? ");
    Object[] params =
        new Object[] {
            supplierOrderId, Constants.STATE_OK,
        };
    params = HqlUtil.appendFieldIn(hql, params, "status", statusList);
    if (StrUtil.isNotEmpty(type)) {
      hql.append(" and type = ? ");
      params = ObjectUtils.objectAdd(params, type);
    }
    return getHqlList(hql.toString(), params);
  }

  @Override
  public long getSumSupplierOrderFormByTypeAndStatus(String type,
      String supplierOrderId, List<String> statusList) {
    Assert.notEmpty(supplierOrderId);
    StringBuilder sql =
        new StringBuilder(
            "select count(*) from t_supplier_order_to_form  where supplier_order_id = ? and c_state = ? ");
    Object[] params =
        new Object[]{
            supplierOrderId, Constants.STATE_OK,
        };
    if (StrUtil.isNotEmpty(type)) {
      sql.append(" and c_type = ? ");
      params = ObjectUtils.objectAdd(params, type);
    }
    if (CollUtil.isNotEmpty(statusList)) {
      params = HqlUtil.appendFieldIn(sql,params,"c_status",statusList);
    }
    return countSql(sql.toString(), params);
  }

  @Override
  public List<SupplierOrderToFormV2> getAllByTypeAndSupplierOrderIdAndStateOrderByTimeAscSQL(
      String type, String supplierOrderId) {
    Assert.notEmpty(supplierOrderId);
    Assert.notNull(type);
    String hql =
        "select * from t_supplier_order_to_form where c_type = ? and supplier_order_id = ? and c_state = ? "
            + "and c_status != ? and c_status!= ? ";
    Object[] params =
        new Object[] {
            type, supplierOrderId, Constants.STATE_OK, SupplierOrderFormStatus.REVERSAL.getStatus(),
            SupplierOrderFormStatus.REVOKE.getStatus()
        };
    hql += "order by c_create_time desc";
    return getSqlList(hql, params);
  }


  @Override
  public List<SupplierOrderToFormV2> getSupplierOrderFormByTypeAndState(String type,
      String supplierOrderId) {
    Assert.notEmpty(supplierOrderId);
    StringBuilder hql =
        new StringBuilder("from SupplierOrderToFormV2  where supplierOrderId = ? and state = ? ");
    Object[] params =
        new Object[] {
            supplierOrderId, Constants.STATE_OK,
        };
    if (StrUtil.isNotEmpty(type)) {
      hql.append(" and type = ? ");
      params = ObjectUtils.objectAdd(params, type);
    }
    return getHqlList(hql.toString(), params);
  }

  @Override
  public PageResult<WarehousingV2DTO> warehousingPageRef(Map<String, Object> queryMap) {
    StringBuilder sql = new StringBuilder();
    sql.append(
        "select so.id as id, so.c_code as code ,tof.c_create_time as createTime ,tof.c_source "
            + "as source ,tof.c_logistics_company as logisticsCompany,tof.c_track_num "
            + "as trackNum,"
            + "  tof.c_product_voucher as productVoucherNo ,sop.`c_code` as productCode, sop"
            + ".`c_brand` as brand "
            + " ,sop.`c_name` as productName ,`sop`"
            + ".`c_model` as manuCode ,sod.`c_stock_input_qty` as stockInputQty, sod"
            + ".`c_stock_output_qty` as stockOutputQty"
            + ",`sod`.`c_invoiced_num` as invoicedNum ,sod.`c_price` as productPrice ,sod.c_batch_no as batchNo ,tof"
            + ".c_status formStatus ,sop.c_unit as unit,sod.c_sort_num,sod.c_sap_row_id ,"
            + "sod.id as detailId, so.c_supplier_name as supplierName, so.c_purchase_man as "
            + "purchaseMan, so.c_purchase_dept as purchaseDept, sod.c_tax_rate as taxRate, "
            + " sod.c_warehouse_name as warehouse, so.c_order_type as orderType "
            + ",so.c_purchase_dept_code as purchaseDeptCode "
            + ",sod.`order_to_form_id` as orderToFormId ,COALESCE(sod.`c_price`,0)*COALESCE(sod.`c_stock_input_qty`,0) as totalPriceAndTax"
            + ", pafo.`c_apply_for_order_no` as purchaseApplyCode, so.`c_project_no` as "
            + "projectNo,sop.`c_salesman` as salesman,so.`c_sale_order_no` as saleOrderNo,"
            + "COALESCE(sod.`c_invoiced_num`,0)*COALESCE(sod.`c_price`,0) as invoicedAmount, "
            + "(COALESCE(sod.c_stock_input_qty, 0) - COALESCE(sod.c_stock_output_qty, 0) - COALESCE(sod.c_invoiced_num, 0))*COALESCE(sod.`c_price`,0) as unInvoicedAmount"
            + ",odBase.`c_num` as num, "
            + "`sod`.`c_invoicable_num` as invoiceAbleNum, "
            + "odBase.`c_settlement_price` as  settlementPrice , tof.c_posting_date as "
            + "postingDate, tof.c_form_code as formCode, tof.c_review_status as auditStatus "
            + "from  t_supplier_order_detail sod "
            + "left join `t_supplier_order_product` sop "
            + "on sod.`order_product_id` = sop.`id`  "
            + "left join `t_supplier_order_to_form` tof "
            + "on sod.`order_to_form_id` = tof.`id` left join `t_supplier_order` so  "
            + "on tof.`supplier_order_id` = so.`id` left join `t_supplier_order_detail` odBase "
            + "on sod.`detailed_id` = odBase.`id` left join `t_purchase_apply_for_order` pafo "
            + "on pafo.`id` =odBase.`purchase_apply_for_order_id` ");
    if (!StrUtil.isBlankIfStr(queryMap.get("invoiceNo"))) {
      sql.append( "left join t_supplier_invoice_to_detail d on d.c_detail_id = sod.id ");
      sql.append( "left join t_input_invoice_order r on r.id = d.c_input_invoice_order_id ");
    }
    Object[] params = this.buildWarehousingWhereQuery(sql, new Object[] {}, queryMap);
    sql.append("order by tof.c_create_time desc ");
    Page<Object[]> pageSqlObject = findPageSqlObject(sql.toString(), params, (Integer) queryMap.get(
            "pageNo"),
        (Integer) queryMap.get("pageSize"));
    List<Object[]> content = pageSqlObject.getContent();
    List<WarehousingV2DTO> result = content.stream().map(objects -> {
      WarehousingV2DTO warehousingDTO = new WarehousingV2DTO();
      warehousingDTO.setId((String) objects[0]);
      warehousingDTO.setCode((String) objects[1]);
      warehousingDTO.setCreateTime(Optional.ofNullable(objects[2]).map(o -> (BigInteger) objects[2])
          .map(BigInteger::longValue).orElse(0L));
      warehousingDTO.setSource(
          Optional.ofNullable(objects[3]).map(o -> (String) objects[3]).orElse(StrUtil.EMPTY));
      warehousingDTO.setLogisticsCompany(
          Optional.ofNullable(objects[4]).map(o -> (String) objects[4]).orElse(StrUtil.EMPTY));
      warehousingDTO.setTrackNum(
          Optional.ofNullable(objects[5]).map(o -> (String) objects[5]).orElse(StrUtil.EMPTY));
      warehousingDTO.setProductVoucherNo(
          Optional.ofNullable(objects[6]).map(o -> (String) objects[6]).orElse(StrUtil.EMPTY));
      warehousingDTO.setProductCode(
          Optional.ofNullable(objects[7]).map(o -> (String) objects[7]).orElse(StrUtil.EMPTY));
      warehousingDTO.setBrand(
          Optional.ofNullable(objects[8]).map(o -> (String) objects[8]).orElse(StrUtil.EMPTY));
      warehousingDTO.setProductName(
          Optional.ofNullable(objects[9]).map(o -> (String) objects[9]).orElse(StrUtil.EMPTY));
      warehousingDTO.setManuCode(
          Optional.ofNullable(objects[10]).map(o -> (String) objects[10]).orElse(StrUtil.EMPTY));
      warehousingDTO.setStockInputQty(
          Optional.ofNullable(objects[11]).map(o -> (BigDecimal) objects[11])
              .orElse(BigDecimal.ZERO));
      warehousingDTO.setStockOutputQty(
          Optional.ofNullable(objects[12]).map(o -> (BigDecimal) objects[12])
              .orElse(BigDecimal.ZERO));
      warehousingDTO.setInvoicedNum(
          Optional.ofNullable(objects[13]).map(o -> (BigDecimal) objects[13])
              .orElse(BigDecimal.ZERO));
      warehousingDTO.setProductPrice(
          Optional.ofNullable(objects[14]).map(o -> (BigDecimal) objects[14])
              .orElse(BigDecimal.ZERO));
      warehousingDTO.setBatchNo(
          Optional.ofNullable(objects[15]).map(o -> (String) objects[15]).orElse(StrUtil.EMPTY));
      warehousingDTO.setFormStatus(
          Optional.ofNullable(objects[16]).map(o -> (String) objects[16]).orElse(StrUtil.EMPTY));
      warehousingDTO.setUnit(
          Optional.ofNullable(objects[17]).map(o -> (String) objects[17]).orElse(StrUtil.EMPTY));
      warehousingDTO.setProductSort(
          Optional.ofNullable(objects[18]).map(o -> (Integer) objects[18]).orElse(0));
      warehousingDTO.setSapRowId(
          Optional.ofNullable(objects[19]).map(o -> (String) objects[19]).orElse(StrUtil.EMPTY));
      warehousingDTO.setDetailId(
          Optional.ofNullable(objects[20]).map(o -> (String) objects[20]).orElse(StrUtil.EMPTY));
      warehousingDTO.setSupplierName(
          Optional.ofNullable(objects[21]).map(o -> (String) objects[21]).orElse(StrUtil.EMPTY));
      warehousingDTO.setPurchaseMan(
          Optional.ofNullable(objects[22]).map(o -> (String) objects[22]).orElse(StrUtil.EMPTY));
      warehousingDTO.setPurchaseDept(
          Optional.ofNullable(objects[23]).map(o -> (String) objects[23]).orElse(StrUtil.EMPTY));
      warehousingDTO.setTaxRate(Optional.ofNullable(objects[24]).map(o -> (BigDecimal) objects[24])
          .orElse(BigDecimal.ZERO));
      warehousingDTO.setWarehouseName(
          Optional.ofNullable(objects[25]).map(o -> (String) objects[25]).orElse(StrUtil.EMPTY));
      warehousingDTO.setOrderType(
          Optional.ofNullable(objects[26]).map(o -> (String) objects[26]).orElse(StrUtil.EMPTY));
      warehousingDTO.setPurchaseDeptCode(
          Optional.ofNullable(objects[27]).map(o -> (String) objects[27]).orElse(StrUtil.EMPTY));
      warehousingDTO.setOrderToFormId(
          Optional.ofNullable(objects[28]).map(o -> (String) objects[28]).orElse(StrUtil.EMPTY));
      warehousingDTO.setTotalPriceAndTax(
          Optional.ofNullable(objects[29]).map(o -> (BigDecimal) objects[29])
              .orElse(BigDecimal.ZERO));
      warehousingDTO.setPurchaseApplyCode(
          Optional.ofNullable(objects[30]).map(o -> (String) objects[30]).orElse(StrUtil.EMPTY));
      warehousingDTO.setProjectNo(
          Optional.ofNullable(objects[31]).map(o -> (String) objects[31]).orElse(StrUtil.EMPTY));
      warehousingDTO.setSalesman(
          Optional.ofNullable(objects[32]).map(o -> (String) objects[32]).orElse(StrUtil.EMPTY));
      warehousingDTO.setSaleOrderNo(
          Optional.ofNullable(objects[33]).map(o -> (String) objects[33]).orElse(StrUtil.EMPTY));
      warehousingDTO.setInvoicedAmount(
          Optional.ofNullable(objects[34]).map(o -> (BigDecimal) objects[34])
              .orElse(BigDecimal.ZERO));
      warehousingDTO.setUnInvoicedAmount(
          Optional.ofNullable(objects[35]).map(o -> (BigDecimal) objects[35])
              .orElse(BigDecimal.ZERO));
      warehousingDTO.setNum(Optional.ofNullable(objects[36]).map(o -> (BigDecimal) objects[36])
          .orElse(BigDecimal.ZERO));
      warehousingDTO.setInvoiceAbleNum(Convert.toBigDecimal(objects[37]));
      warehousingDTO.setSettlementPrice(Convert.toBigDecimal(objects[38]));
      warehousingDTO.setPostingDate(Convert.toLong(objects[39]));
      warehousingDTO.setFormCode(Convert.toStr(objects[40]));
      warehousingDTO.setAuditStatus(Convert.toByte(objects[41]));
      return warehousingDTO;
    }).collect(Collectors.toList());
    return new PageResult<>(result, pageSqlObject.getTotalElements(), pageSqlObject.getTotalPages(),
        (Integer) queryMap.get("pageNo"), (Integer)queryMap.get("pageSize"));
  }

  private Object[] buildWarehousingWhereQuery(StringBuilder sql, Object[] params, Map<String,
      Object> queryMap) {
    sql.append("where tof.`c_type` = ? and sod.c_state = ? "
        + "and tof.c_state = ? and so.c_state = ? ");
    params = new Object[] {SupplierOrderFormType.WAREHOUSING.getType(),Constants.STATE_OK,
        Constants.STATE_OK ,Constants.STATE_OK};
    if (!StrUtil.isBlankIfStr(queryMap.get("invoiceNo"))) {
      sql.append("and r.c_state = ? ");
      params = ObjectUtils.objectAdd(params, Constants.STATE_OK);
    }
    List<String> ids = Convert.toList(String.class,queryMap.get("ids"));
    if (CollUtil.isNotEmpty(ids)) {
      sql.append(" and sod.id in ( ");
      for (int i = 0; i < ids.size(); i++) {
        if (i == ids.size() - 1) {
          sql.append(" ? ) ");
        } else {
          sql.append(" ? , ");
        }
        params = ObjectUtils.objectAdd(params, ids.get(i));
      }
    } else {
      if (StrUtil.isNotEmpty(Convert.toStr(queryMap.get("userGroup")))) {
        sql.append(" and so.c_group_code = ? ");
        params = org.springframework.util.ObjectUtils.addObjectToArray(params, Convert.toStr(queryMap.get("userGroup")));
      }
      List<String> userNameList = Convert.toList(String.class,queryMap.get("userNameList"));
      if (CollUtil.isNotEmpty(userNameList)) {
        sql.append(" and so.c_purchase_man in ( ");
        for (int i = 0; i < userNameList.size(); i++) {
          if (i == userNameList.size() - 1) {
            sql.append(" ? ) ");
          } else {
            sql.append(" ? , ");
          }
          params = ObjectUtils.objectAdd(params, userNameList.get(i));
        }
      } else {
        if (StrUtil.isNotEmpty(Convert.toStr(queryMap.get("purchaseId"))) || StrUtil.isNotEmpty(
            Convert.toStr(queryMap.get("createMan")))) {
          sql.append(" and ( so.c_purchase_man  = ? or so.purchase_id = ? ) ");
          params = org.springframework.util.ObjectUtils.addObjectToArray(params, queryMap.get("createMan"));
          params = org.springframework.util.ObjectUtils.addObjectToArray(params, queryMap.get("purchaseId"));
        }
      }
      if (!StrUtil.isBlankIfStr(queryMap.get("warehouseV2"))) {
        sql.append(" and sod.c_warehouse = ? ");
        params = ObjectUtils.objectAdd(params, queryMap.get("warehouseV2"));
      }
      if (!StrUtil.isBlankIfStr(queryMap.get("orderType"))) {
        sql.append(" and so.c_order_type = ? ");
        params = ObjectUtils.objectAdd(params, queryMap.get("orderType"));
      }
      if (!StrUtil.isBlankIfStr(queryMap.get("orderCode"))) {
        sql.append(" and so.c_code like ? ");
        params =
            ObjectUtils.objectAdd(params, StrUtil.wrap(queryMap.get("orderCode").toString(), "%"));
      }
      if (!StrUtil.isBlankIfStr(queryMap.get("sapProductVoucherNo"))) {
        sql.append(" and tof.c_product_voucher like ? ");
        params = ObjectUtils.objectAdd(params,
            StrUtil.wrap(queryMap.get("sapProductVoucherNo").toString(), "%"));
      }
      if (!StrUtil.isBlankIfStr(queryMap.get("invoiceNo"))) {
        sql.append(" and d.c_invoice_number like ? ");
        params =
            ObjectUtils.objectAdd(params, StrUtil.wrap(queryMap.get("invoiceNo").toString(), "%"));
      }
      if (!StrUtil.isBlankIfStr(queryMap.get("batchNo"))) {
        sql.append(" and sod.c_batch_no like ? ");
        params =
            ObjectUtils.objectAdd(params, StrUtil.wrap(queryMap.get("batchNo").toString(), "%"));
      }
      if (queryMap.get("invoiceQuantityOperator") != null) {
        params = SQLUtils.addStrLogicalOperators(queryMap.get("invoiceQuantityOperator").toString(),
            queryMap.get("invoiceQuantity"), sql, params, "sod.c_invoiced_num");
      }
      if (queryMap.get("startTime") != null) {
        sql.append(" and tof.c_time >= ? ");
        params = ObjectUtils.objectAdd(params, queryMap.get("startTime"));
      }
      if (queryMap.get("endTime") != null) {
        sql.append(" and tof.c_time <= ? ");
        params = ObjectUtils.objectAdd(params, queryMap.get("endTime"));
      }
      if (!StrUtil.isBlankIfStr(queryMap.get("expressNo"))) {
        sql.append(" and tof.c_track_num like ? ");
        params =
            ObjectUtils.objectAdd(params, StrUtil.wrap(queryMap.get("expressNo").toString(), "%"));
      }
      if (!StrUtil.isBlankIfStr(queryMap.get("expressCompany"))) {
        sql.append(" and tof.c_logistics_code = ? ");
        params = ObjectUtils.objectAdd(params, queryMap.get("expressCompany"));
      }
      if (!StrUtil.isBlankIfStr(queryMap.get("productCode"))) {
        sql.append(" and sop.c_code like ? ");
        params = ObjectUtils.objectAdd(params,
            StrUtil.wrap(queryMap.get("productCode").toString(), "%"));
      }
      if (!StrUtil.isBlankIfStr(queryMap.get("brand"))) {
        sql.append(" and sop.c_brand like ? ");
        params = ObjectUtils.objectAdd(params, StrUtil.wrap(queryMap.get("brand").toString(), "%"));
      }
      if (!StrUtil.isBlankIfStr(queryMap.get("productName"))) {
        sql.append(" and sop.c_name like ? ");
        params = ObjectUtils.objectAdd(params,
            StrUtil.wrap(queryMap.get("productName").toString(), "%"));
      }
      //型号
      if (!StrUtil.isBlankIfStr(queryMap.get("specification"))) {
        sql.append(" and sop.c_model like ? ");
        params = ObjectUtils.objectAdd(params,
            StrUtil.wrap(queryMap.get("specification").toString(), "%"));
      }
      if (queryMap.get("priceOperator") != null) {
        params = SQLUtils.addStrLogicalOperators(queryMap.get("priceOperator").toString(),
            queryMap.get("price"), sql, params, "sod.c_price");
      }
      if (queryMap.get("returnQuantityOperator") != null) {
        params = SQLUtils.addStrLogicalOperators(queryMap.get("returnQuantityOperator").toString(),
            queryMap.get("returnQuantity"), sql, params, "sod.c_stock_output_qty");
      }
      if (queryMap.get("unInvoiceNumOperator") != null) {
        String unInvoiceNumField =
            "(COALESCE(sod.c_stock_input_qty, 0) - COALESCE(sod.c_stock_output_qty, 0) - COALESCE(sod.c_invoiced_num, 0))";
        params = SQLUtils.addStrLogicalOperators(queryMap.get("unInvoiceNumOperator").toString(),
            queryMap.get("unInvoiceNum"), sql, params, unInvoiceNumField);
      }
      if (!StrUtil.isBlankIfStr(queryMap.get("writeOffState"))) {
        if (queryMap.get("writeOffState").equals(Constants.WRITE_OFF_STATE_YES.toString())) {
          sql.append(" and tof.c_status = ? ");
          params = ObjectUtils.objectAdd(params, SupplierOrderFormStatus.REVERSAL.getKey());
        } else if ((queryMap.get("writeOffState")
            .equals(Constants.WRITE_OFF_STATE_NO.toString()))) {
          sql.append(" and (tof.c_status != ? or tof.c_status is null )");
          params = ObjectUtils.objectAdd(params, SupplierOrderFormStatus.REVERSAL.getKey());
        } else {
          sql.append(" and tof.c_status = ? ");
          params =
              ObjectUtils.objectAdd(params, SupplierOrderFormStatus.REVERSAL_IN_PROGRESS.getKey());
        }
      }
      if (!StrUtil.isBlankIfStr(queryMap.get("supplierName"))) {
        sql.append(" and so.c_supplier_name like ? ");
        params = ObjectUtils.objectAdd(params, "%" + queryMap.get("supplierName") + "%");
      }
      if (!StrUtil.isBlankIfStr(queryMap.get("purchaseMan"))) {
        params = SQLUtils.addLikeConditions(sql, params, "so.c_purchase_man",
            queryMap.get("purchaseMan").toString());
      }
      if (!StrUtil.isBlankIfStr(queryMap.get("purchaseDept"))) {
        sql.append(" and so.c_purchase_dept like ? ");
        params = ObjectUtils.objectAdd(params, "%" + queryMap.get("purchaseDept") + "%");
      }
      if (queryMap.get("totalPriceAndTaxOperator") != null) {
        String totalPriceAndTaxField = "(COALESCE(sod.`c_price`,0)*COALESCE(sod.`c_stock_input_qty`,0))";
        params =
            SQLUtils.addStrLogicalOperators(queryMap.get("totalPriceAndTaxOperator").toString(),
                queryMap.get("totalPriceAndTax"), sql, params,
                totalPriceAndTaxField);
      }
      if (queryMap.get("invoicedAmountOperator") != null) {
        String invoicedAmountField = "(COALESCE(sod.`c_invoiced_num`,0)*COALESCE(sod.`c_price`,0))";
        params = SQLUtils.addStrLogicalOperators(queryMap.get("invoicedAmountOperator").toString(),
            queryMap.get("invoicedAmount"), sql, params, invoicedAmountField);
      }
      if (queryMap.get("unInvoicedAmountOperator") != null) {
        String unInvoicedAmountField = "((COALESCE(sod.c_stock_input_qty, 0) - COALESCE(sod"
            + ".c_stock_output_qty, 0) - COALESCE(sod.c_invoiced_num, 0))*COALESCE(sod.`c_price`,0))";
        params =
            SQLUtils.addStrLogicalOperators(queryMap.get("unInvoicedAmountOperator").toString(),
                queryMap.get("unInvoicedAmount"), sql, params, unInvoicedAmountField);
      }
      if (!StrUtil.isBlankIfStr(queryMap.get("purchaseApplyCode"))) {
        sql.append(" and pafo.c_apply_for_order_no like ? ");
        params = ObjectUtils.objectAdd(params, "%" + queryMap.get("purchaseApplyCode") + "%");
      }
      if (!StrUtil.isBlankIfStr(queryMap.get("projectNo"))) {
        sql.append(" and so.c_project_no like ? ");
        params = ObjectUtils.objectAdd(params, "%" + queryMap.get("projectNo") + "%");
      }
      if (!StrUtil.isBlankIfStr(queryMap.get("salesman"))) {
        sql.append(" and sop.c_salesman like ? ");
        params = ObjectUtils.objectAdd(params, "%" + queryMap.get("salesman") + "%");
      }
      if (!StrUtil.isBlankIfStr(queryMap.get("saleOrderNo"))) {
        sql.append(" and so.c_sale_order_no like ? ");
        params = ObjectUtils.objectAdd(params, "%" + queryMap.get("saleOrderNo") + "%");
      }
      if (queryMap.get("settlementPriceOperator") != null) {
        params = SQLUtils.addStrLogicalOperators(queryMap.get("settlementPriceOperator").toString(),
            queryMap.get("settlementPrice"), sql, params, "odBase.`c_settlement_price`");
      }
      if (queryMap.get("invoiceAbleNumOperator") != null) {
        params = SQLUtils.addStrLogicalOperators(queryMap.get("invoiceAbleNumOperator").toString(),
            queryMap.get("invoiceAbleNum"), sql, params, "`sod`.`c_invoicable_num`");
      }
      if (!StrUtil.isBlankIfStr(queryMap.get("formCode"))) {
        sql.append(" and tof.c_form_code like ? ");
        params = ObjectUtils.objectAdd(params, "%" + queryMap.get("formCode") + "%");
      }
      if (!StrUtil.isBlankIfStr(queryMap.get("formCode"))) {
        sql.append(" and tof.c_form_code like ? ");
        params = ObjectUtils.objectAdd(params, "%" + queryMap.get("formCode") + "%");
      }
      if (queryMap.get("auditStatus") != null) {
        sql.append(" and tof.c_review_status = ? ");
        params = ObjectUtils.objectAdd(params, queryMap.get("auditStatus"));
      }
      if (queryMap.get("postingDateStart") != null && queryMap.get("postingDateEnd") != null) {
        sql.append(" and tof.c_posting_date >= ? and tof.c_posting_date <= ? ");
        params = ObjectUtils.objectAdd(params, queryMap.get("postingDateStart"));
        params = ObjectUtils.objectAdd(params, queryMap.get("postingDateEnd"));
      }

    }
    return params;
  }

  @Override
  public PurchaseOrderWarehousingStatistics warehousingStatistics2(Map<String, Object> queryMap) {
    StringBuilder sql = new StringBuilder();
    // 构建 SELECT 子句
    sql.append("SELECT ")
        .append("SUM(COALESCE(sod.c_stock_input_qty, 0)) AS totalStockInputQty, ")
        .append("SUM(COALESCE(sod.c_stock_output_qty, 0)) AS totalStockOutputQty, ")
        .append("SUM(COALESCE(sod.c_invoiced_num, 0)) AS totalInvoicedNum, ")
        .append("SUM(COALESCE(sod.c_invoiced_num, 0) * COALESCE(sod.c_price, 0)) AS totalInvoicedAmount, ")
        .append("SUM((COALESCE(sod.c_stock_input_qty, 0) - COALESCE(sod.c_stock_output_qty, 0) - COALESCE(sod.c_invoiced_num, 0)) * COALESCE(sod.c_price, 0)) AS totalUnInvoicedAmount, ")
        .append("SUM(COALESCE(sod.c_stock_input_qty, 0) - COALESCE(sod.c_stock_output_qty, 0) - COALESCE(sod.c_invoiced_num, 0)) AS totalUnInvoiceNum, ")
        .append("SUM(COALESCE(COALESCE(sod.`c_price`,0)*COALESCE(sod.`c_stock_input_qty`,0), 0)) AS totalPriceAndTax, ")
        .append("SUM(ROUND(COALESCE(sod.c_stock_input_qty, 0) * ")
        // NakedPrice num * (c_price /1+taxRate)
        .append("(COALESCE(sod.c_price, 0) / CAST((1 + COALESCE(sod.c_tax_rate, 0)) AS DECIMAL(20, 10))), 2)) AS nakedTotalPrice ");

    sql.append("from  t_supplier_order_detail sod ");
    sql.append("left join `t_supplier_order_product` sop ");
    sql.append("on sod.`order_product_id` = sop.`id`  ");
    sql.append("left join `t_supplier_order_to_form` tof ");
    sql.append("on sod.`order_to_form_id` = tof.`id` left join `t_supplier_order` so  ");
    sql.append("on tof.`supplier_order_id` = so.`id` left join `t_supplier_order_detail` odBase ");
    sql.append("on sod.`detailed_id` = odBase.`id` left join `t_purchase_apply_for_order` pafo ");
    sql.append("on pafo.`id` =odBase.`purchase_apply_for_order_id` ");

    if (!StrUtil.isBlankIfStr(queryMap.get("invoiceNo"))) {
      sql.append( "left join t_supplier_invoice_to_detail d on d.c_detail_id = sod.id ");
      sql.append( "left join t_input_invoice_order r on r.id = d.c_input_invoice_order_id ");
    }
    Object[] params = this.buildWarehousingWhereQuery(sql, new Object[] {}, queryMap);
    Object[] uniqueSqlObj = (Object[]) getUniqueSqlObj(sql.toString(), params);
    PurchaseOrderWarehousingStatistics result = new PurchaseOrderWarehousingStatistics();
    result.setStockInputQty(Convert.toBigDecimal(uniqueSqlObj[0]));
    result.setStockOutputQty(Convert.toBigDecimal(uniqueSqlObj[1]));
    result.setInvoiceNum(Convert.toBigDecimal(uniqueSqlObj[2]));
    result.setInvoicedAmount(Convert.toBigDecimal(uniqueSqlObj[3]));
    result.setUnInvoicedAmount(Convert.toBigDecimal(uniqueSqlObj[4]));
    result.setUnInvoiceNum(Convert.toBigDecimal(uniqueSqlObj[5]));
    result.setTotalPriceAndTax(Convert.toBigDecimal(uniqueSqlObj[6]));
    result.setNakedTotalPrice(Convert.toBigDecimal(uniqueSqlObj[7]));
    return result;
  }

  @Override
  public PurchaseOrderOutBoundDeliveryStatistics outBoundDeliveryStatistics2(
      Map<String, Object> queryMap) {
    StringBuilder sql = new StringBuilder();
    sql.append("select ");
    sql.append("COALESCE(SUM(temp.c_stock_output_qty), 0) as stockOutputQty, ");
    sql.append("COALESCE(SUM(temp.c_invoiced_num), 0) as invoicedNum, ");
    sql.append("COALESCE(SUM(temp.c_return_amount), 0) as returnAmount, ");
    sql.append("COALESCE(SUM(temp.c_return_price), 0) as returnPrice, ");
    sql.append("COALESCE(SUM(temp.stockInputQty), 0) as stockInputQty ");
    sql.append("from (  ");
    sql.append("select distinct sod.id, sod.c_stock_output_qty, ")
        .append(" sod.c_invoiced_num, ")
        .append(" sod.c_return_amount, ")
        .append(" sod.c_return_price, ")
        .append(" (SELECT sod2.c_stock_input_qty FROM t_supplier_order_to_form tof2 ")
        .append(" LEFT JOIN t_supplier_order_detail sod2 ON sod2.order_to_form_id = tof2.id ")
        .append(" WHERE tof2.c_type = ? AND tof2.supplier_order_id = so.id ")
        .append(" AND tof2.id = sod.c_in_warehouse_id AND sod2.c_sort_num = sod.c_sort_num LIMIT 1) AS "
            + "stockInputQty ") // 直接在子查询中计算 stockInputQty
        .append("from t_supplier_order_detail sod ");
    sql.append("left join t_supplier_order_product sop on sod.order_product_id = sop.id ");
    sql.append("left join t_supplier_order_to_form tof on sod.order_to_form_id = tof.id ");
    sql.append("left join t_supplier_order so on tof.supplier_order_id = so.id ");

    if (!StrUtil.isBlankIfStr(queryMap.get("invoiceNo"))) {
      sql.append( "left join t_supplier_invoice_to_detail d on d.c_detail_id = sod.id ");
      sql.append( "left join t_input_invoice_order r on r.id = d.c_input_invoice_order_id ");
    }
    Object[] params = new Object[] {SupplierOrderFormType.WAREHOUSING.getType()};
    params = this.buildOutBoundDelivery(sql, params, queryMap);

    sql.append(") as temp ");
    Object[] objects = (Object[]) getUniqueSqlObj(sql.toString(), params);
    PurchaseOrderOutBoundDeliveryStatistics result = new PurchaseOrderOutBoundDeliveryStatistics();
    result.setStockOutputQty(Convert.toBigDecimal(objects[0]));
    result.setStockInputQty(Convert.toBigDecimal(objects[4]));
    result.setRedInvoiceQty(Convert.toBigDecimal(objects[1]));
    result.setReturnPrice(Convert.toBigDecimal(objects[3]));
    result.setReturnAmount(Convert.toBigDecimal(objects[2]));
    return result;
  }

  @Override
  public PageResult<RetreatWarehousePageV2DTO> outBoundDeliveryPageRef(
      Map<String, Object> queryMap) {
    StringBuilder sql = new StringBuilder();
    sql.append(
        "select  so.id,so.c_code,tof.c_time,tof.c_return_reason,"
            + " tof.c_return_warehouse,tof.c_execution_status,"
            + " tof.c_logistics_company, tof.c_track_num,"
            + " tof.c_consignee,tof.c_receive_address,"
            + " tof.c_need_red_ticket,tof.c_product_voucher,"
            + " tof.c_sap_return_number,sod.c_sap_row_id,sop.c_code as productCode,"
            + " sop.c_brand,sop.c_name,sop.c_manu_code,"
            + " sod.c_stock_output_qty,sod.c_invoiced_num,"
            + " sod.c_price,sod.c_batch_no,tof.c_status,sop.c_unit,sod"
            + ".c_sort_num as sort,sod.c_return_amount,sod.c_return_price ,sod.id as detailid,"
            + "so.c_supplier_name as supplierName, so.c_purchase_man as purchaseMan,"
            + " so.c_purchase_dept as purchaseDept, (select sod2.c_stock_input_qty "
            + " from t_supplier_order_to_form tof2 left join t_supplier_order_detail sod2 on sod2 "
            + ".order_to_form_id = tof2.id "
            + " where tof2.c_type= ? and tof2.supplier_order_id = so.id and tof2.id = sod"
            + ".c_in_warehouse_id and sod2.c_sort_num = sod.c_sort_num LIMIT 1) as stockInputQty  "
            + " ,so.c_purchase_dept_code as purchaseDeptCode "
            + ", sod.order_to_form_id as orderToFormId "
            // v2
            + ", tof.c_form_code as formCode , tof.c_review_time as reviewTime "
            + ",tof.c_review_status as reviewStatus,tof.c_review_reason as reviewReason "
            + ",tof.c_review_id as reviewId ,tof.c_warehouse_code as warehouseCode "
            + ",tof.c_warehouse_name as returnWarehouseName ,sop.c_specification as specification, "
            + "sop.c_model as model, sop.c_ledger_subject as ledgerSubject, sop.c_ledger_subject_code "
            + "as ledgerSubjectCode "
            + " from t_supplier_order_detail sod left join t_supplier_order_product sop"
            + "                   on sod.order_product_id = sop.id"
            + "         left join t_supplier_order_to_form tof"
            + "                   on sod.order_to_form_id = tof.id"
            + "         left join t_supplier_order so"
            + "                   on tof.supplier_order_id = so.id ");
    if (!StrUtil.isBlankIfStr(queryMap.get("invoiceNo"))) {
      sql.append( "left join t_supplier_invoice_to_detail d on d.c_detail_id = sod.id ");
      sql.append( "left join t_input_invoice_order r on r.id = d.c_input_invoice_order_id ");
    }
    Object[] params = new Object[] {SupplierOrderFormType.WAREHOUSING.getType()};
    params = this.buildOutBoundDelivery(sql, params, queryMap);
    sql.append(" order by tof.c_create_time desc  ");
    Page<Object[]> pageSqlObject = findPageSqlObject(sql.toString(), params, (Integer) queryMap.get(
            "pageNo"),
        (Integer) queryMap.get("pageSize"));
    List<Object[]> content = pageSqlObject.getContent();
    List<RetreatWarehousePageV2DTO> result = content.stream().map(objects -> {
      RetreatWarehousePageV2DTO dto = new RetreatWarehousePageV2DTO();
      dto.setId((String) objects[0]);
      dto.setCode((String) objects[1]);
      dto.setTime(Optional.ofNullable(objects[2]).map(
          o -> (BigInteger)objects[2]
      ).map(BigInteger::longValue).orElse(0L));
      dto.setReason(
          Optional.ofNullable(objects[3]).map(o -> ((String) objects[3])).orElse(StrUtil.EMPTY));
      dto.setWarehouse(Optional.ofNullable(objects[4]).map(o -> ((String) objects[4])).orElse(StrUtil.EMPTY));
      dto.setExecutionStatus(Optional.ofNullable(objects[5]).map(o -> ((String) objects[5])).orElse(StrUtil.EMPTY));
      dto.setLogisticsCompany(Optional.ofNullable(objects[6]).map(o -> ((String) objects[6])).orElse(StrUtil.EMPTY));
      dto.setTrackNum(Optional.ofNullable(objects[7]).map(o -> ((String) objects[7])).orElse(StrUtil.EMPTY));
      dto.setConsignee(Optional.ofNullable(objects[8]).map(o -> ((String) objects[8])).orElse(StrUtil.EMPTY));
      dto.setReceiveAddress(Optional.ofNullable(objects[9]).map(o -> ((String) objects[9])).orElse(StrUtil.EMPTY));
      dto.setNeedRedTicket(Optional.ofNullable(objects[10]).map(o -> ((String) objects[10])).orElse(StrUtil.EMPTY));
      dto.setProductVoucher(Optional.ofNullable(objects[11]).map(o -> ((String) objects[11])).orElse(StrUtil.EMPTY));
      dto.setSapReturnNumber(Optional.ofNullable(objects[12]).map(o -> ((String) objects[12])).orElse(StrUtil.EMPTY));
      dto.setSapRowId(Optional.ofNullable(objects[13]).map(o -> ((String) objects[13])).orElse(StrUtil.EMPTY));
      dto.setProductCode(Optional.ofNullable(objects[14]).map(o -> ((String) objects[14])).orElse(StrUtil.EMPTY));
      dto.setBrand(Optional.ofNullable(objects[15]).map(o -> ((String) objects[15])).orElse(StrUtil.EMPTY));
      dto.setName(Optional.ofNullable(objects[16]).map(o -> ((String) objects[16])).orElse(StrUtil.EMPTY));
      dto.setManuCode(Optional.ofNullable(objects[17]).map(o -> ((String) objects[17])).orElse(StrUtil.EMPTY));
      dto.setStockOutPutQty(Optional.ofNullable(objects[18]).map(o -> ((BigDecimal) objects[18])).orElse(BigDecimal.ZERO));
      dto.setInvoicedNum(Optional.ofNullable(objects[19]).map(o -> ((BigDecimal) objects[19])).orElse(BigDecimal.ZERO));
      dto.setPrice(Optional.ofNullable(objects[20]).map(o -> ((BigDecimal) objects[20])).orElse(BigDecimal.ZERO));
      dto.setBatchNo(Optional.ofNullable(objects[21]).map(o -> ((String) objects[21])).orElse(StrUtil.EMPTY));
      dto.setStatus(Optional.ofNullable(objects[22]).map(o -> ((String) objects[22])).orElse(StrUtil.EMPTY));
      dto.setUnit(Optional.ofNullable(objects[23]).map(o -> ((String) objects[23])).orElse(StrUtil.EMPTY));
      dto.setRowId(Optional.ofNullable(objects[24]).map(o -> ((Integer) objects[24])).orElse(0));
      dto.setReturnAmount(Optional.ofNullable(objects[25]).map(o -> ((BigDecimal) objects[25])).orElse(BigDecimal.ZERO));
      dto.setReturnPrice(Optional.ofNullable(objects[26]).map(o -> ((BigDecimal) objects[26])).orElse(BigDecimal.ZERO));
      dto.setDetailId(Optional.ofNullable(objects[27]).map(o -> ((String) objects[27])).orElse(StrUtil.EMPTY));
      dto.setSupplierName(Optional.ofNullable(objects[28]).map(o -> ((String) objects[28])).orElse(StrUtil.EMPTY));
      dto.setPurchaseMan(Optional.ofNullable(objects[29]).map(o -> ((String) objects[29])).orElse(StrUtil.EMPTY));
      dto.setPurchaseDept(Optional.ofNullable(objects[30]).map(o -> ((String) objects[30])).orElse(StrUtil.EMPTY));
      dto.setStockInputQty(Optional.ofNullable(objects[31]).map(o -> ((BigDecimal) objects[31])).orElse(BigDecimal.ZERO));
      dto.setPurchaseDeptCode(Optional.ofNullable(objects[32]).map(o -> ((String) objects[32])).orElse(StrUtil.EMPTY));
      dto.setOrderToFormId(Optional.ofNullable(objects[33]).map(o -> ((String) objects[33])).orElse(StrUtil.EMPTY));
      // v2
      dto.setFormCode(Optional.ofNullable(objects[34]).map(o -> ((String) objects[34])).orElse(StrUtil.EMPTY));
      dto.setReviewTime(Optional.ofNullable(objects[35]).map(o -> Convert.toLong(objects[35])).orElse(null));
      dto.setReviewStatus(Optional.ofNullable(objects[36]).map(o -> Convert.toByte(objects[36])).orElse(null));
      dto.setReviewReason(Optional.ofNullable(objects[37]).map(o -> ((String) objects[37])).orElse(StrUtil.EMPTY));
      dto.setReviewId(Optional.ofNullable(objects[38]).map(o -> ((String) objects[38])).orElse(StrUtil.EMPTY));
      dto.setReturnWarehouseCode(Optional.ofNullable(objects[39]).map(o -> ((String) objects[39])).orElse(StrUtil.EMPTY));
      dto.setReturnWarehouseName(Optional.ofNullable(objects[40]).map(o -> ((String) objects[40])).orElse(StrUtil.EMPTY));
      dto.setSpecification(Optional.ofNullable(objects[41]).map(o -> ((String) objects[41])).orElse(StrUtil.EMPTY));
      dto.setModel(Optional.ofNullable(objects[42]).map(o -> ((String) objects[42])).orElse(StrUtil.EMPTY));
      dto.setLedgerSubject(Optional.ofNullable(objects[43]).map(o -> ((String) objects[43])).orElse(StrUtil.EMPTY));
      dto.setLedgerSubjectCode(Optional.ofNullable(objects[44]).map(o -> ((String) objects[44])).orElse(StrUtil.EMPTY));

      return dto;
    }).collect(Collectors.toList());
    return new PageResult<>(result, pageSqlObject.getTotalElements(), pageSqlObject.getTotalPages(),
        (Integer) queryMap.get("pageNo"), (Integer)queryMap.get("pageSize"));
  }

  private Object[] buildOutBoundDelivery(StringBuilder sql, Object[] params, Map<String, Object> queryMap) {
    sql.append(" where tof.`c_type` = ? and sod.c_state = ? "
        + " and tof.c_state = ? and so.c_state = ? ");
    params = ObjectUtils.objectAdd(params, SupplierOrderFormType.RETURN.getType());
    params = ObjectUtils.objectAdd(params, Constants.STATE_OK);
    params = ObjectUtils.objectAdd(params, Constants.STATE_OK);
    params = ObjectUtils.objectAdd(params, Constants.STATE_OK);
    if (!StrUtil.isBlankIfStr(queryMap.get("invoiceNo"))) {
      sql.append("and r.c_state = ? ");
      params = ObjectUtils.objectAdd(params, Constants.STATE_OK);
    }
    List<String> ids = Convert.toList(String.class,queryMap.get("ids"));
    if (CollUtil.isNotEmpty(ids)) {
      sql.append(" and sod.id in ( ");
      for (int i = 0; i < ids.size(); i++) {
        if (i == ids.size() - 1) {
          sql.append(" ? ) ");
        } else {
          sql.append(" ? , ");
        }
        params = ObjectUtils.objectAdd(params, ids.get(i));
      }
    }else{
      if (StrUtil.isNotEmpty(Convert.toStr(queryMap.get("userGroup")))) {
        sql.append(" and so.c_group_code = ? ");
        params = ObjectUtils.objectAdd(params, Convert.toStr(queryMap.get("userGroup")));
      }
      List<String> userNameList = Convert.toList(String.class,queryMap.get("userNameList"));
      if (CollUtil.isNotEmpty(userNameList)) {
        sql.append(" and so.c_purchase_man in ( ");
        for (int i = 0; i < userNameList.size(); i++) {
          if (i == userNameList.size() - 1) {
            sql.append(" ? ) ");
          } else {
            sql.append(" ? , ");
          }
          params = ObjectUtils.objectAdd(params, userNameList.get(i));
        }
      } else {
        if (StrUtil.isNotEmpty(Convert.toStr(queryMap.get("purchaseId"))) || StrUtil.isNotEmpty(
            Convert.toStr(queryMap.get("createMan")))) {
          sql.append(" and ( so.c_purchase_man  = ? or so.purchase_id = ? ) ");
          params = org.springframework.util.ObjectUtils.addObjectToArray(params,
              queryMap.get("createMan"));
          params = org.springframework.util.ObjectUtils.addObjectToArray(params,
              queryMap.get("purchaseId"));
        }
      }
      if (!StrUtil.isBlankIfStr(queryMap.get("orderCode"))) {
        sql.append(" and so.c_code like ? ");
        params = ObjectUtils.objectAdd(params,
            StrUtil.wrap(queryMap.get("orderCode").toString(), "%"));
      }
      if (!StrUtil.isBlankIfStr(queryMap.get("sapProductVoucherNo"))) {
        sql.append(" and tof.c_product_voucher like ? ");
        params = ObjectUtils.objectAdd(params,
            StrUtil.wrap(queryMap.get("sapProductVoucherNo").toString(), "%"));
      }
      if (!StrUtil.isBlankIfStr(queryMap.get("returnOrderCode"))) {
        sql.append(" and tof.c_sap_return_number like ? ");
        params = ObjectUtils.objectAdd(params,
            StrUtil.wrap(queryMap.get("returnOrderCode").toString(), "%"));
      }
      if (!StrUtil.isBlankIfStr(queryMap.get("invoiceNo"))) {
        sql.append(" and d.c_invoice_number like ? ");
        params = ObjectUtils.objectAdd(params,
            StrUtil.wrap(queryMap.get("invoiceNo").toString(), "%"));
      }
      if (!StrUtil.isBlankIfStr(queryMap.get("batchNo"))) {
        sql.append(" and sod.c_batch_no like ? ");
        params =
            ObjectUtils.objectAdd(params, StrUtil.wrap(queryMap.get("batchNo").toString(), "%"));
      }
      if (queryMap.get("invoiceQuantityOperator") != null) {
        params = SQLUtils.addStrLogicalOperators(queryMap.get("invoiceQuantityOperator").toString(),
            queryMap.get("invoiceQuantity"), sql, params, "sod.c_invoiced_num");
      }
      if (queryMap.get("isNeedRedInvoice") != null) {
        sql.append(" and ifnull(tof.c_need_red_ticket,false) = ? ");
        params = ObjectUtils.objectAdd(params, queryMap.get("isNeedRedInvoice"));
      }
      if (!StrUtil.isBlankIfStr(queryMap.get("startTime"))) {
        sql.append(" and tof.c_time >= ? ");
        params = ObjectUtils.objectAdd(params, queryMap.get("startTime"));
      }
      if (!StrUtil.isBlankIfStr(queryMap.get("endTime"))) {
        sql.append(" and tof.c_time <= ? ");
        params = ObjectUtils.objectAdd(params, queryMap.get("endTime"));
      }
      if (!StrUtil.isBlankIfStr(queryMap.get("returnReason"))) {
        sql.append(" and tof.c_return_reason like ? ");
        params = ObjectUtils.objectAdd(params,
            StrUtil.wrap(queryMap.get("returnReason").toString(), "%"));
      }
      if (!StrUtil.isBlankIfStr(queryMap.get("returnWarehouse"))) {
        sql.append(" and tof.c_return_warehouse = ? ");
        params = ObjectUtils.objectAdd(params, queryMap.get("returnWarehouse"));
      }
      if (!StrUtil.isBlankIfStr(queryMap.get("warehouseExecutionStatus"))) {
        sql.append(" and tof.c_execution_status = ? ");
        params = ObjectUtils.objectAdd(params, queryMap.get("warehouseExecutionStatus"));
      }
      if (!StrUtil.isBlankIfStr(queryMap.get("expressNo"))) {
        sql.append(" and tof.c_track_num like ? ");
        params = ObjectUtils.objectAdd(params,
            StrUtil.wrap(queryMap.get("expressNo").toString(), "%"));
      }
      if (!StrUtil.isBlankIfStr(queryMap.get("expressCompany"))) {
        sql.append(" and tof.c_logistics_code = ? ");
        params = ObjectUtils.objectAdd(params, queryMap.get("expressCompany"));
      }
      if (!StrUtil.isBlankIfStr(queryMap.get("productCode"))) {
        sql.append(" and sop.c_code like ? ");
        params = ObjectUtils.objectAdd(params,
            StrUtil.wrap(queryMap.get("productCode").toString(), "%"));
      }
      if (!StrUtil.isBlankIfStr(queryMap.get("brand"))) {
        sql.append(" and sop.c_brand like ? ");
        params =
            ObjectUtils.objectAdd(params, StrUtil.wrap(queryMap.get("brand").toString(), "%"));
      }
      if (!StrUtil.isBlankIfStr(queryMap.get("productName"))) {
        sql.append(" and sop.c_name like ? ");
        params = ObjectUtils.objectAdd(params,
            StrUtil.wrap(queryMap.get("productName").toString(), "%"));
      }
      if (!StrUtil.isBlankIfStr(queryMap.get("specification"))) {
        sql.append(" and sop.c_specification like ? ");
        params = ObjectUtils.objectAdd(params,
            StrUtil.wrap(queryMap.get("specification").toString(), "%"));
      }
      if (!StrUtil.isBlankIfStr(queryMap.get("model"))) {
        sql.append(" and sop.c_model like ? ");
        params = ObjectUtils.objectAdd(params,
            StrUtil.wrap(queryMap.get("model").toString(), "%"));
      }
      if (!StrUtil.isBlankIfStr(queryMap.get("ledgerSubject"))) {
        sql.append(" and sop.c_ledger_subject like ? ");
        params = ObjectUtils.objectAdd(params,
            StrUtil.wrap(queryMap.get("ledgerSubject").toString(), "%"));
      }
      if (queryMap.get("returnQuantityOperator") != null) {
        params =
            SQLUtils.addStrLogicalOperators(queryMap.get("returnQuantityOperator").toString(),
                queryMap.get("returnQuantity"), sql, params, "sod.c_stock_output_qty");
      }
      if (!StrUtil.isBlankIfStr(queryMap.get("writeOffState"))) {
        if (queryMap.get("writeOffState").equals(Constants.WRITE_OFF_STATE_YES.toString())) {
          sql.append(" and tof.c_status = ? ");
          params = ObjectUtils.objectAdd(params, SupplierOrderFormStatus.REVERSAL.getKey());
        } else if ((queryMap.get("writeOffState")
            .equals(Constants.WRITE_OFF_STATE_NO.toString()))) {
          sql.append(" and (tof.c_status != ? or tof.c_status is null )");
          params = ObjectUtils.objectAdd(params, SupplierOrderFormStatus.REVERSAL.getKey());
        } else {
          sql.append(" and tof.c_status = ? ");
          params = ObjectUtils.objectAdd(params,
              SupplierOrderFormStatus.REVERSAL_IN_PROGRESS.getKey());
        }
      }
      if (!StrUtil.isBlankIfStr(queryMap.get("supplierName"))) {
        sql.append(" and so.c_supplier_name like ? ");
        params = ObjectUtils.objectAdd(params, "%" + queryMap.get("supplierName") + "%");
      }
      if (!StrUtil.isBlankIfStr(queryMap.get("purchaseMan"))) {
        params = SQLUtils.addLikeConditions(sql, params, "so.c_purchase_man",
            queryMap.get("purchaseMan").toString());
      }
      if (!StrUtil.isBlankIfStr(queryMap.get("purchaseDept"))) {
        sql.append(" and so.c_purchase_dept like ? ");
        params = ObjectUtils.objectAdd(params, "%" + queryMap.get("purchaseDept") + "%");
      }
      // v2
      if (!StrUtil.isBlankIfStr(queryMap.get("formCode"))) {
        sql.append(" and tof.c_form_code like ? ");
        params = ObjectUtils.objectAdd(params, "%" + queryMap.get("formCode") + "%");
      }
      if (queryMap.get("reviewTimeStart") != null) {
        sql.append(" and tof.c_review_time >= ? ");
        params = ObjectUtils.objectAdd(params, queryMap.get("reviewTimeStart"));
      }
      if (queryMap.get("reviewTimeEnd") != null) {
        sql.append(" and tof.c_review_time <= ? ");
        params = ObjectUtils.objectAdd(params, queryMap.get("reviewTimeEnd"));
      }
      if (queryMap.get("reviewStatus") != null) {
        sql.append(" and tof.c_review_status = ? ");
        params = ObjectUtils.objectAdd(params, queryMap.get("reviewStatus"));
      }

    }
    return params;
  }
}
