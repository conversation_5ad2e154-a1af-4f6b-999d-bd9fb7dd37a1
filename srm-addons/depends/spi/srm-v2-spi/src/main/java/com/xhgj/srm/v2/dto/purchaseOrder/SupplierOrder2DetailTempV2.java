package com.xhgj.srm.v2.dto.purchaseOrder;


import com.xhgj.srm.jpa.entity.v2.SupplierOrderDetailV2;
import com.xhgj.srm.jpa.entity.v2.SupplierOrderV2;
import lombok.Data;

@Data
public class SupplierOrder2DetailTempV2 {


  /**
   * 对应订单
   */
  SupplierOrderV2 supplierOrder;
  /**
   * 对应订单明细
   */
  SupplierOrderDetailV2 supplierOrderDetail;

  public SupplierOrder2DetailTempV2(SupplierOrderV2 supplierOrder, SupplierOrderDetailV2 supplierOrderDetail) {
    this.supplierOrder = supplierOrder;
    this.supplierOrderDetail = supplierOrderDetail;
  }
}
