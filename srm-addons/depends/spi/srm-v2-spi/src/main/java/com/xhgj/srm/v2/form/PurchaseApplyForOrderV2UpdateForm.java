package com.xhgj.srm.v2.form;/**
 * @since 2025/4/21 15:57
 */

import lombok.Data;
import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;

/**
 *<AUTHOR>
 *@date 2025/4/21 15:57:57
 *@description
 */
@Data
public class PurchaseApplyForOrderV2UpdateForm {

  /**
   * 修改采购员
   */
  @NotBlank(message = "采购员名称不能为空")
  private String purchaseMan;

  /**
   * 修改采购员Code
   */
  @NotBlank(message = "采购员编码不能为空")
  private String purchaseManCode;

  /**
   * 采购部门编码
   */
  @NotBlank(message = "采购部门编码不能为空")
  private String purchaseDepartment;

  /**
   * 申请单备注
   */
  private String remarks;

  /**
   * 组织
   */
  @NotBlank(message = "组织不能为空")
  private String userGroup;

  /**
   * userId
   */
  @NotBlank(message = "用户id不能为空")
  private String userId;

  /**
   * 采购申请明细
   */
  @NotEmpty(message = "采购申请修改明细不能为空")
  @Valid
  private List<PurchaseApplyForOrderUpdateDetail> details;

  @Data
  public static class PurchaseApplyForOrderUpdateDetail {
    /**
     * 采购申请id
     */
    @NotBlank(message = "采购申请id不能为空")
    private String purchaseApplyForOrderId;

    /**
     * 申请数量修改
     */
    @NotNull(message = "申请数量不能为空")
    private BigDecimal applyForNumber;
  }

}
