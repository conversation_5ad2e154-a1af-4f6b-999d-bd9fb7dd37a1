package com.xhgj.srm.v2.form;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;

/**
 *
 */
@Data
public class PurchaseOrderWarehouseApplyV2AddForm {

  /**
   * 采购订单id
   */
  @NotBlank(message = "采购订单id不能为空")
  private String purchaserOrderId;

  /**
   * 仓库code
   */
//  @NotBlank(message = "仓库code不能为空")
  @ApiModelProperty(value = "仓库code")
  private String warehouseCode;

  /**
   * 仓库名称
   */
//  @NotBlank(message = "仓库名称不能为空")
  @ApiModelProperty(value = "仓库名称")
  private String warehouseName;

  /**
   * 过账日期
   */
  @NotNull(message = "过账日期不能为空")
  @ApiModelProperty(value = "过账日期")
  private Long postingDate;

  /**
   * 快递公司
   */
  @ApiModelProperty(value = "快递公司")
  private String logisticsCompany;

  /**
   * 物流单号/送货人手机号(厂家配送时传入)
   */
  @ApiModelProperty("物流单号/送货人手机号(厂家配送时传入)")
  private String trackNum;

  /**
   * 物流编码
   */
  @ApiModelProperty("物流编码")
  private String logisticsCode;

  /**
   * 咸亨电商物流公司编码
   */
  @ApiModelProperty(value = "咸亨电商物流公司编码")
  private String code;

  /**
   * 物料明细
   */
  @ApiModelProperty(value = "物料明细")
  @NotEmpty(message = "物料明细不能为空！")
  @Valid
  private List<PurchaseOrderWarehouseApplyAddFormDetail> productDetailList;

  /**
   * source 来源
   */
  @ApiModelProperty(value = "source", hidden = true)
  private String source;

  @Data
  public static class PurchaseOrderWarehouseApplyAddFormDetail {
    /**
     * 物料明细id
     */
    @NotBlank(message = "物料明细id不能为空")
    private String id;

    /**
     * 本次申请的数量
     */
    @NotNull(message = "本次申请的数量不能为空")
    private BigDecimal applyNum;

    /**
     * 本次质检数量
     */
    @ApiModelProperty(hidden = true)
    private BigDecimal qualityNum;
  }
}
