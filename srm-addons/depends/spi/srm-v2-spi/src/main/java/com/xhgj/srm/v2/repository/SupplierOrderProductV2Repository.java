package com.xhgj.srm.v2.repository;

import com.xhgj.srm.jpa.entity.v2.SupplierOrderProductV2;
import com.xhiot.boot.framework.jpa.repository.BootBaseRepository;

/**
 * <AUTHOR>
 * @since 2022/11/28 15:03
 */
public interface SupplierOrderProductV2Repository extends BootBaseRepository<SupplierOrderProductV2,String> {

  /**
   * 根据物料编码获得物料
   * @param code 物料编码必传
   */
  SupplierOrderProductV2 getFirstByCode(String code);
}
