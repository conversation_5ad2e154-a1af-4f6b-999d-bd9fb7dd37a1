package com.xhgj.srm.v2.form;/**
 * @since 2025/5/14 18:33
 */

import com.xhiot.boot.core.common.exception.CheckException;
import lombok.Data;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 *<AUTHOR>
 *@date 2025/5/14 18:33:22
 *@description 飞搭质检回调
 */
@Data
public class WarehouseApplyQualityCheckCallback {
  /**
   * 入库申请单号-行号
   */
  @NotBlank(message = "入库申请单号-行号不能为空")
  private String warehouseApplyCodeAndIndex;
  /**
   * 已质检数量
   */
  @NotNull(message = "已质检数量不能为空")
  private BigDecimal inspectQty;

  /**
   * 拒绝入库数量
   */
  private BigDecimal cancelQty;
  /**
   * 确认入库数量
   */
  @NotNull(message = "确认入库数量不能为空")
  private BigDecimal confirmQty;
  /**
   * 操作人
   */
  private String operator;

  public BigDecimal getCancelQty() {
    if (cancelQty == null) {
      return inspectQty.subtract(confirmQty);
    }
    return cancelQty;
  }

  public String getWarehouseApplyCode() {
    String[] parts = warehouseApplyCodeAndIndex.split("-");
    if (parts.length > 0) {
      return parts[0];
    }
    throw new CheckException("入库申请单号格式错误");
  }

  public String getWarehouseApplyIndex() {
    String[] parts = warehouseApplyCodeAndIndex.split("-");
    if (parts.length > 1) {
      return parts[1];
    }
    throw new CheckException("入库申请行号格式错误");
  }
}
