use srm;


CREATE TABLE t_invoice_verification (
                                      c_invoice_number varchar(100) NOT NULL COMMENT '发票号',
                                      c_data_json TEXT NULL COMMENT 'json数据',
                                      c_create_time bigint(20) NULL COMMENT '创建时间',
                                      c_update_time bigint(20) NULL COMMENT '更新时间',
                                      create_man varchar(32) NULL COMMENT '创建人',
                                      update_man varchar(30) NULL COMMENT '更新人',
                                      CONSTRAINT t_invoice_verification_pk PRIMARY KEY (c_invoice_number)
)
  ENGINE=InnoDB
DEFAULT CHARSET=utf8mb4
COLLATE=utf8mb4_general_ci
COMMENT='发票验真信息表';


RENAME TABLE t_order_account_invoice TO t_order_supplier_invoice;

-- 含税金额改名
ALTER TABLE t_order_supplier_invoice CHANGE c_price c_total_amount_including_tax decimal(20,10) NULL COMMENT '价税合计';
ALTER TABLE t_order_supplier_invoice MODIFY COLUMN c_total_amount_including_tax decimal(20,10) NULL COMMENT '价税合计';



ALTER TABLE t_order_supplier_invoice ADD c_total_amount decimal(20,10) NULL COMMENT '合计金额';
ALTER TABLE t_order_supplier_invoice ADD c_total_tax_amount decimal(20,10) NULL COMMENT '合计税额';
ALTER TABLE t_order_supplier_invoice ADD c_invoice_type varchar(1) NULL COMMENT '电子专用发票：20，专用发票：01，电子发票（专用发票）：31';
ALTER TABLE t_order_supplier_invoice ADD c_update_time bigint(20) NULL COMMENT '修改时间';

ALTER TABLE t_order_supplier_invoice ADD c_state varchar(1) NULL COMMENT '数据状态';
update t_order_supplier_invoice set c_state = "1";


ALTER TABLE t_order_supplier_invoice MODIFY COLUMN c_state varchar(1) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT 1 NULL COMMENT '数据状态';

ALTER TABLE t_order_supplier_invoice ADD order_invoice_relation_id varchar(32) NULL COMMENT '订单和供应商开票关联表id';

ALTER TABLE t_order ADD order_invoice_relation_id varchar(32) NULL COMMENT '订单和供应商开票id';

ALTER TABLE t_order_supplier_invoice MODIFY COLUMN c_invoice_type varchar(2) CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '电子专用发票：20，专用发票：01，电子发票（专用发票）：31';

-- 订单发票关联表
CREATE TABLE `t_order_invoice_relation` (
                                          `id` varchar(32) NOT NULL,
                                          `c_order_nums` varchar(500) DEFAULT NULL COMMENT '订单id 多个',
                                          `c_invoice_nums` varchar(500) DEFAULT NULL COMMENT '发票id 多个',
                                          `c_invoice_amount` decimal(16,10) DEFAULT NULL COMMENT '发票价税合计',
                                          `c_order_amount` decimal(16,10) DEFAULT NULL COMMENT '订单价税合计',
                                          `c_supplier_id` varchar(32) DEFAULT NULL COMMENT '供应商id',
                                          `c_platform` varchar(50) DEFAULT NULL COMMENT '下单平台',
                                          `c_invoice_state` char(1) DEFAULT NULL COMMENT '1 审核中 2 暂存 3 通过 4 驳回',
                                          `c_operator` varchar(20) DEFAULT NULL COMMENT '应处理人',
                                          `c_auditor` varchar(20) DEFAULT NULL COMMENT '审核人',
                                          `c_examine_time` bigint(32) DEFAULT NULL COMMENT '审核时间',
                                          `c_create_time` bigint(20) DEFAULT NULL COMMENT '创建时间',
                                          `c_update_time` bigint(20) DEFAULT NULL COMMENT '修改时间',
                                          `c_create_man` varchar(32) CHARACTER SET utf8 DEFAULT NULL COMMENT '创建人',
                                          `c_update_man` varchar(32) CHARACTER SET utf8 DEFAULT NULL COMMENT '修改人',
                                          `c_rejection` varchar(255) DEFAULT NULL COMMENT '驳回理由',
                                          `c_erp_payable_no` varchar(32) CHARACTER SET utf8 DEFAULT NULL COMMENT 'erp应付单单号',
                                          `c_erp_payable_money` decimal(20,10) DEFAULT NULL COMMENT 'erp应付单应付金额',
                                          `c_add_invoice_num` varchar(30) DEFAULT NULL COMMENT '增值税发票号',
                                          `c_state` varchar(1) DEFAULT '1' COMMENT '数据状态',
                                          `c_account_id` varchar(32) DEFAULT NULL COMMENT '原对账单id',
                                          PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='订单发票关联表';



ALTER TABLE t_order_invoice_relation MODIFY COLUMN c_state varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT 1 NULL COMMENT '数据状态';
ALTER TABLE t_order_supplier_invoice ADD c_verification_type varchar(2) NULL COMMENT '验真类型';
-- 老发票修改验真类型为已验真
update t_order_supplier_invoice set c_verification_type = "3";

-- 新增验收凭证审核状态
ALTER TABLE t_order ADD c_confirm_voucher_audit_status varchar(1) NULL COMMENT '验收凭证审核状态； 1：审核中、2：驳回、3：审核通过';
ALTER TABLE t_order MODIFY COLUMN c_account_open_invoice_status varchar(1) CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '对账单（供应商）开票状态/ v5.9.0版本改为供应商开票状态';

-- 订单详情添加字段
ALTER TABLE t_order_detail ADD `c_tax_free_cb_price` decimal(20,10) DEFAULT NULL COMMENT '去税单价';
ALTER TABLE t_order_detail ADD `c_total_amount` decimal(20,10) DEFAULT NULL COMMENT '合计金额';
ALTER TABLE t_order_detail ADD `c_total_tax_amount` decimal(20,10) DEFAULT NULL COMMENT '合计税额';
ALTER TABLE t_order_detail ADD `c_total_amount_including_tax` decimal(20,10) DEFAULT NULL COMMENT '价税合计';


ALTER TABLE t_order ADD c_standby varchar(100) NULL COMMENT '对账单开票状态的备用字段';

ALTER TABLE t_order_account ADD c_standby varchar(100) NULL COMMENT '对账单开票字段的备用字段';

ALTER TABLE t_order_invoice_relation MODIFY COLUMN c_order_nums varchar(5000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '订单id 多个';
ALTER TABLE t_order_invoice_relation MODIFY COLUMN c_invoice_nums varchar(5000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '发票id 多个';
update t_order_account set c_state = '0' where c_account_state = '3'

-------------------2023/08/28 供应商列表优化--------------------
ALTER TABLE t_brand ADD INDEX FKA_index_relation (c_relation_id);

ALTER TABLE t_contact ADD INDEX FKA_index_group (supplier_in_group_id);

