use
srm;
ALTER TABLE t_supplier
    ADD c_open_supplier_order BOOL NULL COMMENT '是否开启供应商订单';
-- 将是否开启供应商订单的值设置全部设置为 false
update t_supplier t
set t.c_open_supplier_order = false
where t.c_state = '1'
  and t.c_createTime < 1672416000000;

-- srm_zhens.t_supplier_order definition

CREATE TABLE `t_supplier_order`
(
    `id`                            varchar(32) NOT NULL COMMENT '主键',
    `supplier_id`                   varchar(32)     DEFAULT NULL COMMENT '供应商 id',
    `c_supplier_name`               varchar(100)    DEFAULT NULL COMMENT '供应商名称',
    `c_code`                        varchar(20)     DEFAULT NULL COMMENT '订单号',
    `c_erp_id`                      varchar(100)    DEFAULT NULL COMMENT 'erp 单据 id',
    `c_order_create_time`           bigint(20) DEFAULT NULL COMMENT '单据创建时间',
    `c_group_code`                  varchar(50)     DEFAULT NULL COMMENT '采购组织编码（erp）',
    `c_group_name`                  varchar(100)    DEFAULT NULL COMMENT '采购组织名称',
    `c_purchase_time`               bigint(20) DEFAULT NULL COMMENT '供应商采购日期',
    `c_direct_shipment`             tinyint(1) DEFAULT NULL COMMENT '是否厂家直发',
    `c_price`                       decimal(20, 10) DEFAULT NULL COMMENT '订单金额',
    `c_cancel_return_price`         decimal(20, 10) DEFAULT NULL COMMENT '退货/取消金额',
    `c_final_price`                 decimal(20, 10) DEFAULT NULL COMMENT '最终结算金额',
    `c_receive_mobile`              varchar(50)     DEFAULT NULL COMMENT '联系方式',
    `c_receive_man`                 varchar(50)     DEFAULT NULL COMMENT '收件人',
    `c_purchase_man`                varchar(50)     DEFAULT NULL COMMENT '采购员',
    `c_purchase_code`               varchar(50)     DEFAULT NULL COMMENT '采购员编码',
    `purchase_id`                   varchar(32)     DEFAULT NULL COMMENT '采购 id',
    `c_receive_address`             varchar(200)    DEFAULT NULL COMMENT '收货地址',
    `c_order_confirm_state`         tinyint(1) DEFAULT NULL COMMENT '单子是否确认',
    `c_order_cancel_state`          tinyint(1) DEFAULT NULL COMMENT '单子是否取消',
    `c_order_return_state`          tinyint(1) DEFAULT NULL COMMENT '单子是否有退货',
    `c_order_ship_wait_stock_state` tinyint(1) DEFAULT NULL COMMENT '是否有发货待入库',
    `c_order_state`                 varchar(1)      DEFAULT NULL COMMENT '订单状态',
    `c_update_time`                 bigint(20) DEFAULT NULL COMMENT '修改时间',
    `c_state`                       varchar(1)      DEFAULT NULL COMMENT '数据状态',
    `c_create_time`                 bigint(20) DEFAULT NULL COMMENT '创建时间',
    `c_total_num`                   decimal(20, 10) DEFAULT '0.0000000000' COMMENT '采购件数',
    `c_stock_progress`              varchar(100)    DEFAULT NULL COMMENT '入库进度',
    `c_total_stock_input_qty`       decimal(20, 10) DEFAULT '0.0000000000' COMMENT '总入库数量',
    `c_payment_key`                 varchar(10)     DEFAULT NULL COMMENT 'erp 中的支付方式 key 值',
    `c_payment_value`               varchar(10)     DEFAULT NULL COMMENT 'erp 中的支付方式 value 值',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='供应商订单表';


-- srm_zhens.t_supplier_order_detail definition

CREATE TABLE `t_supplier_order_detail`
(
    `id`                     varchar(32) NOT NULL COMMENT '主键',
    `order_product_id`       varchar(32)     DEFAULT NULL COMMENT '物料 id',
    `order_to_form_id`       varchar(32)     DEFAULT NULL COMMENT '表单 id',
    `c_erp_id`               varchar(50)     DEFAULT NULL COMMENT 'erp id',
    `c_erp_row_num`          int(11) DEFAULT NULL COMMENT 'erp 行号',
    `c_deliver_time`         bigint(20) DEFAULT NULL COMMENT '交货时间',
    `c_num`                  decimal(20, 10) DEFAULT '0.0000000000' COMMENT '采购数量',
    `c_wait_qty`             decimal(20, 10) DEFAULT '0.0000000000' COMMENT '待发数量',
    `c_ship_qty`             decimal(20, 10) DEFAULT '0.0000000000' COMMENT '已发数量',
    `c_return_qty`           decimal(20, 10) DEFAULT '0.0000000000' COMMENT '退货数量',
    `c_cancel_qty`           decimal(20, 10) DEFAULT '0.0000000000' COMMENT '取消数量',
    `c_stock_input_qty`      decimal(20, 10) DEFAULT '0.0000000000' COMMENT '采购入库数量',
    `c_wait_stock_input_qty` decimal(20, 10) DEFAULT '0.0000000000' COMMENT '待入库数量',
    `c_remain_qty`           decimal(20, 10) DEFAULT '0.0000000000' COMMENT '剩余入库数量',
    `c_stock_output_qty`     decimal(20, 10) DEFAULT '0.0000000000' COMMENT '退库数量',
    `c_settle_qty`           decimal(20, 10) DEFAULT '0.0000000000' COMMENT '实际结算数量',
    `c_total_price`          decimal(20, 10) DEFAULT NULL COMMENT '金额',
    `c_mark`                 varchar(200)    DEFAULT NULL COMMENT '备注',
    `c_sales_order_no`       varchar(100)    DEFAULT NULL COMMENT '销售订单号',
    `c_create_time`          bigint(20) DEFAULT NULL COMMENT '创建时间',
    `c_update_time`          bigint(20) DEFAULT NULL COMMENT '修改时间',
    `c_state`                varchar(1)      DEFAULT NULL COMMENT '数据状态',
    `c_sort_num`             int(11) DEFAULT NULL COMMENT '排序号(明细时等于 erp 行号，其它等于对应明细的行号)',
    `detailed_id`            varchar(32)     DEFAULT NULL COMMENT '该行对应的明细的 id',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;


-- srm_zhens.t_supplier_order_product definition

CREATE TABLE `t_supplier_order_product`
(
    `id`           varchar(32) NOT NULL COMMENT '主键',
    `c_code`       varchar(32)     DEFAULT NULL COMMENT '物料编码',
    `c_brand`      varchar(100)    DEFAULT NULL COMMENT '品牌',
    `c_name`       varchar(50)     DEFAULT NULL COMMENT '商品名称',
    `c_manu_code`  varchar(100)    DEFAULT NULL COMMENT '规格型号',
    `c_unit`       varchar(10)     DEFAULT NULL COMMENT '物料单位',
    `c_unit_code`  varchar(50)     DEFAULT NULL COMMENT '物料单位编码',
    `c_price`      decimal(20, 10) DEFAULT NULL COMMENT '单价',
    `c_unit_digit` int(2) DEFAULT NULL COMMENT '单位的位数',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='订单物料';


-- srm_zhens.t_supplier_order_to_form definition

CREATE TABLE `t_supplier_order_to_form`
(
    `id`                  varchar(32) NOT NULL COMMENT '主键',
    `supplier_order_id`   varchar(32)     DEFAULT NULL COMMENT '供应商订单 id',
    `c_type`              varchar(2)      DEFAULT NULL COMMENT '类型',
    `c_create_time`       bigint(20) DEFAULT NULL COMMENT '创建时间',
    `c_time`              bigint(20) DEFAULT NULL COMMENT '发货时间、退货时间、取消时间',
    `c_logistics_company` varchar(50)     DEFAULT NULL COMMENT '物流公司',
    `c_logistics_code` varchar(50)     DEFAULT NULL COMMENT '物流编码',
    `c_track_num`         varchar(50)     DEFAULT NULL COMMENT '快递单号',
    `c_status`            varchar(2)      DEFAULT NULL COMMENT '单子状态',
    `c_state`             varchar(1)      DEFAULT NULL COMMENT '数据状态',
    `c_update_time`       bigint(20) DEFAULT NULL COMMENT '修改时间',
    `c_numbers`           varchar(50)     DEFAULT NULL COMMENT '退货编号、取消编号',
    `c_return_price`      decimal(20, 10) DEFAULT NULL COMMENT '退款金额',
    `c_erp_id`            varchar(50)     DEFAULT NULL COMMENT 'erp id',
    `c_assess_result`     varchar(100)    DEFAULT NULL COMMENT '审核结果',
    `c_reject_reason`     varchar(200)    DEFAULT NULL COMMENT '驳回原因',
    `c_num`               decimal(20, 10) DEFAULT '0.0000000000' COMMENT '发货、取消、退货数量',
    `c_stock_output`      tinyint(1) DEFAULT NULL COMMENT '退货单是否有退库',
    `c_return_stock`      varchar(50)     DEFAULT NULL COMMENT '采购退料单号',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='订单对应的单据表单';

ALTER TABLE t_supplier_order_product ADD c_brand_code varchar(100) NULL COMMENT '品牌编码';
ALTER TABLE t_supplier_order_product CHANGE c_brand_code c_brand_code varchar(100) NULL COMMENT '品牌编码' AFTER c_brand;
