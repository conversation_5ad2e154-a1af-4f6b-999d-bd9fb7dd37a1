use
    srm;
ALTER TABLE t_supplier_order
    ADD c_confirm_time BIGINT(20) NULL COMMENT '订单确认时间';
ALTER TABLE t_supplier_order
    ADD c_complete_ship_time BIGINT(20) NULL COMMENT '完成发货时间(最后一次发货)';
ALTER TABLE t_supplier_order
    ADD c_first_ship_time BIGINT(20) NULL COMMENT '第一次发货时间';
ALTER TABLE t_supplier_order
    CHANGE c_first_time c_first_time BIGINT(20) NULL COMMENT '第一次发货时间' AFTER c_confirm_time;
ALTER TABLE t_supplier_order
    ADD c_complete_order_time BIGINT(20) NULL COMMENT '订单完成时间';

ALTER TABLE t_supplier
    ADD c_order_receive_time_limit INT(4) NULL COMMENT '供应商接单时限';

ALTER TABLE t_supplier_order_to_form
    ADD c_batch_number varchar(20) NULL COMMENT '单据批次号';

-- srm_zhens.c_config_short_message definition

CREATE TABLE `t_config_short_message`
(
    `id`      varchar(32) NOT NULL COMMENT '主键',
    `c_type`  varchar(2) DEFAULT NULL COMMENT '短信配置类型',
    `c_open`  tinyint(1) DEFAULT NULL COMMENT '是否开启短信发送',
    `c_state` varchar(1) DEFAULT NULL COMMENT '数据状态',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8 COMMENT ='短信服务配置';


ALTER TABLE t_supplier_order_to_form
    ADD c_return_id varchar(100) NULL COMMENT '采购退料单 Id';

ALTER TABLE t_supplier_order
    ADD c_send_reminder_count INT(2) NULL COMMENT '发送催单短信的次数';

ALTER TABLE t_supplier_order_detail
    ADD c_detailed_erp_id varchar(32) NULL COMMENT '该行对应的明细 erp id';

ALTER TABLE t_supplier_order_to_form
    MODIFY COLUMN c_return_id varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '采购退料单 Id';
ALTER TABLE t_supplier_order_to_form
    MODIFY COLUMN c_return_stock varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '采购退料单号';
ALTER TABLE t_supplier_order_detail
    ADD c_return_row_and_num_json varchar(1000) NULL COMMENT '退料单实际退货数量记录';

ALTER TABLE t_supplier_order_to_form ADD c_warehousing BOOL NULL COMMENT '是否入库';

ALTER TABLE t_supplier
    ADD c_platform varchar(200) NULL COMMENT '接单平台';







