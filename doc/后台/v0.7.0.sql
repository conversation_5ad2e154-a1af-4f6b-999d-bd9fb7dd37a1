INSERT INTO t_export_filed_base
(id, super_id, c_name, c_sort, c_type, c_regular_filed, c_state, c_default_select)
VALUES('555', '46', '规格', 7, '21', NULL, '1', 1);

INSERT INTO t_export_filed_base
(id, super_id, c_name, c_sort, c_type, c_regular_filed, c_state, c_default_select)
VALUES('556', '46', '型号', 7, '21', NULL, '1', 1);

INSERT INTO t_field_config
(id, c_name, c_key, c_group_type, c_is_default, c_is_modify, c_title, c_big_type, c_ext_json, c_sort)
VALUES('50289b2892fba3140163004ad4ce0001', '规格', null, '5', '1', '1', '物料视图', '1', null, 150);

INSERT INTO t_field_config
(id, c_name, c_key, c_group_type, c_is_default, c_is_modify, c_title, c_big_type, c_ext_json, c_sort)
VALUES('50289b2892fba3140163004ad4ce0002', '型号', null, '5', '1', '1', '物料视图', '1', null, 151);


--退换货订单admin账号支持导出组织下全部数据
INSERT INTO t_permission_type
(id, c_permission_code, user_id, c_type, c_state, c_create_time)
VALUES('40289b288e316b28018e316c0701031d', '1', '402881756ce75bf8016ce75e01eb0003', '19', '1', 1739345779000);

## 迁移记录
CREATE TABLE `t_migration_record`
(
  `id`            varchar(32) NOT NULL COMMENT '记录id',
  `c_batch_no`    varchar(32)  DEFAULT NULL COMMENT '迁移批次号，同一个迁移批号',
  `c_table_name`  varchar(100) DEFAULT NULL COMMENT '迁移表名',
  `c_origin_id`   varchar(32)  DEFAULT NULL COMMENT '原始id',
  `c_new_id`      varchar(32)  DEFAULT NULL COMMENT '新id',
  `c_create_time` bigint(20) DEFAULT NULL COMMENT '创建时间',
  `c_state`       varchar(1)   DEFAULT NULL COMMENT '数据状态',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='迁移记录表';

## 修改组装拆卸单model长度
ALTER TABLE `t_asm_dis_order_item`
  MODIFY COLUMN `c_model` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '规格型号';
