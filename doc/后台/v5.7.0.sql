-- 对账单表增加驳回理由、下单平台字段
ALTER TABLE t_order_account ADD c_grounds_for_rejection varchar(100) NULL COMMENT '驳回理由';
ALTER TABLE t_order_account ADD c_platform_code varchar(32) NULL COMMENT '下单平台Code v5.7之前有多种平台订单同时开票的逻辑，之后改为同一平台才能批量开票';
-- 更改落地商合同表付款比例字段
ALTER TABLE t_landing_contract CHANGE c_payment_ratio_type c_payment_ratio varchar(5) CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '付款比例百分比';
-- 落地商合同表 增加发起付款条件字段
ALTER TABLE t_landing_contract ADD c_payment_condition varchar(32) NULL COMMENT '发起付款条件（枚举值）';
-- 落地商合同表增加付款形式字段
ALTER TABLE t_landing_contract ADD c_payment_type varchar(5) NULL COMMENT '付款形式';
ALTER TABLE t_landing_contract ADD c_back_to_back tinyint(1) NULL COMMENT '账期（是否为背靠背）';
-- 合同付款形式旧数据全部为“与客户回款形式一致”
update t_landing_contract set c_payment_type = "3";
ALTER TABLE t_order_account ADD c_erp_payable_no varchar(32) NULL COMMENT 'erp应付单单号';
ALTER TABLE t_order_account ADD c_erp_payable_money decimal(20,10) NULL COMMENT 'erp应付单应付金额';







