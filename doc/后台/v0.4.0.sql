# 采购订单form v2 新增字段
ALTER TABLE `t_supplier_order_to_form_v2`
  ADD COLUMN `c_warehouse_name` varchar(50) NULL COMMENT '仓库名称(入库申请单/入库单/退库单)' AFTER `c_number`,
  ADD COLUMN `c_warehouse_code` varchar(50) NULL COMMENT '仓库编码(入库申请单/入库单/退库单)' AFTER `c_warehouse_name`,
  ADD COLUMN `c_posting_date` bigint(20) NULL COMMENT '过账日期/退库日期' AFTER `c_warehouse_code`,
  ADD COLUMN `c_quality_check` tinyint(1) NULL COMMENT '是否质检' AFTER `c_posting_date`,
  ADD COLUMN `c_review_id` varchar(32) NULL COMMENT '审核id' AFTER `c_quality_check`,
  ADD COLUMN `c_review_time` bigint(20) NULL COMMENT '审核时间' AFTER `c_review_id`,
  ADD COLUMN `c_review_reason` varchar(200) NULL COMMENT '审核原因' AFTER `c_review_time`,
  ADD COLUMN `c_review_status` tinyint(2) NULL COMMENT '审核状态' AFTER `c_review_reason`,
  ADD COLUMN `c_call_status` tinyint(2) NULL COMMENT '三方调用状态' AFTER `c_review_reason`,
  ADD COLUMN `c_call_time` bigint(20) NULL COMMENT '三方接口调用时间' AFTER `c_call_status`,
  ADD COLUMN `c_form_code` varchar(32) NULL COMMENT '采购订单form单号' AFTER `c_call_time`,
  ADD COLUMN `c_create_user` varchar(32) NULL COMMENT '创建用户id' AFTER `c_form_code`,
  ADD COLUMN `c_create_user_name` varchar(50) NULL COMMENT '创建用户名称' AFTER `c_create_user`;

# 采购订单详情表v2 新增字段
ALTER TABLE `t_supplier_order_detail_v2`
  ADD COLUMN `c_inspect_qty` decimal(20, 10) NULL COMMENT '质检数量' AFTER `c_order_to_form_type`,
  ADD COLUMN `c_in_warehouse_apply_id` varchar(32) NULL COMMENT '入库单申请单id' AFTER `c_inspect_qty`,
  ADD COLUMN `c_in_warehouse_apply_name` varchar(80) NULL COMMENT '入库单申请单名称' AFTER `c_in_warehouse_apply_id`,
  ADD COLUMN `c_index` varchar(10) NULL COMMENT '序号' AFTER `c_in_warehouse_apply_name`;

--库位管理增加新字段
ALTER TABLE t_inventory_location ADD c_prepare_receive_order_type varchar(100) NULL COMMENT '允许SRM制单和收货的订单类型';
ALTER TABLE t_inventory_location ADD c_allow_return_type varchar(2) NULL COMMENT '是否允许SRM退货 0 否 1是';
ALTER TABLE t_inventory_location ADD c_inbound_return_reversal varchar(2) NULL COMMENT '是否允许SRM入库单退库单冲销 0 否 1是';
ALTER TABLE t_inventory_location ADD c_allow_transfer varchar(2) NULL COMMENT '是否允许SRM调拨 0 否 1是';
ALTER TABLE t_inventory_location ADD c_allow_assemble_disassemble varchar(2) NULL COMMENT '是否允许SRM组装拆卸 0 否 1是';

-- 审批配置
INSERT INTO t_approval_template
(id, c_process_code, c_system, c_system_name, c_process_name)
VALUES(replace(uuid(),_utf8'-',_utf8''), 'FLOW1922596565729480706', '1', 'SRM', '采购订单提交退库');

INSERT INTO t_approval_template
(id, c_process_code, c_system, c_system_name, c_process_name)
VALUES(replace(uuid(),_utf8'-',_utf8''), 'FLOW1922827299232747522', '1', 'SRM', '采购订单入库单审核流程');

INSERT INTO t_approval_template
(id, c_process_code, c_system, c_system_name, c_process_name)
VALUES(replace(uuid(),_utf8'-',_utf8''), 'FLOW1917167125750939649', '1', 'SRM', '采购订单审核流程');


ALTER TABLE `t_supplier_order_product_v2`
  MODIFY COLUMN `c_manu_code` varchar(300) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '规格型号' AFTER `c_name`;


