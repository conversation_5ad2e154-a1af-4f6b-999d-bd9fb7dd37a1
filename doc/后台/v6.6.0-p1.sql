## 订单履约发货单增加已退数量
ALTER TABLE `t_order_delivery_detail`
  ADD COLUMN `c_return_num` decimal(20, 10) NULL COMMENT '退货数量';
## 订单履约退款单增加关联发货明细id
ALTER TABLE `t_order_return_detail`
  ADD COLUMN `c_delivery_detail_id` varchar(32) NULL COMMENT '关联发货明细id';
## 旧数据待处理
## 1.更新  t_order_return_detail中关联的发货明细id
ALTER TABLE `t_order_return_detail`
  ADD INDEX `index_c_delivery_detail_id`(`c_delivery_detail_id`);
## 需要备份t_order_return_detail
UPDATE t_order_return_detail r
  JOIN t_order_delivery_detail d ON d.c_code = r.c_code
  LEFT JOIN t_order_delivery td ON d.delivery_id = td.id
SET r.c_delivery_detail_id = d.id
WHERE r.c_delivery_detail_id IS NULL
  AND td.order_id = r.c_order_id
  AND d.c_ship_num = r.c_return_num;
## 需要备份t_order_delivery_detail
UPDATE t_order_delivery_detail d
  JOIN t_order_return_detail r ON d.id = r.c_delivery_detail_id
SET d.c_return_num = r.c_return_num
WHERE d.c_return_num IS NULL;

## 添加c_purchase_order_no字段
ALTER TABLE `t_order_return`
  ADD COLUMN `c_purchase_order_no` varchar(50) NULL COMMENT 'ERP 退货单采购订单号';

## 供应商表新增报备单权限字段
ALTER TABLE srm_prod.t_supplier
  ADD c_open_filing tinyint(1) NULL COMMENT '是否开启报备单';
## 旧数据处理
update t_supplier s
set s.c_open_filing = '0'
where s.c_cooperate_type = '1';
update t_supplier s
set s.c_open_filing = '1'
where s.c_cooperate_type = '2'
   or s.c_cooperate_type = '3';
