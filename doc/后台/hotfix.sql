ALTER TABLE t_order_supplier_invoice ADD c_check_code varchar(100) NULL COMMENT '校验码';
ALTER TABLE t_invoice_verification ADD c_check_code varchar(100) NULL COMMENT '校验码';


ALTER TABLE t_order_delivery ADD c_order_erp_no varchar(100) NULL COMMENT '入库单采购订单号';

--20240711临时需求
ALTER TABLE t_purchase_apply_for_order ADD c_is_worry_order varchar(1) NULL COMMENT '是否急单:Y/N';
ALTER TABLE t_purchase_apply_for_order ADD c_direct_shipment varchar(1) NULL COMMENT '是否直发：1是，0否';
ALTER TABLE t_supplier_order_to_form MODIFY COLUMN c_track_num varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '快递单号';
ALTER TABLE t_order_delivery MODIFY COLUMN c_express_no varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '物流单号';

--20240725-初始化旧数据
-- 自采单结算单价和结算总价为0
update t_supplier_order_detail set c_settlement_price = 0,c_total_settlement_price = 0
where order_to_form_id  in  (
  select distinct tsotf.id
  from t_supplier_order_to_form tsotf
  inner join t_supplier_order tso
  on tsotf.supplier_order_id  = tso.id
  where tsotf.c_type = 1 and tso.c_self_state = 1
);
-- 项目类型为寄售时，是否免费为0
update t_supplier_order_detail tsod set tsod.c_free_state = 0
where tsod.c_project_type = "K" and (tsod.c_free_state = "1" or tsod.c_free_state is null);
-- fix:字段超长报错
ALTER TABLE `t_payment_apply_record`
  MODIFY COLUMN `c_invoice_number` varchar (5000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '进项票发票号 多个' AFTER `c_supplier_order_no`;
ALTER TABLE `t_payment_apply_record`
  MODIFY COLUMN `c_supplier_order_no` varchar(5000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '申请订单号 多个' AFTER `c_reject_reason`;
-- 对接oms 20240801
ALTER TABLE t_order_filing ADD c_receive_address_name varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '客户收货地址省市区名称';

--20240805临时需求
ALTER TABLE t_landing_contract ADD c_registration_rate_update_status tinyint(1) NULL COMMENT '报备单推送的合同比例修改状态';

# 20240829 进项票相关票关联落地商订单字段修改
ALTER TABLE `t_input_invoice_order`
  ADD COLUMN `c_order_ids` text NULL COMMENT '落地商订单关联ids JSONARRAY';



ALTER TABLE `t_supplier_order`
  MODIFY COLUMN `c_project_no` varchar(5000) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '大票项目号';

ALTER TABLE `t_order_payment_to_order`
  ADD COLUMN `c_apply_price` decimal(20, 10) NULL COMMENT '付款申请金额';

## 添加payment_apply_detail索引
ALTER TABLE `t_payment_apply_detail`
  ADD INDEX(`c_supplier_order_no`);

## 修改 t_supplier_order 表 c_code的字符集
ALTER TABLE `t_supplier_order`
  MODIFY COLUMN `c_code` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '订单号';


## 添加t_order_payment_to_order索引
ALTER TABLE `t_order_payment_to_order`
  ADD INDEX(`relation_id`);

## 修改t_order表id字段字符集
ALTER TABLE `t_order`
  MODIFY COLUMN `id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT 'id';

## 添加t_order_payment_to_order索引
ALTER TABLE `t_order_payment_to_order`
  ADD INDEX `index_c_order_payment_id`(`c_order_payment_id`);

## payment_apply_record需要添加t_group_code字段，做数据隔离 + 迁移
ALTER TABLE `t_payment_apply_record`
  ADD COLUMN `c_group_code` varchar(50) NULL COMMENT '组织编码';

UPDATE t_payment_apply_record par
  JOIN (SELECT par.id,
               so.c_group_code
        FROM t_payment_apply_record par
               JOIN t_supplier_order so
                    ON so.c_code = SUBSTRING_INDEX(par.c_supplier_order_no, ',', 1)) temp
  ON par.id = temp.id
SET par.c_group_code = temp.c_group_code;

## 进项票与采购订单关联关系表变动，数据迁移
ALTER TABLE `t_supplier_invoice_to_detail`
  ADD COLUMN `c_input_invoice_order_id` varchar(32) NULL COMMENT '关联进项票id';


ALTER TABLE `t_order`
  ADD COLUMN `c_update_time` bigint(20) NULL COMMENT '更新时间 用于乐观锁更新';

## 2024年10月31日16:31:45 更新c_remaining_withdrawable_amount数据 (发票过账凭证)
update t_financial_vouchers
set c_remaining_withdrawable_amount = COALESCE(c_related_amount, 0) + COALESCE(c_refund_amount, 0) - COALESCE(c_offset_prepaid_amount,0) - COALESCE(c_withdrawn_amount, 0)
where c_state = '1' and c_voucher_type = '1';


## 2024年11月4日14:54:32 更新进项票payType 更新财务凭证的支付方式
ALTER TABLE `t_financial_vouchers`
  ADD INDEX `supplier_id_index`(`supplier_id`);

UPDATE t_input_invoice_order i
  JOIN t_supplier s ON CONVERT(i.c_supplier_id USING utf8) = s.id
SET i.c_pay_type = s.c_pay_type
WHERE i.c_source = '1'
  AND i.c_pay_type IS NULL
  AND i.c_state = '1';

UPDATE t_financial_vouchers f
  JOIN t_supplier s ON CONVERT(f.supplier_id USING utf8) = s.id
SET f.c_payment_type = s.c_pay_type
WHERE f.c_voucher_type = '1'
  AND f.c_payment_type IS NULL
  AND f.c_state = '1';


## 2024年11月11日11:22:22 增加 t_extrafile的c_name长度
ALTER TABLE `t_extrafile`
  MODIFY COLUMN `c_name` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL;

## 2024年11月12日10:58:01 增加 t_supplier_order_detail的c_project_no长度
ALTER TABLE `t_supplier_order_detail`
  MODIFY COLUMN `c_project_no` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '大票项目号';

ALTER TABLE `t_purchase_apply_for_order`
  MODIFY COLUMN `c_project_no` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '大票项目号';


## 2024年11月21日17:41:16 变更t_order_supplier_invoice表结构 增加索引
ALTER TABLE `t_order_supplier_invoice`
  MODIFY COLUMN `order_invoice_relation_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '订单和供应商开票关联表id',
  ADD INDEX `index_order_invoice_relation_id`(`order_invoice_relation_id`);
ALTER TABLE `t_supplier_invoice_to_detail`
  MODIFY COLUMN `c_invoice_number` varchar(4000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '发票号 '
  MODIFY COLUMN `c_seller` varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '销方名称';

## 2024年11月24日14:13:13变成file的url长度
ALTER TABLE `t_file`
  MODIFY COLUMN `c_url` varchar(150) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '文件url';

## 2024年11月24日14:13:13变成file的url长度
ALTER TABLE `t_file`
  MODIFY COLUMN `c_url` varchar(150) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL;

## 2024年11月26日18:56:50 t_order_invoice_to_order 添加索引 优化开票申请查询
ALTER TABLE `t_order_invoice_to_order`
  ADD INDEX `index_order_invoice_id`(`order_invoice_id`);

ALTER TABLE `t_order_invoice_to_order`
  ADD INDEX `index_order_id`(`order_id`);

## 2024年12月11日16:00:44 修改 客户订单报备单中的客户订单长度调整
ALTER TABLE `t_order_filing`
  MODIFY COLUMN `c_order_no` varchar (100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '外部订单号';

## 2024年12月25日13:17:15 对于发票识别表进行改造
ALTER TABLE `t_invoice_ocr_recognition`
  ADD COLUMN `c_total_amount` decimal(18, 2) NULL COMMENT '合计金额',
  ADD COLUMN `c_total_tax_amount` decimal(18, 2) NULL COMMENT '合计税额',
  ADD COLUMN `c_total_amount_including_tax` decimal(18, 2) NULL COMMENT '价税合计';

# 2024年12月26日16:40:25 对于用户管理数据权限进行修改。所有数据范围权限为采购订单和采购申请的，如果选择所在部门或所在部门及以下部门，则修改为所在组织
update t_permission_type
set c_permission_code = '4'
where c_type in ('5', '6')
  and c_permission_code in ('2', '3');



## 2025年1月2日11:27:44 修改账户名称长度
ALTER TABLE `t_entry_registration_landing_merchants`
  MODIFY COLUMN `c_name_of_account` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '账户名称';

## 2025年1月2日13:43:37 数据处理 用户 供应商等级审核
INSERT INTO t_user_check (id, userId, c_type, c_supplierType, c_userErpCode, c_createTime, c_state)
  SELECT
    CONCAT('2c90f05f92d2c9', LPAD(FLOOR(RAND() * 1000000000000000000), 18, '0')) AS id,
    uc.userId,
    uc.c_type,
    '2' AS c_supplierType,
    uc.c_userErpCode,
    UNIX_TIMESTAMP() * 1000 AS c_createTime,
    '1' AS c_state
  FROM t_user_check uc
    where uc.c_state = '1'
    and uc.c_supplierType = '3';

## 2025年1月5日14:30:56 修改客户单位长度
ALTER TABLE `t_order`
  MODIFY COLUMN `c_customer` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '客户单位';

## 2025年1月13日11:18:29 增加落地商付款单会计年份
ALTER TABLE `t_order_payment_collection`
  ADD COLUMN `c_accounting_year` varchar(20) NULL COMMENT 'SAP会计年份';


## 2025年2月11日13:10:40 ocr识别表增加字段
ALTER TABLE `t_invoice_ocr_recognition`
  ADD COLUMN `c_seller_name` varchar(400) NULL COMMENT '销方名称' ,
ADD COLUMN `c_payer_name` varchar(400) NULL COMMENT '购方名称';

## 添加索引
ALTER TABLE `t_purchase_info_record`
  ADD INDEX `i_c_supplier_code`(`c_supplier_code`);

## 添加索引
ALTER TABLE `t_supplier`
  ADD INDEX `i_mdm_code`(`mdm_code`);

## 添加期初订单标识
ALTER TABLE `t_financial_vouchers`
  ADD COLUMN `c_initial_order` tinyint(1) NULL DEFAULT 0 COMMENT '期初订单标识';

ALTER TABLE `t_payment_apply_record`
  ADD COLUMN `c_initial_order` tinyint(1) NULL DEFAULT 0 COMMENT '期初订单标识';


## 添加品牌表的授权开始时间和结束时间
ALTER TABLE `t_brand`
  ADD COLUMN `c_accredit_start_time` BIGINT   NULL     COMMENT '授权开始时间'  AFTER `c_associated_id`,
	ADD COLUMN `c_accredit_end_time` BIGINT   NULL     COMMENT '授权结束时间'  AFTER `c_accredit_start_time`;


## 增加orderPayment字段
ALTER TABLE `t_order_payment`
  ADD COLUMN `c_bank_code` varchar(200) NULL COMMENT '开户银行编码' AFTER `c_reason`;

## 添加字段
ALTER TABLE `t_product`
  ADD COLUMN `c_tax_category_abbr` varchar(100) NULL COMMENT '税收分类简称';

## 添加OrderPartialPayment表的paymentId字段
ALTER TABLE `t_order_partial_payment`
  ADD COLUMN `c_payment_id` varchar(32) NULL COMMENT '付款单ID';
