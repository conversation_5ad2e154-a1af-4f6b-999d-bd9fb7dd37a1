ALTER TABLE t_order_invoice_relation MODIFY COLUMN c_order_nums text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '订单号 多个';
ALTER TABLE t_order_invoice_relation MODIFY COLUMN c_invoice_nums text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '发票号 多个';
ALTER TABLE t_order_invoice_relation MODIFY COLUMN c_order_codes text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '采购订单号';

ALTER TABLE t_order_invoice_relation ADD c_invoice_type text NULL COMMENT '所有发票类型（去重）';
ALTER TABLE t_order_invoice_relation ADD c_first_open_invoice_date BIGINT(13) NULL COMMENT '最早开票日期';
ALTER TABLE t_order_invoice_relation ADD c_total_amount_of_tax_deduction decimal(20,10) NULL COMMENT '去税合计（此发票单里的所有发票去税金额之和）';

ALTER TABLE t_financial_vouchers ADD c_payment_apply_line_number varchar(15) NULL COMMENT '付款申请行号';

ALTER TABLE t_supplier_order_detail ADD c_freight_supplier_id varchar(32) NULL COMMENT '运费供应商 id';
ALTER TABLE t_supplier_order_detail ADD c_freight_supplier_name varchar(100) NULL COMMENT '运费供应商名称';
ALTER TABLE t_supplier_order_detail ADD c_tariff_supplier_id varchar(32) NULL COMMENT '关税供应商 id';
ALTER TABLE t_supplier_order_detail ADD c_tariff_supplier_name varchar(100) NULL COMMENT '关税供应商名称';

ALTER TABLE t_supplier_order_to_form ADD c_return_warehouse varchar(20) NULL COMMENT '退货仓库编码';
ALTER TABLE t_supplier_order_to_form ADD c_consignee varchar(140) NULL COMMENT '收件人';
ALTER TABLE t_supplier_order_to_form ADD c_receive_address varchar(140) NULL COMMENT '收货地址';

ALTER TABLE t_invoice_verification ADD c_seller_name varchar(25) NULL COMMENT '销方名称';















ALTER TABLE srm_prod.t_order_payment ADD c_auto_draw TINYINT(1) NULL COMMENT '是否自动提款';

update t_order_payment set c_auto_draw = false  where
  id in (
    select id from (
                     select p.id,sum(IF(o.c_erp_type = '金蝶订单',1,0)) sumerp from `t_order_payment` p
                                                                                      left join `t_order_payment_to_order` po
                                                                                                on p.`id` = po.`c_order_payment_id`
                                                                                      left join `t_order` o
                                                                                                on po.`relation_id` = o.id
                     where p.`c_state` = '1' and po.c_state = '1'
                       and o.c_state = '1' and po.`c_type` = '1'
                     group by p.id
                     HAVING sumerp > 0)a
  )

update t_order_payment set c_auto_draw = true where c_auto_draw is null



ALTER TABLE srm_prod.t_order_payment_collection ADD c_url varchar(200) NULL COMMENT '银行回款单链接';








CREATE TABLE t_invoice_ocr_recognition (
                                                  id varchar(32) NOT NULL COMMENT 'id',
                                                  ocr_info text NULL COMMENT 'ocr识别json数据',
                                                  CONSTRAINT t_invoice_ocr_recognition_pk PRIMARY KEY (id)
)
  ENGINE=InnoDB
DEFAULT CHARSET=utf8mb4
COLLATE=utf8mb4_general_ci
COMMENT='发票ocr识别信息';

ALTER TABLE t_invoice_ocr_recognition ADD c_invoice_number varchar(50) NULL COMMENT '发票号';

ALTER TABLE t_invoice_ocr_recognition CHANGE ocr_info c_ocr_info text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT 'ocr识别json数据';

ALTER TABLE t_order_invoice_relation ADD c_is_old varchar(1) NULL COMMENT '旧订单标识';

RENAME TABLE t_order_invoice_relation TO t_input_invoice_order;
update t_input_invoice_order set c_is_old = "1";







UPDATE t_financial SET c_bankNum = '1' WHERE c_bankNum REGEXP '^0+$';
