# v3.3.3
#合同表新增负责采购字段
ALTER TABLE t_contract ADD c_purchaserName VARCHAR(50);
ALTER TABLE t_contract ADD c_purchaserManId VARCHAR(32);

#汪东纹 20210903 新增拉黑审核状态及拉黑审核人
ALTER TABLE t_supplier ADD c_shield_state VARCHAR(1);
ALTER TABLE t_supplier ADD c_shield_manager VARCHAR(32);

#任务中心表--wangdw
create table t_mission
(
   id             varchar(32) not null,
   c_type      	  varchar(50) comment '操作类型',
   c_code         varchar(25) comment '任务编码',
   c_total_row          int comment '总行数',
   c_create_man    varchar(10) comment '创建人',
   c_create_man_id   varchar(32) comment '创建人id',
   c_file_name   varchar(50) comment '文件名',
   c_link   varchar(200) comment '下载链接',
   c_reason   varchar(100) comment '失败原因',
   c_state   varchar(1) comment '状态',
   c_fail_link          varchar(200) comment '失败链接',
   c_start_time         bigint comment '开始时间',
   c_complete_time      bigint comment '完成时间',
   primary key (id)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8
    COMMENT '任务中心表';

#任务中心表--wangdw
create table t_mission_detail
(
   id             varchar(32) not null,
   mission_id     varchar(32) comment '任务id',
   c_sign         varchar(50) comment '标识',
   c_information  varchar(200) comment '失败信息',
   c_state   varchar(1) comment '状态',
   c_create_time         bigint comment '创建时间',
   primary key (id)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8
    COMMENT '任务中心详情表';

#--wangdw 210917 任务表新增来源字段
alter table t_mission add c_resource varchar(1);


#--wangdw 220402 订单表收件人字段长增加
alter table t_order modify column c_consignee varchar(20)



#--wangdw 220413 订单退货详情表新增退货详情的类型
alter table t_order_return_detail add c_type varchar(1);


#--wangdw 220413 订单退货详情表的类型
alter table t_order_return_detail add c_cancel_state varchar(1);

#--wangdw 220413 订单退货表新增oms发货单id字段
alter table t_order_return add c_oms_open_id varchar(32);

#--wangdw 220526 供应商表及供应商副本表新增账期字段
alter table t_supplier add c_account_period varchar(1);
alter table t_supplier_fb add c_account_period varchar(1);

#--wangdw 220530 商品表新增起订量
alter table t_product add c_order_quantity varchar(10) comment '起订量';

#--wangdw 220601 订单详情表新增详情发货状态
alter table t_order_detail add c_delivery_state varchar(1) comment '发货状态';


#--wangdw 220928 订单增加派单人公司id字段
alter table t_order add c_company_id varchar(32) comment '派单人公司id';

#-- 处理供应商的英文括号
update t_supplier t set t.c_enterpriseName = replace(replace(t.c_enterpriseName,'(','（'),')','）') where t.c_enterpriseName like '%(%' or t.c_enterpriseName like '%)%'

#--wangdw 221115 订单增加履约派单id
alter table t_order add c_supplier_order_id varchar(32) comment '履约对应派单id';

#--wangdw 221115 订单增加履约派单id
alter table t_order add c_old varchar(1) comment '新旧平台订单标识';


# ********
ALTER TABLE srm.t_contact ADD origin_id varchar(32) NULL COMMENT '原始 id';

ALTER TABLE srm.t_financial ADD origin_id varchar(32) NULL COMMENT '原始 id';

#--wangdw ******** 供应商新增合作类型字段
ALTER TABLE t_supplier ADD c_cooperate_type varchar(1) NULL COMMENT '合作类型';

#--wangdw ******** 订单表新增下单平台名称字段
alter table t_order add c_type_name varchar(100) comment '下单平台名称';

#--wangdw ******** 对账订单明细表新增下单平台名称字段
alter table t_order_account_detail add c_type_name varchar(100) comment '下单平台名称';

#--wangdw ******** 对账订单明细表新增下单平台名称字段
alter table t_order_filing add c_type_name varchar(100) comment '下单平台名称';

#--wangdw ******** 商品明细表新增下单平台名称字段
alter table t_order_detail add c_origin_price decimal(20, 10) COMMENT '明细原金额';

#--wangdw ******** 订单表新增备注字段
alter table t_order add c_remark varchar(500) COMMENT '备注';
#-- ******** 临时需求 旧数据处理：原专业发票和增值税发票，都变成增值税专用发票
update t_order_invoice set c_type = '2' where c_type = '3';

-- ******** 传入金额过大导致数据存入不进去
ALTER TABLE srm.t_order_account MODIFY COLUMN c_price decimal(20,10) NULL COMMENT '对账金额';
ALTER TABLE srm.t_order_account MODIFY COLUMN c_return_price decimal(20,10) NULL COMMENT '已回金额';

--- ******** 数据处理将以下订单的付款状态该为已完成
update t_order set c_payment_status ='4' where c_order_no in (
'PR04630174',
'PR04630322',
'PR04630642',
'PR04666004',
'PR04667318',
'PR04667324',
'PR04680400',
'PR04568732',
'PR04505200',
'PR04599994',
'PR04590119',
'PR04603834',
'PR04611178',
'PR04466520',
'PR04600492',
'PR04624310',
'PR04623788',
'PR04583268',
'PR04584527',
'PR04593154',
'PR04489709',
'PR04491859',
'PR04493444',
'PR04493796',
'PR04493489',
'PR04493416',
'PR04493518',
'PR04493593',
'PR04541110',
'PR04560763',
'PR04567007',
'PR04566805',
'PR04566388',
'PR04574352',
'PR04574452',
'PR04603703',
'PR04621880',
'PR04649403',
'PR04649393',
'PR04649326',
'PR04650074',
'PR04650088',
'PR04480293',
'PR04650088',
'PR04645872',
'PR04614224',
'PR04600413',
'PR04649585',
'PR04650087',
'PR04650054',
'PR04649938',
'PR04649934',
'PR04650533',
'PR04605824',
'PR04621297',
'PR04621241',
'PR04600492',
'PR04629626',
'PR04632556',
'PR04633557',
'PR04536600',
'PR04640280',
'PR04640935',
'PR04608863',
'PR04608416',
'PR04613785',
'PR04613762',
'PR04626071',
'PR04631661',
'PR04630138',
'PR04642933',
'PR04642932',
'PR04642659',
'PR04642805',
'PR04643241',
'PR04643186',
'PR04644442',
'PR04648002',
'PR04549501',
'PR04549503',
'PR04548373',
'PR04531719',
'PR04566437',
'PR04631510',
'PR04637583',
'PR04637613',
'PR04643647'
) and c_order_state != '7'


