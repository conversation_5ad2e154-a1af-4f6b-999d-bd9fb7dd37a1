package com.xhgj.srm.factory;/**
 * @since 2024/12/5 13:41
 */

import com.xhgj.srm.dto.asmDisOrder.AsmDisOrderApplyLinkDTO;
import com.xhgj.srm.dto.asmDisOrder.AsmDisOrderSaveForm.AsmDisOrderItemSaveForm;
import com.xhgj.srm.common.dto.OmsOrderReceiptDto;
import com.xhgj.srm.common.vo.order.OrderCancelDetailVO;
import com.xhgj.srm.common.vo.order.OrderCancelVO;
import com.xhgj.srm.dto.entryregistration.EntryRegistrationDetail;
import com.xhgj.srm.dto.product.externalLink.ExternalLinkSaveForm;
import com.xhgj.srm.dto.supplierCategory.SupplierCategoryDto;
import com.xhgj.srm.jpa.dto.landingContract.LandingBundleOmsDto;
import com.xhgj.srm.jpa.dto.landingContract.LandingContractBundleSaveForm;
import com.xhgj.srm.jpa.dto.supplierCategory.SupplierCategorySaveForm;
import com.xhgj.srm.jpa.entity.AsmDisOrder;
import com.xhgj.srm.jpa.entity.AsmDisOrderItem;
import com.xhgj.srm.jpa.entity.EntryRegistrationDiscount;
import com.xhgj.srm.jpa.entity.Inventory;
import com.xhgj.srm.jpa.entity.LandingContractBundle;
import com.xhgj.srm.jpa.entity.OrderCancel;
import com.xhgj.srm.jpa.entity.OrderCancelDetail;
import com.xhgj.srm.jpa.entity.OrderDetail;
import com.xhgj.srm.jpa.entity.OrderFilingDetail;
import com.xhgj.srm.jpa.entity.OrderReceiptRecord;
import com.xhgj.srm.jpa.entity.OrderReturn;
import com.xhgj.srm.jpa.entity.OrderReturnDetail;
import com.xhgj.srm.jpa.entity.PaymentApplyDetail;
import com.xhgj.srm.jpa.entity.ProductExternalLink;
import com.xhgj.srm.jpa.entity.PurchaseApplyForOrder;
import com.xhgj.srm.jpa.entity.PurchaseOrderPaymentTerms;
import com.xhgj.srm.jpa.entity.Supplier;
import com.xhgj.srm.jpa.entity.SupplierCategory;
import com.xhgj.srm.jpa.entity.SupplierOrder;
import com.xhgj.srm.jpa.entity.SupplierOrderDetail;
import com.xhgj.srm.jpa.entity.SupplierOrderProduct;
import com.xhgj.srm.jpa.entity.SupplierOrderToForm;
import com.xhgj.srm.jpa.entity.TransferOrder;
import com.xhgj.srm.jpa.entity.TransferOrderItem;
import com.xhgj.srm.jpa.entity.v2.PurchaseApplyForOrderV2;
import com.xhgj.srm.jpa.entity.v2.PurchaseOrderPaymentTermsV2;
import com.xhgj.srm.jpa.entity.v2.SupplierOrderDetailV2;
import com.xhgj.srm.jpa.entity.v2.SupplierOrderProductV2;
import com.xhgj.srm.jpa.entity.v2.SupplierOrderToFormV2;
import com.xhgj.srm.jpa.entity.v2.SupplierOrderV2;
import com.xhgj.srm.map.domain.BaseMapStruct;
import com.xhgj.srm.map.domain.IgnoreFieldContext;
import com.xhgj.srm.registration.dto.entryregistration.EntryRegistrationDetailDTO;
import com.xhgj.srm.request.dto.oms.OMSFilingSheetAddParam.OMSProduct;
import com.xhgj.srm.request.dto.person.FindPersonPageParam;
import com.xhgj.srm.request.dto.supplierCategory.CategoryFindDto;
import com.xhgj.srm.v2.dto.AsmDisOrderApplyLinkV2DTO;
import com.xhgj.srm.vo.asmDisOrder.AsmDisOrderItemVO;
import com.xhgj.srm.vo.asmDisOrder.AsmDisOrderVO;
import com.xhgj.srm.vo.product.ExternalLinkVO;
import com.xhgj.srm.dto.transferOrder.TransferOrderSaveForm.TransferOrderItemSaveForm;
import com.xhgj.srm.vo.transferOrder.TransferOrderItemVO;
import com.xhgj.srm.vo.transferOrder.TransferOrderVO;
import org.mapstruct.BeanMapping;
import org.mapstruct.Context;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;
import java.util.List;

/**
 *<AUTHOR>
 *@date 2024/12/5 13:41:35
 *@description
 */
@Mapper
public interface MapStructFactory extends BaseMapStruct {

  MapStructFactory INSTANCE = Mappers.getMapper(MapStructFactory.class);

  /**
   * LandingContractBundle to LandingBundleOmsDto
   * @param landingContractBundle
   * @return
   */
  LandingBundleOmsDto toLandingBundleOmsDto(LandingContractBundle landingContractBundle);

  /**
   * LandingContractBundleSaveForm to LandingContractBundle
   * @param landingContractBundleSaveForm
   * @return
   */
  LandingContractBundle toLandingContractBundle(LandingContractBundleSaveForm landingContractBundleSaveForm);

  /**
   * update LandingContractBundle
   * @param landingContractBundleSaveForm
   * @param landingContractBundle
   */
  @BeanMapping(nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
  void updateLandingContractBundle(LandingContractBundleSaveForm landingContractBundleSaveForm, @MappingTarget LandingContractBundle landingContractBundle);

  /**
   * OrderFilingDetail to OMSProduct
   * @param orderFilingDetail
   * @return
   */
  OMSProduct toOMSProduct(OrderFilingDetail orderFilingDetail);

  /**
   * EntryRegistrationDiscount to EntryRegistrationDiscount
   * @param entryRegistrationDiscount
   * @return
   */
  EntryRegistrationDiscount toEntryRegistrationDiscount(EntryRegistrationDiscount entryRegistrationDiscount);

  /**
   * FindPersonPageParam to FindPersonPageParam
   * @param findPersonPageParam
   * @return
   */
  FindPersonPageParam toFindPersonPageParam(FindPersonPageParam findPersonPageParam);

  /**
   * OrderDetail to OrderDetail
   * @param orderDetail
   * @return
   */
  OrderDetail toOrderDetail(OrderDetail orderDetail);

  /**
   * OrderReturn to OrderReturn
   * @param orderReturn
   * @return
   */
  OrderReturn toOrderReturn(OrderReturn orderReturn);

  /**
   * OrderReturnDetail to OrderReturnDetail
   * @param orderReturnDetail
   * @return
   */
  OrderReturnDetail toOrderReturnDetail(OrderReturnDetail orderReturnDetail);

  /**
   * PaymentApplyDetail to PaymentApplyDetail
   * @param paymentApplyDetail
   * @return
   */
  PaymentApplyDetail toPaymentApplyDetail(PaymentApplyDetail paymentApplyDetail);

  /**
   * PaymentApplyDetail to PaymentApplyDetail
   * @param source
   * @return
   */
  @Mapping(target = "id", expression = "java(context != null && context.shouldIgnore(\"id\") ? null : source.getId())")
  PaymentApplyDetail toPaymentApplyDetail(PaymentApplyDetail source, @Context IgnoreFieldContext context);


  /**
   * SupplierCategorySaveForm to SupplierCategory
   * @param supplierCategorySaveForm
   * @return
   */
  SupplierCategory toSupplierCategory(SupplierCategorySaveForm supplierCategorySaveForm);

  /**
   * SupplierCategory to SupplierCategorySaveForm
   * @param supplierCategory
   * @return
   */
  SupplierCategorySaveForm toSupplierCategorySaveForm(SupplierCategory supplierCategory);

  /**
   * update SupplierCategory
   * @param source
   * @param target
   */
  @BeanMapping(nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
  @Mapping(target = "id", expression = "java(context != null && context.shouldIgnore(\"id\") ? target.getId() : source.getId())")
  void updateSupplierCategory(SupplierCategorySaveForm source, @MappingTarget SupplierCategory target, @Context IgnoreFieldContext context);

  /**
   * SupplierCategory to SupplierCategoryDto
   * @param supplierCategory
   * @return
   */
  SupplierCategoryDto toSupplierCategoryDto(SupplierCategory supplierCategory);

  /**
   * CategoryFindDto to SupplierCategorySaveForm
   * @param dto
   * @return
   */
  @Mapping(target = "categoryCode", source = "code")
  SupplierCategorySaveForm toSupplierCategorySaveForm(CategoryFindDto dto);

  /**
   * ExternalLinkSaveForm to ProductExternalLink
   * @param saveForm
   * @return
   */
  ProductExternalLink toProductExternalLink(ExternalLinkSaveForm saveForm);

  /**
   * update ProductExternalLink
   * @param source
   * @param target
   */
  @BeanMapping(nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
  void updateProductExternalLink(ExternalLinkSaveForm source, @MappingTarget ProductExternalLink target);

  /**
   * ProductExternalLink to ExternalLinkVO
   * @param productExternalLink
   * @return
   */
  ExternalLinkVO toExternalLinkVO(ProductExternalLink productExternalLink);

  /**
   * TransferOrderSaveForm to TransferOrder
   * @param source
   * @return
   */
  TransferOrderItem toTransferOrderItem(TransferOrderItemSaveForm source);

  /**
   * TransferOrder to TransferOrderVO
   * @param source
   * @return
   */
  TransferOrderVO toTransferOrderVO(TransferOrder source);

  /**
   * TransferOrderItem to TransferOrderItemVO
   * @param source
   * @return
   */
  TransferOrderItemVO toTransferOrderItemVO(TransferOrderItem source);

  /**
   * AsmDisOrderItemSaveForm to AsmDisOrderItem
   * @param source
   * @return
   */
  AsmDisOrderItem toAsmDisOrderItem(AsmDisOrderItemSaveForm source);

  /**
   * AsmDisOrder to AsmDisOrderVO
   * @param source
   * @return
   */
  AsmDisOrderVO toAsmDisOrderVO(AsmDisOrder source);

  /**
   * AsmDisOrderItem to AsmDisOrderItemVO
   * @param source
   * @return
   */
  AsmDisOrderItemVO toAsmDisOrderItemVO(AsmDisOrderItem source);

  /**
   * Inventory to Inventory
   * @return
   */
  Inventory toInventory(Inventory source);

  /**
   * OmsOrderReceiptDto to OrderReceiptRecord
   * @param form
   * @return
   */
  @Mapping(target = "id", ignore = true)
  @Mapping(target = "omsUniqueId",source = "id")
  OrderReceiptRecord toOrderReceiptRecord(OmsOrderReceiptDto form);

  @Mapping(target = "id", source = "omsUniqueId")
  List<OmsOrderReceiptDto> toOmsOrderReceiptDtoList(List<OrderReceiptRecord> records);

  /**
   * OrderCancel to OrderCancelVO
   * @param orderCancel
   * @return
   */
  OrderCancelVO toOrderCancelVO(OrderCancel orderCancel);

  /**
   * OrderCancelDetail to OrderCancelDetailVO
   * @param orderCancelDetail
   * @return
   */
  OrderCancelDetailVO toOrderCancelDetailVO(OrderCancelDetail orderCancelDetail);

  /**
   * EntryRegistrationDetailDTO to EntryRegistrationDetail
   * @param entryRegistrationDetailDTO
   * @return
   */
  EntryRegistrationDetail toEntryRegistrationDetail(EntryRegistrationDetailDTO entryRegistrationDetailDTO);

  /**
   * supplier to supplier
   * @param originSupplier
   * @return
   */
  Supplier toSupplier(Supplier originSupplier);

  /**
   * AsmDisOrderApplyLinkDTO to AsmDisOrderApplyLinkV2DTO
   * @param source
   * @return
   */
  AsmDisOrderApplyLinkV2DTO toAsmDisOrderApplyLinkV2DTO(AsmDisOrderApplyLinkDTO source);

  /**
   * SupplierOrder to SupplierOrderV2
   * @param source
   * @return
   */
  @Mapping(target = "id", ignore = true)
  SupplierOrderV2 toSupplierOrderV2(SupplierOrder source);

  /**
   * SupplierOrder to SupplierOrderV2
   * @param source
   * @return
   */
  @Mapping(target = "id", ignore = true)
  PurchaseOrderPaymentTermsV2 toPurchaseOrderPaymentTermsV2(PurchaseOrderPaymentTerms source);

  /**
   * SupplierOrderToForm to SupplierOrderToFormV2
   * @param source
   */
  @Mapping(target = "id", ignore = true)
  SupplierOrderToFormV2 toSupplierOrderToFormV2(SupplierOrderToForm source);

  /**
   * SupplierOrderToForm to SupplierOrderToFormV2
   * @param source
   */
  @Mapping(target = "id", ignore = true)
  SupplierOrderToFormV2 toSupplierOrderToFormV2(SupplierOrderToFormV2 source);

  /**
   * SupplierOrderDetail to SupplierOrderDetailV2
   * @param source
   * @return
   */
  @Mapping(target = "id", ignore = true)
  SupplierOrderDetailV2 toSupplierOrderDetailV2(SupplierOrderDetail source);

  /**
   * SupplierOrderDetailV2 to SupplierOrderDetailV2
   * @param source
   * @return
   */
  @Mapping(target = "id", ignore = true)
  SupplierOrderDetailV2 toSupplierOrderDetailV2(SupplierOrderDetailV2 source);

  /**
   * SupplierOrderProduct to SupplierOrderProductV2
   * @param source
   * @return
   */
  @Mapping(target = "id", ignore = true)
  SupplierOrderProductV2 toSupplierOrderProductV2(SupplierOrderProduct source);

  /**
   * SupplierOrderProductV2 to SupplierOrderProductV2
   * @param source
   * @return
   */
  @Mapping(target = "id", ignore = true)
  SupplierOrderProductV2 toSupplierOrderProductV2(SupplierOrderProductV2 source);

  /**
   * PurchaseApplyForOrder to PurchaseApplyForOrderV2
   * @param source
   * @return
   */
  @Mapping(target = "id", ignore = true)
  PurchaseApplyForOrderV2 toPurchaseApplyForOrderV2(PurchaseApplyForOrder source);
}
