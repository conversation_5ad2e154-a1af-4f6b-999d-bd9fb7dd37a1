package com.xhgj.srm.vo.transferOrder;/**
 * @since 2025/2/26 15:11
 */

import cn.hutool.core.util.StrUtil;
import com.xhgj.srm.common.enums.transferOrder.TransferOrderStatus;
import com.xhgj.srm.common.enums.transferOrder.TransferOrderType;
import lombok.Data;
import java.util.List;

/**
 *<AUTHOR>
 *@date 2025/2/26 15:11:11
 *@description 调拨单VO
 */
@Data
public class TransferOrderVO {
  /**
   * 调拨单id
   */
  private String id;

  /**
   * 调拨单号
   */
  private String code;

  /**
   * 单据类型
   */
  private Byte type;

  /**
   * 单据类型value
   */
  private String typeValue;

  /**
   * 调拨状态
   */
  private Byte status;

  /**
   * 调拨状态value
   */
  private String statusValue;

  /**
   * 创建时间
   */
  private Long createTime;

  /**
   * 创建组织
   */
  private String groupCode;

  /**
   * 创建组织名称
   */
  private String groupName;

  /**
   * 创建部门id
   */
  private String deptId;

  /**
   * 创建部门编码
   */
  private String deptCode;

  /**
   * 创建部门名称
   */
  private String deptName;

  /**
   * 创建人id
   */
  private String createMan;

  /**
   * 创建人 code
   */
  private String createManCode;

  /**
   * 创建人 name
   */
  private String createManName;

  /**
   * 创建人mix
   */
  private String createManMix;

  /**
   * 是否涉及WMS （涉及WMS货物移动）
   */
  private Boolean hasMovement;

  /**
   * 调拨原因
   */
  private String reason;

  /**
   * 审核人
   */
  private String reviewer;

  /**
   * 审核人工号
   */
  private String reviewerCode;

  /**
   * 审核时间
   */
  private Long reviewTime;

  /**
   * 仓库执行员
   */
  private String warehouseOperator;

  /**
   * 仓库执行时间
   */
  private Long warehouseTime;

  /**
   * sap物料凭证号
   */
  private String productVoucher;

  /**
   * sap物料凭证号年份
   */
  private String productVoucherYear;

  /**
   * 供应商id
   */
  private String supplierId;

  /**
   * 供应商名称
   */
  private String supplierName;

  /**
   * 供应商主数据编码
   */
  private String supplierCode;


  /**
   * 收件人
   */
  private String consignee;


  /**
   * 联系方式
   */
  private String receiveMobile;


  /**
   * 收件地址
   */
  private String receiveAddress;


  /**
   * 物流公司名称
   */
  private String logisticsCompany;


  /**
   * 物流公司名称编码
   */
  private String logisticsCode;


  /**
   * 物流单号
   */
  private String trackNum;

  /**
   * 调拨单明细
   */
  private List<TransferOrderItemVO> items;

  /**
   * 单据类型value
   * @return
   */
  public String getTypeValue() {
    return TransferOrderType.getNameByCode(this.type);
  }

  /**
   * 调拨状态value
   * @return
   */
  public String getStatusValue() {
    return TransferOrderStatus.getNameByCode(this.status);
  }

  /**
   * 创建人mix
   * @return
   */
  public String getCreateManMix() {
    if (StrUtil.isNotBlank(createManCode)
        && createManCode.length() > 4
        && StrUtil.isNotBlank(createManName)
    ) {
      return createManCode.substring(createManCode.length() - 4) + createManName;
    }
    return null;
  }
}
