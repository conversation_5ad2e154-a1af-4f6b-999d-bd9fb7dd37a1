package com.xhgj.srm.dto.account;

import cn.hutool.core.util.StrUtil;
import com.xhgj.srm.common.Constants;
import com.xhgj.srm.dto.account.OrderAccountInvoiceInfo;
import com.xhgj.srm.dto.account.OrderInfoDTO;
import com.xhgj.srm.dto.account.ProductInfoDTO;
import com.xhgj.srm.jpa.entity.OrderAccount;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.util.List;
import lombok.Data;

@Data
public class AccountDetailDTO {

  @ApiModelProperty("对账单id")
  private String id;
  @ApiModelProperty("对账单号")
  private String accountNo;
  @ApiModelProperty("创建时间")
  private Long createTime;
  @ApiModelProperty("创建人")
  private String createSupplier;
  @ApiModelProperty("提交时间")
  private Long commitTime;
  @ApiModelProperty("对账单状态")
  private String accountStatus;
  @ApiModelProperty("对账金额")
  private BigDecimal accountPrice;

  @ApiModelProperty("确认时间")
  private Long confirmTime;

  @ApiModelProperty("确认人")
  private String confirmMan;

  @ApiModelProperty("进项票确认时间")
  private Long inputTicketConfirmTime;

  @ApiModelProperty("进项票确认人")
  private String inputTicketConfirmMan;

  @ApiModelProperty("对账开票状态")
  private String accountOpenInvoiceStatus;

  @ApiModelProperty("供应商备注")
  private String supplierRemark;

  @ApiModelProperty("发票信息")
  private List<OrderAccountInvoiceInfo> orderAccountInvoiceInfoList;

  @ApiModelProperty("订单明细")
  private List<OrderInfoDTO> orderInfoList;

  @ApiModelProperty("物料明细")
  private List<ProductInfoDTO> productInfoList;

  @ApiModelProperty("erp系统应付单单号")
  private String erpPayableNo;
  @ApiModelProperty("erp系统应付单应付金额")
  private BigDecimal erpPayableMoney;
  @ApiModelProperty("业务负责人")
  private String businessLeader;
  @ApiModelProperty("对接助理")
  private String dockingAssistant;

  @ApiModelProperty("是否是新订单")
  private Boolean isNew;

  public AccountDetailDTO(OrderAccount orderAccount) {
    this.id = orderAccount.getId();
    this.accountNo = orderAccount.getAccountNo();
    this.createTime = orderAccount.getCreateTime();
    this.createSupplier = orderAccount.getCreateSupplier();
    this.commitTime = orderAccount.getCommitTime();
    this.accountStatus = StrUtil.emptyIfNull(orderAccount.getAccountState());
    this.accountPrice = orderAccount.getPrice();
    this.confirmTime = orderAccount.getAssessTime();
    this.confirmMan = StrUtil.emptyIfNull(orderAccount.getConfirmMan());
    String accountOpenInvoiceStatus1 = orderAccount.getAccountOpenInvoiceStatus();
    this.accountOpenInvoiceStatus = StrUtil.isBlank(StrUtil.emptyIfNull(accountOpenInvoiceStatus1))
        ? Constants.ORDER_INVOICE_STATE_NOT_DONE : accountOpenInvoiceStatus1;
    this.supplierRemark = StrUtil.emptyIfNull(orderAccount.getRemark());
    this.erpPayableMoney = orderAccount.getErpPayableMoney();
    this.erpPayableNo = orderAccount.getErpPayableNo();
    this.inputTicketConfirmMan = StrUtil.emptyIfNull(orderAccount.getInvoiceConfirmMan());
    this.inputTicketConfirmTime = orderAccount.getInvoiceConfirmTime();
  }
}
