package com.xhgj.srm.dto.order;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.StrUtil;
import com.xhgj.srm.common.Constants_order;
import com.xhgj.srm.common.enums.TitleOfTheContractEnum;
import com.xhgj.srm.jpa.dto.BaseDefaultSearchSchemeForm;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2023-01-30 16:57
 */
@Data
public class ExportOrderParams implements BaseDefaultSearchSchemeForm {
  @ApiModelProperty("订单编号")
  private String orderNo;
  @ApiModelProperty("下单平台")
  private String platform;
  @ApiModelProperty("下单金额")
  private String price;
  @ApiModelProperty("供应商名称")
  private String supplierName;
  @ApiModelProperty("客户名称")
  private String customer;
  @ApiModelProperty("收件人")
  private String consignee;
  @ApiModelProperty("联系方式")
  private String mobile;
  @ApiModelProperty("订单状态(1--待履约,2--履约中,3--待验收,4--已取消,5--已退货,6--已验收)搜索")
  private String orderState;
  @ApiModelProperty("订单页签状态(0--全部,1--待履约,2--履约中,3--待验收,4--已完成)")
  private String orderPageState;
  @ApiModelProperty("下单日期(起始)")
  private String startDate;
  @ApiModelProperty("下单日期(截止)")
  private String endDate;
  @ApiModelProperty("方案id")
  private String schemeId;
  @ApiModelProperty("组织")
  private String userGroup;
  @ApiModelProperty("是否仅显示签收凭证待确认的订单")
  private Boolean onlySignVoucherWaitToConfirm;
  @ApiModelProperty("客户回款(1--已完成,2--未完成)")
  private String returnState;
  @ApiModelProperty("开票状态")
  private String invoicingState;
  @ApiModelProperty("发货状态")
  private String deliveryState;
  @ApiModelProperty("回款进度(0-未回款 1--部分回款 ,2--全部回款)")
  private String customerReturnProgress;
  @ApiModelProperty("对账状态")
  private String accountStatus;
  @ApiModelProperty("对账单开票状态")
  private String accountOpenInvoiceStatus;
  @ApiModelProperty("付款状态")
  private String paymentStatus;
  @ApiModelProperty("是否有退货")
  private Boolean salesReturnState;
  @ApiModelProperty("销售订单号")
  private String saleOrderNo;
  @ApiModelProperty("采购订单号")
  private String erpOrderNo;
  @ApiModelProperty("签收凭证审核状态")
  private String signVoucher;
  @ApiModelProperty("订单id")
  private List<String> orderIds;


  @ApiModelProperty("回款比例操作符号")
  private String paymentProportionOperators;

  @ApiModelProperty("回款比例")
  private BigDecimal paymentProportion;

  @ApiModelProperty("最新到账时间（开始）")
  private Long startArrivalTime;

  @ApiModelProperty("最新到账时间（结束）")
  private Long endArrivalTime;

  @ApiModelProperty("最新核销时间（开始）")
  private Long startWriteOffTime;

  @ApiModelProperty("最新核销时间（结束）")
  private Long endWriteOffTime;

  @ApiModelProperty("最新回款方式(1-电汇 2-汇票 3-支票 4-现金 5-电易宝 6-支付宝)")
  private String paymentTypeName;

  @ApiModelProperty("付款条件")
  private String paymentCondition;

  @ApiModelProperty("账期（是否为背靠背）")
  private Boolean backToBack;

  @ApiModelProperty("账期（单位：天）")
  private Integer accountingPeriod;

  @ApiModelProperty("付款条件满足日期（开始）")
  private Long startPaymentConditionTime;

  @ApiModelProperty("付款条件满足日期（结束）")
  private Long endPaymentConditionTime;

  @ApiModelProperty("预计付款时间（开始）")
  private Long startPredictPaymentTime;

  @ApiModelProperty("预计付款时间（结束）")
  private Long endPredictPaymentTime;

  @ApiModelProperty("是否应收")
  private Boolean receivableState;

  @ApiModelProperty("是否到达可付款日期")
  private Boolean payableDate;

  @ApiModelProperty("折扣比例")
  private String dispatchPoints;

  @ApiModelProperty("是否修改点数")
  private Boolean isUpdateRate;

  @ApiModelProperty("是否客户签收")
  private Boolean isCustomerAccept;

  @ApiModelProperty("客户开票日期（开始）")
  private Long startCustomerInvoiceTime;

  @ApiModelProperty("客户开票日期（结束）")
  private Long endCustomerInvoiceTime;

  @ApiModelProperty("第一次发货时间（开始）")
  private Long startFirstShipTime;

  @ApiModelProperty("第一次发货时间（结束）")
  private Long endFirstShipTime;

  /**
   * 客户回款时间开始
   */
  @ApiModelProperty("客户回款时间")
  private Long startCustomerReturnTime;

  /**
   * 客户回款时间结束
   */
  @ApiModelProperty("客户回款时间")
  private Long endCustomerReturnTime;

  /**
   * 申请付款时间
   */
  @ApiModelProperty("申请付款时间")
  private Long startApplyPaymentTime;

  /**
   * 申请付款时间
   */
  @ApiModelProperty("申请付款时间")
  private Long endApplyPaymentTime;

  /**
   * 付款完成时间
   */
  @ApiModelProperty("付款完成时间")
  private Long startPaymentTime;
  /**
   * 付款完成时间
   */
  @ApiModelProperty("付款完成时间")
  private Long endPaymentTime;

  /**
   * 供应商开票时间
   * @return
   */
  @ApiModelProperty("供应商开票时间")
  private Long startSupplierInvoiceTime;

  /**
   * 供应商开票时间
   * @return
   */
  @ApiModelProperty("供应商开票时间")
  private Long endSupplierInvoiceTime;

  /**
   * 签约抬头
   */
  @ApiModelProperty("签约抬头")
  private String titleOfTheContract;

  /**
   * userId
   */
  private String userId;

  /**
   * 转换为queryMap
   */
  public Map<String, Object> toQueryMap() {
    List<String> customerReturnProgressList = ListUtil.empty();
    String orderState = StrUtil.EMPTY;
    String returnState = this.getReturnState();
    if (StrUtil.isNotBlank(returnState)) {
      if (Objects.equals(returnState, Constants_order.CUSTOMER_PAYBACK_CONFIRM)) {
        customerReturnProgressList =
            ListUtil.toList(
                Constants_order.RETURN_PROGRESS_NO, Constants_order.RETURN_PROGRESS_PART);
      } else {
        customerReturnProgressList = ListUtil.toList(Constants_order.RETURN_PROGRESS_ALL);
      }
    }
    if (BooleanUtil.isTrue(this.getOnlySignVoucherWaitToConfirm())) {
      // 1. 已上传签收凭证并且审核状态为”待审核“
      this.setSignVoucher(Constants_order.ORDER_ACCEPT_PENDING_AUDITING);
    }
    Map<String, Object> map = new HashMap<>();
    map.put("userIds", null);
    map.put("orderNo", orderNo);
    map.put("customer", customer);
    map.put("consignee", consignee);
    map.put("mobile", mobile);
    map.put("accepted", orderState);
    map.put("orderState", orderPageState);
    map.put("startDate", startDate);
    map.put("endDate", endDate);
    map.put("platform", platform);
    map.put("supplierName", supplierName);
    map.put("price", price);
    map.put("invoicingState", invoicingState);
    map.put("signVoucher", signVoucher);
    map.put("customerReturnProgress", customerReturnProgress);
    map.put("paymentStatus", paymentStatus);
    map.put("pageNo", 1);
    map.put("pageSize", Integer.MAX_VALUE);
    map.put("customerReturnProgressList", customerReturnProgressList);
    map.put("accountStatus", accountStatus);
    map.put("accountOpenInvoiceStatus", accountOpenInvoiceStatus);
    map.put("salesReturnState", salesReturnState);
    map.put("saleOrderNo", saleOrderNo);
    map.put("erpOrderNo", erpOrderNo);
    map.put("startFirstShipTime", startFirstShipTime);
    map.put("endFirstShipTime", endFirstShipTime);
    map.put("paymentProportionOperators", paymentProportionOperators);
    map.put("paymentProportion", paymentProportion);
    map.put("startArrivalTime", startArrivalTime);
    map.put("endArrivalTime", endArrivalTime);
    map.put("startWriteOffTime", startWriteOffTime);
    map.put("endWriteOffTime", endWriteOffTime);
    map.put("paymentTypeName", paymentTypeName);
    map.put("paymentCondition", paymentCondition);
    map.put("backToBack", backToBack);
    map.put("accountingPeriod", accountingPeriod);
    map.put("startPaymentConditionTime", startPaymentConditionTime);
    map.put("endPaymentConditionTime", endPaymentConditionTime);
    map.put("startPredictPaymentTime", startPredictPaymentTime);
    map.put("endPredictPaymentTime", endPredictPaymentTime);
    map.put("receivableState", receivableState);
    map.put("payableDate", payableDate);
    map.put("rate", dispatchPoints);
    map.put("isUpdateRate", isUpdateRate);
    map.put("isCustomerAccept", isCustomerAccept);
    map.put("startCustomerInvoiceTime", startCustomerInvoiceTime);
    map.put("endCustomerInvoiceTime", endCustomerInvoiceTime);
    // 客户回款时间
    map.put("startCustomerReturnTime", startCustomerReturnTime);
    map.put("endCustomerReturnTime", endCustomerReturnTime);
    // 申请付款时间
    map.put("startApplyPaymentTime", startApplyPaymentTime);
    map.put("endApplyPaymentTime", endApplyPaymentTime);
    // 付款完成时间
    map.put("startPaymentTime", startPaymentTime);
    map.put("endPaymentTime", endPaymentTime);
    // 供应商开票时间
    map.put("startSupplierInvoiceTime", startSupplierInvoiceTime);
    map.put("endSupplierInvoiceTime", endSupplierInvoiceTime);
    // 签约抬头
    if (StrUtil.isNotBlank(titleOfTheContract)) {
      TitleOfTheContractEnum mode1 =
          TitleOfTheContractEnum.getEnumByCode(titleOfTheContract);
      if (mode1 != null) {
        map.put("titleOfTheContract", mode1.getCode());
      }
      TitleOfTheContractEnum mode2 =
          TitleOfTheContractEnum.getEnumByName2(titleOfTheContract);
      if (mode2 != null) {
        map.put("titleOfTheContract", mode2.getCode());
      }
    }
    return map;
  }
}
