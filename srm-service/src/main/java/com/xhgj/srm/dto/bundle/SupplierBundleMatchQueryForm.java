package com.xhgj.srm.dto.bundle;/**
 * @since 2024/11/29 14:23
 */

import cn.hutool.core.util.StrUtil;
import com.xhgj.srm.dto.ProvinceFindDto;
import com.xhgj.srm.service.impl.LandingContractBundleServiceImpl;
import lombok.Data;
import javax.validation.constraints.NotBlank;
import java.util.HashMap;
import java.util.Map;

/**
 *<AUTHOR>
 *@date 2024/11/29 14:23:53
 *@description 供应商绑品匹配查询表单
 */
@Data
public class SupplierBundleMatchQueryForm {

  /**
   * 平台编码
   */
  @NotBlank(message = "平台编码不能为空")
  private String platformCode;

  /**
   * 品牌编码
   */
  private String bundleBrand;

  /**
   * 区域编码
   */
  private String bundleArea;

  /**
   * 客户单位主数据编码
   */
  private String bundleCustomer;

  /**
   * 由调用方填写的请求ID，用于对于批量查询的请求进行标识
   */
  private String requestId;

  /**
   * 转换为查询参数
   * @param impl
   * @return
   */
  public Map<String, Object> toQueryMap(LandingContractBundleServiceImpl impl) {
    Map<String, Object> res = new HashMap<>();
    res.put("platformCode", platformCode);
    res.put("bundleBrand", bundleBrand);
    res.put("bundleArea", bundleArea);
    if (StrUtil.isNotBlank(bundleArea)) {
      ProvinceFindDto provinceFindDto = impl.bfsFindByCodeProxy(bundleArea);
      if (provinceFindDto != null) {
        res.put("bundleArea", String.join("/", provinceFindDto.getPath()));
      }
    }
    res.put("bundleCustomer", bundleCustomer);
    return res;
  }
}
