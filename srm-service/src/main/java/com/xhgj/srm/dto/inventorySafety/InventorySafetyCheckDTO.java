package com.xhgj.srm.dto.inventorySafety;/**
 * @since 2025/2/21 9:42
 */

import lombok.Data;
import java.math.BigDecimal;

/**
 *<AUTHOR>
 *@date 2025/2/21 09:42:25
 *@description
 */
@Data
public class InventorySafetyCheckDTO {
  /**
   * 库存地点 code
   */
  private String warehouse;

  /**
   * 库存地点
   */
  private String warehouseName;

  /**
   * 物料编码
   */
  private String productCode;

  /**
   * 物料名称
   */
  private String name;

  /**
   * 品牌
   */
  private String brandName;

  /**
   * 型号
   */
  private String model;

  /**
   * 基本单位
   */
  private String unit;

  /**
   * 剩余可用数量
   */
  private BigDecimal availableStock;

  /**
   * 安全库存
   */
  private BigDecimal minSafetyStock;

  /**
   * 关联库存id
   */
  private String inventoryId;

  /**
   * 库存安全检查id
   */
  private String inventorySafetyId;

}
