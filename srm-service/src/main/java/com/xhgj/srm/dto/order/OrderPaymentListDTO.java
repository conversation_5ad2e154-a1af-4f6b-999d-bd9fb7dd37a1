package com.xhgj.srm.dto.order;

import com.xhgj.srm.jpa.entity.OrderPayment;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.util.List;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2023-03-16 16:21
 */
@Data
@NoArgsConstructor
public class OrderPaymentListDTO extends BaseOrderPaymentDTO {
  @ApiModelProperty("订单数量")
  private String orderCount;

  @ApiModelProperty("是否自动提款")
  private Boolean autoDraw;

  @ApiModelProperty("签约抬头")
  private String titleOfTheContract;

  @ApiModelProperty("订单明细")
  private List<OrderPaymentOrderInfoDTO> paymentOrderInfoDTOS;

  public OrderPaymentListDTO(OrderPayment orderPayment,BigDecimal applyPrice) {
    super(orderPayment, applyPrice);
    this.orderCount = orderPayment.getOrderCount();
    this.autoDraw = orderPayment.getAutoDraw();
  }
}
