package com.xhgj.srm.dto.order.invoice;

import cn.hutool.core.util.StrUtil;
import com.xhgj.srm.common.Constants;
import com.xhgj.srm.common.dto.FileDTO;
import com.xhgj.srm.jpa.entity.OrderSupplierInvoice;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import lombok.Data;

/**
 * Created by Geng Shy on 2023/8/11
 */
@Data
public class InvoiceParam {
  @ApiModelProperty("id，修改时传入")
  private String id;
  @ApiModelProperty("供应商id")
  private String supplierId;
  /**
   * {@link com.xhgj.srm.common.enums.InvoiceTypeEnum}
   */
  @ApiModelProperty("发票类型")
  @NotBlank
  private String invoiceType;
  @ApiModelProperty("发票号")
  @NotBlank(message = "发票号 必传")
  private String invoiceNum;
  @ApiModelProperty("发票代码")
  private String invoiceCode;
  @ApiModelProperty("开票时间")
  @NotNull
  private Long invoiceTime;
  @ApiModelProperty("合计金额")
  @NotNull
  private BigDecimal totalAmount;
  @ApiModelProperty("合计税额")
  private BigDecimal totalTaxAmount;
  @ApiModelProperty("价税合计")
  @NotNull
  private BigDecimal totalAmountIncludingTax;
  @ApiModelProperty("发票附件")
  private FileDTO file;
  @ApiModelProperty("验真类型")
  private String verificationType;
  @ApiModelProperty(value = "关联数据id")
  private String relationId;
  @ApiModelProperty(value = "校验码")
  private String checkCode;
  @ApiModelProperty(value = "发票内购方")
  private String payerName;
  @ApiModelProperty(value = "购方(组织名称)")
  private String purchaser;
  @ApiModelProperty(value = "销方")
  private String sellerName;
  @ApiModelProperty(value = "发票内销方名")
  private String invoiceSeller;
  /**
   * {@link Constants#INVOICE_INPUT_ORDER_TYPE_ELECTRONIC}
   */
  @ApiModelProperty(value = "订单类型 0-落地商 1-供应商")
  private String orderType;
  @ApiModelProperty(value = "订单抬头")
  private String titleOfTheContract;
  /**
   * {@link Constants#PLATFORM_TYPE_BEFORE}
   */
  @ApiModelProperty(value = "接口来源 1-前台 2-后台")
  private String apiSource;

  public OrderSupplierInvoice buildOrderSupplierInvoice(String orderInvoiceRelationId) {
    OrderSupplierInvoice orderSupplierInvoice = new OrderSupplierInvoice();
    orderSupplierInvoice.setInvoiceNum(invoiceNum);
    orderSupplierInvoice.setInvoiceCode(invoiceCode);
    orderSupplierInvoice.setInvoiceTime(invoiceTime);
    orderSupplierInvoice.setInvoiceType(invoiceType);
    orderSupplierInvoice.setTotalTaxAmount(totalTaxAmount);
    orderSupplierInvoice.setTotalAmountIncludingTax(totalAmountIncludingTax);
    orderSupplierInvoice.setTotalAmount(totalAmount);
    orderSupplierInvoice.setVerificationType(verificationType);
    orderSupplierInvoice.setOrderInvoiceRelationId(orderInvoiceRelationId);
    if (StrUtil.isBlank(id)) {
      orderSupplierInvoice.setCreateTime(System.currentTimeMillis());
    }else {
      orderSupplierInvoice.setId(id);
      orderSupplierInvoice.setUpdateTime(System.currentTimeMillis());
    }
    return orderSupplierInvoice;
  }
}
