package com.xhgj.srm.dto.order.vo;

import cn.hutool.core.util.StrUtil;
import com.xhgj.srm.dto.FileDTO;
import com.xhgj.srm.jpa.entity.Order;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.util.List;

/**
 * OrderDescriptionDetailVO
 */
@Data
@NoArgsConstructor
public class OrderDescriptionDetailVO {

  @ApiModelProperty("订单说明富文本字段")
  private String orderDescriptionTextField;

  @ApiModelProperty("订单说明附件")
  private List<FileDTO> orderDescriptionFiles;

  @ApiModelProperty("供应商id")
  private String supplierId;

  @ApiModelProperty("供应商名称")
  private String supplierName;

  @ApiModelProperty("销售订单号")
  private String saleOrderNo;

  public OrderDescriptionDetailVO(String orderDescriptionTextField,
      List<FileDTO> orderDescriptionFiles) {
    this.orderDescriptionTextField = orderDescriptionTextField;
    this.orderDescriptionFiles = orderDescriptionFiles;
  }

  public OrderDescriptionDetailVO(Order order,String orderDescriptionTextField,
      List<FileDTO> orderDescriptionFiles) {
    this.orderDescriptionTextField = orderDescriptionTextField;
    this.orderDescriptionFiles = orderDescriptionFiles;
    this.supplierId = order.getSupplierId();
    this.saleOrderNo = order.getSaleOrderNo();
    this.supplierName =
        order.getSupplier() != null ? StrUtil.emptyIfNull(order.getSupplier().getEnterpriseName())
            : StrUtil.EMPTY;
  }
}

