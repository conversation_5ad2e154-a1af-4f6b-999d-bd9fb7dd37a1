package com.xhgj.srm.dto.order;

import cn.hutool.core.util.StrUtil;
import com.xhgj.srm.common.enums.OrderInvoiceEnums;
import com.xhgj.srm.jpa.entity.OrderInvoiceTemplate;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2023-06-04 15:32
 */
@Data
@NoArgsConstructor
public class OrderDetailInvoiceDTO {

  @ApiModelProperty("发票类型")
  private String invoiceType;

  @ApiModelProperty("发票类型（中文）")
  private String invoiceTypeStr;

  @ApiModelProperty("发票抬头")
  private String invoiceTitle;

  @ApiModelProperty("纳税识别号")
  private String taxCode;

  @ApiModelProperty("开户行")
  private String bankName;

  @ApiModelProperty("银行账户")
  private String bankAccount;

  @ApiModelProperty("开票电话")
  private String openInvoiceMobile;

  @ApiModelProperty("开票地址")
  private String invoiceAddress;

  @ApiModelProperty("票面信息")
  private String invoiceFaceInfo;

  @ApiModelProperty("发票接收人")
  private String receiveMan;

  @ApiModelProperty("联系人电话")
  private String mobile;

  @ApiModelProperty("收票地址")
  private String receiveAddress;

  public OrderDetailInvoiceDTO(OrderInvoiceTemplate orderInvoiceTemplate) {
    String invoiceType1 = StrUtil.emptyIfNull(orderInvoiceTemplate.getInvoiceType());
    String invoiceTypeStr1 = StrUtil.emptyIfNull(orderInvoiceTemplate.getInvoiceTypeStr());
    if ("0".equals(invoiceType1)) {
      this.invoiceType = OrderInvoiceEnums.NORMAL.getType();
      this.invoiceTypeStr = OrderInvoiceEnums.NORMAL.getDesc();
    }else if ("1".equals(invoiceType1)){
      this.invoiceType = OrderInvoiceEnums.INCREMENT_SPECIAL.getType();
      this.invoiceTypeStr = OrderInvoiceEnums.INCREMENT_SPECIAL.getDesc();
    }else{
      this.invoiceType = StrUtil.EMPTY;
      this.invoiceTypeStr = invoiceTypeStr1;
    }
    this.invoiceTitle = StrUtil.emptyIfNull(orderInvoiceTemplate.getInvoiceTitle());
    this.taxCode = StrUtil.emptyIfNull(orderInvoiceTemplate.getTaxCode());
    this.bankName = StrUtil.emptyIfNull(orderInvoiceTemplate.getBankName());
    this.bankAccount = StrUtil.emptyIfNull(orderInvoiceTemplate.getBankAccount());
    this.openInvoiceMobile = StrUtil.emptyIfNull(orderInvoiceTemplate.getOpenInvoiceMobile());
    this.invoiceAddress = StrUtil.emptyIfNull(orderInvoiceTemplate.getInvoiceAddress());
    this.invoiceFaceInfo = StrUtil.emptyIfNull(orderInvoiceTemplate.getInvoiceFaceInfo());
    this.receiveMan = StrUtil.emptyIfNull(orderInvoiceTemplate.getReceiveMan());
    this.mobile = StrUtil.emptyIfNull(orderInvoiceTemplate.getMobile());
    this.receiveAddress = StrUtil.emptyIfNull(orderInvoiceTemplate.getReceiveAddress());
  }
}
