package com.xhgj.srm.dto.account;

import com.xhgj.srm.common.utils.supplierorder.BigDecimalUtil;
import com.xhgj.srm.jpa.entity.OrderDetail;
import com.xhgj.srm.jpa.entity.SupplierOrderProduct;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2023-02-23 20:43
 */
@Data
@NoArgsConstructor
public class ProductInfoDTO {

  @ApiModelProperty("id")
  private String productId;

  @ApiModelProperty("商品编码")
  private String productCode;

  @ApiModelProperty("品牌")
  private String brandName;

  @ApiModelProperty("商品名称")
  private String productName;

  @ApiModelProperty("型号")
  private String model;

  @ApiModelProperty("数量")
  private BigDecimal productCount;

  @ApiModelProperty("单位")
  private String unit;

  @ApiModelProperty("单价")
  private BigDecimal productPrice;

  @ApiModelProperty("客户订单号")
  private String orderNo;

  @ApiModelProperty("去税单价")
  private BigDecimal  taxFreeCbPrice;

  @ApiModelProperty("订单id")
  private String orderId;

  @ApiModelProperty("规格")
  private String specification;

  public ProductInfoDTO(OrderDetail orderDetail) {
    this.productId = orderDetail.getId();
    this.productCode = orderDetail.getCode();
    this.brandName = orderDetail.getBrand();
    this.productName = orderDetail.getName();
    this.model = orderDetail.getModel();
    this.productCount = BigDecimalUtil.formatForStandard(orderDetail.getNum());
    this.unit = orderDetail.getUnit();
    this.productPrice = orderDetail.getPrice();
    this.orderNo = orderDetail.getOrder().getOrderNo();
    this.taxFreeCbPrice = orderDetail.getTaxFreeCbPrice();
    this.orderId = orderDetail.getOrder().getId();
  }

  public ProductInfoDTO(SupplierOrderProduct product) {
    this.productId = product.getId();
    this.productCode = product.getCode();
    this.brandName = product.getBrand();
    this.productName = product.getName();
    this.model = product.getManuCode();
    this.unit = product.getUnit();
    this.specification = product.getSpecification();
  }
}
