package com.xhgj.srm.service.impl;

import com.xhgj.srm.common.Constants;
import com.xhgj.srm.common.enums.BooleanEnum;
import com.xhgj.srm.common.enums.PayTypeSAPEnums;
import com.xhgj.srm.common.enums.supplier.SupplierLevelEnum;
import com.xhgj.srm.jpa.entity.EntryRegistrationLandingMerchant;
import com.xhgj.srm.jpa.entity.EntryRegistrationOrder;
import com.xhgj.srm.jpa.entity.Group;
import com.xhgj.srm.jpa.entity.Supplier;
import com.xhgj.srm.jpa.entity.User;
import com.xhgj.srm.jpa.repository.EntryRegistrationLandingMerchantRepository;
import com.xhgj.srm.jpa.repository.EntryRegistrationOrderRepository;
import com.xhgj.srm.jpa.repository.FileRepository;
import com.xhgj.srm.jpa.repository.UserRepository;
import com.xhgj.srm.service.ShareEntryRegistrationLandingMerchantService;
import com.xhgj.srm.service.ShareGroupService;
import com.xhiot.boot.core.common.exception.CheckException;
import com.xhiot.boot.framework.jpa.repository.BootBaseRepository;
import javax.annotation.Resource;
import org.springframework.stereotype.Service;

@Service
public class ShareEntryRegistrationLandingMerchantServiceImpl
    implements ShareEntryRegistrationLandingMerchantService {

  @Resource
  private EntryRegistrationLandingMerchantRepository entryRegistrationLandingMerchantRepository;
  @Resource
  private ShareGroupService shareGroupService;
  @Resource
  private UserRepository userRepository;
  @Resource
  private EntryRegistrationOrderRepository entryRegistrationOrderRepository;
  @Resource
  private FileRepository fileRepository;

  @Override
  public BootBaseRepository<EntryRegistrationLandingMerchant, String> getRepository() {
    return entryRegistrationLandingMerchantRepository;
  }

  private User getSupplierCreateMan() {
    User user = userRepository.findFirstByRealNameAndState(Constants.USER_NAME_GUO_JIA_LEI,
        Constants.STATE_OK);
    if (user == null) {
      throw new RuntimeException("没有找到创建人，员工姓名为：" + Constants.USER_NAME_GUO_JIA_LEI);
    }
    return user;
  }

  @Override
  public Supplier convertToSupplier(EntryRegistrationLandingMerchant landingMerchant) {
    Group headquartersGroup = shareGroupService.getHeadquartersGroup();
    EntryRegistrationOrder entryRegistrationOrder =
        entryRegistrationOrderRepository.findById(landingMerchant.getEntryRegistrationOrderId())
            .orElseThrow(() -> CheckException.noFindException(EntryRegistrationOrder.class,
                landingMerchant.getEntryRegistrationOrderId()));
    Supplier supplier = new Supplier();
    supplier.setMdmCode(landingMerchant.getMdmCode());
    supplier.setIsOpenOrder(Constants.STATE_OK);
    supplier.setEnterpriseName(landingMerchant.getEnterpriseName());
    supplier.setCountry(Constants.COUNTRY_ZHONG_GUO);
    supplier.setRegion(landingMerchant.getRegion());
    supplier.setProvince(landingMerchant.getProvince());
    supplier.setCity(landingMerchant.getCity());
    supplier.setUscc(landingMerchant.getUscc());
    supplier.setIndustry(landingMerchant.getIndustry());
    supplier.setSupType(Constants.SUPPLIERTYPE_CHINA);
    supplier.setCreateGroup(headquartersGroup.getName());
    supplier.setCreateCode(headquartersGroup.getCode());
    User guoJiaLei = getSupplierCreateMan();
    supplier.setCreateMan(guoJiaLei.getId());
    supplier.setCreateTime(System.currentTimeMillis());
    supplier.setState(Constants.COMMONSTATE_TEMPORARYSTORAGE);
    supplier.setIsOpen(BooleanEnum.NO.getKey());
    supplier.setIsFromSupplier(BooleanEnum.YES.getKey());
    supplier.setCooperateType(Constants.COOPERATE_TYPE_LANDER);
    String paymentTypeName = PayTypeSAPEnums.getNameByCode(entryRegistrationOrder.getPaymentType());
    supplier.setPayType(paymentTypeName);
    supplier.setType(Constants.SUPPLIERTYPE_CHINA);
    supplier.setEnterpriseLevel(SupplierLevelEnum.SPORADIC.getCode());
    fileRepository.findFirstByRelationIdAndRelationTypeAndState(landingMerchant.getId(),
        Constants.FILE_TYPE_LANDING_MERCHANT_LICENSE, Constants.STATE_OK).ifPresent(file -> {
      supplier.setLicenseUrl(file.getUrl());
    });
    User user = userRepository.findFirstByRealNameAndState(Constants.USER_NAME_GUO_JIA_LEI,
        Constants.STATE_OK);
    if (user != null) {
      supplier.setPurchaserName(Constants.USER_NAME_GUO_JIA_LEI);
      supplier.setPurchaserId(user.getId());
    }
    return supplier;
  }

}
