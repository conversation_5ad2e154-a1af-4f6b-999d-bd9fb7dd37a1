package com.xhgj.srm.service.impl;/**
 * @since 2025/1/9 8:47
 */

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.xhgj.srm.dto.product.externalLink.ExternalLinkSaveForm;
import com.xhgj.srm.jpa.entity.ProductExternalLink;
import com.xhgj.srm.jpa.repository.ProductExternalLinkRepository;
import com.xhgj.srm.service.ShareExternalLinkService;
import com.xhiot.boot.core.common.exception.CheckException;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
public class ShareExternalLinkServiceImpl implements ShareExternalLinkService {

  @Resource
  ProductExternalLinkRepository productExternalLinkRepository;

  @Override
  public void patchUpdateExternalLinks(List<ExternalLinkSaveForm> externalLinks, String productId,
      String userId) {
    if (externalLinks == null) {
      return;
    }
    // patch更新外部链接
    List<ProductExternalLink> origins = productExternalLinkRepository.findAllByProductId(productId);
    // 筛选出新增的
    List<ExternalLinkSaveForm> newOnes =
        externalLinks.stream().filter(item -> StrUtil.isBlank(item.getId()))
            .collect(Collectors.toList());
    // 筛选出需要更新的
    List<ExternalLinkSaveForm> needUpdate =
        externalLinks.stream().filter(item -> StrUtil.isNotBlank(item.getId()))
            .collect(Collectors.toList());
    // 筛选出需要删除的
    List<ProductExternalLink> needDelete = origins.stream()
        .filter(item -> externalLinks.stream().noneMatch(externalLink -> item.getId().equals(externalLink.getId())))
        .collect(Collectors.toList());
    // 新增
    if (CollUtil.isNotEmpty(newOnes)) {
      List<ProductExternalLink> newInserts = newOnes.stream().map(item -> {
        ProductExternalLink productExternalLink =
            com.xhgj.srm.factory.MapStructFactory.INSTANCE.toProductExternalLink(item);
        productExternalLink.setCreateTime(System.currentTimeMillis());
        productExternalLink.setCreateMan(userId);
        productExternalLink.setProductId(productId);
        productExternalLink.setUpdateMan(userId);
        productExternalLink.setUpdateTime(System.currentTimeMillis());
        return productExternalLink;
      }).collect(Collectors.toList());
      productExternalLinkRepository.saveAll(newInserts);
    }
    // 更新
    if (CollUtil.isNotEmpty(needUpdate)) {
      Map<String, ProductExternalLink> id2ProductExternalLink = origins.stream()
          .collect(Collectors.toMap(ProductExternalLink::getId, item -> item, (a, b) -> a));
      List<ProductExternalLink> updates = needUpdate.stream().map(item -> {
        ProductExternalLink productExternalLink = id2ProductExternalLink.get(item.getId());
        if (productExternalLink == null) {
          throw new CheckException("系统中没有此物料外部链接信息");
        }
        com.xhgj.srm.factory.MapStructFactory.INSTANCE.updateProductExternalLink(item, productExternalLink);
        productExternalLink.setUpdateMan(userId);
        productExternalLink.setUpdateTime(System.currentTimeMillis());
        productExternalLink.setProductId(productId);
        return productExternalLink;
      }).collect(Collectors.toList());
      productExternalLinkRepository.saveAll(updates);
    }
    // 删除
    if (CollUtil.isNotEmpty(needDelete)) {
      productExternalLinkRepository.deleteAll(needDelete);
    }
    productExternalLinkRepository.flush();
  }
}

