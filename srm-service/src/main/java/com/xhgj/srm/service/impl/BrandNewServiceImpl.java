package com.xhgj.srm.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.xhgj.srm.common.Constants;
import com.xhgj.srm.common.enums.BrandRelationTypeEnum;
import com.xhgj.srm.dto.BrandInfoDTO;
import com.xhgj.srm.jpa.dao.BrandDao;
import com.xhgj.srm.jpa.entity.Brand;
import com.xhgj.srm.jpa.entity.Supplier;
import com.xhgj.srm.jpa.entity.SupplierInGroup;
import com.xhgj.srm.jpa.repository.BrandRepository;
import com.xhgj.srm.request.dto.partner.SupplierFileDTO;
import com.xhgj.srm.service.BrandNewService;
import com.xhgj.srm.service.SupplierInGroupNewService;
import com.xhgj.srm.service.UserNewService;
import com.xhiot.boot.framework.jpa.repository.BootBaseRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

@Slf4j
@Service
public class BrandNewServiceImpl implements BrandNewService {

  @Autowired
  private BrandRepository repository;
  @Autowired
  private BrandDao dao;
  @Autowired
  private UserNewService userNewService;
  @Autowired
  private FileNewServiceImpl fileService;
  @Autowired
  private SupplierInGroupNewService supplierInGroupNewService;


  @Override
  public BootBaseRepository<Brand, String> getRepository() {
    return repository;
  }

  @Override
  public List<Brand> getBrandsByRelationIdAndType(
      String relationId, BrandRelationTypeEnum relationType) {
    return dao.getBrandListByRelationId(relationId, relationType.getKey());
  }

  /**
   * 根据组织内供应商获取品牌对象列表
   *
   */
  public List<BrandInfoDTO> getBrandInfoDTOListByMpmBrandCode(String mpmBrandCode) {
    List<Brand> brands = repository.findAllByCodeAndRelationTypeAndState(mpmBrandCode,
        BrandRelationTypeEnum.SUPPLIER_IN_GROUP.getKey(),Constants.STATE_OK);
    List<BrandInfoDTO> dtoList = new ArrayList<>();
    for (Brand brand : brands) {
      SupplierInGroup supplierInGroup = supplierInGroupNewService.findById(brand.getRelationId());
      if (Objects.isNull(supplierInGroup) || Objects.equals(Constants.STATE_DELETE,supplierInGroup.getState())) {
        continue;
      }
      Supplier supplier = supplierInGroup.getSupplier();
      if (Objects.isNull(supplier) || Objects.equals(Constants.STATE_DELETE,supplier.getState())) {
        continue;
      }
      BrandInfoDTO brandDTO = new BrandInfoDTO(brand);
      brandDTO.setSupplierCode(StrUtil.emptyIfNull(supplier.getMdmCode()));
      brandDTO.setSupplierName(StrUtil.emptyIfNull(supplier.getEnterpriseName()));
      if (Objects.nonNull(supplierInGroup.getGroup().getId())) {
        brandDTO.setShipperCode(StrUtil.emptyIfNull(supplierInGroup.getGroup().getCode()));
      }
      String associatedName = userNewService.getNameById(StrUtil.emptyIfNull(brand.getAssociatedId()));
      brandDTO.setGeneralLicenseUrl(getSupplierBrandUrl(brand.getId(),
          Constants.FILE_TYPE_BRAND_AUTHORIZATION));
      brandDTO.setProxyLicenseUrl(getSupplierBrandUrl(brand.getId(),Constants.FILE_TYPE_BRAND_AGENT_AUTHORIZATION));
      brandDTO.setTrademarkRegistrationUrl(getSupplierBrandUrl(brand.getId(),Constants.FILE_TYPE_BRAND_TRADEMARK));
      brandDTO.setAssociatedName(associatedName);
      dtoList.add(brandDTO);
    }
    return dtoList;
  }



  public SupplierFileDTO getSupplierBrandUrl(String brandId, String type) {

    return CollUtil.emptyIfNull(
            fileService.getFileListByIdAndType(brandId, type))
        .stream()
        .findAny()
        .map(file -> new SupplierFileDTO(file.getName(), file.getUrl()))
        .orElse(new SupplierFileDTO());
  }




}
