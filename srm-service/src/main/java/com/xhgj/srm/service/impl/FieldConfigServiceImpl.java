package com.xhgj.srm.service.impl;

import com.xhgj.srm.common.constants.Constants_FieldConfig;
import com.xhgj.srm.common.enums.FieldConfigEnum;
import com.xhgj.srm.dto.filedConfig.ProcurementFieldDTO;
import com.xhgj.srm.dto.filedConfig.TemplateFieldDTO;
import com.xhgj.srm.dto.filedConfig.TitleFieldListDTO;
import com.xhgj.srm.jpa.dao.FieldConfigDao;
import com.xhgj.srm.jpa.entity.FieldConfig;
import com.xhgj.srm.jpa.repository.FieldConfigRepository;
import com.xhgj.srm.service.FieldConfigService;
import com.xhiot.boot.framework.jpa.repository.BootBaseRepository;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class FieldConfigServiceImpl implements FieldConfigService {

  @Autowired
  private FieldConfigRepository repository;
  @Autowired private FieldConfigDao dao;

  @Override
  public BootBaseRepository<FieldConfig, String> getRepository() {
    return repository;
  }


  @Override
  public ProcurementFieldDTO buildProcurementField() {
    List<FieldConfig> all = repository.findAllByBigType(Constants_FieldConfig.BIG_TYPE_PURCHASE_APPLY);
    ProcurementFieldDTO procurementFieldDTO = new ProcurementFieldDTO();
    Map<String, List<FieldConfig>> filedMap = all.stream().collect(
        HashMap::new,
        (m, v) -> m.computeIfAbsent(v.getGroupType(), k -> new ArrayList<>()).add(v),
        (m1, m2) -> m2.forEach((k, v) -> m1.merge(k, v, (v1, v2) -> {
          v1.addAll(v2);
          return v1;
        })));

    for (FieldConfigEnum value : FieldConfigEnum.values()) {
      String code = value.getCode();
      if (filedMap.containsKey(code)) {
        List<FieldConfig> fieldConfigs = filedMap.get(code);
        if (fieldConfigs == null || fieldConfigs.isEmpty()) {
          continue; // 跳过空配置
        }

        Map<String, List<FieldConfig>> filedTitleMap = fieldConfigs.stream()
            .collect(Collectors.groupingBy(FieldConfig::getTitle));

        List<TitleFieldListDTO> titleFieldListDTOList = convertToTitleFieldListDTO(filedTitleMap);

        if (FieldConfigEnum.PROCUREMENT_APPLICATION.getCode().equals(code)) {
          procurementFieldDTO.setProcurementApplicationList(titleFieldListDTOList);
        } else if (FieldConfigEnum.PROCUREMENT_APPLICATION_INFO.getCode().equals(code)) {
          procurementFieldDTO.setProcurementApplicationInfo(titleFieldListDTOList);
        } else if (FieldConfigEnum.PRODUCT_DETAIL.getCode().equals(code)) {
          procurementFieldDTO.setProductDetail(titleFieldListDTOList);
        } else if (FieldConfigEnum.COMPONENT_DETAIL.getCode().equals(code)) {
          procurementFieldDTO.setComponentDetail(titleFieldListDTOList);
        }
      }
    }
//    for (FieldConfigEnum value : values) {
//      // 采购申请列表
//      if (Objects.equals(value.getCode(), FieldConfigEnum.PROCUREMENT_APPLICATION.getCode())) {
//        List<FieldConfig> fieldConfigs = filedMap.get(value.getCode());
//        Map<String, List<FieldConfig>> filedTitleMap = fieldConfigs.stream().collect(
//            HashMap::new,
//            (m, v) -> m.computeIfAbsent(v.getTitle(), k -> new ArrayList<>()).add(v),
//            (m1, m2) -> m2.forEach((k, v) -> m1.merge(k, v, (v1, v2) -> {
//              v1.addAll(v2);
//              return v1;
//            })));
//        List<TitleFieldListDTO> titleFieldListDTOList = new ArrayList<>();
//        for (String s : filedTitleMap.keySet()) {
//          TitleFieldListDTO titleFieldListDTO = new TitleFieldListDTO();
//          titleFieldListDTO.setTitle(s);
//          titleFieldListDTO.setFields(convertToTemplateFieldDTOList(filedTitleMap.get(s)));
//          titleFieldListDTOList.add(titleFieldListDTO);
//        }
//        procurementFieldDTO.setProcurementApplicationList(titleFieldListDTOList);
//      }
//      // 采购申请详情
//      if (Objects.equals(value.getCode(), FieldConfigEnum.PROCUREMENT_APPLICATION_INFO.getCode())) {
//        List<FieldConfig> fieldConfigs = filedMap.get(value.getCode());
//        Map<String, List<FieldConfig>> filedTitleMap = fieldConfigs.stream().collect(
//            HashMap::new,
//            (m, v) -> m.computeIfAbsent(v.getTitle(), k -> new ArrayList<>()).add(v),
//            (m1, m2) -> m2.forEach((k, v) -> m1.merge(k, v, (v1, v2) -> {
//              v1.addAll(v2);
//              return v1;
//            })));
//        List<TitleFieldListDTO> titleFieldListDTOList = new ArrayList<>();
//        for (String s : filedTitleMap.keySet()) {
//          TitleFieldListDTO titleFieldListDTO = new TitleFieldListDTO();
//          titleFieldListDTO.setTitle(s);
//          titleFieldListDTO.setFields(convertToTemplateFieldDTOList(filedTitleMap.get(s)));
//          titleFieldListDTOList.add(titleFieldListDTO);
//        }
//        procurementFieldDTO.setProcurementApplicationInfo(titleFieldListDTOList);
//      }
//      // 物料详情
//      if (Objects.equals(value.getCode(), FieldConfigEnum.PRODUCT_DETAIL.getCode())) {
//        List<FieldConfig> fieldConfigs = filedMap.get(value.getCode());
//        Map<String, List<FieldConfig>> filedTitleMap = fieldConfigs.stream().collect(
//            HashMap::new,
//            (m, v) -> m.computeIfAbsent(v.getTitle(), k -> new ArrayList<>()).add(v),
//            (m1, m2) -> m2.forEach((k, v) -> m1.merge(k, v, (v1, v2) -> {
//              v1.addAll(v2);
//              return v1;
//            })));
//        List<TitleFieldListDTO> titleFieldListDTOList = new ArrayList<>();
//        for (String s : filedTitleMap.keySet()) {
//          TitleFieldListDTO titleFieldListDTO = new TitleFieldListDTO();
//          titleFieldListDTO.setTitle(s);
//          titleFieldListDTO.setFields(convertToTemplateFieldDTOList(filedTitleMap.get(s)));
//          titleFieldListDTOList.add(titleFieldListDTO);
//        }
//        procurementFieldDTO.setProductDetail(titleFieldListDTOList);
//      }
//      // 组件详情
//      if (Objects.equals(value.getCode(), FieldConfigEnum.COMPONENT_DETAIL.getCode())) {
//        List<FieldConfig> fieldConfigs = filedMap.get(value.getCode());
//        Map<String, List<FieldConfig>> filedTitleMap = fieldConfigs.stream().collect(
//            HashMap::new,
//            (m, v) -> m.computeIfAbsent(v.getTitle(), k -> new ArrayList<>()).add(v),
//            (m1, m2) -> m2.forEach((k, v) -> m1.merge(k, v, (v1, v2) -> {
//              v1.addAll(v2);
//              return v1;
//            })));
//        List<TitleFieldListDTO> titleFieldListDTOList = new ArrayList<>();
//        for (String s : filedTitleMap.keySet()) {
//          TitleFieldListDTO titleFieldListDTO = new TitleFieldListDTO();
//          titleFieldListDTO.setTitle(s);
//          titleFieldListDTO.setFields(convertToTemplateFieldDTOList(filedTitleMap.get(s)));
//          titleFieldListDTOList.add(titleFieldListDTO);
//        }
//        procurementFieldDTO.setComponentDetail(titleFieldListDTOList);
//      }
//    }
    return procurementFieldDTO;
  }

  @Override
  public List<TitleFieldListDTO> getPurchaseOrderV2DefaultView() {
    List<FieldConfig> all =
        repository.findAllByBigTypeAndGroupTypeOrderByGroupTypeAscSortAsc(Constants_FieldConfig.BIG_TYPE_PURCHASE_ORDER,
            FieldConfigEnum.PROCUREMENT_ORDER.getCode()
    );
    // 保持有序
    Map<String, List<FieldConfig>> filedTitleMap = all.stream()
        .collect(Collectors.groupingBy(FieldConfig::getTitle,LinkedHashMap::new,Collectors.toList()));

    return convertToTitleFieldListDTO(filedTitleMap);
  }

  private List<TitleFieldListDTO> convertToTitleFieldListDTO(Map<String, List<FieldConfig>> filedTitleMap) {
    List<TitleFieldListDTO> result = new ArrayList<>();
    for (Map.Entry<String, List<FieldConfig>> entry : filedTitleMap.entrySet()) {
      TitleFieldListDTO dto = new TitleFieldListDTO();
      dto.setTitle(entry.getKey());
      dto.setFields(convertToTemplateFieldDTOList(entry.getValue()));
      result.add(dto);
    }
    return result;
  }

  private List<TemplateFieldDTO> convertToTemplateFieldDTOList(List<FieldConfig> fieldConfigs) {
    List<TemplateFieldDTO> templateFieldDTOList = new ArrayList<>();
    for (FieldConfig fieldConfig : fieldConfigs) {
      TemplateFieldDTO templateFieldDTO = new TemplateFieldDTO();
      templateFieldDTO.setName(fieldConfig.getName());
      templateFieldDTO.setKey(fieldConfig.getKey());
      templateFieldDTO.setGroupType(fieldConfig.getGroupType());
      templateFieldDTO.setIsDefault(fieldConfig.getIsDefault());
      templateFieldDTO.setIsModify(fieldConfig.getIsModify());
      templateFieldDTOList.add(templateFieldDTO);
    }
    return templateFieldDTOList;
  }
}
