package com.xhgj.srm.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.NumberUtil;
import com.xhgj.srm.jpa.entity.Order;
import com.xhgj.srm.jpa.entity.OrderAccount;
import com.xhgj.srm.jpa.repository.OrderAccountRepository;
import com.xhgj.srm.jpa.repository.OrderRepository;
import com.xhgj.srm.service.TempOrderAccountService;
import com.xhiot.boot.core.common.exception.CheckException;
import com.xhiot.boot.framework.jpa.repository.BootBaseRepository;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;
import java.util.concurrent.atomic.AtomicReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @since 2023-05-10 9:37
 */
@Service
public class TempOrderAccountServiceImpl implements TempOrderAccountService {

  @Autowired private OrderAccountRepository repository;
  @Autowired private OrderRepository orderRepository;

  @Override
  public BootBaseRepository<OrderAccount, String> getRepository() {
    return repository;
  }

  @Override
  public BigDecimal getFinalAccountPrice(List<String> orderIdList){
    AtomicReference<BigDecimal> finalAccountPrice = new AtomicReference<>(BigDecimal.ZERO);
    if(CollUtil.isNotEmpty(orderIdList)){
      orderIdList.forEach(orderId->{
            Order order = orderRepository.findById(orderId).orElseThrow(() -> CheckException
                .noFindException(Order.class, orderId));
            finalAccountPrice.set(NumberUtil.add(finalAccountPrice.get(),
                order.getPrice().subtract(order.getRefundPrice())));
          }
      );
    }
    return finalAccountPrice.get().setScale(2, RoundingMode.HALF_UP);
  }

}
