package com.xhgj.srm.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.xhgj.srm.jpa.dao.OrderDetailDao;
import com.xhgj.srm.jpa.entity.OrderDetail;
import com.xhgj.srm.jpa.repository.OrderDetailRepository;
import com.xhgj.srm.service.TempOrderDetailService;
import com.xhiot.boot.framework.jpa.repository.BootBaseRepository;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/** <AUTHOR> @ClassName TempOrderDetailServiceImpl */
@Service
public class TempOrderDetailServiceImpl implements TempOrderDetailService {
  @Autowired private OrderDetailRepository repository;
  @Autowired private OrderDetailDao dao;

  @Override
  public BootBaseRepository<OrderDetail, String> getRepository() {
    return repository;
  }

  @Override
  public List<OrderDetail> getOrderDetailByOrderId(String orderId) {
    return CollUtil.emptyIfNull(dao.getOrderDetailByOrderId(orderId));
  }
}
