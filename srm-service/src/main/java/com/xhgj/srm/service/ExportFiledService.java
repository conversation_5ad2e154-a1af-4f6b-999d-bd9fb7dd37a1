package com.xhgj.srm.service;

import com.xhgj.srm.common.dto.exportfiled.ExportFiledDetailDTO;
import com.xhgj.srm.common.dto.exportfiled.ExportFiledSelectDTO;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2024-05-24 9:42
 */
public interface ExportFiledService {


  ExportFiledDetailDTO getExportFiled(String type, String tempId,String userId);

  void deleteTemplate(String templateId);

  /**
   * 获取字段配置表
   *
   * @param type 类型
   * @param templateId 模板 id
   * @return ExportFiledDetailDTO
   */
  ExportFiledDetailDTO getExportFiledV2(String userGroup, String type, String templateId, String userId);

  /**
   * 根据组织code 获取fieldMap映射用于导出
   */
  Map<String, String> getFieldMap(List<ExportFiledSelectDTO> choose, String userGroup);
}
