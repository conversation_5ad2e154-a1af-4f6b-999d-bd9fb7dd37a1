package com.xhgj.srm.service;

import com.xhgj.srm.dto.order.CreatePaymentOrderResult;
import com.xhgj.srm.dto.order.OrderPaymentInfoDTO;
import com.xhgj.srm.dto.order.OrderPaymentListDTO;
import com.xhgj.srm.dto.order.OrderPaymentListQuery;
import com.xhgj.srm.dto.order.OrderPaymentRefundDTO;
import com.xhgj.srm.dto.order.SubmitPaymentOrderApiParams;
import com.xhgj.srm.dto.order.SubmitPaymentOrderParams;
import com.xhgj.srm.dto.order.SubmitPaymentRefundParams;
import com.xhgj.srm.dto.order.SubmitPaymentVoucherParams;
import com.xhgj.srm.dto.order.UpdatePaymentVoucherParams;
import com.xhgj.srm.jpa.dto.order.OrderPaymentStatistics;
import com.xhgj.srm.jpa.entity.OrderPayment;
import com.xhgj.srm.jpa.entity.OrderPaymentCollection;
import com.xhiot.boot.framework.jpa.service.BootBaseService;
import com.xhiot.boot.mvc.base.PageResult;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

/**
 * <AUTHOR>
 * @since 2023-03-16 15:07
 */
public interface OrderPaymentService extends BootBaseService<OrderPayment, String> {

  /**
   * 提交付款的信息
   *
   * @param params 参数 必传
   */
  CreatePaymentOrderResult submitPaymentOrderForApi(SubmitPaymentOrderApiParams params);

  /**
   * 提交付款的信息
   *
   * @param params 参数 必传
   */
  CreatePaymentOrderResult submitNewPaymentOrderForHtApi(SubmitPaymentOrderParams params);

  /**
   * 分页获取付款单
   *
   * @param orderPaymentListQuery 查询条件
   * @param pageable 分页参数
   */
  Page<OrderPaymentListDTO> getOrderPaymentPage(
      OrderPaymentListQuery orderPaymentListQuery, Pageable pageable);

  /**
   * 分页获取付款单
   *
   * @param orderPaymentListQuery 查询条件
   */
  PageResult<OrderPaymentListDTO> getOrderPaymentPageRef(OrderPaymentListQuery orderPaymentListQuery);

  /**
   * 前台项目根据付款单 id 查询付款单
   *
   * @param paymentId 付款单 id 必传
   */
  OrderPaymentInfoDTO getOrderPaymentInfoById(String paymentId);

  /**
   * 后台项目 根据付款单 id 查询付款单
   *
   * @param paymentId 付款单 id 必传
   */
  OrderPaymentInfoDTO getOrderPaymentInfoByIdAdmin(String paymentId);


  /**
   * 分页获取付款单信息
   *
   * @param supplierId 供应商 id
   * @param paymentNo 付款单号
   * @param paymentStatus 付款状态
   * @param submitMan 提交人
   * @param createTimeStart 提交时间范围 开始
   * @param createTimeEnd 提交时间范围 结束
   * @param pageable 分页参数 结束
   */
  Page<OrderPayment> findPage(
      String supplierId,
      String paymentNo,
      String paymentStatus,
      String submitMan,
      String orderCount,
      String applyPrice,
      String paymentPrice,
      Long createTimeStart,
      Long createTimeEnd,
      Boolean autoDraw,
      Pageable pageable);

  /**
   * 填写erp凭证
   *
   * @param params 参数 必传
   */
  void saveErpPaymentVoucher(SubmitPaymentVoucherParams params);

  /**
   * 修改erp凭证
   *
   * @param params 参数 必传
   */
  void  updateErpPaymentVoucher(UpdatePaymentVoucherParams params);
  /**
   * 删除erp凭证
   *
   * @param id 参数 必传
   */
  void delErpPaymentVoucher(String id);

  /***
   *  通过状态获取付款单的数量
   * @param paymentState 支付状态 必填
   * <AUTHOR>
   * @date 2023/3/22 17:21
   * @return long
   */
  Map<String, Long> getOrderPaymentIngNum();

  /**
   *  获取付款单的应付金额
   * @param paymentId
   */
  BigDecimal getPaymentFinalPrice(String paymentId);


  /**
   * 根据付款单号获取付款申请
   * @param paymentApplyNo 付款单号
   * @return
   */
  Optional<OrderPayment> getOrderPaymentNo(String paymentApplyNo);

  /**
   * 同步落地商付款时间，付款金额
   */
  void syncOrderPaymentTask();

  /**
   * @description: 付款单申请退款
   * @param: SubmitPaymentRefundParams
   **/
  void submitPaymentRefund(SubmitPaymentRefundParams params);

  /**
   * @description: 获取付款单申请退款详情
   * @param: orderPaymentCollectionId 回款单id
   **/
  OrderPaymentRefundDTO getPaymentRefundInfoById(String orderPaymentCollectionId);

  /**
   * 付款单统计
   * @param orderPaymentListQuery
   */
  OrderPaymentStatistics getOrderPaymentStatistics(OrderPaymentListQuery orderPaymentListQuery);


  void saveOrderPartialPayment(OrderPayment orderPayment, List<OrderPaymentCollection> orderPaymentCollections);

  /**
   * 根据订单id查询付款单
   * @param orderId
   * @return
   */
  List<OrderPayment> findByOrderId(String orderId);

  void abandonOrderPaymentById(String paymentId);
}
