package com.xhgj.srm.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.StrUtil;
import com.xhgj.cloud.dock.provider.support.ConstantsEAS;
import com.xhgj.cloud.dock.provider.support.dto.DockSupplier;
import com.xhgj.cloud.dock.provider.support.dto.DockSupplier.Bank;
import com.xhgj.cloud.dock.provider.support.dto.DockSupplier.DockSupplierBuilder;
import com.xhgj.srm.common.Constants;
import com.xhgj.srm.common.enums.PayTypeSAPEnums;
import com.xhgj.srm.common.enums.ProvinceSAPEnum;
import com.xhgj.srm.common.enums.SettleCurrency;
import com.xhgj.srm.common.enums.VoucherAccountPeriodEnum;
import com.xhgj.srm.common.enums.VoucherAccountPeriodEnum.VoucherAccountPeriod;
import com.xhgj.srm.common.enums.supplier.SupplierLevelEnum;
import com.xhgj.srm.dto.financial.FinancialDTO;
import com.xhgj.srm.jpa.dao.ContactDao;
import com.xhgj.srm.jpa.dao.FinancialDao;
import com.xhgj.srm.jpa.dao.SupplierInGroupDao;
import com.xhgj.srm.jpa.dao.UserToGroupDao;
import com.xhgj.srm.jpa.entity.Contact;
import com.xhgj.srm.jpa.entity.EntryRegistrationLandingMerchant;
import com.xhgj.srm.jpa.entity.EntryRegistrationOrder;
import com.xhgj.srm.jpa.entity.Financial;
import com.xhgj.srm.jpa.entity.Group;
import com.xhgj.srm.jpa.entity.Supplier;
import com.xhgj.srm.jpa.entity.SupplierInGroup;
import com.xhgj.srm.jpa.entity.User;
import com.xhgj.srm.jpa.entity.UserToGroup;
import com.xhgj.srm.jpa.repository.ContactRepository;
import com.xhgj.srm.jpa.repository.FileRepository;
import com.xhgj.srm.jpa.repository.FinancialRepository;
import com.xhgj.srm.jpa.repository.GroupRepository;
import com.xhgj.srm.jpa.repository.SupplierInGroupRepository;
import com.xhgj.srm.jpa.repository.SupplierRepository;
import com.xhgj.srm.jpa.repository.UserRepository;
import com.xhgj.srm.request.dto.edge.CountryDomain;
import com.xhgj.srm.request.service.third.xhgj.XhgjEdgeService;
import com.xhgj.srm.service.ShareSupplierInGroupService;
import com.xhgj.srm.service.ShareSupplierService;
import com.xhiot.boot.core.common.exception.CheckException;
import com.xhiot.boot.core.common.util.StringUtils;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.TreeSet;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
public class ShareSupplierInGroupServiceImpl implements ShareSupplierInGroupService {

  @Resource
  private SupplierRepository supplierRepository;
  @Resource
  private GroupRepository groupRepository;
  @Resource
  private SupplierInGroupRepository supplierInGroupRepository;
  @Resource
  private ShareSupplierService shareSupplierService;
  @Resource
  private FileRepository fileRepository;
  @Resource
  private UserRepository userRepository;
  @Resource
  private ContactRepository contactRepository;
  @Resource
  private FinancialRepository financialRepository;
  @Resource
  private SupplierInGroupDao supplierInGroupDao;
  @Resource
  private XhgjEdgeService xhgjEdgeService;
  @Resource
  FinancialDao financialDao;
  @Resource
  UserToGroupDao userToGroupDao;
  @Resource
  ContactDao contactDao;

  @Override
  @Transactional
  public SupplierInGroup createIfNotExist(String groupCode, String supplierId,
      EntryRegistrationLandingMerchant landingMerchant,
      EntryRegistrationOrder entryRegistrationOrder) {
    Supplier supplier = supplierRepository.findById(supplierId)
        .orElseThrow(() -> new CheckException("新增供应商关联组织时，供应商不存在"));
    Group group = groupRepository.findFirstByCodeAndState(groupCode, Constants.STATE_OK);
    if (group == null) {
      throw new CheckException("新增供应商关联组织时，组织不存在");
    }
    Optional<SupplierInGroup> supplierInGroupOptional =
        supplierInGroupRepository.findFirstBySupplierIdAndGroupIdAndState(supplierId, group.getId(),
            Constants.STATE_OK);
    SupplierInGroup supplierInGroup = supplierInGroupOptional.orElseGet(SupplierInGroup::new);
    supplierInGroup.setSupplier(supplier);
    supplierInGroup.setSupplierId(supplierId);
    supplierInGroup.setGroupId(group.getId());
    supplierInGroup.setGroup(group);
    supplierInGroup.setRegAddress(landingMerchant.getRegAddress());
    supplierInGroup.setCreateTime(System.currentTimeMillis());
    supplierInGroup.setState(Constants.STATE_OK);
    supplierInGroup.setSettleCurrency(SettleCurrency.PRE001.getKey());
    VoucherAccountPeriod voucherAccountPeriod =
        VoucherAccountPeriodEnum.findNearestNotExceeding(entryRegistrationOrder.getAccountPeriod());
    supplierInGroup.setAccountPeriod(voucherAccountPeriod.getType());
    supplierInGroup.setCorporate(landingMerchant.getCorporate());
    supplierInGroup.setRegAddress(landingMerchant.getRegAddress());
    User user = userRepository.findFirstByRealNameAndState(Constants.USER_NAME_GUO_JIA_LEI,
        Constants.STATE_OK);
    if (user != null) {
      supplierInGroup.setPurchaser(user);
      supplierInGroup.setPurchaserId(user.getId());
    }
    fileRepository.findFirstByRelationIdAndRelationTypeAndState(landingMerchant.getId(),
        Constants.FILE_TYPE_LANDING_MERCHANT_LICENSE, Constants.STATE_OK).ifPresent(file -> {
      supplierInGroup.setLicenseUrl(file.getUrl());
    });
    supplierInGroup.setEnterpriseLevel(SupplierLevelEnum.SPORADIC.getCode());
    supplierInGroup.setEnterpriseNature(landingMerchant.getCompanyAttributeType());
    supplierInGroupRepository.save(supplierInGroup);
    addContactIfNotExist(entryRegistrationOrder.getCooperationContactName(), supplier.getId(),
        entryRegistrationOrder.getCooperationContactPhone(), user.getId(),
        Constants.WAN_JU_GROUP_ID, supplierInGroup.getId());
    addFinancialIfNotExist(landingMerchant, supplier, supplierInGroup.getId());
    // 更新供应商完善度
    Integer supplierIntegrity = shareSupplierService.getSupplierIntegrity(supplierInGroup);
    supplierInGroup.setIntegrity(
        supplierIntegrity == null ? StrUtil.EMPTY : supplierIntegrity.toString());
    supplierInGroupRepository.save(supplierInGroup);
    return supplierInGroup;
  }

  @Override
  public DockSupplier buildDockSupplier(String id, Group group, String noticePersonId) {
    Supplier supplier =
        supplierRepository.findById(id).orElseThrow(() -> CheckException.noFindException(Supplier.class, id));
    DockSupplierBuilder builder = DockSupplier.builder();
    String country = supplier.getCountry();
    if (StrUtil.isNotEmpty(country)) {
      country = xhgjEdgeService.getCountryByName(country).map(CountryDomain::getCode).orElse(null);
    }
    builder.mdmCode(supplier.getMdmCode()).name(supplier.getEnterpriseName()).country(country)
        .province(StrUtil.isNotEmpty(supplier.getProvince()) ? ProvinceSAPEnum.getCodeByName(
            supplier.getProvince()) : null)
        // ******** 个人供应商该字段传联系方式
        .uscc(Objects.equals(supplier.getSupType(), Constants.SUPPLIERTYPE_PERSONAL)
            ? supplier.getMobile() : supplier.getUscc()).paymentType(
            StrUtil.isNotEmpty(supplier.getPayType()) ? PayTypeSAPEnums.getCodeByName(
                supplier.getPayType()) : null).supType(supplier.getSupType())
        .groupCode(Optional.ofNullable(group).map(Group::getErpCode).orElse(null));
    if (group != null) {
      this.fillDockSupplierInGroupData(id, group, builder, noticePersonId);
    }
    return builder.build();
  }

  /**
   * 填充对接供应商对象
   * @param supplierId
   * @param group
   * @param builder
   * @param noticePersonId
   */
  public void fillDockSupplierInGroupData(
      String supplierId,
      Group group,
      DockSupplier.DockSupplierBuilder builder,String noticePersonId) {
    Assert.notEmpty(supplierId);
    Assert.notNull(builder);
    Optional.ofNullable(group)
        .map(code -> supplierInGroupDao.getByGroupAndSupplier(supplierId, group.getErpCode()))
        .ifPresent(
            supplierInGroup -> {
              User purchaser = supplierInGroup.getPurchaser();
              if (purchaser == null) {
                throw new CheckException("供应商没有配置负责人!");
              }
              List<Bank> bankList =
                  CollUtil.emptyIfNull(financialDao.getBySupplierId(supplierId))
                      .stream()
                      .filter(financial -> StrUtil.isNotBlank(financial.getBankCode())
                          &&StrUtil.isNotBlank(financial.getBankNum()))
                      .map(financial -> Bank.builder().name(financial.getBankName())
                          .num(financial.getBankNum()).account(financial.getBankAccount())
                          .address(financial.getBankAddress()).code(financial.getBankCode())
                          .swiftCode(financial.getSwiftCode()).build())
                      .collect(Collectors.collectingAndThen(
                          Collectors.toCollection(() -> new TreeSet<>(
                              Comparator.comparing(bank-> bank.getCode()+"@"+bank.getNum()))),
                          ArrayList::new));
              builder
                  .orgCodeList(Collections.singletonList(group.getErpCode()))
                  .corporate(supplierInGroup.getCorporate())
                  .regAddress(supplierInGroup.getRegAddress())
                  .purchaseCode(purchaser.getCode())
                  .purchaseDeptCode(
                      Optional.ofNullable(
                                      userToGroupDao.getUserToGroupByUserIdAndGroupId(
                                              purchaser.getId(), group.getId()))
                          .map(UserToGroup::getDeptId)
                          .flatMap(deptId -> groupRepository.findById(deptId))
                          .map(Group::getErpCode)
                          .orElse(StrUtil.EMPTY))
                  .invoiceType(supplierInGroup.getInvoiceType())
                  .taxRate(supplierInGroup.getTaxRate())
                  .settleCurrency(supplierInGroup.getSettleCurrency())
                  .banks(bankList)
                  .contacts(
                      CollUtil.emptyIfNull(
                              contactDao.getContactListBySupplierInGroupId(
                                  supplierInGroup.getId()))
                          .stream()
                          .map(
                              contact ->
                                  DockSupplier.Contact.builder()
                                      .id(contact.getId())
                                      .name(contact.getName())
                                      .phone(contact.getPhone())
                                      .email(contact.getMail())
                                      .post(contact.getDuty())
                                      .build())
                          .collect(Collectors.toList()))
                  .noticePersonIdList(
                      Collections.singletonList(
                          Optional.ofNullable(noticePersonId)
                              .flatMap(item -> userRepository.findById(noticePersonId))
                              .map(User::getMdmId)
                              .orElse(StrUtil.EMPTY)))
                  .noticeContent(
                      buildNoticeContent(
                          supplierInGroup.getSupplier().getEnterpriseName(), group.getName()))
                  .accountPeriod(supplierInGroup.getAccountPeriod())
                  .shortName(supplierInGroup.getAbbreviation());
            });
  }



  @Override
  public DockSupplier buildDockSupplierWithBank(String id) {
    Supplier supplier = supplierRepository.findById(id)
        .orElseThrow(() -> CheckException.noFindException(Supplier.class, id));
    DockSupplierBuilder builder = DockSupplier.builder();
    String country = supplier.getCountry();
    if (StrUtil.isNotEmpty(country)) {
      country = xhgjEdgeService.getCountryByName(country).map(CountryDomain::getCode).orElse(null);
    }
    builder.mdmCode(supplier.getMdmCode()).name(supplier.getEnterpriseName()).country(country)
        .province(StrUtil.isNotEmpty(supplier.getProvince()) ? ProvinceSAPEnum.getCodeByName(
            supplier.getProvince()) : null)
        // ******** 个人供应商该字段传联系方式
        .uscc(Objects.equals(supplier.getSupType(), Constants.SUPPLIERTYPE_PERSONAL)
            ? supplier.getMobile() : supplier.getUscc()).paymentType(
            StrUtil.isNotEmpty(supplier.getPayType()) ? PayTypeSAPEnums.getCodeByName(
                supplier.getPayType()) : null).supType(supplier.getSupType());
    this.fillDockSupplierDataWithBank(id, builder);
    return builder.build();
  }

  /**
   * 填充对接供应商和银行信息
   * @param supplierId
   * @param builder
   */
  public void fillDockSupplierDataWithBank(String supplierId,
      DockSupplier.DockSupplierBuilder builder) {
    Assert.notEmpty(supplierId);
    Assert.notNull(builder);
    List<Bank> bankList = CollUtil.emptyIfNull(financialDao.getBySupplierId(supplierId)).stream()
        .filter(financial -> StrUtil.isNotBlank(financial.getBankCode()) && StrUtil.isNotBlank(
            financial.getBankNum())).map(
            financial -> Bank.builder().name(financial.getBankName()).num(financial.getBankNum())
                .account(financial.getBankAccount()).address(financial.getBankAddress())
                .code(financial.getBankCode()).swiftCode(financial.getSwiftCode()).build()).collect(
            Collectors.collectingAndThen(Collectors.toCollection(() -> new TreeSet<>(
                    Comparator.comparing(bank -> bank.getCode() + "@" + bank.getNum()))),
                ArrayList::new));
    builder.banks(bankList);
    builder.updateSign(ConstantsEAS.UPDATE_SIGN_BANK);
  }




  private String buildNoticeContent(String supplierName, String orgName) {
    return StrUtil.format(NOTICE_TEMPLATE_SUPPLIER_TO_ERP, supplierName, orgName);
  }

  private static final String NOTICE_TEMPLATE_SUPPLIER_TO_ERP =
      "【推送成功】您推送到ERP的供应商已推送成功！\r\n供应商名称：{}\r\n推送组织：{}\r\n";

  public void addContactIfNotExist(String contacts, String supplierId, String mobile, String userId,
      String groupId, String supplierInGroupId) {
    if (StringUtils.isNullOrEmpty(supplierId)) {
      throw new CheckException("供应商为空");
    }
    if (StringUtils.isNullOrEmpty(userId)) {
      throw new CheckException("用户id为空");
    }
    if (StringUtils.isNullOrEmpty(mobile)) {
      throw new CheckException("供应商联系方式为空");
    }
    if (StringUtils.isNullOrEmpty(contacts)) {
      throw new CheckException("供应商联系人为空");
    }
    Supplier supplier = supplierRepository.findById(supplierId)
        .orElseThrow(() -> new CheckException("供应商不存在，无法新增联系人"));
    List<Contact> contactList =
        contactRepository.findAllBySupplierIdAndSupplierInGroupIdAndState(supplierId, supplierInGroupId,
            Constants.STATE_OK);
    Contact selectContact = CollUtil.emptyIfNull(contactList).stream()
        .filter(contact -> Objects.equals(contact.getName(), contacts)).findFirst().orElse(null);
    if (selectContact == null) {
      Contact contact = new Contact();
      contact.setCreateMan(userId);
      contact.setName(contacts);
      contact.setSex("2");
      contact.setPhone(mobile);
      contact.setCreateTime(System.currentTimeMillis());
      contact.setSupplier(supplier);
      contact.setSupplierId(supplier.getId());
      contact.setState(Constants.STATE_OK);
      contact.setSupplierInGroupId(supplierInGroupId);
      contactRepository.save(contact);
    }
  }

  private void addFinancialIfNotExist(EntryRegistrationLandingMerchant landingMerchant,
      Supplier supplier, String supplierInGroupId) {
    List<Financial> financials =
        financialRepository.findAllBySupplierInGroupIdAndState(supplierInGroupId,
            Constants.STATE_OK);
    Financial financialEntry;
    financialEntry = CollUtil.emptyIfNull(financials).stream().filter(
            financial -> Objects.equals(financial.getBankNum(), landingMerchant.getBankAccountNumber()))
        .findFirst().orElse(null);
    if (financialEntry == null) {
      financialEntry = new Financial();
    }
    FinancialDTO financialInfo = new FinancialDTO();
    financialInfo.setBankNum(landingMerchant.getBankAccountNumber());
    financialInfo.setBankName(landingMerchant.getBankName());
    financialInfo.setBankAccount(landingMerchant.getAccountName());
    financialInfo.setBankAddress(landingMerchant.getBankAddress());
    financialInfo.setBankCode(landingMerchant.getBankCode());
    financialInfo.setTaxNo(landingMerchant.getUscc());
    financialInfo.setInvoiceType(landingMerchant.getInvoiceType());
    financialInfo.setTaxRate(landingMerchant.getTaxRate().stripTrailingZeros().toPlainString());
    financialInfo.setSettleCurrency(SettleCurrency.PRE001.getKey());
    //    financialInfo.setSwiftCode();
    //    financialInfo.setAccountUrl("https://example.com/financial_statement.pdf");
    Financial financial = financialInfo.buildFinancial(financialInfo, supplier);
    financial.setId(financialEntry.getId());
    financial.setSupplierInGroupId(supplierInGroupId);
    financialRepository.save(financial);
  }
}
