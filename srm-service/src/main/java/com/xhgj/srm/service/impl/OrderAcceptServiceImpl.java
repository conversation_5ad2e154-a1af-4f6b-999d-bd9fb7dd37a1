package com.xhgj.srm.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.StrUtil;
import com.xhgj.srm.common.Constants_order;
import com.xhgj.srm.jpa.entity.OrderAccept;
import com.xhgj.srm.jpa.repository.OrderAcceptRepository;
import com.xhgj.srm.service.OrderAcceptService;
import com.xhiot.boot.core.common.exception.CheckException;
import com.xhiot.boot.framework.jpa.repository.BootBaseRepository;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @since 2023-02-03 15:20
 */
@Service
public class OrderAcceptServiceImpl implements OrderAcceptService {

  @Autowired private OrderAcceptRepository repository;

  @Override
  public BootBaseRepository<OrderAccept, String> getRepository() {
    return repository;
  }

  @Override
  public List<OrderAccept> getByOrderId(String orderId) {
    Assert.notBlank(orderId);
    return repository.getByOrderIdOrderByCreateTimeDesc(orderId);
  }

  @Override
  public long countByOrderId(String orderId) {
    return repository.countByOrderId(orderId);
  }

  @Override
  public void updateAuditStateByOrderId(String orderId, String userId, String auditState,
      String groundsForRejection) {
    List<OrderAccept> orderAccepts = getByOrderId(orderId);
    if (CollUtil.isEmpty(orderAccepts)) {
      throw new CheckException("orderId错误，或者找不到对应的签收信息");
    }
    for (OrderAccept orderAccept : CollUtil.emptyIfNull(orderAccepts)) {
      orderAccept.setAuditStatus(auditState);
      orderAccept.setGroundsForRejection(groundsForRejection);
      orderAccept.setAuditMan(userId);
      save(orderAccept);
    }
  }

  @Override
  public Optional<String> getOperatorManId(String orderId) {
    List<OrderAccept> orderAccepts = getByOrderId(orderId);
    if (CollUtil.isEmpty(orderAccepts)) {
      throw new CheckException("orderId错误，或者找不到对应的签收信息");
    }
    OrderAccept orderAccept = orderAccepts.get(0);
    String fistUploadMan = orderAccept.getFistUploadMan();
    if (StrUtil.isBlank(fistUploadMan)) {
      return Optional.empty();
    }
    return Optional.of(fistUploadMan);
  }

  @Override
  public String getAcceptState(String orderId) {
    Assert.notBlank(orderId);
    List<OrderAccept> orderAccepts = getByOrderId(orderId);
    if (CollUtil.isEmpty(orderAccepts)) {
      return Constants_order.ORDER_ACCEPT_PENDING_UPLOADING;
    }
    OrderAccept orderAccept = orderAccepts.get(0);
    if (StrUtil.isBlank(orderAccept.getAuditStatus())) {
      return Constants_order.ORDER_ACCEPT_PENDING_UPLOADING;
    }
    return orderAccept.getAuditStatus();
  }

  @Override
  public Optional<OrderAccept> getFirstByOrderId(String orderId) {
    Assert.notBlank(orderId);
    return repository.getFirstByOrderId(orderId);
  }

  @Override
  public List<String> getFilesByOrderId(String orderId) {
    Assert.notBlank(orderId);
    List<OrderAccept> orderAccepts = repository.getByOrderIdOrderByCreateTimeDesc(orderId);
    if (CollUtil.isEmpty(orderAccepts)) {
      return new ArrayList<>();
    }
    ArrayList<String> result = new ArrayList<>();
    for (OrderAccept orderAccept : orderAccepts) {
      String fileIds = orderAccept.getFileIds();
      if (StrUtil.isNotBlank(fileIds)) {
        List<String> split = StrUtil.split(fileIds, ",", -1, true, true);
        result.addAll(split);
      }
    }
    return result;
  }
}
