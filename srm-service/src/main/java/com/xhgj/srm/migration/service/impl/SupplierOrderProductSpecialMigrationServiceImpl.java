package com.xhgj.srm.migration.service.impl;/**
 * @since 2025/5/30 16:17
 */

import com.xhgj.srm.common.Constants;
import com.xhgj.srm.common.enums.supplierorder.SupplierOrderFormStatus;
import com.xhgj.srm.common.enums.supplierorder.SupplierOrderFormType;
import com.xhgj.srm.factory.MapStructFactory;
import com.xhgj.srm.jpa.entity.v2.SupplierOrderDetailV2;
import com.xhgj.srm.jpa.entity.v2.SupplierOrderToFormV2;
import com.xhgj.srm.jpa.entity.v2.migration.MigrationRecord;
import com.xhgj.srm.jpa.repository.MigrationRecordRepository;
import com.xhgj.srm.migration.service.MigrationService;
import com.xhgj.srm.v2.repository.SupplierOrderDetailV2Repository;
import com.xhgj.srm.v2.repository.SupplierOrderToFormV2Repository;
import com.xhiot.boot.core.common.exception.CheckException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 *<AUTHOR>
 *@date 2025/5/30 16:17:10
 *@description
 */
@Service
@Slf4j
public class SupplierOrderProductSpecialMigrationServiceImpl implements MigrationService {
  public static final String TABLE = "t_supplier_order_product_special";
  @Resource
  private MigrationRecordRepository migrationRecordRepository;
  // ------v1--------
  // -----v2--------
  @Resource
  private SupplierOrderDetailV2Repository supplierOrderDetailV2Repository;
  @Resource
  private SupplierOrderToFormV2Repository supplierOrderToFormV2Repository;

  @Override
  public String migrateTable(String originId, String relationId, String batchNo, Map<String, Object> ext) {
    // 1.根据originId查询v2版本
    SupplierOrderDetailV2 detailOrigin = supplierOrderDetailV2Repository.findById(originId)
        .orElseThrow(() -> new CheckException("订单明细不存在"));
    SupplierOrderToFormV2 formOrigin =
        supplierOrderToFormV2Repository.findById(detailOrigin.getOrderToFormId())
            .orElseThrow(() -> new CheckException("订单表单不存在"));
    SupplierOrderDetailV2 newDetail =
        MapStructFactory.INSTANCE.toSupplierOrderDetailV2(detailOrigin);
    // 2.其他规则
    // 申请入库数量 -> 入库数量
    newDetail.setNum(detailOrigin.getStockInputQty());
    // 已入库数量 -> 申请入库数量
    newDetail.setStockInputQty(detailOrigin.getNum());
    // 取消数量 -> 0
    newDetail.setCancelQty(BigDecimal.ZERO);
    // 如果冲销完成了则需要将取消入库数量修改为入库数量
    if (SupplierOrderFormStatus.REVERSAL.getKey().equals(formOrigin.getStatus())) {
      newDetail.setCancelQty(detailOrigin.getStockInputQty());
    }
    // 质检数量 -> 无需质检
    newDetail.setInspectQty(null);
    // 3.save
    supplierOrderDetailV2Repository.saveAndFlush(newDetail);
    this.recordMigration(null, newDetail.getId(), batchNo, migrationRecordRepository);
    // 4.更新detailOrigin中的相关关联id
    detailOrigin.setInWareHouseApplyId(relationId);
    detailOrigin.setInWareHouseApplyName("WER2505010001-" + newDetail.getIndex());
    supplierOrderDetailV2Repository.saveAndFlush(detailOrigin);
    return newDetail.getId();
  }


  @Override
  public void refreshLinkTable(String originId, String newId) {
    // todo
  }

  @Override
  public void rollback(MigrationRecord migrationRecord) {
    // 查询v2版本
    SupplierOrderDetailV2 supplierOrderDetailV2 = supplierOrderDetailV2Repository.findById(migrationRecord.getNewId())
        .orElseThrow(() -> new CheckException("订单明细不存在"));
    // 删除v2版本
    supplierOrderDetailV2.setState(Constants.STATE_DELETE);
    supplierOrderDetailV2Repository.saveAndFlush(supplierOrderDetailV2);
    // 恢复相关关联关系
    this.refreshLinkTableRollback(migrationRecord.getOriginId(), migrationRecord.getNewId());
    migrationRecord.setState(Constants.STATE_DELETE);
    migrationRecordRepository.saveAndFlush(migrationRecord);
  }

  @Override
  public void refreshLinkTableRollback(String originId, String newId) {
    // todo
  }

  @Override
  public String getTableName() {
    return TABLE;
  }

  @Override
  public List<String> getOriginIds(String relationId) {
    // 查询所有入库单
    List<SupplierOrderToFormV2> supplierOrderToFormV2s =
        supplierOrderToFormV2Repository.getAllByTypeAndSupplierOrderIdAndStateOrderByTimeAsc(
            SupplierOrderFormType.WAREHOUSING.getKey(), relationId, Constants.STATE_OK);
    return supplierOrderToFormV2s.stream().map(SupplierOrderToFormV2::getId).collect(Collectors.toList());
  }
}
