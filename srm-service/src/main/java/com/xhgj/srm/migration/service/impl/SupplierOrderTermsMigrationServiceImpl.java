package com.xhgj.srm.migration.service.impl;/**
 * @since 2025/5/30 16:17
 */

import com.xhgj.srm.common.Constants;
import com.xhgj.srm.factory.MapStructFactory;
import com.xhgj.srm.jpa.entity.PurchaseOrderPaymentTerms;
import com.xhgj.srm.jpa.entity.v2.PurchaseOrderPaymentTermsV2;
import com.xhgj.srm.jpa.entity.v2.migration.MigrationRecord;
import com.xhgj.srm.jpa.repository.MigrationRecordRepository;
import com.xhgj.srm.jpa.repository.PurchaseOrderPaymentTermsRepository;
import com.xhgj.srm.migration.service.MigrationService;
import com.xhgj.srm.v2.repository.PurchaseOrderPaymentTermsV2Repository;
import com.xhiot.boot.core.common.exception.CheckException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 *<AUTHOR>
 *@date 2025/5/30 16:17:10
 *@description
 */
@Service
@Slf4j
public class SupplierOrderTermsMigrationServiceImpl implements MigrationService {
  public static final String TABLE = "t_purchase_order_payment_terms";
  @Resource
  private MigrationRecordRepository migrationRecordRepository;
  // ------v1--------
  @Resource
  private PurchaseOrderPaymentTermsRepository purchaseOrderPaymentTermsRepository;
  // -----v2--------
  @Resource
  private PurchaseOrderPaymentTermsV2Repository purchaseOrderPaymentTermsV2Repository;

  @Override
  public String migrateTable(String originId, String relationId, String batchNo,
      Map<String, Object> ext) {
    // 1.判断v1版本是否存在
    PurchaseOrderPaymentTerms purchaseOrderPaymentTerms = purchaseOrderPaymentTermsRepository.findById(originId)
        .orElseThrow(() -> new CheckException("订单付款条件不存在"));
    // 2.迁移v1版本到v2版本
    PurchaseOrderPaymentTermsV2 purchaseOrderPaymentTermsV2 = MapStructFactory.INSTANCE.toPurchaseOrderPaymentTermsV2(purchaseOrderPaymentTerms);
    // 3.他规则
    // 4.save
    purchaseOrderPaymentTermsV2Repository.saveAndFlush(purchaseOrderPaymentTermsV2);
    purchaseOrderPaymentTerms.setState(Constants.STATE_DELETE);
    purchaseOrderPaymentTermsRepository.saveAndFlush(purchaseOrderPaymentTerms);
    // 5.记录日志
    this.recordMigration(originId, purchaseOrderPaymentTermsV2.getId(), batchNo, migrationRecordRepository);
    return purchaseOrderPaymentTermsV2.getId();
  }

  @Override
  public List<String> getOriginIds(String relationId) {
    List<PurchaseOrderPaymentTerms> purchaseOrderPaymentTerms =
        purchaseOrderPaymentTermsRepository.findAllByPurchaseOrderIdAndState(relationId,
            Constants.STATE_OK);
    return purchaseOrderPaymentTerms.stream().map(PurchaseOrderPaymentTerms::getId)
        .collect(Collectors.toList());
  }

  @Override
  public void refreshLinkTable(String originId, String newId) {
    // do nothing
  }

  @Override
  public void rollback(MigrationRecord migrationRecord) {
    // 查询v2版本
    PurchaseOrderPaymentTermsV2 purchaseOrderPaymentTermsV2 = purchaseOrderPaymentTermsV2Repository.findById(migrationRecord.getNewId())
        .orElseThrow(() -> new CheckException("订单付款条件不存在"));
    // 删除v2版本
    purchaseOrderPaymentTermsV2.setState(Constants.STATE_DELETE);
    purchaseOrderPaymentTermsV2Repository.saveAndFlush(purchaseOrderPaymentTermsV2);
    // 恢复v1版本
    PurchaseOrderPaymentTerms purchaseOrderPaymentTerms = purchaseOrderPaymentTermsRepository.findById(migrationRecord.getOriginId())
        .orElseThrow(() -> new CheckException("订单付款条件不存在"));
    purchaseOrderPaymentTerms.setState(Constants.STATE_OK);
    purchaseOrderPaymentTermsRepository.saveAndFlush(purchaseOrderPaymentTerms);
    // 恢复相关关联关系
    this.refreshLinkTableRollback(migrationRecord.getOriginId(), migrationRecord.getNewId());
    migrationRecord.setState(Constants.STATE_DELETE);
    migrationRecordRepository.saveAndFlush(migrationRecord);
  }

  @Override
  public void refreshLinkTableRollback(String originId, String newId) {
    // do nothing
  }

  @Override
  public String getTableName() {
    return TABLE;
  }
}
