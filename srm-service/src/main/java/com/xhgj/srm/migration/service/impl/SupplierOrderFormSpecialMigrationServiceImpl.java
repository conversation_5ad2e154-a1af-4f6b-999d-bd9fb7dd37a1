package com.xhgj.srm.migration.service.impl;/**
 * @since 2025/5/30 16:17
 */

import cn.hutool.core.convert.Convert;
import com.xhgj.srm.common.Constants;
import com.xhgj.srm.common.enums.supplierorder.SupplierOrderFormCallStatus;
import com.xhgj.srm.common.enums.supplierorder.SupplierOrderFormStatus;
import com.xhgj.srm.common.enums.supplierorder.SupplierOrderFormType;
import com.xhgj.srm.factory.MapStructFactory;
import com.xhgj.srm.jpa.entity.v2.SupplierOrderToFormV2;
import com.xhgj.srm.jpa.entity.v2.migration.MigrationRecord;
import com.xhgj.srm.jpa.repository.MigrationRecordRepository;
import com.xhgj.srm.migration.service.MigrationService;
import com.xhgj.srm.v2.constants.SupplierOrderFormSource;
import com.xhgj.srm.v2.repository.SupplierOrderToFormV2Repository;
import com.xhiot.boot.core.common.exception.CheckException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 *<AUTHOR>
 *@date 2025/5/30 16:17:10
 *@description
 */
@Service
@Slf4j
public class SupplierOrderFormSpecialMigrationServiceImpl implements MigrationService {
  public static final String TABLE = "t_supplier_order_to_form_special";
  @Resource
  private MigrationRecordRepository migrationRecordRepository;
  // ------v1--------
  // -----v2--------
  @Resource
  private SupplierOrderToFormV2Repository supplierOrderToFormV2Repository;

  @Override
  public String migrateTable(String originId, String relationId, String batchNo, Map<String, Object> ext) {
    // 1.根据originId查询v2版本
    SupplierOrderToFormV2 supplierOrderToFormV2 = supplierOrderToFormV2Repository.findById(originId)
        .orElseThrow(() -> new CheckException("未找到订单表单"));
    SupplierOrderToFormV2 newSupplierOrderToFormV2 =
        MapStructFactory.INSTANCE.toSupplierOrderToFormV2(supplierOrderToFormV2);
    newSupplierOrderToFormV2.setType(SupplierOrderFormType.DELIVER.getType());
    newSupplierOrderToFormV2.setReviewStatus(null);
    // 2.其他规则
    // 退库单单号固定前面几位 WER250501，后面3位根据补位
    Integer count = Convert.toInt(ext.get(SupplierOrderFormType.DELIVER.getType()));
    String formCode = "WER250501" + String.format("%03d", count);
    ext.put("formCode", formCode);
    newSupplierOrderToFormV2.setFormCode(formCode);
    // 入库单的来源 SAP OR SRM
    newSupplierOrderToFormV2.setSource(supplierOrderToFormV2.getSource());
    // 是否需要质检 -> 否
    newSupplierOrderToFormV2.setQualityCheck(false);
    // 物流状态 -> 已签收
    newSupplierOrderToFormV2.setStatus(SupplierOrderFormStatus.RECEIPT.getKey());
    newSupplierOrderToFormV2.setSupplierOrderId(relationId);
    // 推送按钮 来源为SAP则显示无需推送 为SRM则展示推送成功
    if (SupplierOrderFormSource.SAP.equals(supplierOrderToFormV2.getSource())) {
      newSupplierOrderToFormV2.setCallStatus(SupplierOrderFormCallStatus.NO_CALL.getStatus());
    } else {
      newSupplierOrderToFormV2.setCallStatus(SupplierOrderFormCallStatus.CALL_SUCCESS.getStatus());
      newSupplierOrderToFormV2.setCallTime(newSupplierOrderToFormV2.getCreateTime());
    }
    // 3.save
    supplierOrderToFormV2Repository.saveAndFlush(newSupplierOrderToFormV2);
    this.recordMigration(null, newSupplierOrderToFormV2.getId(), batchNo, migrationRecordRepository);
    // 生成特殊form的相关物料明细 -- 并更新
    return newSupplierOrderToFormV2.getId();
  }


  @Override
  public void refreshLinkTable(String originId, String newId) {
    // do nothing
  }

  @Override
  public void rollback(MigrationRecord migrationRecord) {
    // 查询v2版本
    SupplierOrderToFormV2 supplierOrderToFormV2 = supplierOrderToFormV2Repository.findById(migrationRecord.getNewId())
        .orElseThrow(() -> new CheckException("未找到订单表单"));
    // 删除v2版本
    supplierOrderToFormV2.setState(Constants.STATE_DELETE);
    supplierOrderToFormV2Repository.saveAndFlush(supplierOrderToFormV2);
    // 恢复相关关联关系
    this.refreshLinkTableRollback(migrationRecord.getOriginId(), migrationRecord.getNewId());
    migrationRecord.setState(Constants.STATE_DELETE);
    migrationRecordRepository.saveAndFlush(migrationRecord);
  }

  @Override
  public void refreshLinkTableRollback(String originId, String newId) {
    // do nothing
  }

  @Override
  public String getTableName() {
    return TABLE;
  }

  @Override
  public List<String> getOriginIds(String relationId) {
    throw new UnsupportedOperationException("不支持此操作");
  }
}
