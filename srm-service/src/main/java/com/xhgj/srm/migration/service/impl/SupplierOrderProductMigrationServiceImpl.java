package com.xhgj.srm.migration.service.impl;/**
 * @since 2025/5/30 16:17
 */

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.TypeReference;
import com.xhgj.srm.common.Constants;
import com.xhgj.srm.common.enums.BootDictEnum;
import com.xhgj.srm.common.enums.SimpleBooleanEnum;
import com.xhgj.srm.dto.SrmProductInfoDTO;
import com.xhgj.srm.jpa.entity.SupplierOrderDetail;
import com.xhgj.srm.jpa.entity.SupplierOrderProduct;
import com.xhgj.srm.jpa.entity.v2.SupplierOrderDetailV2;
import com.xhgj.srm.jpa.entity.v2.SupplierOrderProductV2;
import com.xhgj.srm.jpa.entity.v2.migration.MigrationRecord;
import com.xhgj.srm.jpa.repository.MigrationRecordRepository;
import com.xhgj.srm.jpa.repository.SupplierOrderDetailRepository;
import com.xhgj.srm.jpa.repository.SupplierOrderProductRepository;
import com.xhgj.srm.factory.MapStructFactory;
import com.xhgj.srm.jpa.util.BootDictUtil;
import com.xhgj.srm.migration.service.MigrationService;
import com.xhgj.srm.request.service.third.mpm.MPMService;
import com.xhgj.srm.v2.repository.SupplierOrderDetailV2Repository;
import com.xhgj.srm.v2.repository.SupplierOrderProductV2Repository;
import com.xhiot.boot.core.common.exception.CheckException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 *<AUTHOR>
 *@date 2025/5/30 16:17:10
 *@description
 */
@Service
@Slf4j
public class SupplierOrderProductMigrationServiceImpl implements MigrationService {
  public static final String TABLE = "t_supplier_order_product";
  @Resource
  private MigrationRecordRepository migrationRecordRepository;
  @Resource
  private MPMService mpmService;
  // ------v1--------
  @Resource
  private SupplierOrderProductRepository supplierOrderProductRepository;
  @Resource
  private SupplierOrderDetailRepository supplierOrderDetailRepository;
  // -----v2--------
  @Resource
  private SupplierOrderProductV2Repository supplierOrderProductV2Repository;
  @Resource
  private SupplierOrderDetailV2Repository supplierOrderDetailV2Repository;

  @Override
  public String migrateTable(String originId, String relationId, String batchNo, Map<String, Object> ext) {
    // 1.判断V1版本是否存在
    SupplierOrderProduct supplierOrderProduct = supplierOrderProductRepository.findById(originId)
        .orElseThrow(() -> new CheckException("订单物料不存在"));
    // 2.迁移v1版本到v2版本
    SupplierOrderProductV2 supplierOrderProductV2 =
        MapStructFactory.INSTANCE.toSupplierOrderProductV2(supplierOrderProduct);
    // 3.其他规则
    // #rule 根据物料编码调用mpm接口查询 展示300货物类或者400服务类
    String groupCode = Convert.toStr(ext.get("groupCode"));
    List<SrmProductInfoDTO> productInfoListByCodes = mpmService.getProductInfoListByCodes(
        Collections.singletonList(supplierOrderProductV2.getCode()), groupCode,
        new TypeReference<List<SrmProductInfoDTO>>() {});
    if (CollUtil.isNotEmpty(productInfoListByCodes)) {
      SrmProductInfoDTO srmProductInfoDTO = productInfoListByCodes.get(0);
      String productType = srmProductInfoDTO.getProductType();
      supplierOrderProductV2.setItemGroupCode(productType);
      String productTypeName = BootDictUtil.bootDictService.getDictValueByKey(BootDictEnum.ITEM_GROUP.getKey(), productType);
      supplierOrderProductV2.setItemGroup(productTypeName);
      String isQualityInspection = srmProductInfoDTO.getIsQualityInspection();
      supplierOrderProductV2.setQualityCheck(StrUtil.equals(isQualityInspection, SimpleBooleanEnum.YES.getKey()));
    }
    // 4.save
    supplierOrderProductV2Repository.saveAndFlush(supplierOrderProductV2);
    // 5.记录日志
    this.recordMigration(originId, supplierOrderProductV2.getId(), batchNo, migrationRecordRepository);
    return supplierOrderProductV2.getId();
  }

  @Override
  public void refreshLinkTable(String originId, String newId) {
    // 查询supplierOrderDetail有关联的
    List<SupplierOrderDetailV2> detailV2List =
        supplierOrderDetailV2Repository.findAllByOrderProductIdAndState(originId,
            Constants.STATE_OK);
    for (SupplierOrderDetailV2 detailV2 : detailV2List) {
      detailV2.setOrderProductId(newId);
      supplierOrderDetailV2Repository.saveAndFlush(detailV2);
    }
  }

  @Override
  public void rollback(MigrationRecord migrationRecord) {
    // 删除v2版本
    supplierOrderProductV2Repository.deleteById(migrationRecord.getNewId());
    // 恢复相关关联关系
    this.refreshLinkTableRollback(migrationRecord.getOriginId(), migrationRecord.getNewId());
    migrationRecord.setState(Constants.STATE_DELETE);
    migrationRecordRepository.saveAndFlush(migrationRecord);
  }

  @Override
  public void refreshLinkTableRollback(String originId, String newId) {
    // do nothing
  }

  @Override
  public String getTableName() {
    return TABLE;
  }

  @Override
  public List<String> getOriginIds(String relationId) {
    List<SupplierOrderDetail> supplierOrderDetails =
        supplierOrderDetailRepository.findAllById(Collections.singletonList(relationId));
    return supplierOrderDetails.stream().map(SupplierOrderDetail::getOrderProductId)
        .collect(Collectors.toList());
  }
}
