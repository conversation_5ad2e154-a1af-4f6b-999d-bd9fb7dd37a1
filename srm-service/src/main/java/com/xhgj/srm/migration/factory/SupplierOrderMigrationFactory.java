package com.xhgj.srm.migration.factory;/**
 * @since 2025/5/30 17:03
 */

import com.xhgj.srm.migration.service.MigrationService;
import com.xhgj.srm.migration.service.impl.SupplierOrderDetailMigrationServiceImpl;
import com.xhgj.srm.migration.service.impl.SupplierOrderDetailSpecialMigrationServiceImpl;
import com.xhgj.srm.migration.service.impl.SupplierOrderFormMigrationServiceImpl;
import com.xhgj.srm.migration.service.impl.SupplierOrderFormSpecialMigrationServiceImpl;
import com.xhgj.srm.migration.service.impl.SupplierOrderMigrationServiceImpl;
import com.xhgj.srm.migration.service.impl.SupplierOrderProductMigrationServiceImpl;
import com.xhgj.srm.migration.service.impl.SupplierOrderTermsMigrationServiceImpl;
import org.springframework.stereotype.Component;
import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

/**
 *<AUTHOR>
 *@date 2025/5/30 17:03:00
 *@description
 */
@Component
public class SupplierOrderMigrationFactory {
  Map<String, MigrationService> migrationServiceMap = new HashMap<>();
  @Resource
  private SupplierOrderDetailMigrationServiceImpl supplierOrderDetailMigrationService;
  @Resource
  private SupplierOrderProductMigrationServiceImpl supplierOrderProductMigrationService;
  @Resource
  private SupplierOrderFormMigrationServiceImpl supplierOrderFormMigrationService;
  @Resource
  private SupplierOrderMigrationServiceImpl supplierOrderMigrationService;
  @Resource
  private SupplierOrderTermsMigrationServiceImpl supplierOrderTermsMigrationService;
  @Resource
  private SupplierOrderFormSpecialMigrationServiceImpl supplierOrderFormSpecialMigrationService;
  @Resource
  private SupplierOrderDetailSpecialMigrationServiceImpl supplierOrderDetailSpecialMigrationService;

  public MigrationService getMigrationService(String tableName) {
    return migrationServiceMap.get(tableName);
  }

  @PostConstruct
  public void init() {
    migrationServiceMap.put(SupplierOrderDetailMigrationServiceImpl.TABLE, supplierOrderDetailMigrationService);
    migrationServiceMap.put(SupplierOrderProductMigrationServiceImpl.TABLE, supplierOrderProductMigrationService);
    migrationServiceMap.put(SupplierOrderFormMigrationServiceImpl.TABLE, supplierOrderFormMigrationService);
    migrationServiceMap.put(SupplierOrderMigrationServiceImpl.TABLE, supplierOrderMigrationService);
    migrationServiceMap.put(SupplierOrderTermsMigrationServiceImpl.TABLE, supplierOrderTermsMigrationService);
    migrationServiceMap.put(SupplierOrderFormSpecialMigrationServiceImpl.TABLE, supplierOrderFormSpecialMigrationService);
    migrationServiceMap.put(SupplierOrderDetailSpecialMigrationServiceImpl.TABLE, supplierOrderDetailSpecialMigrationService);
  }

}
