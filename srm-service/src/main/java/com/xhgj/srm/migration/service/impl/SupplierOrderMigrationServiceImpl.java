package com.xhgj.srm.migration.service.impl;/**
 * @since 2025/5/30 16:17
 */

import com.xhgj.srm.common.Constants;
import com.xhgj.srm.common.Constants_FileRelationType;
import com.xhgj.srm.common.enums.PurchaseOrderTypeEnum;
import com.xhgj.srm.common.enums.VerifyConfigTypeEnum;
import com.xhgj.srm.common.enums.supplierorder.SupplierOrderFormStatus;
import com.xhgj.srm.common.enums.supplierorder.SupplierOrderFormType;
import com.xhgj.srm.factory.MapStructFactory;
import com.xhgj.srm.jpa.entity.InputInvoiceOrderSplit;
import com.xhgj.srm.jpa.entity.ReturnExchangeOrder;
import com.xhgj.srm.jpa.entity.SupplierOrder;
import com.xhgj.srm.jpa.entity.SupplierOrderToForm;
import com.xhgj.srm.jpa.entity.VerifyConfig;
import com.xhgj.srm.jpa.entity.v2.SupplierOrderV2;
import com.xhgj.srm.jpa.entity.v2.migration.MigrationRecord;
import com.xhgj.srm.jpa.repository.FileRepository;
import com.xhgj.srm.jpa.repository.MigrationRecordRepository;
import com.xhgj.srm.jpa.repository.OrderInvoiceRelationSplitRepository;
import com.xhgj.srm.jpa.repository.ReturnExchangeOrderRepository;
import com.xhgj.srm.jpa.repository.SupplierOrderRepository;
import com.xhgj.srm.jpa.repository.SupplierOrderToFormRepository;
import com.xhgj.srm.jpa.repository.VerifyConfigRepository;
import com.xhgj.srm.migration.service.MigrationService;
import com.xhgj.srm.v2.repository.SupplierOrderV2Repository;
import com.xhiot.boot.core.common.exception.CheckException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 *<AUTHOR>
 *@date 2025/5/30 16:17:10
 *@description
 */
@Service
@Slf4j
public class SupplierOrderMigrationServiceImpl implements MigrationService {
  public static final String TABLE = "t_supplier_order";
  @Resource
  private MigrationRecordRepository migrationRecordRepository;
  @Resource
  private FileRepository fileRepository;
  @Resource
  private OrderInvoiceRelationSplitRepository orderInvoiceRelationSplitRepository;
  @Resource
  private ReturnExchangeOrderRepository returnExchangeOrderRepository;
  @Resource
  private VerifyConfigRepository verifyConfigRepository;

  // ------v1--------
  @Resource
  private SupplierOrderRepository supplierOrderRepository;
  @Resource
  private SupplierOrderToFormRepository supplierOrderToFormRepository;
  // -----v2--------
  @Resource
  private SupplierOrderV2Repository supplierOrderV2Repository;

  @Override
  public String migrateTable(String originId, String relationId, String batchNo,
      Map<String, Object> ext) {
    // 1.判断v1版本是否存在
    SupplierOrder supplierOrder = supplierOrderRepository.findById(originId)
        .orElseThrow(() -> new CheckException("订单不存在"));
    // 判断此组织是否开启V2权限
    // 获取第一个的组织
    VerifyConfig verifyConfig = verifyConfigRepository.findFirstByConfigTypeAndEnable(
        VerifyConfigTypeEnum.WORKBENCH_TWO_ZERO_AVAILABLE_ORG.getCode(), Boolean.TRUE);
    List<String> organizationRoleList = verifyConfig.getOrganizationRoleList();
    if (!organizationRoleList.contains(supplierOrder.getGroupCode())) {
      throw new CheckException("组织未开启V2权限");
    }
    // 2.迁移v1版本到v2版本
    SupplierOrderV2 supplierOrderV2 = MapStructFactory.INSTANCE.toSupplierOrderV2(supplierOrder);
    // 3.其他规则
    // #rule 1.0采购订单赠品标识的赋值赠品采购订单，有自采标识的赋值自采采购订单
    if (Boolean.TRUE.equals(supplierOrder.getFreeState())) {
      supplierOrderV2.setOrderType(PurchaseOrderTypeEnum.GIFT.getKey());
    }
    if (Boolean.TRUE.equals(supplierOrder.getSelfState())) {
      supplierOrderV2.setOrderType(PurchaseOrderTypeEnum.SELF_PURCHASE.getKey());
    }
    // #rule 1.0采购订单中，如果存在冲销中的入库单和退库单则不允许迁移
    List<SupplierOrderToForm> allByTypeAndStateAndStatus =
        supplierOrderToFormRepository.getAllByTypeAndStateAndStatus(
            SupplierOrderFormType.WAREHOUSING.getType(),
            SupplierOrderFormStatus.REVERSAL_IN_PROGRESS.getStatus(), Constants.STATE_OK);
    if (!allByTypeAndStateAndStatus.isEmpty()) {
      throw new CheckException("存在冲销中的入库单，无法迁移");
    }
    allByTypeAndStateAndStatus =
        supplierOrderToFormRepository.getAllByTypeAndStateAndStatus(
            SupplierOrderFormType.RETURN.getType(),
            SupplierOrderFormStatus.REVERSAL_IN_PROGRESS.getStatus(), Constants.STATE_OK);
    if (!allByTypeAndStateAndStatus.isEmpty()) {
      throw new CheckException("存在冲销中的退库单，无法迁移");
    }
    // 4.save
    supplierOrderV2Repository.saveAndFlush(supplierOrderV2);
    supplierOrder.setState(Constants.STATE_DELETE);
    supplierOrderRepository.saveAndFlush(supplierOrder);
    // 5.记录日志
    this.recordMigration(originId, supplierOrderV2.getId(), batchNo, migrationRecordRepository);
    return supplierOrderV2.getId();
  }

  @Override
  public List<String> getOriginIds(String relationId) {
    throw new UnsupportedOperationException("不支持此操作");
  }

  @Override
  public void refreshLinkTable(String originId, String newId) {
    // 订单附件关联关系修改
    fileRepository.findAllByRelationIdAndRelationTypeAndState(originId, Constants_FileRelationType.ORDER_ANNEX,
        Constants.STATE_OK).ifPresent(files -> {
      files.forEach(file -> {
        file.setRelationId(newId);
        fileRepository.save(file);
      });
    });
    // 采购合同关联关系修改
    fileRepository.findAllByRelationIdAndRelationTypeAndState(originId, Constants_FileRelationType.ORDER_CONTRACT,
        Constants.STATE_OK).ifPresent(files -> {
      files.forEach(file -> {
        file.setRelationId(newId);
        fileRepository.save(file);
      });
    });
    // 进项票关联采购订单id
    List<InputInvoiceOrderSplit> allBySupplierOrderId =
        orderInvoiceRelationSplitRepository.findAllBySupplierOrderId(originId);
    allBySupplierOrderId.forEach(item -> {
      item.setSupplierOrderId(newId);
      orderInvoiceRelationSplitRepository.save(item);
    });
    // 退换货订单关联
    ReturnExchangeOrder firstBySupplierOrderIdAndState =
        returnExchangeOrderRepository.findFirstBySupplierOrderIdAndState(originId,
            Constants.STATE_OK);
    if (firstBySupplierOrderIdAndState != null) {
      firstBySupplierOrderIdAndState.setSupplierOrderId(newId);
      returnExchangeOrderRepository.save(firstBySupplierOrderIdAndState);
    }
  }

  @Override
  public void rollback(MigrationRecord migrationRecord) {
    // 查询v2版本
    SupplierOrderV2 supplierOrderV2 = supplierOrderV2Repository.findById(migrationRecord.getNewId())
        .orElseThrow(() -> new CheckException("订单不存在"));
    // 删除v2版本
    supplierOrderV2.setState(Constants.STATE_DELETE);
    supplierOrderV2Repository.saveAndFlush(supplierOrderV2);
    // 恢复v1版本
    SupplierOrder supplierOrder = supplierOrderRepository.findById(migrationRecord.getOriginId())
        .orElseThrow(() -> new CheckException("订单不存在"));
    supplierOrder.setState(Constants.STATE_OK);
    supplierOrderRepository.saveAndFlush(supplierOrder);
    // 恢复相关关联关系
    this.refreshLinkTableRollback(migrationRecord.getOriginId(), migrationRecord.getNewId());
    migrationRecord.setState(Constants.STATE_DELETE);
    migrationRecordRepository.saveAndFlush(migrationRecord);
  }

  @Override
  public void refreshLinkTableRollback(String originId, String newId) {
    // todo
    // 订单附件关联关系修改
    fileRepository.findAllByRelationIdAndRelationTypeAndState(newId, Constants_FileRelationType.ORDER_ANNEX,
        Constants.STATE_OK).ifPresent(files -> {
      files.forEach(file -> {
        file.setRelationId(originId);
        fileRepository.save(file);
      });
    });
    // 采购合同关联关系修改
    fileRepository.findAllByRelationIdAndRelationTypeAndState(newId, Constants_FileRelationType.ORDER_CONTRACT,
        Constants.STATE_OK).ifPresent(files -> {
      files.forEach(file -> {
        file.setRelationId(originId);
        fileRepository.save(file);
      });
    });
    // 进项票关联采购订单id
    List<InputInvoiceOrderSplit> allBySupplierOrderId =
        orderInvoiceRelationSplitRepository.findAllBySupplierOrderId(newId);
    allBySupplierOrderId.forEach(item -> {
      item.setSupplierOrderId(originId);
      orderInvoiceRelationSplitRepository.save(item);
    });
    // 退换货订单关联
    ReturnExchangeOrder firstBySupplierOrderIdAndState =
        returnExchangeOrderRepository.findFirstBySupplierOrderIdAndState(newId,
            Constants.STATE_OK);
    if (firstBySupplierOrderIdAndState != null) {
      firstBySupplierOrderIdAndState.setSupplierOrderId(originId);
      returnExchangeOrderRepository.save(firstBySupplierOrderIdAndState);
    }
  }

  @Override
  public String getTableName() {
    return TABLE;
  }
}
