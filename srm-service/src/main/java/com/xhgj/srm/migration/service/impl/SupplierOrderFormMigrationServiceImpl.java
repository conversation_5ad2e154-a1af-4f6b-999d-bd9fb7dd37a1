package com.xhgj.srm.migration.service.impl;/**
 * @since 2025/5/30 16:17
 */

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import com.xhgj.srm.common.Constants;
import com.xhgj.srm.common.enums.supplierorder.SupplierOrderFormCallStatus;
import com.xhgj.srm.common.enums.supplierorder.SupplierOrderFormReviewStatus;
import com.xhgj.srm.common.enums.supplierorder.SupplierOrderFormType;
import com.xhgj.srm.jpa.entity.SupplierOrderDetail;
import com.xhgj.srm.jpa.entity.SupplierOrderToForm;
import com.xhgj.srm.jpa.entity.v2.SupplierOrderToFormV2;
import com.xhgj.srm.jpa.entity.v2.migration.MigrationRecord;
import com.xhgj.srm.jpa.repository.MigrationRecordRepository;
import com.xhgj.srm.jpa.repository.SupplierOrderDetailRepository;
import com.xhgj.srm.jpa.repository.SupplierOrderToFormRepository;
import com.xhgj.srm.factory.MapStructFactory;
import com.xhgj.srm.migration.service.MigrationService;
import com.xhgj.srm.v2.constants.SupplierOrderFormSource;
import com.xhgj.srm.v2.repository.SupplierOrderToFormV2Repository;
import com.xhiot.boot.core.common.exception.CheckException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 *<AUTHOR>
 *@date 2025/5/30 16:17:10
 *@description
 */
@Service
@Slf4j
public class SupplierOrderFormMigrationServiceImpl implements MigrationService {
  public static final String TABLE = "t_supplier_order_to_form";
  @Resource
  private MigrationRecordRepository migrationRecordRepository;
  // ------v1--------
  @Resource
  private SupplierOrderToFormRepository supplierOrderToFormRepository;
  @Resource
  private SupplierOrderDetailRepository supplierOrderDetailRepository;
  // -----v2--------
  @Resource
  private SupplierOrderToFormV2Repository supplierOrderToFormV2Repository;

  @Override
  public String migrateTable(String originId, String relationId, String batchNo, Map<String, Object> ext) {
    // 1.判断V1版本是否存在
    SupplierOrderToForm supplierOrderToForm = supplierOrderToFormRepository.findById(originId)
        .orElseThrow(() -> new RuntimeException("未找到订单表单"));
    List<SupplierOrderDetail> supplierOrderDetails =
        supplierOrderDetailRepository.findByOrderToFormIdAndState(originId, Constants.STATE_OK);
    // 2.迁移V1版本到V2
    SupplierOrderToFormV2 supplierOrderToFormV2 = MapStructFactory.INSTANCE.toSupplierOrderToFormV2(supplierOrderToForm);
    // 3.其他规则
    // #rule 发货单-------------------------------------
    // 不进行迁移
    if (supplierOrderToFormV2.getType().equals(SupplierOrderFormType.DELIVER.getType())) {
      return null;
    }
    // #rule 入库单-------------------------------------
    if (supplierOrderToFormV2.getType().equals(SupplierOrderFormType.WAREHOUSING.getType())) {
      // 展示审批通过
      supplierOrderToFormV2.setReviewStatus(SupplierOrderFormReviewStatus.NORMAL.getCode());
      // 过账日期取创建时间
      supplierOrderToFormV2.setPostingDate(supplierOrderToFormV2.getCreateTime());
      // 推送按钮 来源为SAP则显示无需推送 为SRM则展示推送成功
      if (SupplierOrderFormSource.SAP.equals(supplierOrderToFormV2.getSource())) {
        supplierOrderToFormV2.setCallStatus(SupplierOrderFormCallStatus.NO_CALL.getStatus());
      } else {
        supplierOrderToFormV2.setCallStatus(SupplierOrderFormCallStatus.CALL_SUCCESS.getStatus());
        supplierOrderToFormV2.setCallTime(supplierOrderToFormV2.getCreateTime());
      }
      // 入库单号固定前面几位 GR250501，后面4位根据补位
      Integer count = Convert.toInt(ext.get(SupplierOrderFormType.WAREHOUSING.getType()));
      String formCode = "GR250501" + String.format("%04d", count);
      supplierOrderToFormV2.setFormCode(formCode);
      ext.put(SupplierOrderFormType.WAREHOUSING.getType(), count + 1);
    }
    // #rule 退库单-------------------------------------
    if (supplierOrderToFormV2.getType().equals(SupplierOrderFormType.RETURN.getType())) {
      // 展示审批通过
      supplierOrderToFormV2.setReviewStatus(SupplierOrderFormReviewStatus.NORMAL.getCode());
      // 退库时间取创建时间
      supplierOrderToFormV2.setPostingDate(supplierOrderToFormV2.getCreateTime());
      // 退库单单号固定前面几位 TK250501，后面4位根据补位
      Integer count = Convert.toInt(ext.get(SupplierOrderFormType.RETURN.getType()));
      String formCode = "TK250501" + String.format("%04d", count);
      supplierOrderToFormV2.setFormCode(formCode);
      ext.put(SupplierOrderFormType.RETURN.getType(), count + 1);
    }
    // #rule v2的新字段仓库从明细上取值
    if (CollUtil.isEmpty(supplierOrderDetails)) {
      SupplierOrderDetail supplierOrderDetail = supplierOrderDetails.get(0);
      supplierOrderToFormV2.setWarehouseName(supplierOrderDetail.getWarehouseName());
      supplierOrderToFormV2.setWarehouseCode(supplierOrderDetail.getWarehouse());
    }
    ext.put("type", supplierOrderToFormV2.getType());
    // 4.save
    supplierOrderToFormV2.setSupplierOrderId(relationId);
    supplierOrderToFormV2Repository.saveAndFlush(supplierOrderToFormV2);
    supplierOrderToForm.setState(Constants.STATE_DELETE);
    supplierOrderToFormRepository.saveAndFlush(supplierOrderToForm);
    // 5.记录日志
    this.recordMigration(originId, supplierOrderToFormV2.getId(), batchNo, migrationRecordRepository);
    return supplierOrderToFormV2.getId();
  }

  @Override
  public void refreshLinkTable(String originId, String newId) {
    // do nothing
  }

  @Override
  public void rollback(MigrationRecord migrationRecord) {
    // 查询v2版本
    SupplierOrderToFormV2 supplierOrderToFormV2 = supplierOrderToFormV2Repository.findById(migrationRecord.getNewId())
        .orElseThrow(() -> new CheckException("未找到订单表单"));
    // 删除v2版本
    supplierOrderToFormV2.setState(Constants.STATE_DELETE);
    supplierOrderToFormV2Repository.saveAndFlush(supplierOrderToFormV2);
    // 恢复v1版本
    SupplierOrderToForm supplierOrderToForm = supplierOrderToFormRepository.findById(migrationRecord.getOriginId())
        .orElseThrow(() -> new CheckException("未找到订单表单"));
    supplierOrderToForm.setState(Constants.STATE_OK);
    supplierOrderToFormRepository.saveAndFlush(supplierOrderToForm);
    // 恢复相关关联关系
    this.refreshLinkTableRollback(migrationRecord.getOriginId(), migrationRecord.getNewId());
    migrationRecord.setState(Constants.STATE_DELETE);
    migrationRecordRepository.saveAndFlush(migrationRecord);
  }

  @Override
  public void refreshLinkTableRollback(String originId, String newId) {
    // do nothing
  }

  @Override
  public String getTableName() {
    return TABLE;
  }

  @Override
  public List<String> getOriginIds(String relationId) {
    List<SupplierOrderToForm> supplierOrderToForms =
        supplierOrderToFormRepository.findBySupplierOrderIdAndState(relationId, Constants.STATE_OK);
    return supplierOrderToForms.stream().map(SupplierOrderToForm::getId).collect(Collectors.toList());
  }
}
