package com.xhgj.srm.aop;/**
 * @since 2025/5/26 16:37
 */

import com.xhgj.srm.common.enums.VoucherAccountPeriodEnum;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * <AUTHOR>
 */
@Component
public class ThreadLocalCacheInterceptor implements HandlerInterceptor {

  @Override
  public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) throws Exception {
    // todo 清理所有ThreadLocal缓存
    VoucherAccountPeriodEnum.clearCache();
  }

  public static void clearCache() {
    VoucherAccountPeriodEnum.clearCache();
  }
}
