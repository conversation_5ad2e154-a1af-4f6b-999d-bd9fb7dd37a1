package com.xhgj.srm.mission.common;

import java.util.HashMap;
import java.util.Map;
import lombok.Getter;

/**
 * <AUTHOR>
 * @since 2024/8/14 15:03
 */
@Getter
public enum MissionTypeEnum {
  /**
   * 旧数据处理
   */
  BATCH_TASK_OLD_DATA("1", "旧数据处理", MissionSourceEnum.BEFORE, DispatchModeEnum.FC),
  /** 导出物料明细 */
  BATCH_TASK_PRODUCT_DETAIL("35", "导出-物料明细", MissionSourceEnum.BEFORE, DispatchModeEnum.FC),
  /** 导出进项票列表 */
  BATCH_TASK_EXPORT_INPUT_INVOICE("47", "导出-进项票列表", MissionSourceEnum.AFTER, DispatchModeEnum.FC),

  /** 导出供应商订单 （暂时作废） */
  //  BATCH_TASK_EXPORT_SUPPLIER_ORDER("21", "导出-供应商订单", MissionSourceEnum.AFTER,
  // DispatchModeEnum.FC),
  /** 导出入库单信息 */
  BATCH_TASK_EXPORT_STORAGE_ORDER("51", "导出-入库单信息", MissionSourceEnum.AFTER, DispatchModeEnum.FC),
  /** 导出退库单信息 */
  BATCH_TASK_EXPORT_RETURN_STORAGE_ORDER(
      "52", "导出-退库单信息", MissionSourceEnum.AFTER, DispatchModeEnum.FC),
  /** 导出供应商账号信息 */
  BATCH_TASK_EXPORT_SUPPLIER_ACCOUNT_INFO(
      "28", "导出-供应商账号信息", MissionSourceEnum.AFTER, DispatchModeEnum.FC),
  /** 前台导出供应商订单 */
  BATCH_TASK_EXPORT_SUPPLIER_ORDER_SUPPLIER(
      "23", "前台导出-供应商订单", MissionSourceEnum.BEFORE, DispatchModeEnum.FC),
  /** 导出付款申请 */
  BATCH_TASK_EXPORT_PAYMENT_APPLICATION(
      "58", "导出-付款申请", MissionSourceEnum.AFTER, DispatchModeEnum.FC),
  /** 导出付款单 */
  BATCH_TASK_EXPORT_ORDER_PAYMENT(
      "36", "导出-付款单", MissionSourceEnum.AFTER, DispatchModeEnum.FC),
  /** 导出落地商订单发货明细 */
  BATCH_TASK_EXPORT_DELIVERY_DETAIL(
      "60", "导出-落地商订单发货明细", MissionSourceEnum.AFTER, DispatchModeEnum.FC),
  /** 导出落地商订单退货明细 */
  BATCH_TASK_EXPORT_RETURN_DETAIL(
      "61", "导出-落地商订单退货明细", MissionSourceEnum.AFTER, DispatchModeEnum.FC),
  /** 导出物料库存 */
  BATCH_TASK_EXPORT_PRODUCT_STOCK(
      "62", "导出-物料库存", MissionSourceEnum.BEFORE, DispatchModeEnum.FC),
  /** 导出采购申请单 */
  BATCH_TASK_EXPORT_PURCHASE_APPLY_FOR_ORDER(
      "42", "导出-采购申请单", MissionSourceEnum.AFTER, DispatchModeEnum.FC),
  /** 导出落地商订单 */
  BATCH_TASK_EXPORT_ORDER(
      "25", "导出-落地商订单", MissionSourceEnum.AFTER, DispatchModeEnum.FC),
  /** 同步客户平台列表 */
  BATCH_TASK_SYNC_CUSTOMER_PLATFORM_LIST(
      "63", "同步客户平台列表", MissionSourceEnum.AFTER, DispatchModeEnum.FC),
  /**
   * 供应商品牌导入批改
   */
  BATCH_TASK_IMPORT_SUPPLIER_BRAND("64", "供应商品牌导入批改", MissionSourceEnum.AFTER, DispatchModeEnum.FC),
  /**
   * 供应商类目导入批改
   */
  BATCH_TASK_IMPORT_SUPPLIER_CATEGORY("65", "供应商类目导入批改", MissionSourceEnum.AFTER, DispatchModeEnum.FC),
  /**
   * 供应商等级导入批改
   */
  BATCH_TASK_IMPORT_SUPPLIER_LEVEL("66", "供应商等级导入批改", MissionSourceEnum.AFTER, DispatchModeEnum.FC),
  /**
   * 库存列表导出
   */
  BATCH_TASK_EXPORT_INVENTORY_LIST("67", "库存列表导出", MissionSourceEnum.AFTER, DispatchModeEnum.FC),
  /**
   * 退换货订单导出
   */
  BATCH_TASK_EXPORT_RETURN_EXCHANGE("68", "导出-退货单订单", MissionSourceEnum.AFTER, DispatchModeEnum.FC),
  /** 同步库位管理列表 */
  BATCH_TASK_SYNC_INVENTORY_LOCATION_LIST("69", "同步库位管理列表", MissionSourceEnum.AFTER, DispatchModeEnum.FC),
  /**
   * 库存安全管理导出
   */
  BATCH_TASK_EXPORT_INVENTORY_SAFETY("70", "导出-库存安全管理", MissionSourceEnum.AFTER, DispatchModeEnum.FC),
  /**
   * 库存安全管理导入批改
   */
  BATCH_TASK_IMPORT_INVENTORY_SAFETY("71", "库存安全管理导入批改", MissionSourceEnum.AFTER, DispatchModeEnum.FC),
  /**
   * 组装拆卸单导出
   */
  BATCH_TASK_EXPORT_ASSEMBLY_DISASSEMBLY("72", "导出-组装拆卸单", MissionSourceEnum.AFTER, DispatchModeEnum.FC),
  /**
   * 导入退库单信息
   */
  BATCH_TASK_IMPORT_OUTBOUND_DELIVERY("73", "导入退库单信息", MissionSourceEnum.AFTER, DispatchModeEnum.FC),
  /**
   * 导入期初采购订单
   */
  BATCH_TASK_IMPORT_INITIAL_PURCHASE_ORDER("74", "导入期初采购订单", MissionSourceEnum.AFTER, DispatchModeEnum.FC),
  /**
   * 导入入库单信息
   */
  BATCH_TASK_IMPORT_INBOUND_DELIVERY("75", "导入入库单信息", MissionSourceEnum.AFTER, DispatchModeEnum.FC),
  /**
   * 需付款列表导出
   */
  BATCH_TASK_EXPORT_ORDER_NEED_PAYMENT("76", "需付款列表导出", MissionSourceEnum.AFTER, DispatchModeEnum.FC),
  /**
   * 导出采购申请单V2
   */
  BATCH_TASK_EXPORT_ORDER_NEED_PAYMENT_V2("77", "导出采购申请单V2", MissionSourceEnum.AFTER, DispatchModeEnum.FC),
  /**
   * 导入批改库位管理
   */
  BATCH_TASK_IMPORT_INVENTORY_LOCATION("78", "导入批改库位管理", MissionSourceEnum.AFTER, DispatchModeEnum.FC),
  /**
   * 导出v2退库单
   */
  BATCH_TASK_EXPORT_RETURN_STORAGE_ORDER_V2("79", "导出v2退库单", MissionSourceEnum.AFTER, DispatchModeEnum.FC),
  /**
   * 导入采购订单v2
   */
  BATCH_TASK_IMPORT_PURCHASE_ORDER_V2("80", "导入采购订单v2", MissionSourceEnum.AFTER, DispatchModeEnum.FC),
  /** 导出入库单信息 */
  BATCH_TASK_EXPORT_STORAGE_ORDER_V2("81", "导出-入库单信息V2", MissionSourceEnum.AFTER,
      DispatchModeEnum.FC),
  /**
   * 导入期初采购订单
   */
  BATCH_TASK_IMPORT_INITIAL_PURCHASE_ORDER_V2("82", "导入期初采购订单V2", MissionSourceEnum.AFTER,
      DispatchModeEnum.FC),
  /**
   * 导入退换货订单
   */
  BATCH_TASK_IMPORT_RETURN_EXCHANGE_ORDER("83", "导入退换货订单", MissionSourceEnum.AFTER,
      DispatchModeEnum.FC),
  /** 导入退库单信息V2 */
  BATCH_TASK_IMPORT_RETURN_STORAGE_ORDER_V2("84", "导入退库单信息V2", MissionSourceEnum.AFTER,
      DispatchModeEnum.FC),
  /**
   * 采购订单数据迁移
   */
  BATCH_TASK_MIGRATION_PURCHASE_ORDER("85", "采购订单数据迁移", MissionSourceEnum.AFTER,
      DispatchModeEnum.FC),
  /**
   * 采购申请数据迁移
   */
  BATCH_TASK_MIGRATION_PURCHASE_APPLY_FOR_ORDER("86", "采购申请数据迁移", MissionSourceEnum.AFTER,
      DispatchModeEnum.FC)
  ;

  /** 类型编码 */
  private final String code;

  /** 类型名称 */
  private final String typeName;

  /** 所属平台 */
  private final MissionSourceEnum platformType;

  /**
   * 优先使用的调度模式，当前逻辑如下（具体可见 MissionDispatcher 类）：
   *
   * <ul>
   *   <li>{@link DispatchModeEnum#MQ}
   *       <ul>
   *         <li>默认模式
   *       </ul>
   *   <li>{@link DispatchModeEnum#FC}
   *       <ul>
   *         <li>若调度器配置了 FC 函数地址，则使用 FC 调度
   *         <li>若调度器<b>未配置 FC 函数地址，仍使用 MQ 调度</b>
   *       </ul>
   * </ul>
   */
  private final DispatchModeEnum preferDispatchMode;

  @SuppressWarnings("unused")
  MissionTypeEnum(String code, String typeName, MissionSourceEnum platformType) {
    this.code = code;
    this.typeName = typeName;
    this.platformType = platformType;
    this.preferDispatchMode = DispatchModeEnum.MQ;
  }

  MissionTypeEnum(
      String code,
      String typeName,
      MissionSourceEnum platformType,
      DispatchModeEnum preferDispatchMode) {
    this.code = code;
    this.typeName = typeName;
    this.platformType = platformType;
    this.preferDispatchMode = preferDispatchMode;
  }

  private static final Map<String, MissionTypeEnum> CODE_TO_ENUM_MAP = new HashMap<>();

  static {
    for (MissionTypeEnum value : MissionTypeEnum.values()) {
      CODE_TO_ENUM_MAP.put(value.getCode(), value);
    }
  }

  @SuppressWarnings("unused")
  public static MissionTypeEnum fromCode(String code) {
    return CODE_TO_ENUM_MAP.get(code);
  }
}
