package com.xhgj.srm.service;

import com.xhgj.srm.common.dto.SupplierAccountAndOrderReceivingQuery;
import com.xhgj.srm.jpa.entity.SupplierPerformance;
import com.xhiot.boot.framework.jpa.service.BootBaseService;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2023-04-14 10:02
 */
public interface SupplierPerformanceService extends BootBaseService<SupplierPerformance,String> {

  /**
   * 通过供应商 id 获取供应商的履约信息
   * @param supplierIdList 供应商 id 集合
   */
  List<SupplierPerformance> getBySupplierIdList(List<String> supplierIdList);

  /**
   * 根据平台编码和供应商id查询
   * @param supplierId 供应商id
   * @param platformCode 平台编码
   * @return SupplierPerformance
   */
  SupplierPerformance findBySupplierIdAndPlatformCode(String supplierId, String platformCode);

  /**
   * 导出供应商履约信息
   * @param query
   * @return
   */
  List<SupplierPerformance>  exportByAccountAndOrderReceivingQuery(SupplierAccountAndOrderReceivingQuery query);
}
