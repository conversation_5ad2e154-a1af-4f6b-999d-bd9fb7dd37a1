package com.xhgj.srm.service;

import com.xhgj.srm.domain.CheckRelationDTO;
import com.xhgj.srm.jpa.entity.User;
import com.xhgj.srm.jpa.entity.UserCheck;
import com.xhiot.boot.framework.jpa.service.BootBaseService;
import org.springframework.lang.NonNull;
import java.util.List;
import java.util.Optional;

public interface UserCheckService extends BootBaseService<UserCheck, String> {

  void deleteCheckUserByUser(String userId);

  /**
   * 获取用户对不同等级供应商执行不同操作时的审核人
   *
   * @param userId 用户 id
   * @param type 操作类型：{@link com.xhgj.srm.common.Constants#USERCHECKTYPE_MAP}
   * @param level 供应商等级：{@link com.xhgj.srm.common.Constants#SUPPLIERLEVELMAP}
   * @return 若返回 null，则代表该用户的操作不需要审核
   */
  @NonNull
  Optional<User> getUserCheckMan(String userId, String type, String level);

  /**
   * 批量保存 UserCheck （先删后增）
   *
   * @param checkRelationList 参数对象列表
   * @param user 关联的用户实体，必传
   */
  void saveUserCheckInBatch(List<CheckRelationDTO> checkRelationList, User user);
}
