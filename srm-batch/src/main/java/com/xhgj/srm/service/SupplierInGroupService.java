package com.xhgj.srm.service;

import com.xhgj.srm.jpa.entity.Supplier;
import com.xhgj.srm.jpa.entity.SupplierInGroup;
import com.xhiot.boot.framework.jpa.service.BootBaseService;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2022/7/12 15:23
 */
public interface SupplierInGroupService extends BootBaseService<SupplierInGroup,String> {

  /**
   * 根据用户 id 和组织编码和供应商类型获得组织下国内供应商列表
   * @param userId 用户 id
   * @param userGroup 使用组织编码
   * @param supplierInGroupIdList 供应商 id 集合
   */
  List<SupplierInGroup> getSupplierInGroupChinaByUserId(String userId, String userGroup,List<String> supplierInGroupIdList);
  /**
   * 根据用户 id 和组织编码和供应商类型获得组织下国际供应商列表
   * @param userId 用户 id
   * @param groupId 使用组织编码
   * @param supplierInGroupIdList 供应商 id 集合
   */
  List<SupplierInGroup> getSupplierInGroupAbroadByUserId(String userId, String groupId, List<String> supplierInGroupIdList);
  /**
   * 根据用户 id 和组织编码和供应商类型获得组织下个人供应商列表
   * @param userId 用户 id
   * @param groupId 使用组织编码
   * @param supplierInGroupIdList 供应商 id 集合
   */
  List<SupplierInGroup> getSupplierInGroupPersonByUserId(String userId, String groupId, List<String> supplierInGroupIdList);

  /**
   * 根据企业名称和组织编码查询组织下供应商
   *
   * @param enterpriseName 企业名称，必传
   * @param groupCode 组织编码，必传
   * @param supType 供应商类型
   */
  SupplierInGroup getSupplierInGroupByEnterpriseNameAndGroupCode(
      String enterpriseName, String groupCode, String supType);

  /**
   * 根据主数据编码和组织编码查询组织下供应商
   *
   * @param mdmCode 主数据编码，必传
   * @param groupCode 组织编码，必传
   */
  SupplierInGroup getSupplierInGroupByMDMAndGroupCode(String mdmCode, String groupCode);

  /**
   * 该供应商是否在万聚组织下
   * @param supplier 供应商
   * @return boolean
   */
  boolean isWanJuOrganization(Supplier supplier);
}
