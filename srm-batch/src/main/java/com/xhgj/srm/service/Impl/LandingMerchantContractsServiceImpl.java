package com.xhgj.srm.service.Impl;

import cn.hutool.core.util.StrUtil;
import com.xhgj.srm.common.Constants;
import com.xhgj.srm.common.dto.CompleteFileDTO;
import com.xhgj.srm.jpa.entity.File;
import com.xhgj.srm.jpa.entity.LandingMerchantContract;
import com.xhgj.srm.jpa.repository.FileRepository;
import com.xhgj.srm.jpa.repository.LandingMerchantContractRepository;
import com.xhgj.srm.service.LandingMerchantContractsService;
import com.xhiot.boot.framework.jpa.repository.BootBaseRepository;
import java.util.List;
import java.util.Optional;
import javax.annotation.Resource;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @since 2023-04-14 10:53
 */
@Service
public class LandingMerchantContractsServiceImpl implements LandingMerchantContractsService {

  @Autowired
  private LandingMerchantContractRepository repository;
  @Resource
  private FileRepository fileRepository;

  @Override
  public BootBaseRepository<LandingMerchantContract, String> getRepository() {
    return repository;
  }

  @Override
  public Optional<LandingMerchantContract> findFirstByContractNumAndState(String contractNum,
      String state) {
    if (StrUtil.isBlank(contractNum) || StrUtil.isBlank(state)) {
      return Optional.empty();
    }
    return repository.findFirstByContractNoAndState(contractNum, state);
  }

  @Override
  public void addFile(String fileAssociationId, String fileAssociationType,
      LandingMerchantContract landingMerchantContract, CompleteFileDTO fileDTO) {
    Optional<List<File>> filesOptional =
        fileRepository.findAllByRelationIdAndRelationTypeAndState(fileAssociationId,
            fileAssociationType, Constants.STATE_OK);
    filesOptional.ifPresent(files -> {
      files.forEach(file -> {
        file.setState(Constants.STATE_DELETE);
        fileRepository.save(file);
      });
    });
    landingMerchantContract.setSignatureStatus(Constants.SIGNATURE_STATUS_YES);
    File file = new File();
    file.setName(fileDTO.getName());
    file.setUrl(fileDTO.getUrl() + "/" + fileDTO.getName());
    file.setRelationType(fileAssociationType);
    file.setRelationId(fileAssociationId);
    file.setCreateTime(System.currentTimeMillis());
    file.setState(Constants.STATE_OK);
    file.setDescription(fileDTO.getDescription());
    fileRepository.save(file);
  }
}
