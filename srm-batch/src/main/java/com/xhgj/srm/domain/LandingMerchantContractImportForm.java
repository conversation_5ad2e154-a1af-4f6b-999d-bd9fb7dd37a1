package com.xhgj.srm.domain;

import lombok.Data;
import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * 落地商合同导入form
 */
@Data
public class LandingMerchantContractImportForm {
  /**
   * *合同号
   */
  private String contractNo;

  /**
   * *下单平台
   */
  private String platformName;

  /**
   * *对方签约主体
   */
  private String supplierName;

  /**
   * *合作类型
   */
  private String cooperationType;

  /**
   * *合同有效期
   */
  private String validityPeriod;

  /**
   * *仓储
   */
  private String storage;

  /**
   * 仓库地址与面积
   */
  private String storageAddressAndArea;

  /**
   * 应付款比例
   */
  private String paymentRatio;

  /**
   * *付款条件
   */
  private String paymentTerms;

  /**
   * 账期
   */
  private String accountPeriod;

  /**
   * *付款方式
   */
  private String paymentForm;

  /**
   * 保证金
   */
  private String deposit;

  /**
   * 保证金状态
   */
  private String depositState;

  /**
   * *票种
   */
  private String invoiceType;

  /**
   * *税率
   */
  private String taxRate;

  /**
   * *保底金额
   */
  private String minimumAmount;

  /**
   * 违约金
   */
  private String penalty;


  /**
   * 获取所有必填项 转换为  List<String>
   */
  public List<String> getRequiredFields() {
    return Arrays.asList(contractNo, platformName, supplierName, cooperationType, validityPeriod,
        storage, paymentTerms, paymentForm, invoiceType, taxRate,
        minimumAmount);
  }
}
