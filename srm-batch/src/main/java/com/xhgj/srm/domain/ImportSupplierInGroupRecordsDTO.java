package com.xhgj.srm.domain;

import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 此类用于导入国内，国际供应商的标记
 *
 * <AUTHOR>
 * @since 2022/7/13 14:54
 */
@Data
@NoArgsConstructor
public class ImportSupplierInGroupRecordsDTO {

  /** 记录组织下供应商 id */
  private String supplierInGroupId;

  /** 记录下供应商部分是否校验通过 */
  private Boolean validPass;

  public ImportSupplierInGroupRecordsDTO(String supplierInGroupId, Boolean validPass) {
    this.supplierInGroupId = supplierInGroupId;
    this.validPass = validPass;
  }
}
