package com.xhgj.srm.domain;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import java.util.List;
import javax.validation.Valid;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/** <AUTHOR> @ClassName ReadUserDTO */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ReadUserDTO {
  @ExcelProperty(index = 0, value = "姓名")
  private String name;

  @ExcelProperty(index = 1, value = "手机号码")
  private String mobile;

  @ExcelProperty(index = 2, value = "ERP编码")
  private String erpCode;

  @ExcelProperty(index = 3, value = "ERPID")
  private String erpId;

  @ExcelProperty(index = 4, value = "角色编码")
  private String roleCode;

  @ExcelProperty(index = 5, value = "新增审核人ERP编码-战略")
  private String addUserStrategyCode;

  @ExcelProperty(index = 6, value = "一般")
  private String addUserCommonCode;

  @ExcelProperty(index = 7, value = "项目")
  private String addUserProjectCode;

  @ExcelProperty(index = 8, value = "零星")
  private String addUserSporadicCode;

  @ExcelProperty(index = 9, value = "潜在")
  private String addUserPotentialCode;

  @ExcelProperty(index = 10, value = "电商")
  private String addUserOnlineCode;

  @ExcelProperty(index = 11, value = "修改审核人ERP编码-战略")
  private String updateUserStrategyCode;

  @ExcelProperty(index = 12, value = "一般")
  private String updateUserCommonCode;

  @ExcelProperty(index = 13, value = "项目")
  private String updateUserProjectCode;

  @ExcelProperty(index = 14, value = "零星")
  private String updateUserSporadicCode;

  @ExcelProperty(index = 15, value = "潜在")
  private String updateUserPotentialCode;

  @ExcelProperty(index = 16, value = "电商")
  private String updateUserOnlineCode;

  @ExcelProperty(index = 17, value = "拉黑审核人ERP编码-战略")
  private String blockUserStrategyCode;

  @ExcelProperty(index = 18, value = "一般")
  private String blockUserCommonCode;

  @ExcelProperty(index = 19, value = "项目")
  private String blockUserProjectCode;

  @ExcelProperty(index = 20, value = "零星")
  private String blockUserSporadicCode;

  @ExcelProperty(index = 21, value = "潜在")
  private String blockUserPotentialCode;

  @ExcelProperty(index = 22, value = "电商")
  private String blockUserOnlineCode;

  @ExcelProperty(index = 23, value = "SRM组织编码")
  private String groupCode;

  @ExcelProperty(index = 24, value = "SRM部门编码")
  private String deptCode;

  /** SRM组织编码及SRM部门编码 这个是需要我们自己整理的，EasyExcel做不到 */
  @ExcelIgnore private List< ReadUserGroupDTO> groupList;
}
