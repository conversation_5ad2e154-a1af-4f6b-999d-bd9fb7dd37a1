package com.xhgj.srm.batch.controller;

import com.xhgj.srm.service.FinancialVouchersService;
import com.xhiot.boot.core.common.exception.CheckException;
import com.xhiot.boot.mvc.base.ResultBean;
import io.swagger.annotations.ApiOperation;
import java.io.IOException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

/**
 * <AUTHOR>
 * @since 2024/6/25 10:47
 */
@RestController
@RequestMapping("financialVouchers")
public class FinancialVouchersController {
  @Autowired private FinancialVouchersService financialVouchersService;

  @ApiOperation("导入修复提款申请对应的财务凭证")
  @PostMapping("repairDrawPaymentFinancialVoucher")
  public ResultBean<Integer> repairDrawPaymentFinancialVoucher(MultipartFile file)
      throws IOException {
    if (file == null || file.isEmpty()) {
      throw new CheckException("文件为空！");
    }
    return new ResultBean<>(
        financialVouchersService.repairDrawPaymentFinancialVoucher(file.getInputStream()));
  }
}
