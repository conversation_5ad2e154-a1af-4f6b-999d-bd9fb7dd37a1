package com.xhgj.srm.mobile.dto.supplier;

import com.xhgj.srm.jpa.entity.Supplier;
import java.math.BigDecimal;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class OrderSupplierDTO {

  private String id;
  private String enterName;
  private BigDecimal price;

  public OrderSupplierDTO(Supplier supplier) {
    this.id = supplier.getId();
    this.enterName = supplier.getEnterpriseName();
  }
}
