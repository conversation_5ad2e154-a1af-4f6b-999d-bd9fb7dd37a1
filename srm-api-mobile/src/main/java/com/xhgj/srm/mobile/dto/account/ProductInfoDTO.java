package com.xhgj.srm.mobile.dto.account;

import com.xhgj.srm.jpa.entity.OrderDetail;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2023-02-23 20:43
 */
@Data
public class ProductInfoDTO {

  @ApiModelProperty("id")
  private String productId;

  @ApiModelProperty("商品编码")
  private String productCode;

  @ApiModelProperty("品牌")
  private String brandName;

  @ApiModelProperty("商品名称")
  private String productName;

  @ApiModelProperty("型号")
  private String model;

  @ApiModelProperty("数量")
  private BigDecimal productCount;

  @ApiModelProperty("单位")
  private String unit;

  @ApiModelProperty("单价")
  private BigDecimal productPrice;

  @ApiModelProperty("客户订单号")
  private String orderNo;

  @ApiModelProperty("订单id")
  private String orderId;

  @ApiModelProperty("去税单价")
  private BigDecimal taxFreeCbPrice;

  @ApiModelProperty("退货数量")
  private BigDecimal returnNum;

  public ProductInfoDTO(OrderDetail orderDetail) {
    this.productId = orderDetail.getId();
    this.productCode = orderDetail.getCode();
    this.brandName = orderDetail.getBrand();
    this.productName = orderDetail.getName();
    this.model = orderDetail.getModel();
    this.productCount = orderDetail.getNum();
    this.unit = orderDetail.getUnit();
    this.productPrice = orderDetail.getPrice();
    this.orderNo = orderDetail.getOrder().getOrderNo();
    this.orderId = orderDetail.getOrder().getId();
    this.taxFreeCbPrice = orderDetail.getTaxFreeCbPrice();
    this.returnNum = orderDetail.getReturnNum();
  }
}
