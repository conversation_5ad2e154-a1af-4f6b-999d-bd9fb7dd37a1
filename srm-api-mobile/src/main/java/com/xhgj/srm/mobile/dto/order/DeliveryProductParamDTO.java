package com.xhgj.srm.mobile.dto.order;

import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import lombok.Data;

@Data
public class DeliveryProductParamDTO {

  @ApiModelProperty("物料编码")
  private String code;

  @ApiModelProperty("品牌")
  private String brand;

  @ApiModelProperty("商品名称")
  private String name;

  @ApiModelProperty("型号")
  private String model;

  @ApiModelProperty("数量")
  private BigDecimal num;

  @ApiModelProperty("单位")
  private String unit;

  @ApiModelProperty("单价")
  private BigDecimal price;

  @ApiModelProperty("发货数量")
  private BigDecimal delCount;

  @ApiModelProperty("行id")
  private String rowId;

  @ApiModelProperty("成本价税率")
  private String costPriceTaxRate;
}
