package com.xhgj.srm.mobile.dto.returned;

import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.NotBlank;
import lombok.Data;

@Data
public class OrderReturnCheckParam {

  @ApiModelProperty("退货单id")
  @NotBlank(message = "退货单id不能为空")
  private String orderReturnId;

  @ApiModelProperty("是否通过")
  @NotBlank(message = "审核结果不能为空")
  private String isPass;

  @ApiModelProperty("原因")
  private String reason;
}
