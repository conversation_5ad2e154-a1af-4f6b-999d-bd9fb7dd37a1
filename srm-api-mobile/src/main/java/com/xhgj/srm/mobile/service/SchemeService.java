package com.xhgj.srm.mobile.service;

import com.xhgj.srm.jpa.entity.SearchScheme;
import com.xhgj.srm.mobile.dto.scheme.SchemeAddParamDTO;
import com.xhgj.srm.mobile.dto.scheme.SchemeDataDTO;
import com.xhgj.srm.mobile.dto.scheme.SchemeUpdateParamDTO;
import com.xhgj.srm.mobile.dto.scheme.SingleBaseParam;
import com.xhiot.boot.framework.jpa.service.BootBaseService;
import java.util.List;

/**
 * @ClassName SchemeService Create by Liuyq on 2021/6/18 16:07
 */
public interface SchemeService extends BootBaseService<SearchScheme, String> {

  /**
   * 新增检索方案
   *
   * @param schemeAddParamDTO
   */
  void addScheme(SchemeAddParamDTO schemeAddParamDTO);

  /**
   * @Title: @Description:修改检索方案
   *
   * @param schemeUpdateParamDTO
   * <AUTHOR>
   * @date 2021/6/11 15:18
   */
  void updateScheme(SchemeUpdateParamDTO schemeUpdateParamDTO);

  /**
   * @Title: @Description:删除检索方案
   *
   * @param param
   * <AUTHOR>
   * @date 2021/6/11 15:18
   */
  void deleteScheme(SingleBaseParam param);

  /**
   * 获取我的检索方案列表 @Author: liuyq @Date: 2021/6/25 15:15
   *
   * @param supplierUserId
   * @param type
   * @return java.util.List<com.xhgj.srm.api.dto.SchemeDataDTO>
   */
  List<SchemeDataDTO> getMySchemeList(String supplierUserId, String type);

  /**
   * 获取检索方案详情 @Author: liuyq @Date: 2021/6/23 14:14
   *
   * @param schemeId
   * @return com.xhgj.srm.api.dto.SchemeDataDTO
   */
  SchemeDataDTO getSchemeDetail(String schemeId);

  /**
   * 设为默认检索方案 @Author: liuyq @Date: 2021/6/23 14:14
   *
   * @param param
   * @return void
   */
  void setSchemeDefault(SingleBaseParam param);
}
