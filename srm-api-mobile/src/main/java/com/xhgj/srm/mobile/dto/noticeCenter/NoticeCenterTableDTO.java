package com.xhgj.srm.mobile.dto.noticeCenter;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import com.xhgj.srm.common.enums.NoticeCenterType;
import com.xhgj.srm.jpa.entity.NoticeCenter;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import lombok.Data;

/**
 * <AUTHOR> @ClassName NoticeCenterTableDTO
 */
@Data
public class NoticeCenterTableDTO {
  @ApiModelProperty("id")
  private String id;

  @ApiModelProperty("内容")
  private String content;

  @ApiModelProperty("类型")
  private String type;

  @ApiModelProperty("是否已读")
  private Boolean isRead;

  @ApiModelProperty("类型名称")
  private String typeToName;

  @ApiModelProperty("时间")
  private String time;

  public NoticeCenterTableDTO(NoticeCenter noticeCenter) {
    this.id = noticeCenter.getId();
    this.content = noticeCenter.getContent();
    this.isRead = noticeCenter.getIsRead();
    this.type = noticeCenter.getType();
    this.typeToName = NoticeCenterType.findByType(noticeCenter.getType()).getValue();
    this.time =
        DateUtil.format(
            new Date(noticeCenter.getCreateTime()), DatePattern.NORM_DATETIME_MINUTE_PATTERN);
  }
}
