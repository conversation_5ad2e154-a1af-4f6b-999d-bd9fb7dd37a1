package com.xhgj.srm.mobile.dto.supplierOrder.supplierOrderReturn;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class SupplierOrderCountDTO {

  @ApiModelProperty("发货单统计")
  private Long invoiceCount;

  @ApiModelProperty("退货单统计")
  private Long returnOrderCount;

  @ApiModelProperty("取消单统计")
  private Long cancellationOrderCount;

  public SupplierOrderCountDTO(
      Long invoiceCount, Long returnOrderCount, Long cancellationOrderCount) {
    this.invoiceCount = invoiceCount;
    this.returnOrderCount = returnOrderCount;
    this.cancellationOrderCount = cancellationOrderCount;
  }
}
