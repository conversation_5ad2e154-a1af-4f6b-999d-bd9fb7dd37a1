package com.xhgj.srm.mobile.config;

import com.xhgj.srm.mobile.service.UsersService;
import com.xhiot.boot.security.config.BootSecurityConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.config.annotation.method.configuration.EnableGlobalMethodSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR>
 * @since 2021/4/14 19:39
 */
@Configuration
@EnableWebSecurity
@EnableGlobalMethodSecurity(prePostEnabled = true)
@Slf4j
public class SecurityConfig extends BootSecurityConfig {

  public SecurityConfig() {
    log.info("Init SecurityConfig...");
  }

  @Bean
  @Transactional
  public UserDetailsService userDetailsService(UsersService usersService) {
    log.info("=== SrmSecurityConfig#userDetailsService()...");
    // 实现 UserDetailsService 的 loadUserByName(String) 方法
    return usersService::loadUserByName;
  }
}
