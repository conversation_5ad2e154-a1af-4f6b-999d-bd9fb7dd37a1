package com.xhgj.srm.mobile.dto.account;

import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import com.xhgj.srm.common.Constants;
import com.xhgj.srm.common.Constants_order;
import com.xhgj.srm.jpa.entity.Order;
import com.xhiot.boot.core.common.util.StringUtils;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2023-02-23 20:38
 */
@Data
public class OrderInfoDTO {

  @ApiModelProperty("订单 id")
  private String orderId;

  @ApiModelProperty("客户订单号")
  private String orderNo;

  @ApiModelProperty("客户名称")
  private String customer;

  @ApiModelProperty("下单平台")
  private String platform;

  @ApiModelProperty("下单平台Code")
  private String platformCode;

  @ApiModelProperty("下单时间")
  private Long orderTime;

  @ApiModelProperty("下单金额")
  private BigDecimal orderPrice;

  @ApiModelProperty("最终结算金额")
  private BigDecimal finalPrice;

  @ApiModelProperty("签收凭证")
  private String signVoucherState;

  @ApiModelProperty("客户开票状态")
  private String orderInvoiceStatus;

  @ApiModelProperty("客户回款")
  private String customerPayback;

  @ApiModelProperty("对账单开票")
  private String accountOpenInvoiceStatus;

  @ApiModelProperty("采购订单号")
  private String erpOrderNo;

  public OrderInfoDTO(Order order, String typeName) {
    this.orderId = order.getId();
    this.orderNo = order.getOrderNo();
    this.orderTime = order.getOrderTime();
    this.platform = StrUtil.emptyIfNull(typeName);
    this.orderPrice = order.getPrice();
    this.customer = StringUtils.emptyIfNull(order.getCustomer());
    this.orderInvoiceStatus =
        !StringUtils.isNullOrEmpty(order.getInvoicingState())
            ? Constants_order.INVOICE_STATE_MAP.get(order.getInvoicingState())
            : "未开票";
    this.customerPayback =
        Constants_order.getCustomerPaybackStateNameByReturnProgress(
            order.getCustomerReturnProgress(), Constants_order.CUSTOMER_PAYBACK_UN);
    this.finalPrice = NumberUtil.sub(order.getPrice(), order.getRefundPrice());
    this.accountOpenInvoiceStatus =
        StrUtil.blankToDefault(
            order.getSupplierOpenInvoiceStatus(), Constants.ORDER_INVOICE_STATE_NOT_DONE);
    this.platformCode = order.getType();
    this.erpOrderNo = order.getErpOrderNo();
  }
}
