package com.xhgj.srm.mobile.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.xhgj.srm.common.Constants;
import com.xhgj.srm.common.enums.NoticeCenterType;
import com.xhgj.srm.jpa.dao.NoticeCenterDao;
import com.xhgj.srm.jpa.entity.NoticeCenter;
import com.xhgj.srm.jpa.entity.SearchScheme;
import com.xhgj.srm.jpa.repository.NoticeCenterRepository;
import com.xhgj.srm.mobile.dto.noticeCenter.NoticeCenterDTO;
import com.xhgj.srm.mobile.dto.noticeCenter.NoticeCenterQueryDTO;
import com.xhgj.srm.mobile.dto.noticeCenter.NoticeCenterSchemeDTO;
import com.xhgj.srm.mobile.dto.noticeCenter.NoticeCenterTableDTO;
import com.xhgj.srm.mobile.service.NoticeCenterService;
import com.xhgj.srm.mobile.service.SearchSchemeService;
import com.xhiot.boot.core.common.exception.CheckException;
import com.xhiot.boot.framework.jpa.repository.BootBaseRepository;
import com.xhiot.boot.framework.web.util.PageResultBuilder;
import com.xhiot.boot.mvc.base.PageResult;
import java.util.List;
import java.util.stream.Collectors;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR> @ClassName NoticeCenterServiceImpl
 */
@Service
public class NoticeCenterServiceImpl implements NoticeCenterService {

  @Autowired private SearchSchemeService searchSchemeService;
  @Autowired private NoticeCenterRepository repository;
  @Autowired private NoticeCenterDao dao;

  @Override
  public BootBaseRepository<NoticeCenter, String> getRepository() {
    return repository;
  }

  @Override
  public PageResult<NoticeCenterTableDTO> getPage(
      NoticeCenterQueryDTO queryDTO, Pageable toPageable) {
    String supplierId = queryDTO.getSupplierId();
    String schemeId = queryDTO.getSchemeId();
    String userId = queryDTO.getUserId();
    String content = queryDTO.getContent();
    String type = queryDTO.getMessageType();
    Boolean isRead = queryDTO.getReadState();
    // 查询方案
    if (StrUtil.isEmpty(schemeId)) {
      SearchScheme search =
          searchSchemeService.getDefaultSearchScheme(userId, Constants.SEARCH_TYPE_NOTICE_CENTER);
      if (search != null) {
        schemeId = search.getId();
      }
    }
    if (StrUtil.isNotEmpty(schemeId)) {
      SearchScheme search = searchSchemeService.get(schemeId);
      if (search != null && StrUtil.isNotEmpty(search.getContent())) {
        NoticeCenterSchemeDTO schemeDTO =
            JSON.parseObject(search.getContent(), new TypeReference<NoticeCenterSchemeDTO>() {});
        if (schemeDTO != null) {
          content = StrUtil.blankToDefault(content, schemeDTO.getContent());
          type = StrUtil.blankToDefault(type, schemeDTO.getMessageType());
        }
      }
    }
    return PageResultBuilder.buildPageResult(
        dao.getPage(
            supplierId,
            isRead,
            type,
            content,
            toPageable.getPageNumber() + 1,
            toPageable.getPageSize()),
        NoticeCenterTableDTO::new);
  }

  @Override
  public NoticeCenterDTO getById(String id) {
    NoticeCenter noticeCenter =
        get(id, () -> CheckException.noFindException(NoticeCenter.class, id));
    noticeCenter.setIsRead(true);
    save(noticeCenter);
    return new NoticeCenterDTO(noticeCenter);
  }

  @Override
  public List<NoticeCenterTableDTO> getNoticeCenterList(String supplierId, String size) {
    return CollUtil.emptyIfNull(dao.getNoticeCenterList(supplierId, Integer.parseInt(size)))
        .stream()
        .map(NoticeCenterTableDTO::new)
        .collect(Collectors.toList());
  }

  @Override
  public void save(
      NoticeCenterType type, String content, String supplierOrderId, String supplierId) {
    NoticeCenter noticeCenter = new NoticeCenter();
    noticeCenter.setContent(content);
    noticeCenter.setType(type.getKey());
    noticeCenter.setSupplierId(supplierId);
    noticeCenter.setSupplierOrderId(supplierOrderId);
    noticeCenter.setIsRead(false);
    noticeCenter.setState(Constants.STATE_OK);
    noticeCenter.setCreateTime(System.currentTimeMillis());
    save(noticeCenter);
  }
}
