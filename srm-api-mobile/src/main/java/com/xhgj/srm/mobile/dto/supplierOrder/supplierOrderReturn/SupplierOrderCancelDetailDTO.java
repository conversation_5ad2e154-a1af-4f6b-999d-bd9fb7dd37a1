package com.xhgj.srm.mobile.dto.supplierOrder.supplierOrderReturn;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.xhgj.srm.common.enums.supplierorder.SupplierOrderFormStatus;
import com.xhgj.srm.jpa.entity.SupplierOrder;
import com.xhgj.srm.jpa.entity.SupplierOrderToForm;
import com.xhgj.srm.mobile.dto.supplierOrder.CancelProductDTO;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import java.util.List;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR> @ClassName SupplierOrderDTO
 */
@Data
@NoArgsConstructor
public class SupplierOrderCancelDetailDTO extends BaseSupplierOrderDetailDTO {
  @ApiModelProperty("退货单id")
  private String id;

  @ApiModelProperty("订单id")
  private String supplierOrderId;

  @ApiModelProperty("取消单编号")
  private String cancelNumber;

  @ApiModelProperty("取消时间")
  private String cancelTime;

  @ApiModelProperty("状态 （0 --已取消）")
  private String state;

  @ApiModelProperty("状态对应名称")
  private String stateToName;

  @ApiModelProperty("物料明细")
  private List<CancelProductDTO> cancelProductDTOList;

  public SupplierOrderCancelDetailDTO(
      SupplierOrderToForm supplierOrderToForm,
      SupplierOrder supplierOrder,
      List<CancelProductDTO> cancelProductDTOList) {
    super(supplierOrder);
    this.id = supplierOrderToForm.getId();
    this.supplierOrderId = supplierOrderToForm.getSupplierOrderId();
    this.cancelNumber = StrUtil.emptyIfNull(supplierOrderToForm.getNumbers());
    this.cancelTime =
        ObjectUtil.isNotEmpty(supplierOrderToForm.getTime()) && supplierOrderToForm.getTime() > 0
            ? DateUtil.format(
                new Date(supplierOrderToForm.getTime()), DatePattern.NORM_DATETIME_PATTERN)
            : "";
    this.state = StrUtil.emptyIfNull(supplierOrderToForm.getStatus());
    this.stateToName =
        SupplierOrderFormStatus.findValueByStatus(supplierOrderToForm.getStatus()).getDesc();
    this.cancelProductDTOList = cancelProductDTOList;
  }
}
