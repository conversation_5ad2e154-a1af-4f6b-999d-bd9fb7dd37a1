package com.xhgj.srm.mobile.service;

import com.xhgj.srm.jpa.entity.OrderDetail;
import com.xhgj.srm.mobile.dto.order.delivery.OrderDeliveryProductDetailDTO;
import com.xhgj.srm.mobile.dto.order.vo.OrderDetailVO;
import com.xhgj.srm.mobile.dto.order.vo.OrderProductInfoVO;
import com.xhgj.srm.mobile.dto.supplier.invoice.OrderAmount;
import com.xhiot.boot.framework.jpa.service.BootBaseService;
import com.xhiot.boot.mvc.base.PageResult;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @since 2023-06-13 13:42
 */
public interface OrderDetailService extends BootBaseService<OrderDetail, String> {
  /**
   * 获取订单的金额信息
   *
   * @param orderId
   * @return
   */
  OrderAmount getOrderAmount(String orderId);

  /**
   * @param erpRowId erp物料行Id
   * @return
   */
  Optional<OrderDetail> findByErpRowId(String erpRowId);

  /**
   * 获取订单详情
   *
   * @param orderId 订单id
   * @return
   */
  OrderDetailVO getOrderDetail(String orderId);

  /**
   * 根据订单id批量获取订单详情
   *
   * @param orderIds
   * @return
   */
  List<OrderDetailVO> batchGetOrderProductById(List<String> orderIds);

  /**
   * 查询订单商品、发货、退货信息
   *
   * @param orderId 订单id
   */
  OrderProductInfoVO getOrderProductInfo(String orderId);


  List<OrderProductInfoVO> batchGetOrderProductInfo(List<String> orderIds);

  /**
   * 查询订单商品发货信息
   *
   * @param orderId 订单id
   */
  PageResult<OrderDeliveryProductDetailDTO> getProductInfoPage(
      String orderId, String keyWord, Integer pageNo, Integer pageSize, String ids);
}
