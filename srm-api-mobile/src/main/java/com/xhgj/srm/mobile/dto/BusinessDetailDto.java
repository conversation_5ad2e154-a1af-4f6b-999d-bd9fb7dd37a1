package com.xhgj.srm.mobile.dto;

import com.xhgj.srm.common.Constants;
import com.xhgj.srm.jpa.entity.Supplier;
import com.xhgj.srm.jpa.entity.SupplierFb;
import com.xhiot.boot.core.common.util.DateUtils;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @ClassName BusinessInformationDto Create by Liuyq on 2021/6/1 15:13
 */
@Data
public class BusinessDetailDto {
  @ApiModelProperty("供应商id")
  private String id;

  @ApiModelProperty("企业名称")
  private String enterpriseName;

  @ApiModelProperty("法定代表人")
  private String corporate;

  @ApiModelProperty("经营状态")
  private String regStatus;

  @ApiModelProperty("统一社会信用代码")
  private String uscc;

  @ApiModelProperty("成立日期")
  private String date;

  @ApiModelProperty("营业期限-开始")
  private String startDate;

  @ApiModelProperty("营业期限-结束")
  private String endDate;

  @ApiModelProperty("注册资本")
  private String regCapital;

  @ApiModelProperty("公司性质")
  private String enterpriseNature;

  @ApiModelProperty("实缴资本")
  private String paidCapital;

  @ApiModelProperty("参保人数")
  private String insNum;

  @ApiModelProperty("纳税人识别号")
  private String taxNumber;

  @ApiModelProperty("曾用名")
  private String usedName;

  @ApiModelProperty("纳税人资质")
  private String taxQualification;

  @ApiModelProperty("英文名称")
  private String englishName;

  @ApiModelProperty("登记机关")
  private String regAuthority;

  @ApiModelProperty("工商注册号")
  private String regNo;

  @ApiModelProperty("组织机构代码")
  private String orgCode;

  @ApiModelProperty("行业")
  private String industry;

  @ApiModelProperty("人员规模")
  private String peopleNum;

  @ApiModelProperty("注册地址")
  private String regAddress;

  @ApiModelProperty("经营范围")
  private String businessScope;

  @ApiModelProperty("营业执照")
  private String licenseUrl;

  @ApiModelProperty("数据状态")
  private String state;

  @ApiModelProperty("文件前缀")
  private String baseUrl;

  public BusinessDetailDto(
      Supplier supplier,
      SupplierFb supplierFb,
      long date,
      long startDate,
      long endDate,
      String baseUrl) {
    this.id = supplier.getId();
    this.enterpriseName =
        supplierFb == null ? supplier.getEnterpriseName() : supplierFb.getEnterpriseName();
    this.corporate = supplierFb == null ? supplier.getCorporate() : supplierFb.getCorporate();
    this.regStatus = supplierFb == null ? supplier.getManageType() : supplierFb.getManageType();
    this.uscc = supplierFb == null ? supplier.getUscc() : supplierFb.getUscc();
    this.date = date > 0 ? DateUtils.formatTimeStampToNormalDate(date) : "";
    this.startDate = startDate > 0 ? DateUtils.formatTimeStampToNormalDate(startDate) : "";
    this.endDate = endDate > 0 ? DateUtils.formatTimeStampToNormalDate(endDate) : "";
    this.regCapital = supplierFb == null ? supplier.getRegCapital() : supplierFb.getRegCapital();
    this.enterpriseNature =
        supplierFb == null ? supplier.getEnterpriseNature() : supplierFb.getEnterpriseNature();
    this.paidCapital = supplierFb == null ? supplier.getPaidCapital() : supplierFb.getPaidCapital();
    this.insNum = supplierFb == null ? supplier.getInsNum() : supplierFb.getInsNum();
    this.taxNumber = supplierFb == null ? supplier.getTaxNumber() : supplierFb.getTaxNumber();
    this.usedName = supplierFb == null ? supplier.getUsedName() : supplierFb.getUsedName();
    this.taxQualification =
        supplierFb == null ? supplier.getTaxQualification() : supplierFb.getTaxQualification();
    this.englishName = supplierFb == null ? supplier.getEnglishName() : supplierFb.getEnglishName();
    this.regAuthority =
        supplierFb == null ? supplier.getRegAuthority() : supplierFb.getRegAuthority();
    this.regNo = supplierFb == null ? supplier.getRegNo() : supplierFb.getRegNo();
    this.orgCode = supplierFb == null ? supplier.getOrgCode() : supplierFb.getOrgCode();
    this.industry = supplierFb == null ? supplier.getIndustry() : supplierFb.getIndustry();
    this.peopleNum = supplierFb == null ? supplier.getPeopleNum() : supplierFb.getPeopleNum();
    this.orgCode = supplierFb == null ? supplier.getOrgCode() : supplierFb.getOrgCode();
    this.regAddress = supplierFb == null ? supplier.getRegAddress() : supplierFb.getRegAddress();
    this.businessScope =
        supplierFb == null ? supplier.getBusinessScope() : supplierFb.getBusinessScope();
    this.licenseUrl = supplierFb == null ? supplier.getLicenseUrl() : supplierFb.getLicenseUrl();
    this.state =
        Constants.AUDIT_STATE_PURCHASEIN.equals(supplier.getAuditState())
            ? Constants.SUPPLIER_CHANGE_RECODE_TO_NAME.get(Constants.SUPPLIER_CHANGE_RECODE_CHECK)
            : Constants.SUPPLIER_CHANGE_RECODE_TO_NAME.get(Constants.SUPPLIER_CHANGE_RECODE_OK);
    this.baseUrl = baseUrl;
  }
}
