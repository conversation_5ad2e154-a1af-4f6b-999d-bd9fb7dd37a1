package com.xhgj.srm.mobile.dto.supplierOrder;

import cn.hutool.core.util.StrUtil;
import com.xhgj.srm.common.enums.supplierorder.SupplierOrderRefuseState;
import com.xhgj.srm.jpa.entity.SupplierOrder;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR> @ClassName SupplierOrderTableDTO
 */
@Data
@NoArgsConstructor
public class SupplierOrderTableDTO extends BaseSupplierOrderDTO {

  @ApiModelProperty("采购件数")
  private String number;

  @ApiModelProperty("是否有待确认")
  private Boolean confirmState;

  @ApiModelProperty("是否有取消")
  private Boolean cancelState;

  @ApiModelProperty("是否有退货")
  private Boolean returnState;

  @ApiModelProperty("是否有拒单")
  private Boolean refuseState;

  @ApiModelProperty("开票状态")
  private String supplierOpenInvoiceState;

  public SupplierOrderTableDTO(SupplierOrder supplierOrder, boolean hasReturnOrder) {
    super(supplierOrder);
    this.confirmState = supplierOrder.getOrderConfirmState();
    this.cancelState = supplierOrder.getOrderCancelState();
    this.returnState = supplierOrder.getOrderReturnState() && hasReturnOrder;
    this.number =
        supplierOrder.getTotalNum() != null
            ? supplierOrder.getTotalNum().stripTrailingZeros().toPlainString()
            : "";
    this.refuseState =
        StrUtil.equals(supplierOrder.getRefuseState(), SupplierOrderRefuseState.REFUSE.getKey());
    this.supplierOpenInvoiceState = supplierOrder.getSupplierOpenInvoiceState();
  }
}
