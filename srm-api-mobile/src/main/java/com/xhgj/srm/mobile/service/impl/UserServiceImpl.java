package com.xhgj.srm.mobile.service.impl;

import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.StrUtil;
import com.xhgj.srm.jpa.dao.UserDao;
import com.xhgj.srm.jpa.entity.User;
import com.xhgj.srm.jpa.repository.UserRepository;
import com.xhgj.srm.mobile.service.UserService;
import com.xhiot.boot.core.common.exception.CheckException;
import com.xhiot.boot.framework.jpa.repository.BootBaseRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

/**
 * <AUTHOR> @ClassName UserServiceImpl
 */
@Repository
public class UserServiceImpl implements UserService {

  @Autowired private UserRepository repository;
  @Autowired private UserDao dao;

  @Override
  public BootBaseRepository<User, String> getRepository() {
    return repository;
  }

  @Override
  public User getByCode(String code) {
    Assert.notEmpty(code);
    return dao.getUserByCode(code);
  }

  @Override
  public String getUserRealName(String userId) {
    if (StrUtil.isBlank(userId)) {
      return "";
    }
    User user = get(userId, () -> CheckException.noFindException(User.class, userId));
    return user.getRealName();
  }
}
