package com.xhgj.srm.mobile.dto.order;

import cn.hutool.core.util.StrUtil;
import com.xhgj.srm.common.enums.OrderInvoiceEnums;
import com.xhgj.srm.jpa.entity.OrderInvoice;
import com.xhiot.boot.core.common.exception.CheckException;
import com.xhiot.boot.core.common.util.dict.BootDictEnumUtil;
import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

/**
 * <AUTHOR>
 * @since 2023-02-02 10:19
 */
@Data
@NoArgsConstructor
public class BaseOrderInvoice {
  @ApiModelProperty("落地商订单 id")
  @NotBlank(message = "落地商订单 id 必传")
  private String orderId;

  @ApiModelProperty("发票类型")
  @NotNull(message = "发票类型 必传")
  private OrderInvoiceEnums orderInvoiceEnums;

  @ApiModelProperty("发票抬头")
  @NotBlank(message = "发票抬头 必传")
  @Length(max = 140, message = "发票抬头 超长")
  private String title;

  @ApiModelProperty("税号")
  @Pattern(regexp = "^[A-NP-Z0-9]{1,20}", message = "税号格式错误")
  private String taxNumber;

  @ApiModelProperty("开户银行")
  @Length(max = 140, message = "开户银行 超长")
  @NotBlank(message = "开户银行必传")
  private String bankName;

  /** 银行账户可以输入“-” */
  @ApiModelProperty("银行账户")
  @NotBlank(message = "银行账户 必传")
  //  @Pattern(regexp = "^\\d{1,20}$", message = "银行账户格式错误")
  private String bankAccount;

  @ApiModelProperty("电话")
  @NotBlank(message = "电话 必传")
  private String mobile;

  @ApiModelProperty("地址")
  @Length(max = 140, message = "地址 超长")
  @NotBlank(message = "地址必传")
  private String address;

  @ApiModelProperty("票面描述")
  @Length(max = 500, message = "票面描述 超长")
  private String content;

  @ApiModelProperty("收件人")
  @Length(max = 20, message = "收件人 超长")
  @NotBlank(message = "收件人 必传")
  private String receiveMan;

  @ApiModelProperty("收件人联系电话")
  @NotBlank(message = "收件人联系 必传")
  private String receiveMobile;

  @ApiModelProperty("收件人地址")
  @NotBlank(message = "收件人地址 必传")
  @Length(max = 50, message = "收件人地址 超长")
  private String receiveAddress;

  @ApiModelProperty("其它备注")
  @Length(max = 500, message = "其它备注 超长")
  private String remark;

  public BaseOrderInvoice(OrderInvoice orderInvoice) {
    this.orderId = orderInvoice.getOrderId();
    this.orderInvoiceEnums =
        BootDictEnumUtil.getEnumByKey(OrderInvoiceEnums.class, orderInvoice.getType())
            .orElseThrow(() -> new CheckException("发票类型非法，请联系管理员！"));
    this.title = StrUtil.emptyIfNull(orderInvoice.getTitle());
    this.taxNumber = StrUtil.emptyIfNull(orderInvoice.getTaxNumber());
    this.bankName = StrUtil.emptyIfNull(orderInvoice.getBankName());
    this.bankAccount = StrUtil.emptyIfNull(orderInvoice.getBankAccount());
    this.mobile = StrUtil.emptyIfNull(orderInvoice.getMobile());
    this.address = StrUtil.emptyIfNull(orderInvoice.getAddress());
    this.content = StrUtil.emptyIfNull(orderInvoice.getContent());
    this.receiveMan = StrUtil.emptyIfNull(orderInvoice.getReceiveMan());
    this.receiveMobile = StrUtil.emptyIfNull(orderInvoice.getReceiveMobile());
    this.receiveAddress = StrUtil.emptyIfNull(orderInvoice.getReceiveAddress());
    this.remark = StrUtil.emptyIfNull(orderInvoice.getRemark());
  }
}
