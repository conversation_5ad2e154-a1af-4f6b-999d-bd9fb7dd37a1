package com.xhgj.srm.mobile.dto.order;

import com.xhgj.srm.mobile.enums.OrderType;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/06/07 09:32
 */
@Data
public class MobileOrderTableDTO {

  /** 订单类型 */
  @ApiModelProperty("订单类型")
  private OrderType type;

  @ApiModelProperty("订单类型 对应名称")
  private String typeToName;

  @ApiModelProperty("落地商订单")
  private OrderTableDTO order;

  @ApiModelProperty("供应商订单")
  private SupplierOrderTableDTO supplierOrder;

  public MobileOrderTableDTO(
      OrderType type, OrderTableDTO order, SupplierOrderTableDTO supplierOrder) {
    this.type = type;
    this.typeToName = type.getDesc();
    this.order = order;
    this.supplierOrder = supplierOrder;
  }
}
