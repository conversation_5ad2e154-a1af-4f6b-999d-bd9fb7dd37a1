package com.xhgj.srm.mobile.utils;

import static com.xhgj.srm.mobile.service.impl.SupplierUserServiceImpl.VERIFY_ID;

import com.xhiot.boot.redis.util.RedisUtil;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.*;
import java.util.Random;
import java.util.UUID;
import javax.imageio.ImageIO;
import javax.imageio.stream.ImageOutputStream;
import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;

/**
 * @ClassName ImageValidateCode Create by Liuyq on 2021/7/5 9:12
 */
@Slf4j
public class ImageValidateCode {
  // 图片的宽度。
  private int width = 160;
  // 图片的高度。
  private int height = 40;
  // 验证码字符个数
  private int codeCount = 6;
  // 验证码干扰线数
  private int lineCount = 150;
  // 验证码
  private String code = null;
  // 验证码图片Buffer
  private BufferedImage buffImg = null;

  // 验证码范围,去掉0(数字)和O(拼音)容易混淆的(小写的1和L也可以去掉,大写不用了)
  private char[] codeSequence = {'1', '2', '3', '4', '5', '6', '7', '8', '9'};

  /** 默认构造函数,设置默认参数 */
  public ImageValidateCode(HttpServletResponse response, RedisUtil redisUtil) {
    this.createCode(response, redisUtil);
  }

  public void createCode(HttpServletResponse response, RedisUtil redisUtil) {
    int x = 0, fontHeight = 0, codeY = 0;
    int red = 0, green = 0, blue = 0;

    x = width / (codeCount + 2); // 每个字符的宽度(左右各空出一个字符)
    fontHeight = height - 2; // 字体的高度
    codeY = height - 4;

    // 图像buffer
    buffImg = new BufferedImage(width, height, BufferedImage.TYPE_INT_RGB);
    Graphics2D g = buffImg.createGraphics();
    // 生成随机数
    Random random = new Random();
    // 将图像填充为白色
    g.setColor(Color.WHITE);
    g.fillRect(0, 0, width, height);
    // 创建字体,可以修改为其它的
    Font font = new Font("Fixedsys", Font.PLAIN, fontHeight);
    //        Font font = new Font("Times New Roman", Font.ROMAN_BASELINE, fontHeight);
    g.setFont(font);

    for (int i = 0; i < lineCount; i++) {
      // 设置随机开始和结束坐标
      int xs = random.nextInt(width); // x坐标开始
      int ys = random.nextInt(height); // y坐标开始
      int xe = xs + random.nextInt(width / 8); // x坐标结束
      int ye = ys + random.nextInt(height / 8); // y坐标结束

      // 产生随机的颜色值，让输出的每个干扰线的颜色值都将不同。
      red = random.nextInt(255);
      green = random.nextInt(255);
      blue = random.nextInt(255);
      g.setColor(new Color(red, green, blue));
      g.drawLine(xs, ys, xe, ye);
    }

    // randomCode记录随机产生的验证码
    StringBuffer randomCode = new StringBuffer();
    // 随机产生codeCount个字符的验证码。
    for (int i = 0; i < codeCount; i++) {
      String strRand = String.valueOf(codeSequence[random.nextInt(codeSequence.length)]);
      // 产生随机的颜色值，让输出的每个字符的颜色值都将不同。
      red = random.nextInt(255);
      green = random.nextInt(255);
      blue = random.nextInt(255);
      g.setColor(new Color(red, green, blue));
      g.drawString(strRand, (i + 1) * x, codeY);
      // 将产生的四个随机数组合在一起。
      randomCode.append(strRand);
    }
    // 将六位数字的验证码保存到Session中。
    code = randomCode.toString();

    String verid = UUID.randomUUID().toString().replaceAll("-", "");
    //        //将uuid串存入redis
    Cookie cookie = new Cookie(VERIFY_ID, verid);
    response.addCookie(cookie);
    StringBuilder verifyKey = new StringBuilder();
    verifyKey.append(verid);
    log.info("verifyKey.toString()=" + verifyKey.toString());
    //        //将验证码存入redis
    redisUtil.set(verifyKey.toString(), code, 180);
  }

  public void write(OutputStream sos) throws IOException {
    ImageIO.write(buffImg, "png", sos);
    sos.close();
  }

  public BufferedImage getBuffImg() {
    return buffImg;
  }

  public String getCode() {
    return code;
  }

  public InputStream getImageStream(BufferedImage bimage) {
    InputStream is = null;
    ByteArrayOutputStream bs = new ByteArrayOutputStream();
    ImageOutputStream imOut;
    try {
      imOut = ImageIO.createImageOutputStream(bs);
      ImageIO.write(bimage, "png", imOut);
      is = new ByteArrayInputStream(bs.toByteArray());
    } catch (IOException e) {
      e.printStackTrace();
    }
    return is;
  }
}
