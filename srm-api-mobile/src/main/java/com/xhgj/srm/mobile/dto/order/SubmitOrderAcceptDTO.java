package com.xhgj.srm.mobile.dto.order;

import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2023-02-03 15:09
 */
@Data
public class SubmitOrderAcceptDTO {
  @ApiModelProperty("数据信息")
  @NotEmpty(message = "缺少必要参数")
  private List<DataInfo> dataInfo;

  @ApiModelProperty("订单 id")
  private String orderId;

  @ApiModelProperty("上传人")
  @NotBlank(message = "上传人必传")
  private String uploadMan;

  @Data
  public static class DataInfo {
    @ApiModelProperty("签收单信息 id")
    private String id;

    @ApiModelProperty("签收单类型，1-两单,2-签收单")
    @NotBlank(message = "签收单类型必传")
    private String type;

    @ApiModelProperty("文件数组")
    private List<String> fileIdList;
  }
}
