package com.xhgj.srm.api.dto.purchase.order;

import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import lombok.Data;

/**
 * Created by Geng Shy on 2023/12/13
 */
@Data
public class PurchaseOrderInvoiceVO {
  @ApiModelProperty("发货单 id")
  private String id;

  @ApiModelProperty("发货时间")
  private Long deliveryTime;

  @ApiModelProperty("物流公司")
  private String logisticsCompany;

  @ApiModelProperty("物流编码")
  private String logisticsCode;

  @ApiModelProperty("快递单号")
  private String trackNum;

  @ApiModelProperty("单据状态")
  private String state;

  @ApiModelProperty("关联入库单号")
  private String warehouseEntryNumber;

  @ApiModelProperty("是否已入库")
  private Boolean warehousing;

  @ApiModelProperty("物料明细")
  private List<PurchaseOrderInvoiceProductVO> shipProductDTOList;

  @ApiModelProperty("物料凭证号")
  private String productVoucher;

  @ApiModelProperty("关联入库单冲销是否成功")
  private Boolean warehousingEntryReversal;

}
