package com.xhgj.srm.api.dto.enterprise;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class EnterPriseInfoDTO {

    @ApiModelProperty(value = "企业信息")
    private List<EnterPriseInfoDataDTO> enterPriseInfoDataDTOS;

    @ApiModelProperty(value = "总页数")
    private int totalPages;

    @ApiModelProperty(value = "总条数")
    private int totalCount;


}
