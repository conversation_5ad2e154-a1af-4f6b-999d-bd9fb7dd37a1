package com.xhgj.srm.api.dto;

import com.google.errorprone.annotations.DoNotMock;
import com.xhgj.srm.api.dto.supplier.SupplierFileDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import javax.validation.constraints.DecimalMax;
import javax.validation.constraints.DecimalMin;
import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.math.BigDecimal;
import java.util.List;

/** 账号详情的 供应商信息 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class SupplierInformation {

  /** 税率。 */
  @ApiModelProperty("税率")
  private BigDecimal taxRate;

  /** 保证金，精度为20位，小数点后10位。 */
  @ApiModelProperty("保证金")
  private BigDecimal deposit;

  /** 保证金状态 */
  @ApiModelProperty("保证金状态")
  private Boolean depositState;

  /** 账期天数。 */
  @ApiModelProperty("账期天数。")
  private Integer accountPeriod;

  /** 备注 */
  @ApiModelProperty("备注")
  private String notes;

  /** 平台名称 */
  @ApiModelProperty("平台名称")
  private String platformName;

  /** 法人身份证 */
  @ApiModelProperty("法人身份证")
  private FileDTO legalPersonIdCard;

  /** 产品资质书 */
  @ApiModelProperty("产品资质书")
  private List<FileDTO> productQualification;
}
