package com.xhgj.srm.api.dto.supplier;

import com.xhgj.srm.jpa.entity.BaseSupplier;
import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.NotBlank;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2022/7/31 15:12
 */
@Data
@NoArgsConstructor
public abstract class BaseSupplierMainDataDTO {

  @NotBlank(message = "供应商 id 必传！")
  @ApiModelProperty("供应商 id")
  private String id;

  @NotBlank(message = "企业名称必传！")
  @ApiModelProperty("企业名称")
  private String name;

  public BaseSupplierMainDataDTO(BaseSupplier supplier) {
    this.id = supplier.getId();
    this.name = supplier.getEnterpriseName();
  }
}
