package com.xhgj.srm.api.service;

import com.xhgj.srm.api.dto.SupplierPerformanceParam;
import com.xhgj.srm.api.dto.supplierPerformance.SupplierPerformancePageDTO;
import com.xhgj.srm.jpa.entity.SupplierPerformance;
import com.xhiot.boot.framework.jpa.service.BootBaseService;
import com.xhiot.boot.mvc.base.PageResult;
import java.util.List;
import java.util.Map;

public interface SupplierPerformanceService extends BootBaseService<SupplierPerformance, String> {

    /**
     * 批量删除/更新/添加操作
     *
     * @param performances 落地商履约信息
     * @param supplierId 供应商id
     */
    @Deprecated
    void batchUpdatePerformance(List<SupplierPerformanceParam> performances, String supplierId, String userId);

    /**
     * 更新落地商履约状态
     *
     * @param supplierId 供应商id
     */
    void updatePerformanceStatus(String supplierId, String status);

  /**
   * 根据供应商 id 和平台编码获取履约信息
   * @param supplierId 供应商 id 必传
   * @param platformCode 平台编码 必传
   * @return
   */
  SupplierPerformance getFirstBySupplierIdAndPlatformCode(String supplierId,String platformCode);

  /**
   * @param platformCode 平台编码
   * @return 供应商履约信息集合
   */
  List<SupplierPerformance> findAllByPlatformCode(String platformCode);

  /**
   * @param platformCode 平台编码
   * @return 分页履约信息
   */
  PageResult<SupplierPerformancePageDTO> findPageByPlatformCode(String platformCode, int pageNo,
      int pageSize
  );

  /**
   * @param platformCode 平台编码
   * @return 统计开通此平台的的落地商，包含生效的和未生效的。
   */
  long countByPlatformCode(String platformCode);

  /**
   * @param platformCode 平台编码
   * @param platformCode
   * @return
   */
  Map<String, Long> countByPlatformCodeIn(List<String> platformCode);

  /**
   * 根据供应商id删除履约信息
   * @param supplierId 供应商id
   */
  void deleteBySupplierId(String supplierId);

  /**
   * 查询第一个根据合同id
   * @param landingContractId 合同id
   * @return SupplierPerformance
   */
  SupplierPerformance findFirstByLandingContractId(String landingContractId);

  /**
   * 批量查询第一个根据合同ids
   */
  List<SupplierPerformance> findFirstByLandingContractIds(List<String> landingContractIds);

  /**
   * 根据平台编码查询履约信息
   * @param platform
   * @return
   */
  SupplierPerformance findByPlatform(String platform);

  List<SupplierPerformance> findAllByContactsIsNull();

  List<SupplierPerformance> findAllByBusinessLeaderIsNull();
}
