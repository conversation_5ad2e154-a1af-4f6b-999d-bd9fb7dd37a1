package com.xhgj.srm.api.controller.v2.purchaseOrder;/**
 * @since 2025/4/28 14:20
 */

import com.xhgj.srm.api.controller.AbstractRestController;
import com.xhgj.srm.api.dto.SingleBaseParam;
import com.xhgj.srm.api.dto.order.AddOrderAnnexParam;
import com.xhgj.srm.api.dto.purchase.order.PurchaseOrderContractVO;
import com.xhgj.srm.api.service.PurchaseOrderService;
import com.xhgj.srm.api.service.SupplierOrderService;
import com.xhgj.srm.service.SharePurchaseOrderService;
import com.xhiot.boot.mvc.base.ResultBean;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.SneakyThrows;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import javax.annotation.Resource;
import javax.validation.constraints.NotBlank;
import java.net.URLEncoder;

/**
 *<AUTHOR>
 *@date 2025/4/28 14:20:17
 *@description
 */
@RestController
@RequestMapping("/v2/purchaseOrder")
@Validated
@Api(tags = {"采购订单合同文档管理v2 api"})
public class PurchaseOrderV2ContractController extends AbstractRestController  {
  @Resource
  private PurchaseOrderService purchaseOrderService;
  @Resource
  private SupplierOrderService supplierOrderService;
  @Resource
  private SharePurchaseOrderService sharePurchaseOrderService;

  @SneakyThrows
  @ApiOperation(value = "导出采购合同")
  @PostMapping(value = "downloadPurchaseOrderContract", consumes =
      {MediaType.APPLICATION_JSON_VALUE})
  public ResponseEntity<byte[]> downloadSendTickets(@RequestBody SingleBaseParam param) {
    HttpHeaders headers = new HttpHeaders();
    headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);
    headers.setContentDispositionFormData("attachment",
        URLEncoder.encode("purchaseOrderContract" + System.currentTimeMillis() + ".docx", "UTF-8"));
    byte[] bytes = sharePurchaseOrderService.downloadPurchaseOrderContract(param.getId());
    return new ResponseEntity<>(bytes, headers, HttpStatus.CREATED);
  }

  @SneakyThrows
  @ApiOperation(value = "打印采购合同")
  @PostMapping(value = "printOutPurchaseOrderContract", consumes =
      {MediaType.APPLICATION_JSON_VALUE})
  public ResponseEntity<byte[]> printOutPurchaseOrderContract(@RequestBody SingleBaseParam param) {
    HttpHeaders headers = new HttpHeaders();
    headers.setContentType(MediaType.APPLICATION_PDF);
    headers.setContentDispositionFormData("inline",
        URLEncoder.encode("purchaseOrderContract" + System.currentTimeMillis() + ".pdf", "UTF-8"));
    byte[] bytes = purchaseOrderService.printOutPurchaseOrderContract(param.getId());
    return new ResponseEntity<>(bytes, headers, HttpStatus.CREATED);
  }

  @ApiOperation("下载合同附件")
  @GetMapping("contract-attachment")
  @Deprecated
  public ResultBean<String> getContractAttachment(@RequestParam String id) {
    return new ResultBean<>(purchaseOrderService.getContractAttachment(id));
  }

  @ApiOperation(value = "获取订单合同信息", notes = "获取订单合同信息")
  @GetMapping(value = "/getContractFiles")
  public ResultBean<PurchaseOrderContractVO> getContractFiles(@RequestParam String id) {
    return new ResultBean<>(purchaseOrderService.getContractFiles(id));
  }

  @ApiOperation("新增订单附件")
  @PostMapping("addOrderAnnex")
  public ResultBean<String> addOrderAnnex(@Validated @RequestBody
  AddOrderAnnexParam addOrderAnnexParam){
    return new ResultBean<>(supplierOrderService.addOrderAnnex(addOrderAnnexParam));
  }

  @ApiOperation("删除订单附件")
  @PostMapping("deleteOrderAnnex")
  public ResultBean<Boolean> deleteOrderAnnex(@NotBlank String annexId){
    return new ResultBean<>(supplierOrderService.deleteOrderAnnex(annexId));
  }
}
