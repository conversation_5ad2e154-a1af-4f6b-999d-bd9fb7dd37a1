package com.xhgj.srm.api.controller;

import com.xhgj.srm.api.dto.group.AssessGroupDTO;
import com.xhgj.srm.api.dto.supplierorder.VerifyConfigInfoDTO;
import com.xhgj.srm.api.service.VerifyConfigService;
import com.xhiot.boot.mvc.base.ResultBean;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import javax.annotation.Resource;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * VerifyConfigController
 */
@RestController
@RequestMapping("/verifyConfig")
@Api(tags = {"校验配置接口"})
@Slf4j
@Validated
public class VerifyConfigController {
  @Resource
  private VerifyConfigService service;

  @GetMapping
  @ApiOperation("获取配置列表")
  public ResultBean<List<VerifyConfigInfoDTO>> getAllConfig() {
    return new ResultBean<>(service.getAllConfig());
  }

  @GetMapping(value = "/getVerifyConfigInfo")
  @ApiOperation("获取采购订单校验配置信息")
  public ResultBean<VerifyConfigInfoDTO> getVerifyConfigInfo() {
    return new ResultBean<>(service.getVerifyConfigInfo());
  }

  @PostMapping(value = "/enableConfig")
  @ApiOperation("开启或关闭配置")
  public ResultBean<Boolean> enableConfig(
      @RequestParam @NotBlank(message = "配置id不能为空") String id,
      @RequestParam @NotNull(message = "开启或关闭配置不能为空") Boolean enable) {
    service.enableConfig(id,enable);
    return new ResultBean<>(Boolean.TRUE);
  }


  @PostMapping(value = "/updateOrganizationRole")
  @ApiOperation("编辑配置作用组织")
  public ResultBean<Boolean> updateOrganizationRole(
      @RequestBody VerifyConfigInfoDTO param) {
    service.updateOrganizationRole(param);
    return new ResultBean<>(Boolean.TRUE);
  }

  @GetMapping(value = "/getAllGroupsByKeyWord")
  @ApiOperation("根据组织或编码查询组织列表")
  public ResultBean<List<AssessGroupDTO>> getAllGroupsByKeyWord(@RequestParam String keyword) {
    return new ResultBean<>(service.getAllGroupsByKeyWord(keyword));
  }
}
