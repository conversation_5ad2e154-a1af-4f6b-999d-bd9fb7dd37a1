package com.xhgj.srm.api.controller.dataProcess;/**
 * @since 2025/6/3 10:46
 */

import com.xhgj.srm.api.controller.AbstractRestController;
import com.xhgj.srm.jpa.entity.User;
import com.xhgj.srm.migration.service.ApplyForSupplierOrderMigrationCoordinator;
import com.xhgj.srm.migration.service.SupplierOrderMigrationCoordinator;
import com.xhiot.boot.mvc.base.ResultBean;
import io.swagger.annotations.ApiOperation;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;
import javax.annotation.Resource;

/**
 *<AUTHOR>
 *@date 2025/6/3 10:46:39
 *@description
 */
@RestController
@RequestMapping("/dataMigration")
public class DataMigrationController extends AbstractRestController {

  @Resource
  SupplierOrderMigrationCoordinator supplierOrderMigrationCoordinator;
  @Resource
  ApplyForSupplierOrderMigrationCoordinator applyForSupplierOrderMigrationCoordinator;

  /**
   * 迁移供应商订单
   * @param supplierOrderId
   */
  @ApiOperation(value = "迁移供应商订单", tags = "0.7.0")
  @PostMapping("/migrate")
  public void process(@RequestParam String supplierOrderId) {
    supplierOrderMigrationCoordinator.migrateSupplierOrder(supplierOrderId);
  }

  /**
   * 迁移供应商订单回滚
   * @param supplierOrderId
   * @param newSupplierOrderId
   */
  @ApiOperation(value = "迁移供应商订单回滚", tags = "0.7.0")
  @PostMapping("/rollback")
  public void rollback(
      @RequestParam String supplierOrderId,
      @RequestParam String newSupplierOrderId) {
    supplierOrderMigrationCoordinator.migrateSupplierOrderRollback(supplierOrderId, newSupplierOrderId);
  }

  /**
   * 导入迁移的供应商订单
   * @param file
   */
  @ApiOperation(value = "导入迁移的供应商订单", tags = "0.7.0")
  @PostMapping(value = "/import", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
  public ResultBean<Boolean> importSupplierOrderFile(MultipartFile file) {
    if (file == null || file.isEmpty()) {
      throw new IllegalArgumentException("文件不能为空");
    }
    User user = getUser();
    supplierOrderMigrationCoordinator.importFile(file, user);
    return new ResultBean<>(true);
  }

  /**
   * 迁移采购申请单
   * @param applyForNo
   */
  @ApiOperation(value = "迁移采购申请单", tags = "0.7.0")
  @PostMapping("/migrateApplyFor")
  public void migrateApplyFor(@RequestParam String applyForNo) {
    applyForSupplierOrderMigrationCoordinator.migrateApplyForSupplierOrder(applyForNo);
  }

  /**
   * 迁移采购申请单回滚
   * @param applyForNo
   */
  @ApiOperation(value = "迁移采购申请单回滚", tags = "0.7.0")
  @PostMapping("/rollbackApplyFor")
  public void rollbackApplyFor(@RequestParam String applyForNo) {
    applyForSupplierOrderMigrationCoordinator.migrateApplyForSupplierOrderRollback(applyForNo);
  }

  /**
   * 导入迁移的采购申请单
   */
  @ApiOperation(value = "导入迁移的采购申请单", tags = "0.7.0")
  @PostMapping(value = "/importApplyFor", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
  public ResultBean<Boolean> importApplyFor(MultipartFile file) {
    if (file == null || file.isEmpty()) {
      throw new IllegalArgumentException("文件不能为空");
    }
    User user = getUser();
    applyForSupplierOrderMigrationCoordinator.importFile(file, user);
    return new ResultBean<>(true);
  }
}
