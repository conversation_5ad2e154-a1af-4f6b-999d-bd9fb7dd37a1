package com.xhgj.srm.api.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.CharUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.xhgj.srm.api.dto.CheckReversalInventoryParam;
import com.xhgj.srm.api.dto.InventoryLocationDTO;
import com.xhgj.srm.api.dto.InventoryLocationQueryForm;
import com.xhgj.srm.api.dto.InventoryLocationUpdateParam;
import com.xhgj.srm.api.service.InventoryLocationService;
import com.xhgj.srm.api.service.MissionService;
import com.xhgj.srm.common.Constants;
import com.xhgj.srm.jpa.dao.InventoryLocationDao;
import com.xhgj.srm.jpa.entity.InventoryLocation;
import com.xhgj.srm.jpa.entity.Mission;
import com.xhgj.srm.jpa.entity.User;
import com.xhgj.srm.jpa.repository.InventoryLocationRepository;
import com.xhgj.srm.jpa.repository.MissionRepository;
import com.xhgj.srm.jpa.sharding.util.ShardingContext;
import com.xhgj.srm.mission.common.MissionTypeEnum;
import com.xhgj.srm.mission.dispatcher.MissionDispatcher;
import com.xhgj.srm.request.dto.mdm.OrganizationStockDTO;
import com.xhgj.srm.request.service.third.xhgj.XhgjPersonRequest;
import com.xhgj.srm.util.ImportExcelUtil;
import com.xhiot.boot.core.common.exception.CheckException;
import com.xhiot.boot.framework.jpa.repository.BootBaseRepository;
import com.xhiot.boot.mvc.base.PageResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;
import javax.annotation.Resource;
import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * InventoryLocationServiceImpl
 */
@Service
@Slf4j
public class InventoryLocationServiceImpl implements InventoryLocationService {
  @Resource
  private InventoryLocationRepository repository;

  @Resource
  private InventoryLocationDao inventoryLocationDao;

  @Resource
  private MissionRepository missionRepository;

  @Resource
  private MissionService missionService;

  @Resource
  private MissionDispatcher missionDispatcher;

  @Resource
  private XhgjPersonRequest xhgjPersonRequest;

  @Resource
  private ImportExcelUtil importExcelUtil;

  @Override
  public BootBaseRepository<InventoryLocation, String> getRepository() {
    return repository;
  }

  @Override
  public PageResult<InventoryLocationDTO> getInventoryLocationList(
      InventoryLocationQueryForm param) {
    Page<InventoryLocation> inventoryLocationPage =
        inventoryLocationDao.getInventoryLocationList(param.toQueryMap());
    List<InventoryLocation> content = inventoryLocationPage.getContent();
    if (CollUtil.isEmpty(content)) {
      return PageResult.empty(param.getPageNo(), param.getPageSize());
    }
    List<InventoryLocationDTO> result = content.stream().map(InventoryLocationDTO::new).collect(Collectors.toList());
    return new PageResult<>(result, inventoryLocationPage.getTotalElements(), inventoryLocationPage.getTotalPages(),
        param.getPageNo(), param.getPageSize());
  }

  @Override
  @Transactional(rollbackFor = Exception.class)
  public void batchUpdateInventoryLocation(InventoryLocationUpdateParam param) {
    if (StrUtil.equals(Constants.STATE_OK,param.getIsWms())&& StrUtil.isBlank(param.getBusinessType())) {
      throw new CheckException("选择涉及WMS为是时，涉及WMS的业务类型必填！");
    }
    List<InventoryLocation> inventoryLocationList = repository.findAllById(param.getIds());
    inventoryLocationList.forEach(inventoryLocation -> {
      inventoryLocation.setIsWms(param.getIsWms());
      inventoryLocation.setBusinessType(param.getBusinessType());
      inventoryLocation.setUpdateTime(System.currentTimeMillis());
    });
    repository.saveAll(inventoryLocationList);
  }

  @Override
  public void syncInventoryLocationListByMDM(User user) {
    if (missionRepository.existsByTypeAndState(Constants.MISSION_TYPE_NAME_INVENTORY_LOCATION,
        Constants.MISSION_STATE_ING)) {
      throw new CheckException("有进行中的任务请等待");
    }
    Mission mission =
        missionService.createMission(
            user, Constants.MISSION_TYPE_NAME_INVENTORY_LOCATION, Constants.PLATFORM_TYPE_AFTER, null, null);
    Map<String, Object> mapParam = new HashMap<>(1);
    mapParam.put("userId", user.getId());
    missionDispatcher.doDispatch(
        mission.getId(),
        JSON.toJSONString(mapParam),
        MissionTypeEnum.BATCH_TASK_SYNC_INVENTORY_LOCATION_LIST);
  }

  @Override
  public void syncInventoryLocationListTask() {
    long startTime = System.currentTimeMillis(); // 记录开始时间
    List<OrganizationStockDTO> organizationStockDTOList;
    try {
      organizationStockDTOList = xhgjPersonRequest.getAllOrgAndStock();
    } catch (RuntimeException e) {
      throw new CheckException("信息化人员信息服务异常,请稍候再试");
    }
    if (CollUtil.isEmpty(organizationStockDTOList))return;
    List<InventoryLocation> resultList =  new ArrayList<>();
    for (OrganizationStockDTO organizationStockDTO : organizationStockDTOList) {
      String groupCode = organizationStockDTO.getGroupCode();
      String groupName = organizationStockDTO.getGroupName();
      String sapStockAddr = organizationStockDTO.getSapStockAddr();
      String sapStockAddrCode = organizationStockDTO.getSapStockAddrCode();
      List<String> addrList = StrUtil.split(sapStockAddr, CharUtil.COMMA, true, true);
      List<String> addrCodeList = StrUtil.split(sapStockAddrCode, CharUtil.COMMA, true, true);
      if (CollUtil.isEmpty(addrList) || CollUtil.isEmpty(addrCodeList)
          || addrList.size() != addrCodeList.size()) {
        continue;
      }
      // 遍历并创建或更新InventoryLocation对象
      for (int i = 0; i < addrCodeList.size(); i++) {
        String warehouseName = addrList.get(i);
        String warehouse = addrCodeList.get(i);
        InventoryLocation inventoryLocation =
            repository.findFirstByGroupCodeAndWarehouseAndState(groupCode, warehouse,
                Constants.STATE_OK).orElse(null);
        if (inventoryLocation != null) {
          inventoryLocation.setGroupCode(groupCode);
          inventoryLocation.setGroupName(groupName);
          inventoryLocation.setWarehouse(warehouse);
          inventoryLocation.setWarehouseName(warehouseName);
          inventoryLocation.setUpdateTime(System.currentTimeMillis());
        }else {
          inventoryLocation = new InventoryLocation();
          inventoryLocation.setGroupCode(groupCode);
          inventoryLocation.setGroupName(groupName);
          inventoryLocation.setWarehouse(warehouse);
          inventoryLocation.setWarehouseName(warehouseName);
          inventoryLocation.setUpdateTime(System.currentTimeMillis());
          inventoryLocation.setCreateTime(System.currentTimeMillis());
          inventoryLocation.setState(Constants.STATE_OK);
        }
        resultList.add(inventoryLocation);
      }
    }
    repository.saveAll(resultList);
    log.info("定时任务同步MDM库位列表完成，数量:{},耗时:{}ms", organizationStockDTOList.size(), System.currentTimeMillis() - startTime);

  }

  @Override
  public void importInventoryLocation(MultipartFile file, User user) {
    String savePath = null;
    try {
      savePath = importExcelUtil.saveExcel(file);
    } catch (IOException e) {
      throw new RuntimeException(e);
    }
    Mission mission = missionService.createMission(user,
        MissionTypeEnum.BATCH_TASK_IMPORT_INVENTORY_LOCATION.getTypeName(), Constants.PLATFORM_TYPE_AFTER,
        null, null);
    Map<String, Object> mapParam = new HashMap<>(4);
    mapParam.put("userId", user.getId());
    mapParam.put("filePath", savePath);
    mapParam.put("fileName", file.getOriginalFilename());
    mapParam.put("version", ShardingContext.getVersion());
    missionDispatcher.doDispatch(
        mission.getId(),
        JSON.toJSONString(mapParam),
        MissionTypeEnum.BATCH_TASK_IMPORT_INVENTORY_LOCATION);
  }

  @Override
  public void checkInventoryLocation(CheckReversalInventoryParam param) {
    InventoryLocation inventoryLocation =
        repository.findFirstByGroupCodeAndWarehouseAndState(param.getGroupCode(),
            param.getWarehouse(), Constants.STATE_OK).orElse(null);
    if (StrUtil.equals(param.getInboundReturnReversal(), Constants.STATE_OK)) {
      if (inventoryLocation == null || !StrUtil.equals(inventoryLocation.getInboundReturnReversal(), Constants.STATE_OK)) {
        throw new CheckException("此仓库不允许在SRM操作冲销，请联系仓管人员去SAP操作！");
      }
    }
    //通过订单采购组织&仓库编码，查询此仓库在库位管理中，涉及WMS的业务类型是否有采购订
    if (BooleanUtil.isTrue(param.getIsWms()) && inventoryLocation != null) {
      if (StrUtil.contains(inventoryLocation.getBusinessType(),Constants.STATE_OK)) {
        throw new CheckException(String.format("%s库位：在WMS中管理，请联系您公司的仓库同事在WMS入库！", param.getWarehouse()));
      }
    }

  }
}

