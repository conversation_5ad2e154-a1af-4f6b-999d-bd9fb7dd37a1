package com.xhgj.srm.api.dto.supplier;

import com.xhgj.srm.jpa.entity.Supplier;
import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.NotBlank;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class ProvisionalSupplierPageDTO {
  @ApiModelProperty("供应商id")
  private String supplierId;

  @ApiModelProperty("mdm编码")
  private String mdmCode;

  @ApiModelProperty("企业名称")
  private String supplierName;

  @ApiModelProperty("入库时间")
  private Long createTime;

  public ProvisionalSupplierPageDTO(Supplier supplier) {
    this.supplierId = supplier.getId();
    this.mdmCode = supplier.getMdmCode();
    this.supplierName = supplier.getEnterpriseName();
    this.createTime = supplier.getCreateTime();
  }
}
