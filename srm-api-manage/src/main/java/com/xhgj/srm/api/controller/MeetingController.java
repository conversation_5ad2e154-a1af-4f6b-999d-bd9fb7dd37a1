package com.xhgj.srm.api.controller;



import com.xhgj.srm.api.dto.MeetingListPage;
import com.xhgj.srm.api.dto.MeetingListPageData;
import com.xhgj.srm.api.service.FileService;
import com.xhgj.srm.api.service.MeetingService;
import com.xhgj.srm.api.service.UserService;
import com.xhiot.boot.mvc.base.ResultBean;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.SneakyThrows;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.net.URLEncoder;

@Api(tags = {"会议管理接口"})
@RestController
@RequestMapping("/meeting")
public class MeetingController {

    @Autowired
    UserService userService;
    @Autowired
    MeetingService meetingService;
    @Autowired
    FileService fileService;

    @ApiOperation(value = "分页获取会议列表", notes = "分页获取会议列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "userGroup", value = "使用组织编码"),
            @ApiImplicitParam(name = "enterpriseName", value = "供应商名称"),
            @ApiImplicitParam(name = "starttime", value = "开始时间"),
            @ApiImplicitParam(name = "endtime", value = "结束时间"),
            @ApiImplicitParam(name = "meetingName", value = "名称"),
            @ApiImplicitParam(name = "personnel", value = "参会人员"),
            @ApiImplicitParam(name = "schemeId", value = "检索id"),
            @ApiImplicitParam(name = "userId", value = "用户id"),
            @ApiImplicitParam(name = "pageNo", value = "当前页"),
            @ApiImplicitParam(name = "pageSize", value = "每页展示数量")
    })
    @RequestMapping(value = "/getMeetingResourcePage", method = RequestMethod.GET)
    public ResultBean<?> getMeetingResourcePage(
            String userGroup, String enterpriseName, String starttime, String endtime, String meetingName,
            String personnel,String userId,String schemeId,
            @RequestParam(defaultValue = "1") Integer pageNo,
            @RequestParam(defaultValue = "10") Integer pageSize
    ) {
        return new ResultBean<>(meetingService.getMeetingPage(userGroup, enterpriseName, starttime, endtime, meetingName, personnel, userId,schemeId,pageNo, pageSize));
    }

    @ApiOperation(value = "添加会议纪要", notes = "添加会议纪要")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "supplierid", value = "供应商id(与供应商名称二选一，必填其一)"),
            @ApiImplicitParam(name = "supplierName", value = "供应商名称(与供应商id二选一，必填其一)"),
            @ApiImplicitParam(name = "name", value = "名称"),
            @ApiImplicitParam(name = "place", value = "会议地点"),
            @ApiImplicitParam(name = "personnel", value = "参会人员"),
            @ApiImplicitParam(name = "depart", value = "参会部门"),
            @ApiImplicitParam(name = "userGroup", value = "组织编码"),
            @ApiImplicitParam(name = "meettime", value = "会议日期"),
            @ApiImplicitParam(name = "file", value = "附件", required = true),
            @ApiImplicitParam(name = "userId", value = "登录人id", required = true)
    })
    @RequestMapping(value = "/addMeeting", method = RequestMethod.POST)
    public ResultBean<Boolean> addMeeting(
            String supplierid, String supplierName, String name, String place, String personnel, String depart, String meettime, String file, String userId,String userGroup
    ) {
        meetingService.addMeeting(supplierid, supplierName, name, place, personnel, depart, meettime, file, userId,userGroup);
        return new ResultBean<>(true, "操作成功!");
    }


    @ApiOperation(value = "修改会议纪要", notes = "修改会议纪要")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "meetingid", value = "会议id", required = true),
            @ApiImplicitParam(name = "supplierId", value = "供应商id(与供应商名称二选一，必填其一)"),
            @ApiImplicitParam(name = "supplierName", value = "供应商名称(与供应商id二选一，必填其一)"),
            @ApiImplicitParam(name = "userId", value = "用户id"),
            @ApiImplicitParam(name = "name", value = "名称"),
            @ApiImplicitParam(name = "place", value = "会议地点"),
            @ApiImplicitParam(name = "personnel", value = "参会人员"),
            @ApiImplicitParam(name = "depart", value = "参会部门"),
            @ApiImplicitParam(name = "meettime", value = "会议日期"),
            @ApiImplicitParam(name = "file", value = "附件")
    })
    @RequestMapping(value = "/updateMeeting", method = RequestMethod.POST)
    public ResultBean<Boolean> updateMeeting(
            String meetingid,String supplierId,String supplierName, String name, String place, String personnel, String depart, String meettime,
            String file,String userId
    ) {
        meetingService.updateMeeting(meetingid,  name,  place,  personnel,  depart,  meettime,  file,supplierId,supplierName,userId);
        return new ResultBean<>(true, "操作成功!");
    }

    /**
     * <p>Title：删除会议纪要</p>
     * <p>Description: <p>
     *
     * @return
     * <AUTHOR>
     * @date 2019年8月8日 上午10:15:20
     */
    @ApiOperation(value = "删除会议纪要", notes = "删除会议纪要")
    @ApiImplicitParam(name = "meetingids", value = "会议id", required = true)
    @RequestMapping(value = "/delMeeting", method = RequestMethod.POST)
    public ResultBean<Boolean> delMeeting(
            String meetingids,String userId
    ) {
        meetingService.delMeeting(meetingids,userId);
        return new ResultBean<>(true, "操作成功!");
    }

    @ApiOperation(value = "获取会议列表(下拉选择)", notes = "获取会议列表(下拉选择)")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "erpCode", value = "使用组织编码", required = true),
            @ApiImplicitParam(name = "name", value = "名称"),
    })
    @RequestMapping(value = "/getMeetingList", method = RequestMethod.GET)
    public ResultBean<MeetingListPage<MeetingListPageData>> getMeetingList(
            String erpCode, String name
    ) {
        return new ResultBean<>(meetingService.getMeetingList(erpCode,name));
    }


    @ApiOperation(value = "上传会议附件", notes = "上传会议附件")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "会议id", required = true),
            @ApiImplicitParam(name = "files", value = "文件", required = true),
    })
    @RequestMapping(value = "/uploadFile", method = RequestMethod.POST)
    public ResultBean<Boolean> uploadFile(
            String id, String files
    ) {

        meetingService.uploadFile(id,  files,  null);
        return new ResultBean<>(true, "操作成功!");
    }

    @ApiOperation(value = "删除会议附件", notes = "删除会议附件")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "附件id", required = true),
            @ApiImplicitParam(name = "meetingId", value = "会议号id", required = true)
    })
    @RequestMapping(value = "/deleteFileById", method = RequestMethod.POST)
    public ResultBean<Boolean> deleteFileById(
            String id, String meetingId
    ) {
        meetingService.deleteFileById(id, meetingId);
        return new ResultBean<>(true, "操作成功!");
    }

    @SneakyThrows
    @ApiOperation(value = "导入会议", notes = "导入会议")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "erpCode", value = "用户id"),
    })
    @PostMapping("/importMeeting")
    public ResultBean<Boolean> importMeeting(
            MultipartFile file,
            String userId
    ) {
        String originalFilename = file.getOriginalFilename();
        meetingService.addMeetingExcelSave(originalFilename,file.getInputStream(),userId);
        return new ResultBean<>(true, "操作成功！");
    }

    @ApiOperation(value = "会议信息多组织同步", notes = "会议信息多组织同步")
    @RequestMapping(value = "/synMeetingGroupInfo", method = RequestMethod.POST)
    public ResultBean<Boolean> synMeetingGroupInfo() {
        meetingService.synMeetingGroupInfo();
        return new ResultBean<>(true, "操作成功!");
    }

}
