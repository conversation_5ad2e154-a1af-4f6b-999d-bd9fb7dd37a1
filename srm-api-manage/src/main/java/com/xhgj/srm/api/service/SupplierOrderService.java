package com.xhgj.srm.api.service;

import com.xhgj.srm.api.dto.order.AddOrderAnnexParam;
import com.xhgj.srm.api.dto.supplierorder.ConfigShortMessageDTO;
import com.xhgj.srm.api.dto.supplierorder.ExportOrderDetailsParams;
import com.xhgj.srm.api.dto.supplierorder.NoticeAssessResultParams;
import com.xhgj.srm.api.dto.supplierorder.OrderProgressBarTimeDTO;
import com.xhgj.srm.api.dto.supplierorder.ReminderProductShipParams;
import com.xhgj.srm.api.dto.supplierorder.ReturnOrCancelDetailDTO;
import com.xhgj.srm.api.dto.supplierorder.ReturnOrCancelFormDTO;
import com.xhgj.srm.api.dto.supplierorder.ReturnOrCancelFormQuery;
import com.xhgj.srm.api.dto.supplierorder.ReturnOrCancelParams;
import com.xhgj.srm.api.dto.supplierorder.SaveSupplierOrderParams;
import com.xhgj.srm.api.dto.supplierorder.SupplierOrderCancelDTO;
import com.xhgj.srm.api.dto.supplierorder.SupplierOrderCountDTO;
import com.xhgj.srm.api.dto.supplierorder.SupplierOrderDetailedDTO;
import com.xhgj.srm.api.dto.supplierorder.SupplierOrderListDTO;
import com.xhgj.srm.api.dto.supplierorder.SupplierOrderPageQuery;
import com.xhgj.srm.api.dto.supplierorder.SupplierOrderReturnDTO;
import com.xhgj.srm.api.dto.supplierorder.SupplierOrderShipDTO;
import com.xhgj.srm.api.dto.supplierorder.UpdateReturnFormParams;
import com.xhgj.srm.api.dto.supplierorder.UpdateShipFormParams;
import com.xhgj.srm.api.dto.supplierorder.UpdateStockQtyParams;
import com.xhgj.srm.common.dto.ConfirmNeedNoticeCarDTO;
import com.xhgj.srm.common.enums.supplierorder.SupplierOrderFormStatus;
import com.xhgj.srm.common.enums.supplierorder.SupplierOrderFormType;
import com.xhgj.srm.jpa.entity.SupplierOrder;
import com.xhgj.srm.jpa.entity.SupplierOrderDetail;
import com.xhgj.srm.jpa.entity.SupplierOrderToForm;
import com.xhgj.srm.jpa.entity.User;
import com.xhiot.boot.framework.jpa.service.BootBaseService;
import com.xhiot.boot.mvc.base.PageResult;
import java.math.BigDecimal;
import java.util.List;
import org.springframework.data.domain.Pageable;
import org.springframework.web.multipart.MultipartFile;
import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @since 2022/11/27 15:29
 */
public interface SupplierOrderService extends BootBaseService<SupplierOrder, String> {

  void sendExpediedSms(String supplierOrderId);

  /**
   * erp 取消
   *
   * @param supplierOrder 供应商订单 必传
   * @param detailList    详情 必传
   */
  void erpCancel(SupplierOrder supplierOrder, List<SupplierOrderDetail> detailList);

  /**
   * 创建供应商订单的表单
   * @param supplierOrder 供应商订单 必传
   * @param supplierOrderDetailList 表单详细
   * @param type 表单类型 必传
   * @param status 订单状态
   * @param time 时间
   * @param stockOutPutForm 是否退货退库
   * @param returnPrice 退回金额
   * @param num 数量
   * @param logisticsCompany 物流公司
   * @param logisticsCode 物流编码
   * @param logisticsCode 物流单号
   * @param remark 备注
   */
  SupplierOrderToForm createSupplierOrderToForm(
      SupplierOrder supplierOrder,
      List<SupplierOrderDetail> supplierOrderDetailList,
      SupplierOrderFormType type,
      SupplierOrderFormStatus status,
      long time,
      Boolean stockOutPutForm,
      BigDecimal returnPrice,
      BigDecimal num,
      String logisticsCompany,
      String logisticsCode,
      String trackNum,
      String remark);

  /**
   * 保存或修改销售订单信息
   *
   * @param saveSupplierOrderParams 参数 必传
   */
  void saveOrUpdateSupplierOrder(SaveSupplierOrderParams saveSupplierOrderParams);
  /**
   * 获得入库进度 采购入库数量/总数量-取消数量-退货数量
   *
   * @param totalNum           总数量 必传
   * @param totalStockInputQty 采购入库件数 必传
   * @param supplierOrderId    供应商订单 id 必传
   */
  void setStockProgress(
      SupplierOrder supplierOrder,
      BigDecimal totalNum,
      BigDecimal totalStockInputQty,
      String supplierOrderId);

  /**
   * ERP采购订单入库数量变动
   *
   * @param updateStockQtyParams 参数 必传
   */
  void updateStockQty(UpdateStockQtyParams updateStockQtyParams);

  /**
   * 采购退料单审核结果通知 SRM
   *
   * @param noticeAssessResultParams 参数 必传
   */
  void noticeAssessResult(NoticeAssessResultParams noticeAssessResultParams);

  /**
   * 分页获取供应商订单
   *
   * @param query 查询参数
   * @param toPageable 分页参数 必传
   * @param user 用户信息 必传
   */
  PageResult<SupplierOrderListDTO> getPageSupplierOrderPage(
      User user, SupplierOrderPageQuery query, Pageable toPageable);

  /**
   * 根据供应商订单 id 获得订单明细
   *
   * @param id 供应商订单 id 必传
   */
  SupplierOrderDetailedDTO getSupplierOrderDetailedDTOById(String id);

  /**
   * 根据供应商订单 id 获得发货单
   *
   * @param id 供应商订单 id 必传
   */
  List<SupplierOrderShipDTO> getSupplierOrderShipDTO(String id);

  /**
   * 确认发货单
   *
   * @param orderToFormId 发货单 id 必传
   */
  String confirmReceipt(String orderToFormId,User user);

  /** 获取供应商订单的数量（待履约、履约中）
   * @param user 用户
   */
  SupplierOrderCountDTO getSupplierOrderCount(User user);

  /**
   * 整单取消
   *
   * @param supplierOrderId 供应商订单 id 必传
   */
  void allCancel(String supplierOrderId);

  /**
   * 退货取消
   *
   * @param returnOrCancelParams 退货/取消参数 必传
   */
  void returnOrCancel(ReturnOrCancelParams returnOrCancelParams);

  /**
   * 根据供应商订单 id 获得退货单
   *
   * @param id 供应商订单 id 必传
   */
  List<SupplierOrderReturnDTO> getSupplierOrderReturnDTOById(String id);
  /**
   * 根据供应商订单 id 获得取消单
   *
   * @param id 供应商订单 id 必传
   */
  List<SupplierOrderCancelDTO> getSupplierOrderCancelDTOById(String id);

  /**
   * 撤销退货单
   *
   * @param orderToFormId 退货单 id 必传
   */
  void revokeReturn(String orderToFormId);

  /**
   * 修改退货单
   *
   * @param updateReturnFormParams 修改退货单信息
   */
  void updateReturnForm(UpdateReturnFormParams updateReturnFormParams);

  /**
   * 获取订单进度时间
   *
   * @param id 供应商订单 id 必传
   */
  OrderProgressBarTimeDTO getOrderProgressBarTime(String id);

  /**
   * 修改入库单
   *
   * @param updateShipFormParams 必传
   */
  void updateShipForm(UpdateShipFormParams updateShipFormParams);

  /**
   * 分页查询退货取消单
   *
   * @param user 用户 必传
   * @param returnOrCancelFormQuery 查询条件
   * @param toPageable 分页参数 必传
   */
  PageResult<ReturnOrCancelFormDTO> getReturnOrCancelPage(
      User user, ReturnOrCancelFormQuery returnOrCancelFormQuery, Pageable toPageable);

  /**
   * 获得退货取消表单信息
   *
   * @param formId 表单 id 必传
   */
  ReturnOrCancelDetailDTO getReturnOrCancelDetail(String formId);

  /**
   * 配置短信服务
   *
   * @param params 参数必传
   */
  void configShortMessage(List<ConfigShortMessageDTO> params);

  /**
   * 获取短信服务的配置
   */
  List<ConfigShortMessageDTO> getConfigShortMessage();

  /**
   * 催单短信
   * @param supplierOrderId 供应商订单 id 必传
   */
  void reminder(String supplierOrderId);

  /**
   * 定时发送催单短信
   */
  void sendReminderSmsTask();

  /**
   * 导出供应商订单明细
   * @param id 供应商订单 id 必传
   * @param user 用户信息 必传
   * @param userGroup 组织编码 必传
   */
  void exportOrderDetails(String id,User user ,String userGroup);

  /**
   * 导出供应商订单
   * @param exportOrderDetailsParams 参数 必传
   * @param user 用户信息 必传
   */
  void exportSupplierOrder(ExportOrderDetailsParams exportOrderDetailsParams);

  /**
   * 取消发货单
   * @param orderToFormId 发货单 id 必传
   */
  void cancelShipForm(String orderToFormId);

  /**
   * 向商城保存物流信息
   * @param supplierToFormId 单据 id 必传
   */
  void saveExpressInfo(String supplierToFormId);

  /**
   * 提醒物料发货
   * @param params 参数 必传
   */
  void reminderProductShip(ReminderProductShipParams params);

  /**
   * 上传订单附件
   * @param addOrderAnnexParam addOrderAnnexParam
   * @return 保存成功的附件id
   */
  String addOrderAnnex(AddOrderAnnexParam addOrderAnnexParam);

  /**
   * 删除订单附件
   * @param annexId 附件id
   * @return 是否删除成功
   */
  Boolean deleteOrderAnnex(String annexId);

  /**
   * 挂起供应商订单
   * @param supplierOrderId 供应商订单id
   */
  void suspendOrder(String supplierOrderId);

  /**
   * 拒绝供应商拒单申请
   * @param supplierOrderId 供应商订单id
   */
  void refusalOfApplyForRefusalOrder(String supplierOrderId);
  /**
   * 拒单审核钉钉回调
   * @param confirmNeedNoticeCarDTO
   */
  void refuseOrderCallback(ConfirmNeedNoticeCarDTO confirmNeedNoticeCarDTO);

  void dataHandle(MultipartFile file);

  /**
   * 批量删除暂存的采购订单
   * @param ids
   */
  void delSupplierOrderBatch(List<String> ids, String userId);

  /**
   * 删除驳回的采购订单
   * @param ids
   */
  void delRejectSupplierOrder(List<String> ids, String userId);
}
