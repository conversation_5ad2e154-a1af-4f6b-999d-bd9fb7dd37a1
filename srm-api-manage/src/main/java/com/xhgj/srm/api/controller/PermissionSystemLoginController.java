package com.xhgj.srm.api.controller;

import com.xhgj.srm.api.service.PermissionSystemLoginService;
import com.xhiot.boot.security.component.BootLoginHandler;
import com.xhiot.boot.security.dto.TokenDTO;
import javax.annotation.Resource;
import org.springframework.web.bind.annotation.RestController;

/**
 * Created by Geng Shy on 2023/10/23
 */
@RestController
public class PermissionSystemLoginController implements BootLoginHandler {
  @Resource
  PermissionSystemLoginService service;

  @Override
  public TokenDTO login(String username, String password) {
    return service.login(username, password);
  }
}
