package com.xhgj.srm.api.dto.group;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

/** <AUTHOR> @ClassName CheckIsExistSupplierByGroupDTO */
@Data
@NoArgsConstructor
public class CheckIsExistSupplierByGroupDTO {
  @ApiModelProperty("组织id")
  private String groupId;

  @ApiModelProperty("是否存在")
  private Boolean isExist;

  public CheckIsExistSupplierByGroupDTO(String groupId, Boolean isExist) {
    this.groupId = groupId;
    this.isExist = isExist;
  }
}
