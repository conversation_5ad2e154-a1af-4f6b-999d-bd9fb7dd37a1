package com.xhgj.srm.api.controller.v2.purchaseOrder;/**
 * @since 2025/4/28 14:20
 */

import cn.hutool.core.collection.CollUtil;
import com.xhgj.srm.api.constants.ConstantsLockByUserForType;
import com.xhgj.srm.api.controller.AbstractRestController;
import com.xhgj.srm.api.dto.*;
import com.xhgj.srm.api.dto.purchase.order.*;
import com.xhgj.srm.api.service.PurchaseOrderService;
import com.xhgj.srm.jpa.entity.User;
import com.xhgj.srm.service.InitialPurchaseOrderService;
import com.xhgj.srm.v2.dto.UpdateSupplierOrderBaseInfoV2DTO;
import com.xhgj.srm.v2.form.PurchaseOrderAddV2Form;
import com.xhgj.srm.v2.form.purchaseOrder.purchaseOrderExport;
import com.xhgj.srm.v2.service.purchaseOrder.PurchaseOrderV2BaseService;
import com.xhgj.srm.v2.vo.PurchaseOrderV2DetailedVO;
import com.xhgj.srm.v2.vo.purchaseOrder.SupplierOrderSyncVO;
import com.xhiot.boot.mvc.base.ResultBean;
import com.xhiot.boot.mvc.lock.BootLockByUserFor;
import com.xhiot.boot.repeat.annotation.RepeatSubmit;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import javax.annotation.Resource;
import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 *<AUTHOR>
 *@date 2025/4/28 14:20:17
 *@description
 */
@RestController
@RequestMapping("/v2/purchaseOrder")
@Validated
@Api(tags = {"采购订单基础管理v2 api"})
public class PurchaseOrderV2BaseController extends AbstractRestController  {
  @Resource
  private PurchaseOrderService purchaseOrderService;
  @Resource
  private PurchaseOrderV2BaseService purchaseOrderV2BaseService;
  @Resource
  private InitialPurchaseOrderService initialPurchaseOrderService;

  @ApiOperation(value = "新增采购订单", notes = "新增采购订单")
  @PostMapping(value = "/addOrUpdatePurchaseOrder")
  @BootLockByUserFor(ConstantsLockByUserForType.SAVE_PURCHASE_ORDER)
  public ResultBean<String> addOrUpdatePurchaseOrder(@Valid @RequestBody PurchaseOrderAddV2Form form) {
    return new ResultBean<>(purchaseOrderV2BaseService.addOrUpdatePurchaseOrder(form, getUser()));
  }

  @ApiOperation(value = "根据采购订单 id 获得订单明细", tags = "0.4.0")
  @ApiImplicitParams(@ApiImplicitParam(name = "id", value = "采购订单 id"))
  @GetMapping("/purchaseOrderDetailed")
  public ResultBean<PurchaseOrderV2DetailedVO> getPurchaseOrderDetailed(@NotBlank String id) {
    return new ResultBean<>(purchaseOrderV2BaseService.getPurchaseOrderDetailed(id));
  }

  @ApiOperation(value = "导入采购订单")
  @ApiImplicitParams({
      @ApiImplicitParam(name = "file", value = "文件", required = true)
  })
  @PostMapping("/saveSupplierOrderExcel")
  public ResultBean<Boolean> saveSupplierOrderExcel(
      @RequestParam MultipartFile file,
      @RequestParam @NotBlank(message = "用户 id 不能为空") String userId) {
    // todo 未来极大可能变化，现在使用V1方法
    purchaseOrderService.saveSupplierOrderExcel(file,userId);
    return new ResultBean<>();
  }

  /**
   * 期初采购订单导入
   */
  @PostMapping(value = "/initialPurchaseOrder/import", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
  public ResultBean<Boolean> importOrder(MultipartFile file) {
    if (file == null || file.isEmpty()) {
      throw new IllegalArgumentException("文件不能为空");
    }
    // todo 未来极大可能变化，现在使用V1方法
    User user = getUser();
    initialPurchaseOrderService.importOrder(file, user.getId());
    return new ResultBean<>(true);
  }


  @ApiOperation("修改基础信息")
  @PostMapping(value = "updateSupplierOrderBaseInfo", consumes = MediaType.APPLICATION_JSON_VALUE)
  @RepeatSubmit(interval = 10000)
  public ResultBean<Boolean> updateSupplierOrderBaseInfo(
      @RequestBody @Valid UpdateSupplierOrderBaseInfoV2DTO params) {
    purchaseOrderV2BaseService.updateSupplierOrderBaseInfo(params);
    return new ResultBean<>(true);
  }


  @ApiOperation("修改供方和收方联系人信息")
  @PostMapping(value = "updateSupplierContact")
  public ResultBean<Boolean> updateSupplierContact(
      @RequestBody @Valid UpdateSupplierContactParam param) {
    purchaseOrderService.updateSupplierContact(param);
    return new ResultBean<>(true);
  }

  @ApiOperation("提交内部备注")
  @PostMapping("submitInternalRemark")
  public ResultBean<Boolean> submitInternalRemark(@RequestBody @Valid
  SupplierOrderInternalRemarkParam param) {
    purchaseOrderService.submitInternalRemark(param,getUser());
    return new ResultBean<>(true);
  }

  @ApiOperation("更新备注")
  @ApiImplicitParams(@ApiImplicitParam(name = "id", value = "采购订单物料id"))
  @PutMapping("updateNotes")
  public ResultBean<Boolean> updateNotes(@RequestBody UpdateNotesParam param) {
    purchaseOrderService.updateNotes(param);
    return new ResultBean<>(true);
  }

  @ApiOperation("导出供应商订单")
  @PostMapping(value = "exportSupplierOrder", consumes = MediaType.APPLICATION_JSON_VALUE)
  public ResultBean<Boolean> exportSupplierOrder(@RequestBody @Valid purchaseOrderExport export) {
    purchaseOrderV2BaseService.exportSupplierOrder(export, getUser());
    return new ResultBean<>(true, "操作成功");
  }

  /**
   *  驳回暂存下的采购订单，仅admin和订单采购员本人展示删除按钮
   */
  @ApiOperation("采购订单删除-驳回和暂存状态")
  @DeleteMapping(value = "")
  public ResultBean<Boolean> deletePurchaseOrder(@RequestBody List<String> ids) {
    if (CollUtil.isEmpty(ids)) {
      return new ResultBean<>(false, "订单id不能为空");
    }
    purchaseOrderV2BaseService.deletePurchaseOrder(ids, getUser());
    return new ResultBean<>(true);
  }

  /**
   * 采购订单系统信息中同步记录重推
   */
  @ApiOperation("采购订单系统信息中同步记录重推")
  @PostMapping(value = "rePushPurchaseOrder")
  @ApiImplicitParams({
      @ApiImplicitParam(name = "id", value = "采购订单 id"),
      @ApiImplicitParam(name = "type", value = "同步类型,1为飞搭审核,2为SAP状态同步")}
  )
  public ResultBean<Boolean> rePushPurchaseOrder(
      @RequestParam("id") String id,
      @RequestParam("type") String type
  ) {
    purchaseOrderV2BaseService.rePushPurchaseOrder(id, type, getUser());
    return new ResultBean<>(true);
  }

  /**
   * 获取采购订单系统信息中同步记录
   */
  @ApiOperation("获取采购订单系统信息中同步记录")
  @GetMapping(value = "getPurchaseOrderSyncRecord")
  @ApiImplicitParams({@ApiImplicitParam(name = "id", value = "采购订单 id")}
  )
  public ResultBean<SupplierOrderSyncVO> getPurchaseOrderSyncRecord(
      @RequestParam("id") String id) {
    return new ResultBean<>(purchaseOrderV2BaseService.getPurchaseOrderSyncRecord(id));
  }
}
