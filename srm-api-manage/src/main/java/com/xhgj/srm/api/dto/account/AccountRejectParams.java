package com.xhgj.srm.api.dto.account;

import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.NotBlank;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

/**
 * 对账单驳回入参类
 * <AUTHOR> @date 2023/6/28
 */
@Data
public class AccountRejectParams {
  @ApiModelProperty("对账单id")
  @NotBlank(message = "对账单id不能为空")
  private String id;
  @ApiModelProperty("驳回理由")
  @Length(max = 100,message = "驳回理由长度限制100字符以内")
  private String reason;

}
