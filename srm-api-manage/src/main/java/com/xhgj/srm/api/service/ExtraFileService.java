package com.xhgj.srm.api.service;

import com.xhgj.srm.api.dto.supplier.SupplierFileDTO;
import com.xhgj.srm.jpa.entity.ExtraFile;
import com.xhiot.boot.framework.jpa.service.BootBaseService;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2021/3/1 18:52
 */
public interface ExtraFileService extends BootBaseService<ExtraFile, String> {

  /**
   * 新增自定义附件
   *
   * <AUTHOR>
   * @since 2019年5月20日 上午11:45:45
   */
  void addZDYFile(String relationid, String filesall);

  /**
   * 新增自定义附件（先删后增）
   *
   * @param relationId 关联 id，必传
   * @param customName 自定义类型名称，必传
   * @param files 参数对象列表
   */
  void addZDYFile(String relationId, String customName, List<SupplierFileDTO> files);

  /**
   * 将供应商的自定义附件复制到目标供应商副本
   *
   * @param supplierId 供应商 id
   * @param supplierFbId 目标供应商副本 id
   */
  void copySupplierZDYFileToSupplierFb(String supplierId, String supplierFbId);

  /**
   * 删除供应商的自定义附件
   *
   * @param supplierId 供应商 id
   * @since 16:51 2019/8/28
   */
  void deleteAllZDYFile(String supplierId);

  void updateSupplierExtraFilesByFb(String supplierId, String fbid);

  void updateSupplierFbExtraFilesByFb(String oldFbId, String fbid);

  /**
   * 根据关联 id 获取自定义附件
   *
   * @param relationId 关联 id，必传
   */
  List<ExtraFile> getFileListByRId(String relationId);
}
