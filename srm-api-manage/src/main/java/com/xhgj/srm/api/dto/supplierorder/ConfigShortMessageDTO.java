package com.xhgj.srm.api.dto.supplierorder;

import cn.hutool.core.util.ObjectUtil;
import com.xhgj.srm.common.enums.ShortMessageEnum;
import com.xhgj.srm.jpa.entity.ConfigShortMessage;
import com.xhiot.boot.core.common.exception.CheckException;
import com.xhiot.boot.core.common.util.dict.BootDictEnumUtil;
import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2022/12/19 10:06
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ConfigShortMessageDTO {

  @ApiModelProperty("类型")
  @NotNull(message = "配置类型必传")
  private ShortMessageEnum type;

  @ApiModelProperty("是否开启")
  @NotNull(message = "是否开启必传")
  private Boolean open;

  public ConfigShortMessageDTO(ConfigShortMessage configShortMessage) {
    this.type =
        BootDictEnumUtil.getEnumByKey(ShortMessageEnum.class, configShortMessage.getType())
            .orElseThrow(() -> new CheckException("【" + configShortMessage.getId() + "】短信配置类型非法"));
    this.open = ObjectUtil.defaultIfNull(configShortMessage.getOpen(), Boolean.FALSE);
  }
}
