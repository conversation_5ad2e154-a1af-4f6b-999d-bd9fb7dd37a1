package com.xhgj.srm.api.dto.purchase.order;

import cn.hutool.core.util.StrUtil;
import com.xhgj.srm.api.dto.supplierorder.BaseSupplierOrderProductDTO;
import com.xhgj.srm.api.dto.supplierorder.PurchaseOrderInvoiceRelation;
import com.xhgj.srm.common.utils.supplierorder.BigDecimalUtil;
import com.xhgj.srm.jpa.entity.SupplierOrderDetail;
import com.xhgj.srm.jpa.entity.SupplierOrderProduct;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

/**
 * Created by Geng Shy on 2023/12/13
 */

@Data
@NoArgsConstructor
public class PurchaseOrderProductDetailedReturnVO {
  @ApiModelProperty("id")
  private String id;
  @ApiModelProperty("物料编码")
  private String productCode;
  @ApiModelProperty("品牌")
  private String brand;
  @ApiModelProperty("名称")
  private String productName;
  @ApiModelProperty("规格型号")
  private String manuCode;
  @ApiModelProperty("单位")
  private String unit;
  @ApiModelProperty("退货数量")
  private BigDecimal returnQty;
  @ApiModelProperty("退库数量")
  private BigDecimal stockOutputQty;
  @ApiModelProperty("单价")
  private BigDecimal productPrice;
  @ApiModelProperty("行id")
  private String sapRowId;
  @ApiModelProperty("批号")
  private String batchNo;
  @ApiModelProperty("退库单sap物料凭证行项目")
  private String returnProductVoucherRowId;
  @ApiModelProperty("冲销凭证行号")
  private String sapReversalRowNo;
  @ApiModelProperty("入库单名称")
  private String inWareHouseName;
  @ApiModelProperty("退库单价")
  private BigDecimal returnPrice;
  @ApiModelProperty("本次退库金额")
  private BigDecimal returnAmount;
  @ApiModelProperty("已开红票数量")
  private BigDecimal redTicketNum;
  @ApiModelProperty("冲销状态 true 冲销 false 未冲销")
  private Boolean reversalStatus;
  @ApiModelProperty("关联发票号")
  private List<PurchaseOrderInvoiceRelation> purchaseOrderInvoiceRelationList;

  public PurchaseOrderProductDetailedReturnVO(SupplierOrderDetail supplierOrderDetail) {
    SupplierOrderProduct supplierOrderProduct = supplierOrderDetail.getSupplierOrderProduct();
    Integer unitDigit = supplierOrderProduct.getUnitDigit();
    this.id = StrUtil.emptyIfNull(supplierOrderDetail.getId());
    this.productCode =
        StrUtil.emptyIfNull(supplierOrderProduct.getCode());
    this.brand =
        StrUtil.emptyIfNull(supplierOrderProduct.getBrand());
    this.returnQty =
        BigDecimalUtil.setScaleBigDecimalHalfUp(supplierOrderDetail.getReturnQty(), 3);
    this.productName =
        StrUtil.emptyIfNull(supplierOrderProduct.getName());
    this.manuCode =
        StrUtil.emptyIfNull(supplierOrderProduct.getManuCode());
    this.unit =
        StrUtil.emptyIfNull(supplierOrderProduct.getUnit());
    this.stockOutputQty =
        BigDecimalUtil.setScaleBigDecimalHalfUp(supplierOrderDetail.getStockOutputQty(), 3);
    this.productPrice = supplierOrderDetail.getPrice();
    this.sapRowId = supplierOrderDetail.getSortNum().toString();
    this.batchNo = supplierOrderDetail.getBatchNo();
    this.returnProductVoucherRowId = supplierOrderDetail.getSapRowId();
    this.sapReversalRowNo = supplierOrderDetail.getSapReversalRowNo();
    this.inWareHouseName = supplierOrderDetail.getInWareHouseName();
    this.returnPrice = supplierOrderDetail.getReturnPrice();
    this.returnAmount = supplierOrderDetail.getReturnAmount();
    this.redTicketNum = BigDecimalUtil.formatForStandard(supplierOrderDetail.getInvoicedNum());
  }
}
