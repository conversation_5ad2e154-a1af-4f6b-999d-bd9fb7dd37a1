package com.xhgj.srm.api.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.xhgj.cloud.dock.provider.support.dto.DockSupplier;
import com.xhgj.srm.api.service.SupplierService;
import com.xhgj.srm.api.service.XhgjService;
import com.xhgj.srm.common.utils.DateUtil;
import com.xhgj.srm.jpa.entity.BaseSupplier;
import com.xhgj.srm.jpa.entity.Supplier;
import com.xhgj.srm.jpa.entity.User;
import com.xhgj.srm.request.dto.edge.CountryDTO;
import com.xhgj.srm.request.dto.edge.CountryDomain;
import com.xhgj.srm.request.dto.edge.IndustryDTO;
import com.xhgj.srm.request.dto.edge.ProvinceCityDTO;
import com.xhgj.srm.request.dto.partner.BusinessInfoDTO;
import com.xhgj.srm.request.dto.partner.PartnerDTO;
import com.xhgj.srm.request.dto.partner.PartnerIcpDTO;
import com.xhgj.srm.request.dto.partner.SrmSaveOrUpdateSupplier;
import com.xhgj.srm.request.enums.PartnerType;
import com.xhgj.srm.request.service.third.xhgj.XhgjDockRequest;
import com.xhgj.srm.request.service.third.xhgj.XhgjEdgeService;
import com.xhgj.srm.request.service.third.xhgj.XhgjPartnerRequest;
import com.xhiot.boot.core.common.exception.CheckException;
import com.xhiot.boot.core.common.util.DateUtils;
import com.xhiot.boot.core.common.util.StringUtils;
import com.xhiot.boot.core.common.util.dict.BootDictEnumUtil;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @since 2022/8/2 22:05
 */
@Service
public class XhgjServiceImpl implements XhgjService {
  @Autowired private XhgjPartnerRequest xhgjPartnerRequest;
  @Autowired private XhgjDockRequest xhgjDockRequest;
  @Autowired private XhgjEdgeService xhgjEdgeService;
  @Autowired private SupplierService supplierService;

  @Override
  public String createPartnerAssess(BaseSupplier supplier, User user) {
    // 此处应该 MDM 生成审核 id，由 SRM 保存，暂时使用回调的形式实现同步，后续可改造为 MQ
    return xhgjPartnerRequest
        .saveOrUpdateMainDataWithAssess(buildMDMParamBySupplier(supplier, user))
        .orElseThrow(() -> new CheckException("调用 MDM 接口异常：审核 id 获取失败！"));
  }

  @Override
  public String createPartner(BaseSupplier supplier, User user) {
    return xhgjPartnerRequest
        .saveOrUpdateMainData(buildMDMParamBySupplier(supplier, user))
        .orElseThrow(() -> new CheckException("调用 MDM 接口异常：获取新生成的主数据编码失败！"));
  }

  @Override
  public Optional<PartnerDTO> getPartnerByCode(String code) {
    if (StringUtils.isNullOrEmpty(code)) {
      throw new CheckException("主数据编码缺失，请核实！");
    }
    return xhgjPartnerRequest.getPartnerByCode(code);
  }

  @Override
  public Optional<PartnerDTO> getPartnerByTianYanCha(String name) {
    return xhgjPartnerRequest.getTianYanDetails(name);
  }

  @Override
  public Optional<PartnerDTO> getPartnerByName(String name, String type) {
    return xhgjPartnerRequest.getPartnerByName(name, type);
  }

  @Override
  public void markPartnerSupplier(String mdmCode) {
    xhgjPartnerRequest.markSupplier(mdmCode);
  }

  @Override
  public List<PartnerDTO> searchPartner(String name, String type) {
    return xhgjPartnerRequest.searchPartner(name, type).orElse(Collections.emptyList());
  }

  @Override
  public List<CountryDTO> getAllCountry() {
    return xhgjEdgeService.getAllCountry();
  }

  @Override
  public List<IndustryDTO> getAllIndustry() {
    return xhgjEdgeService.getAllIndustry();
  }

  @Override
  public List<ProvinceCityDTO> getAllProvinceCity() {
    return xhgjEdgeService.getAllProvinceCity();
  }

  @Override
  public List<ProvinceCityDTO> getAllProvinceCityWithCountry() {
    return xhgjEdgeService.getAllProvinceCityWithCountry().orElse(Collections.emptyList());
  }

  public void createSupplierAddTask(DockSupplier dto) {
    String mdmCode = dto.getMdmCode();
    if (StrUtil.isBlank(mdmCode)) {
      throw new CheckException("该供应商无 mdm 编码无法创建推送任务，请联系管理员处理！");
    }
    xhgjDockRequest.createSupplierAddTask(dto);
  }

  @Override
  public String getCountryCodeByName(String name) {
    if (StringUtils.isNullOrEmpty(name)) {
      return StrUtil.EMPTY;
    } else {
      return xhgjEdgeService
          .getCountryByName(name)
          .map(CountryDomain::getCode)
          .orElse(StrUtil.EMPTY);
    }
  }

  @Override
  public Optional<PartnerDTO> getPersonPartner(String name, String mobile) {
    return xhgjPartnerRequest.getPersonPartner(name, mobile);
  }

  @Override
  public Optional<BusinessInfoDTO> syncTianYan(String syncName, String partnerCode) {
    return xhgjPartnerRequest.sysnTianYan(syncName, partnerCode);
  }

  @Override
  public Optional<BusinessInfoDTO> getTianYanInfo(String mdmCode, String name) {
    return xhgjPartnerRequest.getTianYanInfo(mdmCode, name);
  }
  @Override
  public List<PartnerIcpDTO> getPartnerIprByKeyWord(String keyWord,String supplierId){
    // 调用天眼查接口获取网络备案信息
    List<PartnerIcpDTO>  partnerIcpDTOlist =
        xhgjPartnerRequest.getPartnerIprByKeyWord(keyWord).orElse(Collections.emptyList());
    // 如果前端回传的供应商id,不为空。需要保存该供应商的网络备案信息（为了节省调用天眼查接口次数，避免网页上频繁打开页面，但是不保存供应商信息）
    if(StrUtil.isNotEmpty (supplierId)){
      Supplier supplier =
          supplierService.get(
              supplierId, () -> CheckException.noFindException(Supplier.class, supplierId));
      if(CollUtil.isNotEmpty(partnerIcpDTOlist)){
        String json = JSON.toJSONString(partnerIcpDTOlist);
        supplier.setWebsiteRegistration(json);
      }
      supplier.setWebsiteRegistrationSyncTime(DateUtils.formatTimeStampToNormalDateTime(System.currentTimeMillis()));
      supplierService.save(supplier);
    }
    return  partnerIcpDTOlist;
  }

  private SrmSaveOrUpdateSupplier buildMDMParamBySupplier(BaseSupplier supplier, User user) {
    SrmSaveOrUpdateSupplier param = new SrmSaveOrUpdateSupplier();
    PartnerType partnerType =
        BootDictEnumUtil.getEnumByKey(PartnerType.class, supplier.getSupType())
            .orElseThrow(
                () ->
                    new CheckException(
                        "供应商类型【" + supplier.getSupType() + "】异常，无法转化为合作商类型，请联系管理员！"));
    param.setPartnerType(partnerType);
    param.setApplyManMobile(user.getMobile());
    param.setPartnerName(supplier.getEnterpriseName());
    param.setCreditCode(supplier.getUscc());
    param.setCity(supplier.getCity());
    param.setProvince(supplier.getProvince());
    param.setMobile(supplier.getMobile());
    param.setCountry(supplier.getCountry());
    param.setIndustry(supplier.getIndustry());
    if (supplier instanceof Supplier) {
      param.setMdmCode(((Supplier) supplier).getMdmCode());
    }
    return param;
  }
}
