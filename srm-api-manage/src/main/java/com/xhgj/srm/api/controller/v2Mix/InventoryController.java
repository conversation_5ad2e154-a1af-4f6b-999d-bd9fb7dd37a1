package com.xhgj.srm.api.controller.v2Mix;

import com.xhgj.srm.api.controller.AbstractRestController;
import com.xhgj.srm.api.dto.InventoryDTO;
import com.xhgj.srm.api.service.InventoryService;
import com.xhgj.srm.dto.InventoryQueryForm;
import com.xhiot.boot.mvc.base.PageResult;
import com.xhiot.boot.mvc.base.ResultBean;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;
import java.util.Map;

/**
 * InventoryController
 */
@Api(tags = {"库存管理接口"})
@RestController
@Validated
@RequestMapping("/inventory")
public class InventoryController extends AbstractRestController {

  @Resource
  private InventoryService inventoryService;

  @GetMapping(value = "getAllInventoryList")
  @ApiOperation(value = "库存列表（全部库存）", tags = "0.5.0")
  public ResultBean<PageResult<InventoryDTO>> getAllInventoryList(@Valid InventoryQueryForm param) {
    return new ResultBean<>(inventoryService.getAllInventoryList(param));
  }

  @GetMapping(value = "getCurrentInventoryList")
  @ApiOperation(value = "库存列表（实时库存）", tags = "0.5.0")
  public ResultBean<List<InventoryDTO>> getCurrentInventoryList(@Valid InventoryQueryForm param) {
    return new ResultBean<>(inventoryService.getCurrentInventoryList(param));
  }

  @GetMapping(value = "getLastSyncTime")
  @ApiOperation("获取最近同步时间")
  public ResultBean<Map<String,Long>> getLastSyncTime() {
    return new ResultBean<>(inventoryService.getLastSyncTime());
  }

  @PostMapping(value = "export")
  @ApiOperation("导出库存列表")
  public ResultBean<Boolean> export(@RequestBody @Valid InventoryQueryForm param) {
    inventoryService.export(param,getUser());
    return new ResultBean<>(true, "操作成功");
  }

  @ApiOperation(value = "查询预计导出库存信息条数")
  @PostMapping(value = "getExportCount")
  public ResultBean<Long> getExportCount(
      @RequestBody InventoryQueryForm param) {
    return new ResultBean<>(inventoryService.getExportCount(param));
  }
}
