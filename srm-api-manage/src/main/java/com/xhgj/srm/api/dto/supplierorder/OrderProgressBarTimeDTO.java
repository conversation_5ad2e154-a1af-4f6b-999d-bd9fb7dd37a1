package com.xhgj.srm.api.dto.supplierorder;

import com.xhgj.srm.jpa.entity.SupplierOrder;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2022/12/14 13:37
 */
@Data
public class OrderProgressBarTimeDTO {

  @ApiModelProperty("订单确认时间")
  private Long confirmTime;

  @ApiModelProperty("首次发货时间")
  private Long firstShipTime;

  @ApiModelProperty("发货完成时间")
  private Long completeShipTime;

  @ApiModelProperty("订单完成时间")
  private Long completeOrderTime;

  public OrderProgressBarTimeDTO(SupplierOrder supplierOrder) {
    this.confirmTime = supplierOrder.getConfirmTime();
    this.firstShipTime = supplierOrder.getFirstShipTime();
    this.completeShipTime = supplierOrder.getCompleteShipTime();
    this.completeOrderTime = supplierOrder.getCompleteOrderTime();
  }
}
