package com.xhgj.srm.api.service;

import com.xhgj.srm.api.dto.group.AssessGroupDTO;
import com.xhgj.srm.api.dto.supplierorder.VerifyConfigInfoDTO;
import com.xhgj.srm.common.enums.VerifyConfigTypeEnum;
import com.xhgj.srm.jpa.entity.Supplier;
import com.xhgj.srm.jpa.entity.VerifyConfig;
import com.xhiot.boot.framework.jpa.service.BootBaseService;
import java.util.List;

/**
 * VerifyConfigService
 */
public interface VerifyConfigService extends BootBaseService<VerifyConfig, String> {

  VerifyConfigInfoDTO getVerifyConfigInfo();

  void enableConfig(String id, Boolean enable);

  void updateOrganizationRole(VerifyConfigInfoDTO param);

  List<AssessGroupDTO> getAllGroupsByKeyWord(String keyword);

  VerifyConfig getVerifyConfigByType(VerifyConfigTypeEnum typeEnum);

  /**
   * 获取所有配置
   * @return
   */
  List<VerifyConfigInfoDTO> getAllConfig();

  /**
   * 校验配置中心配置
   * @param supplier 供应商
   * @param groupCode 订单下组织编码
   */
  void verifyAll(Supplier supplier, String groupCode);

  /**
   * 根据配置类型校验
   * @param verifyConfigTypeEnum 配置类型
   * @param supplier 供应商
   * @param groupCode 订单下组织编码
   */
  void verify(VerifyConfigTypeEnum verifyConfigTypeEnum, Supplier supplier, String groupCode);


}

