package com.xhgj.srm.api.dto.supplier;

import com.xhgj.srm.jpa.entity.BaseSupplierInGroup;
import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * 海外供应商
 *
 * <AUTHOR>
 * @since 2022/7/11 15:46
 */
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
public class AbroadSupplierDTO extends BaseSupplierDTO {
  @ApiModelProperty("国家")
  private String country;
  @ApiModelProperty("账期")
  private String accountPeriod;
  @ApiModelProperty("调整后的等级")
  private String newLevel;
  @ApiModelProperty(value = "企业简称")
  private String abbreviation;

  public AbroadSupplierDTO(
      BaseSupplierInGroup supplierInGroup,
      List<SupplierFinancialDTO> financials,
      List<SupplierContactDTO> contacts,
      String chargeManDesc,
      Boolean hasUpdatePermission,String level) {
    super(supplierInGroup, financials, contacts, chargeManDesc, hasUpdatePermission,null);
    this.country = supplierInGroup.getSupplier().getCountry();
    this.accountPeriod = supplierInGroup.getAccountPeriod();
    this.newLevel = level;
    this.abbreviation = supplierInGroup.getAbbreviation();
  }
}
