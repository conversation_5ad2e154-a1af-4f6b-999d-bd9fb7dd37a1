package com.xhgj.srm.api.dto;

import com.xhgj.srm.jpa.entity.BidProject;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;


/**
 * BidProjectDTO
 */
@Data
@NoArgsConstructor
public class BidProjectDTO {
  @ApiModelProperty("id")
  private String id;
  @ApiModelProperty("服务批次年份")
  private String batchYear;
  @ApiModelProperty("项目全称")
  private String projectName;
  @ApiModelProperty("服务开始时间")
  private Long serviceStartTime;
  @ApiModelProperty("项目经理。多个以、分割")
  private String projectManager;
  @ApiModelProperty("履约公司。多个以、分割")
  private String performanceCompany;
  @ApiModelProperty("合作履约公司。多个以、分割")
  private String cooperationCompany;
  @ApiModelProperty("创建时间-时间戳")
  private Long createTime;

  public BidProjectDTO(BidProject bidProject) {
    this.id = bidProject.getId();
    this.batchYear = bidProject.getBatchYear();
    this.projectName = bidProject.getProjectName();
    this.serviceStartTime = bidProject.getServiceStartTime();
    this.projectManager = bidProject.getProjectManager();
    this.performanceCompany = bidProject.getPerformanceCompany();
    this.cooperationCompany = bidProject.getCooperationCompany();
    this.createTime = bidProject.getCreateTime();
  }
}
