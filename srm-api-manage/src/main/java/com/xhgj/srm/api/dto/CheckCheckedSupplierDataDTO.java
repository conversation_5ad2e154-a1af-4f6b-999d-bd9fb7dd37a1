package com.xhgj.srm.api.dto;

import com.xhgj.srm.common.Constants;
import com.xhgj.srm.common.enums.supplier.SupplierLevelEnum;
import com.xhgj.srm.jpa.entity.SupplierFb;
import com.xhiot.boot.core.common.util.DateUtils;
import com.xhiot.boot.core.common.util.StringUtils;
import lombok.Data;

@Data
public class CheckCheckedSupplierDataDTO {

    private String id;
    private String ckId;
    private String enterName;
    private String level;
    private String brand;
    private String enterNature;
    private String industry;
    private String createTime;
    private String useGroup;
    private String purchaser;
    private String type;
    private String checkMan;
    private String checkState;
    private String reason;
    private String checkTime;


    public CheckCheckedSupplierDataDTO(SupplierFb supplierFb) {
        this.id = supplierFb.getId();
        this.enterNature = supplierFb.getEnterpriseNature();
        this.useGroup = supplierFb.getUseGroup();
        this.enterName = supplierFb.getEnterpriseName();
        this.industry = supplierFb.getIndustry();
        this.level = !StringUtils.isNullOrEmpty(supplierFb.getEnterpriseLevel())?
            SupplierLevelEnum.getAbbrByCode(supplierFb.getEnterpriseLevel()):"";
        this.purchaser = !StringUtils.isNullOrEmpty(supplierFb.getPurchaserName())?supplierFb.getPurchaserName():"";
        if(supplierFb.getSupplier().getEditTime()!=null&&supplierFb.getSupplier().getEditTime()>0){
            this.createTime = supplierFb.getSupplier().getEditTime()>0? DateUtils.formatTimeStampToNormalDateTime(supplierFb.getSupplier().getEditTime()):"";
            this.type = Constants.SUPPLIERCHECKTYPE_MAP_UPDATE;
        } else {
            this.createTime = supplierFb.getSupplier().getCreateTime()>0? DateUtils.formatTimeStampToNormalDateTime(supplierFb.getSupplier().getCreateTime()):"";
            this.type = Constants.SUPPLIERCHECKTYPE_MAP_ADD;
        }
    }

}
