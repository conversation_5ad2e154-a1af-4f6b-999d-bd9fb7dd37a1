package com.xhgj.srm.api.dto.workbench;

import com.xhgj.srm.common.utils.supplierorder.BigDecimalUtil;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import lombok.Data;

/** <AUTHOR> @ClassName DataBoardDataDTO */
@Data
@ApiModel("基础键值对对象")
public class BaseDataBoardDTO {
  @ApiModelProperty("键")
  private String key;

  @ApiModelProperty("值")
  private BigDecimal value;

  public BaseDataBoardDTO(String key, Double value){
    this.value = BigDecimalUtil.formatForStandard(value);
    this.key = key;
  }

  public BaseDataBoardDTO(String key, BigDecimal value){
    this.value = BigDecimalUtil.formatForStandard(value);
    this.key = key;
  }
}
