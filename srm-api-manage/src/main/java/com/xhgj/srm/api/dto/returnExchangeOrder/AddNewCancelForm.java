package com.xhgj.srm.api.dto.returnExchangeOrder;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * @Author: fanghuanxu
 * @Date: 2025/2/13 10:12
 * @Description: 新增取消单参数
 */
@Data
public class AddNewCancelForm {

  @ApiModelProperty(value = "退换货订单id", required = true)
  @NotBlank(message = "退换货订单id不能为空")
  private String returnExchangeOrderId;

  @ApiModelProperty(value = "关联supplierOrder订单id", required = true)
  @NotBlank(message = "关联supplierOrder订单id不能为空")
  private String supplierOrderId;

  @ApiModelProperty(value = "取消行id", required = true)
  @NotEmpty(message = "取消行id不能为空")
  private List<String> supplierOrderDetails;
}
