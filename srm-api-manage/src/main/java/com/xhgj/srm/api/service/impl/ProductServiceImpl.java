package com.xhgj.srm.api.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.xhgj.srm.api.dto.ProductTableVO;
import com.xhgj.srm.api.dto.SrmProductInfoDTO;
import com.xhgj.srm.api.dto.WebProductInfoList.WebProductInfo;
import com.xhgj.srm.api.service.*;
import com.xhgj.srm.common.Constants;
import com.xhgj.srm.common.Constants_Excel;
import com.xhgj.srm.common.Constants_PurchaseInfoRecord;
import com.xhgj.srm.common.utils.ExportUtil;
import com.xhgj.srm.jpa.annotations.DefaultSearchScheme;
import com.xhgj.srm.jpa.dto.form.ProductSearchManageForm;
import com.xhgj.srm.jpa.entity.SearchScheme;
import com.xhgj.srm.jpa.sharding.enums.VersionEnum;
import com.xhgj.srm.jpa.sharding.util.ShardingContext;
import com.xhgj.srm.request.service.third.mpm.MPMService;
import com.xhgj.srm.request.vo.mpm.MPMUnitVO;
import com.xhgj.srm.v2.vo.ProductResultVO;
import com.xhiot.boot.core.common.exception.CheckException;
import com.xhiot.boot.mvc.base.PageResult;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;
import org.springframework.web.multipart.MultipartFile;

@Slf4j
@Service
public class ProductServiceImpl implements ProductService {
  @Resource private SearchSchemeService searchSchemeService;
  @Autowired private ExportUtil ex;
  @Resource
  MPMService mpmService;

  @Override
  @DefaultSearchScheme(searchType = Constants.SEARCH_TYPE_SUPPLIER_PRODUCT_LIST)
  public PageResult<ProductTableVO> getNormalProductPageRef(ProductSearchManageForm form) {
    TypeReference<PageResult<ProductTableVO>> typeRef = new TypeReference<PageResult<ProductTableVO>>() {};
    return mpmService.getProductTablePageBySupplier(form.toQueryMap(), typeRef);
  }

  @Override
  public List<SrmProductInfoDTO> getSrmProductInfoList(List<String> codes, String useCode) {
    return mpmService.getProductInfoListByCodes(codes, useCode,
        new TypeReference<List<SrmProductInfoDTO>>() {});
  }

  private Map<String, Object> handleRetrievalScheme(String schemeId, String userId, String type) {
    //处理方案
    Map<String, Object> stringObjectMap = null;
    SearchScheme defaultSearchScheme = null;
    if (!ObjectUtils.isEmpty(userId) && !ObjectUtils.isEmpty(type)) {
      defaultSearchScheme = searchSchemeService.getDefaultSearchScheme(userId, type);
      if (defaultSearchScheme != null ) {
        stringObjectMap = JSON.parseObject(defaultSearchScheme.getContent(),
            new TypeReference<Map<String, Object>>() {});
      }
    }
    if (defaultSearchScheme == null && StrUtil.isNotEmpty(schemeId)) {
      SearchScheme search = searchSchemeService.get(schemeId);
      if (search != null && StrUtil.isNotEmpty(search.getContent())) {
        stringObjectMap =
            JSON.parseObject(search.getContent(), new TypeReference<Map<String, Object>>() {});
      }
    }
    return stringObjectMap;
  }

  @Override
  public byte[] downloadInvoiceZipByOrderId(List<WebProductInfo> webProductInfoList)
      throws IOException {
    XSSFWorkbook workbook = new XSSFWorkbook();
    List<String> titles = new ArrayList<>();
    List<Integer> widths = new ArrayList<>();
    if (VersionEnum.V2 == ShardingContext.getVersion()) {
      titles = Constants_Excel.DOWNLOAD_WEB_PRODUCT_INFO_V2;
    } else {
      titles = Constants_Excel.DOWNLOAD_WEB_PRODUCT_INFO;
    }
    int size = titles.size();
    for (int i = 0; i < size; i++) {
      widths.add(30);
    }
    Sheet sheet = ex.createSheet(workbook, "product", widths);
    CellStyle titleStyle = ex.getTitleStyle(workbook);
    CellStyle baseStyle = ex.getBaseStyle(workbook);
    Row titleRow = sheet.createRow(0);
    ex.createTitle(titles, titleStyle,titleRow);
    List<WebProductInfo> emptyIfNull = CollUtil.emptyIfNull(webProductInfoList);
    for (int i = 0; i < emptyIfNull.size(); i++) {
      Row row = sheet.createRow(i + 1);
      WebProductInfo webProductInfo = emptyIfNull.get(i);
      String serialNumber = webProductInfo.getSerialNumber();
      String code = webProductInfo.getCode();
      String name = webProductInfo.getName();
      String brandName = webProductInfo.getBrandName();
      String manuCode = webProductInfo.getManuCode();
      String specification = webProductInfo.getSpecification();
      String model = webProductInfo.getModel();
      String unitName = webProductInfo.getUnitName();
      String productRate = webProductInfo.getProductRate();
      String markupCoefficient = webProductInfo.getMarkupCoefficient();
      String transferPrice = webProductInfo.getTransferPrice();
      ;
      String desc = webProductInfo.getDesc();
      String num = webProductInfo.getNum();
      String price = webProductInfo.getPrice();
      String taxRate = webProductInfo.getTaxRate();
      String remark = webProductInfo.getRemark();
      String surcharge = webProductInfo.getSurcharge();
      int index = 0;
      ex.createCell(row, index++, serialNumber, baseStyle);
      ex.createCell(row, index++, code, baseStyle);
      ex.createCell(row, index++, brandName, baseStyle);
      ex.createCell(row, index++, name, baseStyle);
      ex.createCell(row, index++, desc, baseStyle);
      if (VersionEnum.V2 == ShardingContext.getVersion()) {
        ex.createCell(row, index++, specification, baseStyle);
        ex.createCell(row, index++, model, baseStyle);
      } else {
        ex.createCell(row, index++, manuCode, baseStyle);
      }
      ex.createCell(row, index++, unitName, baseStyle);
      ex.createCell(row, index++, productRate, baseStyle);
      ex.createCell(row, index++, markupCoefficient, baseStyle);
      ex.createCell(row, index++, transferPrice, baseStyle);
      ex.createCell(row, index++, num, baseStyle);
      ex.createCell(row, index++, price, baseStyle);
      ex.createCell(row, index++, taxRate, baseStyle);
      ex.createCell(row, index++, remark, baseStyle);
      ex.createCell(row, index++, surcharge, baseStyle);
    }
    ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
    workbook.write(byteArrayOutputStream);
    return byteArrayOutputStream.toByteArray();
  }

  @Override
  public List<WebProductInfo> fillWebProductInfo(MultipartFile file) throws IOException {
    if (file==null||file.isEmpty()) {
      throw new CheckException("上传文件为空，请检查后重新上传");
    }
    List<String> titles = new ArrayList<>();
    if (VersionEnum.V2 == ShardingContext.getVersion()) {
      titles = Constants_Excel.DOWNLOAD_RETURN_EXCHANGE_ORDER_INFO_V2;
    } else {
      titles = Constants_Excel.DOWNLOAD_RETURN_EXCHANGE_ORDER_INFO;
    }
    List<WebProductInfo> webProductInfoList = new ArrayList<>();
    try(Workbook workbook = ex.buildByFile(StrUtil.emptyIfNull(file.getOriginalFilename()),
        file.getInputStream());) {
      Sheet sheet = workbook.getSheetAt(0);
      if (!ex.validateExcel(sheet,0, titles)) {
        throw new CheckException("文件异常，请导入指定模板。");
      }
      int rowNumCount = sheet.getPhysicalNumberOfRows();
      if (rowNumCount>1) {
        for (int i = 0; i < rowNumCount; i++) {
          Row row = sheet.getRow(i + 1);
          if (ex.isEmptyRow(row)) {
            continue;
          }

          boolean fail = false;
          WebProductInfo webProductInfo = new WebProductInfo();
          int index = 0;
          String serialNumber = ex.getCellStringValue(row.getCell(index++));
          String code = ex.getCellStringValue(row.getCell(index++));
          String brandName = ex.getCellStringValue(row.getCell(index++));
          String name = ex.getCellStringValue(row.getCell(index++));
          String desc = ex.getCellStringValue(row.getCell(index++));
          if (VersionEnum.V2 == ShardingContext.getVersion()) {
            String specification = ex.getCellStringValue(row.getCell(index++));
            String model = ex.getCellStringValue(row.getCell(index++));
            webProductInfo.setSpecification(specification);
            webProductInfo.setModel(model);
          } else {
            String manuCode = ex.getCellStringValue(row.getCell(index++));
            webProductInfo.setManuCode(manuCode);
          }
          String unitName = ex.getCellStringValue(row.getCell(index++));
          String productRate = ex.getCellStringValue(row.getCell(index++));
          String markupCoefficient = ex.getCellStringValue(row.getCell(index++));
          String transferPrice = ex.getCellStringValue(row.getCell(index++));
          String num = ex.getCellStringValue(row.getCell(index++));
          String price = ex.getCellStringValue(row.getCell(index++));
          String taxRate = ex.getCellStringValue(row.getCell(index++));
          String remark = ex.getCellStringValue(row.getCell(index++));
          String surcharge = ex.getCellStringValue(row.getCell(index++));
          StringBuilder sb = new StringBuilder("【" + serialNumber + "】【" + code + "】");
          webProductInfo.setSerialNumber(serialNumber);
          webProductInfo.setCode(code);
          webProductInfo.setName(name);
          webProductInfo.setBrandName(brandName);
          webProductInfo.setUnitName(unitName);
          webProductInfo.setProductRate(productRate);
          webProductInfo.setMarkupCoefficient(markupCoefficient);
          webProductInfo.setTransferPrice(transferPrice);
          webProductInfo.setDesc(desc);
          webProductInfo.setNum(num);
          webProductInfo.setPrice(price);
          webProductInfo.setTaxRate(taxRate);
          webProductInfo.setRemark(remark);
          webProductInfo.setSurcharge(surcharge);
          BigDecimal numBigDecimal;
          try {
            numBigDecimal = new BigDecimal(num);
            int precision = numBigDecimal.scale();
            if (precision>3) {
              sb.append(",订货数量最大填写三位小数");
              fail =true;
            }
          } catch (Exception e) {
            sb.append(",订货数量非法");
            fail =true;
          }
          BigDecimal priceBigDecimal;
          try {
            priceBigDecimal = new BigDecimal(price);
            int precision = priceBigDecimal.scale();
            if (precision>6) {
              sb.append(",含税单价最大支持六位小数");
              fail =true;
            }
          } catch (Exception e) {
            sb.append(",含税单价非法");
            fail =true;
          }
          Set<String> taxRateSet = Constants_PurchaseInfoRecord.TAX_RATE_MAP.keySet();
          if (!taxRateSet.contains(taxRate)) {
            sb.append(",税率输入错误");
            fail =true;
          }
          if (StrUtil.length(remark)>500) {
            sb.append(",备注最大只支持500字");
            fail =true;
          }
          if (fail) {
            webProductInfo.setMsg(sb.toString());
          }
          webProductInfoList.add(webProductInfo);
        }
      }
      return webProductInfoList;
    }
  }

  @Override
  public PageResult<MPMUnitVO> getUnitList(String name, Integer pageNo, Integer pageSize) {
    return mpmService.getUnitList(name, pageNo, pageSize);
  }

  @Override
  public ProductResultVO getNormalProductDetail(String code) {
    TypeReference<ProductResultVO> typeRef = new TypeReference<ProductResultVO>() {};
    return mpmService.getProductDetail(code, typeRef);
  }
}
