package com.xhgj.srm.api.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.PageUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.xhgj.auth.form.req.LoginReq;
import com.xhgj.srm.api.domain.SrmUserDetails;
import com.xhgj.srm.api.dto.CheckRelationDTO;
import com.xhgj.srm.api.dto.PurchaseUserPageDTO;
import com.xhgj.srm.api.dto.PurchaserDataDTO;
import com.xhgj.srm.api.dto.UserAddOrUpdateParamDTO;
import com.xhgj.srm.api.dto.UserDetailDTO;
import com.xhgj.srm.api.dto.UserPageDTO;
import com.xhgj.srm.api.dto.group.UserToGroupTableDTO;
import com.xhgj.srm.api.mapper.AddOrUpdateUserMapper;
import com.xhgj.srm.api.mapper.UserDetailMapper;
import com.xhgj.srm.api.service.GroupService;
import com.xhgj.srm.api.service.MissionService;
import com.xhgj.srm.api.service.PermissionTypeService;
import com.xhgj.srm.api.service.SupplierInGroupService;
import com.xhgj.srm.api.service.UserCheckService;
import com.xhgj.srm.api.service.UserService;
import com.xhgj.srm.api.service.UserToGroupService;
import com.xhgj.srm.util.ImportExcelUtil;
import com.xhgj.srm.api.utils.ManageHttpUtil;
import com.xhgj.srm.common.Constants;
import com.xhgj.srm.common.Constants_Batch;
import com.xhgj.srm.common.Constants_Redis;
import com.xhgj.srm.common.config.SrmConfig;
import com.xhgj.srm.common.enums.purchase.order.PurchaseApplyOperationPermissionsEnum;
import com.xhgj.srm.jpa.dao.ContractDao;
import com.xhgj.srm.jpa.dao.FileDao;
import com.xhgj.srm.jpa.dao.GroupDao;
import com.xhgj.srm.jpa.dao.MeetingDao;
import com.xhgj.srm.jpa.dao.SearchSchemeDao;
import com.xhgj.srm.jpa.dao.SupplierDao;
import com.xhgj.srm.jpa.dao.SupplierOrderDao;
import com.xhgj.srm.jpa.dao.SupplierPerformanceDao;
import com.xhgj.srm.jpa.dao.UserCheckDao;
import com.xhgj.srm.jpa.dao.UserDao;
import com.xhgj.srm.jpa.dao.UserToGroupDao;
import com.xhgj.srm.jpa.entity.Contract;
import com.xhgj.srm.jpa.entity.Group;
import com.xhgj.srm.jpa.entity.Meeting;
import com.xhgj.srm.jpa.entity.Mission;
import com.xhgj.srm.jpa.entity.PermissionType;
import com.xhgj.srm.jpa.entity.SearchScheme;
import com.xhgj.srm.jpa.entity.SupplierInGroup;
import com.xhgj.srm.jpa.entity.SupplierOrder;
import com.xhgj.srm.jpa.entity.User;
import com.xhgj.srm.jpa.entity.UserCheck;
import com.xhgj.srm.jpa.entity.UserToGroup;
import com.xhgj.srm.jpa.repository.GroupRepository;
import com.xhgj.srm.jpa.repository.PermissionTypeRepository;
import com.xhgj.srm.jpa.repository.SupplierRepository;
import com.xhgj.srm.jpa.repository.UserRepository;
import com.xhgj.srm.jpa.repository.UserToGroupRepository;
import com.xhgj.srm.sender.mq.sender.BatchTaskMqSender;
import com.xhiot.boot.core.common.exception.CheckException;
import com.xhiot.boot.core.common.util.BootMailUtil;
import com.xhiot.boot.core.common.util.DateUtils;
import com.xhiot.boot.core.common.util.ExcelUtil;
import com.xhiot.boot.core.common.util.StringUtils;
import com.xhiot.boot.framework.jpa.repository.BootBaseRepository;
import com.xhiot.boot.mvc.base.PageResult;
import com.xhiot.boot.redis.util.RedisUtil;
import java.io.File;
import java.io.FileOutputStream;
import java.io.OutputStream;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;
import org.springframework.web.multipart.MultipartFile;

/**
 * <AUTHOR>
 * @since 2021/2/24 09:51
 */
@Service
@Slf4j
public class UserServiceImpl implements UserService {

  private final String baseUrl;
  private final String tempUrl;
  @Autowired private UserRepository repository;
  @Autowired private UserDao userDao;
  @Autowired private UserCheckDao userCheckDao;
  @Autowired private GroupDao groupDao;
  @Autowired private MeetingDao meetingDao;
  @Autowired private ManageHttpUtil httpUtil;
  @Autowired private FileDao fileDao;
  @Autowired private GroupRepository groupRepository;
  @Autowired private UserCheckService userCheckService;
  @Autowired private SearchSchemeDao searchSchemeDao;
  @Autowired private UserToGroupService userToGroupService;
  @Autowired private UserToGroupDao userToGroupDao;
  @Autowired private PermissionTypeService userToPermissionService;
  @Autowired private AddOrUpdateUserMapper addOrUpdateUserMapper;
  @Autowired private UserDetailMapper userDetailMapper;
  @Autowired private ImportExcelUtil importExcelUtil;
  @Autowired private MissionService missionService;
  @Autowired private BatchTaskMqSender batchTaskMqSender;
  @Autowired private SupplierInGroupService supplierInGroupService;
  @Autowired private GroupService groupService;
  @Autowired private UserToGroupRepository userToGroupRepository;
  @Autowired private RedisUtil redisUtil;
  @Resource private ContractDao contractDao;
  @Resource private SupplierPerformanceDao supplierPerformanceDao;
  @Resource
  private PermissionTypeRepository permissionTypeRepository;
  @Autowired
  private SupplierOrderDao supplierOrderDao;
  public UserServiceImpl(SrmConfig config) {
    this.baseUrl = config.getUploadUrl();
    this.tempUrl = config.getMeetingTempUrl();
  }

  @Override
  public BootBaseRepository<User, String> getRepository() {
    return repository;
  }

  @Override
  public UserDetails loadUserInfo(User user) {
    return new SrmUserDetails(user);
  }

  @Override
  public UserDetails loadUserByName(String name) {
    if (name.equals(LoginReq.ADMIN_SRM)) {
      name = LoginReq.ADMIN;
    }
    User user = getUserByName(name);
    if (user == null) {
      throw new UsernameNotFoundException("人员【" + name + "】不存在！");
    }
    return loadUserInfo(user);
  }

  @Override
  public User getUserByName(String name) {
    return userDao.getUserByName(name);
  }

  @Override
  public User getUserByRealName(String realName) {
    return userDao.getUserByRealName(realName);
  }

  @Override
  public PageResult<UserPageDTO> getUserPage(
      String realName,
      String depart,
      String erpCode,
      String mobile,
      String email,
      String schemeId,
      String userId,
      int pageNo,
      int pageSize) {
    // 查询方案
    if (StringUtils.isNullOrEmpty(schemeId)) {
      SearchScheme search =
          searchSchemeDao.getDefaultSearchScheme(userId, Constants.SEARCH_TYPE_USER);
      if (search != null) {
        schemeId = search.getId();
      }
    }
    if (!StringUtils.isNullOrEmpty(schemeId)) {
      SearchScheme search = searchSchemeDao.get(schemeId);
      if (search != null && !StringUtils.isNullOrEmpty(search.getContent())) {
        JSONObject searchJo = JSONObject.parseObject(search.getContent());
        if (searchJo != null) {
          depart = StrUtil.blankToDefault(depart,searchJo.containsKey("deptName") ?
              searchJo.getString(
              "deptName") : "");
          realName = StrUtil.blankToDefault(realName,searchJo.containsKey("realName") ?
              searchJo.getString("realName") : "");
          erpCode = StrUtil.blankToDefault(erpCode,searchJo.containsKey("code") ?
              searchJo.getString(
              "code") : "");
          mobile = StrUtil.blankToDefault(mobile,searchJo.containsKey("mobile") ?
              searchJo.getString(
              "mobile") : "");
          email =StrUtil.blankToDefault( email,searchJo.containsKey("email") ? searchJo.getString(
              "email") : "");
        }
      }
    }
    Page<User> page =
        userDao.getUserPage(
            realName, depart, erpCode, mobile, email, StrUtil.EMPTY, pageNo, pageSize);
    int totalPages = page.getTotalPages();
    List<UserPageDTO> pageDataList = new ArrayList<>();
    if (!(pageNo > totalPages)) {
      List<User> userList = page.getContent();
      PageUtil.setOneAsFirstPageNo();
      for (User user : userList) {
        UserPageDTO data = new UserPageDTO(user);
        pageDataList.add(data);
      }
    }
    return new PageResult<>(pageDataList, page.getTotalElements(), totalPages, pageNo, pageSize);
  }

  @Override
  public PageResult<PurchaseUserPageDTO> getPurchaseUserPage(
      String realName, Integer pageNo, Integer pageSize) {
    Page<User> page =
        userDao.getUserPage(
            realName,
            StrUtil.EMPTY,
            StrUtil.EMPTY,
            StrUtil.EMPTY,
            StrUtil.EMPTY,
            StrUtil.EMPTY,
            pageNo,
            pageSize);
    int totalPages = page.getTotalPages();
    List<PurchaseUserPageDTO> pageDataList = new ArrayList<>();
    if (!(pageNo > totalPages)) {
      List<User> userList = page.getContent();
      PageUtil.setOneAsFirstPageNo();
      for (User user : userList) {
        PurchaseUserPageDTO data = new PurchaseUserPageDTO(user);
        pageDataList.add(data);
      }
    }
    return new PageResult<>(pageDataList, page.getTotalElements(), totalPages, pageNo, pageSize);
  }

  @Override
  public List<String> getAllUserIdList(String state, String excludeId) {
    return CollUtil.emptyIfNull(repository.getAllByStateAndExcludeId(state, excludeId));
  }

  @Override
  public void batchUpdateUserSupplierOrderScope() {
    List<User> userList =
        repository.getAllByStateAndRole(
            Constants.STATE_OK, Constants.SUPPLIER_USER_ROLE_ORDINARY);
    for (User user : userList) {
      userToPermissionService.saveOrUpdateUserToPermission(
        Constants.DATA_PERMISSIONS_TYPE_OWM,
        user.getId(),
        Constants.USER_PERMISSION_SUPPLIER_ORDER);
    }
  }

  @Transactional(rollbackFor = Exception.class)
  @Override
  public void addOrUpdateUser(UserAddOrUpdateParamDTO userAddParamDTO) {
    String userId = userAddParamDTO.getId();
    if (userDao.getUserCountByNameOrErpCode(
            userAddParamDTO.getMobile(), userAddParamDTO.getErpCode(), userId)
        > 0) {
      throw new CheckException("已存在该用户");
    }
    List<CheckRelationDTO> checkRelationList = userAddParamDTO.getCheckRelationDTOList();
    if (CollUtil.isNotEmpty(checkRelationList)) {
      for (CheckRelationDTO checkRelationDTO : checkRelationList) {
        if (StringUtils.isNullOrEmpty(checkRelationDTO.getErpCode())) {
          throw new CheckException("审核人ERP编码为空");
        }
      }
    }
    //未在mpm授权登录的用户自动授权
    if (StrUtil.isNotBlank(userAddParamDTO.getOaId())) {
      if (!httpUtil.allocatePersons(userAddParamDTO.getOaId())) {
        throw new CheckException("当前人员授权失败");
      }
    }
    User user =
        Optional.ofNullable(userId)
            .filter(StrUtil::isNotEmpty)
            .map(id -> get(id, () -> CheckException.noFindException(User.class, id)))
            .orElseGet(User::new);
    //sap版本 用户code变动 修改code时 同步其他表code
    String code = user.getCode();
    if(StrUtil.isNotEmpty(code)){
      userCheckDao.updateCodeByCode(code,userAddParamDTO.getErpCode());
      supplierPerformanceDao.updateByErpCode(code,userAddParamDTO.getErpCode());
    }

    user = addOrUpdateUserMapper.sourceToTarget(userAddParamDTO, user);
    repository.save(user);
    // 分配组织部门
    userToGroupService.addUserToGroup(userAddParamDTO.getGroupParams(), user);
    // 存储审核关系信息
    userCheckService.saveUserCheckInBatch(checkRelationList, user);
    // 储存数据范围和操作权限
    userToPermissionService.saveOrUpdateUserToPermission(
        userAddParamDTO.getContractDataScope(), user.getId(), Constants.USER_PERMISSION_CONTRACT);
    userToPermissionService.saveOrUpdateUserToPermission(
        userAddParamDTO.getInquiryDataScope(), user.getId(), Constants.USER_PERMISSION_INQUIRY);
    userToPermissionService.saveOrUpdateUserToPermission(
        userAddParamDTO.getBlockSupplier(), user.getId(), Constants.USER_PERMISSION_BLOCK_SUPPLIER);
    userToPermissionService.saveOrUpdateUserToPermission(
        userAddParamDTO.getUpdateSupplier(),
        user.getId(),
        Constants.USER_PERMISSION_UPDATE_SUPPLIER);
    userToPermissionService.saveOrUpdateUserToPermission(
        userAddParamDTO.getSupplierOrderDataScope(),
        user.getId(),
        Constants.USER_PERMISSION_SUPPLIER_ORDER);
    userToPermissionService.saveOrUpdateUserToPermission(
        StrUtil.blankToDefault(userAddParamDTO.getPurchaseApplyDataScope(),Constants.DATA_PERMISSIONS_TYPE_OWM),
        user.getId(),
        Constants.USER_PERMISSION_PURCHASE_APPLY);
    userToPermissionService.saveOrUpdateUserToPermission(
        StrUtil.blankToDefault(userAddParamDTO.getImportPriceLibrary(),
            com.xhiot.boot.core.common.constants.Constants.NO),
        user.getId(),
        Constants.USER_PERMISSION_IMPORT_PRICE_LIBRARY);
    userToPermissionService.saveOrUpdateUserToPermission(
        userAddParamDTO.getInvoiceDataScope(),
        user.getId(),
        Constants.USER_PERMISSION_INVOICE);
    userToPermissionService.saveOrUpdateUserToPermission(
        StrUtil.blankToDefault(userAddParamDTO.getPriceLibrary()
            ,Constants.DATA_PERMISSIONS_TYPE_ORGANIZATION),
        user.getId(),
        Constants.USER_PERMISSION_PRICE_LIBRARY);
    userToPermissionService.saveOrUpdateUserToPermission(
        StrUtil.blankToDefault(userAddParamDTO.getReturnExchangeDataScope()
            ,Constants.DATA_PERMISSIONS_TYPE_ORGANIZATION),
        user.getId(),
        Constants.USER_PERMISSION_RETURN_EXCHANGE_ORDER);

    userToPermissionService.saveOrUpdateUserToPermission(
        StrUtil.blankToDefault(userAddParamDTO.getReturnExchangeDataScope()
            ,Constants.DATA_PERMISSIONS_TYPE_ORGANIZATION),
        user.getId(),
        Constants.USER_PERMISSION_RETURN_EXCHANGE_ORDER);

    List<PermissionType> permissionTypes =
        permissionTypeRepository.findAllByUserIdAndTypeAndState(user.getId(),
            Constants.USER_PERMISSION_PURCHASE_APPLY_ORDER_PURCHASER, Constants.STATE_OK);
    for (PermissionType permissionType : CollUtil.emptyIfNull(permissionTypes)) {
      permissionTypeRepository.delete(permissionType);
    }
    userToPermissionService.saveOrUpdateUserToPermission(
        StrUtil.blankToDefault(userAddParamDTO.getPurchaseApplyOperationPermissions()
            , PurchaseApplyOperationPermissionsEnum.PURCHASER.getKey()),
        user.getId(),
        Constants.USER_PERMISSION_PURCHASE_APPLY_ORDER_PURCHASER);
    userToPermissionService.saveOrUpdateUserToPermission(
        StrUtil.blankToDefault(userAddParamDTO.getExportApplyForOrder(),
            com.xhiot.boot.core.common.constants.Constants.NO),
        user.getId(),
        Constants.USER_PERMISSION_EXPORT_APPLY_FOR_ORDER);
    userToPermissionService.saveOrUpdateUserToPermission(
        StrUtil.blankToDefault(userAddParamDTO.getExportSupplierOrder(),
            com.xhiot.boot.core.common.constants.Constants.NO),
        user.getId(),
        Constants.USER_PERMISSION_EXPORT_SUPPLIER_ORDER);
    userToPermissionService.saveOrUpdateUserToPermission(
        StrUtil.blankToDefault(userAddParamDTO.getUpdateLeader(),
            com.xhiot.boot.core.common.constants.Constants.NO),
        user.getId(),
        Constants.USER_PERMISSION_UPDATE_LEADER);
    userToPermissionService.saveOrUpdateUserToPermission(
        StrUtil.blankToDefault(userAddParamDTO.getExportFinancialVoucher(),
            Constants.NOT_EXPORT_IMPORT_KEY),
        user.getId(),
        Constants.USER_PERMISSION_EXPORT_FINANCIAL_VOUCHER
    );
    userToPermissionService.saveOrUpdateUserToPermission(
        StrUtil.blankToDefault(userAddParamDTO.getExportPaymentApply(),
            Constants.NOT_EXPORT_IMPORT_KEY),
        user.getId(),
        Constants.USER_PERMISSION_EXPORT_PAYMENT_APPLY
    );
    userToPermissionService.saveOrUpdateUserToPermission(
        StrUtil.blankToDefault(userAddParamDTO.getExportWarehouseAndReturnOrder(),
            Constants.NOT_EXPORT_IMPORT_KEY),
        user.getId(),
        Constants.USER_PERMISSION_EXPORT_WAREHOUSE_RETURN
    );
    userToPermissionService.saveOrUpdateUserToPermission(
        StrUtil.blankToDefault(userAddParamDTO.getExportInventory(),
            Constants.NOT_EXPORT_IMPORT_KEY),
        user.getId(),
        Constants.USER_PERMISSION_EXPORT_INVENTORY
    );
    userToPermissionService.saveOrUpdateUserToPermission(
        StrUtil.blankToDefault(userAddParamDTO.getExportReturnExchange(),
            Constants.EXPORT_OWM_PURCHASE_TYPE),
        user.getId(),
        Constants.USER_PERMISSION_EXPORT_RETURN_EXCHANGE
    );
    userToPermissionService.saveOrUpdateUserToPermission(
        StrUtil.blankToDefault(userAddParamDTO.getInventorySafety(),
            com.xhiot.boot.core.common.constants.Constants.NO),
        user.getId(),
        Constants.USER_PERMISSION_INVENTORY_SAFETY);

    userToPermissionService.saveOrUpdateUserToPermission(
        StrUtil.blankToDefault(userAddParamDTO.getAllOrgOrders(),
            com.xhiot.boot.core.common.constants.Constants.NO),
        user.getId(),
        Constants.USER_PERMISSION_VIEW_ALL_ORGANIZATION);

    userToPermissionService.saveOrUpdateUserToPermission(
        StrUtil.blankToDefault(userAddParamDTO.getExportAssembleDisassemble(),
            Constants.EXPORT_OWM_PURCHASE_TYPE),
        user.getId(),
        Constants.USER_PERMISSION_EXPORT_ASSEMBLY_DISASSEMBLY);
//    userToPermissionService.saveOrUpdateUserToPermission(
//        StrUtil.blankToDefault(userAddParamDTO.getExportInputInvoice(),
//            Constants.NOT_EXPORT_IMPORT_KEY),
//        user.getId(),
//        Constants.USER_PERMISSION_EXPORT_INPUT_INVOICE
//    );
    // 清除用户缓存
    redisUtil.del(Constants_Redis.CACHE_GET_USER_ID_LIST_KEY);
    redisUtil.del(Constants_Redis.CACHE_GET_USER_NAME_LIST_KEY);
    // v5.3.0 临时需求 处理名称：”未添加采购账号的合同也进到SRM“， 时间：2023/4/26
    List<Contract> contracts = null;
    if (!ObjectUtils.isEmpty(userAddParamDTO.getName())) {
      contracts = contractDao.getCreateManIdIsNullContractByCreateUser(userAddParamDTO.getName());
    }
    if (ObjectUtils.isEmpty(contracts)) {
      return;
    }
    contracts.forEach(
        contract -> {
          contract.setCreateManId(userAddParamDTO.getErpId());
          contract.setPurchaserName(userAddParamDTO.getName());
          contract.setPurchaserManId(userAddParamDTO.getErpId());
          contractDao.update(contract);
        });
  }

  /*  @Transactional(rollbackFor = Exception.class)
  @Override
  public void updateUser(UserUpdateParamDTO userUpdateParamDTO) {
    User user =
        repository
            .findById(userUpdateParamDTO.getId())
            .orElseThrow(
                () -> CheckException.noFindException(User.class, userUpdateParamDTO.getId()));
    List<CheckRelationDTO> checkRelationDTOList = userUpdateParamDTO.getCheckRelationDTOList();
    if (checkRelationDTOList != null && checkRelationDTOList.size() > 0) {
      for (CheckRelationDTO checkRelationDTO : checkRelationDTOList) {
        if (StringUtils.isNullOrEmpty(checkRelationDTO.getErpCode())) {
          throw new CheckException("审核人ERP编码为空");
        }
      }
    }
    // 更新供应商相应字段
    if (!userUpdateParamDTO.getDepartId().equals(user.getGroup().getId())) {
      List<Supplier> supplierList = supplierDao.getNormalSupplierListByPurchaser(user.getId());
      for (Supplier supplier : supplierList) {
        Group group =
            groupRepository
                .findById(userUpdateParamDTO.getDepartId())
                .orElseThrow(
                    () ->
                        CheckException.noFindException(
                            Group.class, userUpdateParamDTO.getDepartId()));
        if (group != null) {
          supplier.setDepartName(group.getName());
          supplier.setDepartId(group.getErpCode());
          supplierRepository.save(supplier);
        }
      }
    }
    user = userUpdateParamDTO.updateUser(user);
    if (!userUpdateParamDTO.getDepartId().equals(user.getGroup().getId())) {
      String departId = userUpdateParamDTO.getDepartId();
      Group group =
          groupRepository
              .findById(departId)
              .orElseThrow(() -> CheckException.noFindException(Group.class, departId));
      if (group != null) {
        String groupCode = group.getGroupCode();
        Group org = groupDao.getCurGroupByCode(groupCode, "");
        if (org == null) {
          throw new CheckException("【" + groupCode + "】组织不存在");
        }
        user.setDepart(group.getName());
        user.setDepartCode(group.getErpCode());
        user.setUserGroup(org.getName());
        user.setGroupErpCode(org.getErpCode());
        user.setGroup(group);
      }
    }
    repository.save(user);
    // 存储审核关系信息
    userCheckService.saveUserCheckInBatch(checkRelationDTOList, user);
  }*/

  @Override
  public void deleteUser(String userIds) {
    if (StringUtils.isNullOrEmpty(userIds)) {
      throw new CheckException("请求接口有误");
    }
    String[] users = userIds.split(",");
    for (String s : users) {
      User user =
          repository.findById(s).orElseThrow(() -> CheckException.noFindException(User.class, s));
      if (user != null) {
        List<SupplierInGroup> supplierList = supplierInGroupService.getEnterpriseByUser(s);
        if (supplierList != null && supplierList.size() > 0) {
          throw new CheckException(user.getRealName() + "存在关联的供应商,暂时无法删除");
        }
      }
    }
    for (String s : users) {
      User user =
          repository.findById(s).orElseThrow(() -> CheckException.noFindException(User.class, s));
      if (user != null) {
        userCheckService.deleteCheckUserByUser(s);
        user.setState(Constants.STATE_DELETE);
        repository.save(user);
      }
    }
  }

  @Override
  public UserDetailDTO getUserDetail(String userId) {
    User user = get(userId);
    if (user == null) {
      throw new CheckException("用户不存在");
    }
    List<UserCheck> userCheckList = userCheckDao.getUserCheckListByUser(userId);
    List<CheckRelationDTO> checkRelationList = new ArrayList<>();
    if (CollUtil.isNotEmpty(userCheckList)) {
      for (UserCheck uc : userCheckList) {
        CheckRelationDTO checkRelationDTO = new CheckRelationDTO();
        checkRelationDTO.setErpCode(uc.getUserErpCode());
        checkRelationDTO.setLevel(uc.getSupplierType());
        checkRelationDTO.setType(uc.getType());
        checkRelationList.add(checkRelationDTO);
      }
    }
    // 储存数据范围和操作权限
    String contractDataScope =
        userToPermissionService.getUserPermissionCodeByUserIdAndType(
            userId, Constants.USER_PERMISSION_CONTRACT);
    String inquiryDataScope =
        userToPermissionService.getUserPermissionCodeByUserIdAndType(
            userId, Constants.USER_PERMISSION_INQUIRY);
    String blockSupplier =
        userToPermissionService.getUserPermissionCodeByUserIdAndType(
            userId, Constants.USER_PERMISSION_BLOCK_SUPPLIER);
    String updateSupplier =
        userToPermissionService.getUserPermissionCodeByUserIdAndType(
            userId, Constants.USER_PERMISSION_UPDATE_SUPPLIER);
    String supplierOrderDataScope =
        userToPermissionService.getUserPermissionCodeByUserIdAndType(
            userId, Constants.USER_PERMISSION_SUPPLIER_ORDER);
    String purchaseApplyDataScope =
        userToPermissionService.getUserPermissionCodeByUserIdAndType(
            userId, Constants.USER_PERMISSION_PURCHASE_APPLY);
    String returnExchangeDataScope =
        StrUtil.blankToDefault(userToPermissionService.getUserPermissionCodeByUserIdAndType(
            userId, Constants.USER_PERMISSION_RETURN_EXCHANGE_ORDER),Constants.DATA_PERMISSIONS_TYPE_ORGANIZATION
        );

    String importPriceLibrary =
        StrUtil.blankToDefault(userToPermissionService.getUserPermissionCodeByUserIdAndType(
            userId, Constants.USER_PERMISSION_IMPORT_PRICE_LIBRARY),
            com.xhiot.boot.core.common.constants.Constants.NO);
    String invoiceDataScope =
        StrUtil.blankToDefault(userToPermissionService.getUserPermissionCodeByUserIdAndType(
            userId, Constants.USER_PERMISSION_INVOICE),Constants.DATA_PERMISSIONS_TYPE_ORGANIZATION
        );
    String priceLibrary =
        StrUtil.blankToDefault(userToPermissionService.getUserPermissionCodeByUserIdAndType(
                userId, Constants.USER_PERMISSION_PRICE_LIBRARY),Constants.DATA_PERMISSIONS_TYPE_ORGANIZATION
            );
    String exportApplyForOrder =
        StrUtil.blankToDefault(userToPermissionService.getUserPermissionCodeByUserIdAndType(
            userId, Constants.USER_PERMISSION_EXPORT_APPLY_FOR_ORDER),com.xhiot.boot.core.common.constants.Constants.NO
        );
    String exportSupplierOrder =
        StrUtil.blankToDefault(userToPermissionService.getUserPermissionCodeByUserIdAndType(
            userId, Constants.USER_PERMISSION_EXPORT_SUPPLIER_ORDER),com.xhiot.boot.core.common.constants.Constants.NO
        );
    String updateLeader =
        StrUtil.blankToDefault(userToPermissionService.getUserPermissionCodeByUserIdAndType(
            userId, Constants.USER_PERMISSION_UPDATE_LEADER),com.xhiot.boot.core.common.constants.Constants.NO
        );
    // 财务凭证列表导出权限
    String exportFinancialVoucher =
        StrUtil.blankToDefault(userToPermissionService.getUserPermissionCodeByUserIdAndType(
            userId, Constants.USER_PERMISSION_EXPORT_FINANCIAL_VOUCHER),
            Constants.NOT_EXPORT_IMPORT_KEY
        );
    // 付款申请列表导出权限
    String exportPaymentApply =
        StrUtil.blankToDefault(userToPermissionService.getUserPermissionCodeByUserIdAndType(
                userId, Constants.USER_PERMISSION_EXPORT_PAYMENT_APPLY),
            Constants.NOT_EXPORT_IMPORT_KEY
        );
    // 入库/退库单导出权限
    String exportWarehouseAndReturnOrder =
        StrUtil.blankToDefault(userToPermissionService.getUserPermissionCodeByUserIdAndType(
                userId, Constants.USER_PERMISSION_EXPORT_WAREHOUSE_RETURN),
            Constants.NOT_EXPORT_IMPORT_KEY
        );
    // 库存列表导出权限
    String exportInventory =
        StrUtil.blankToDefault(userToPermissionService.getUserPermissionCodeByUserIdAndType(
                userId, Constants.USER_PERMISSION_EXPORT_INVENTORY),
            Constants.NOT_EXPORT_IMPORT_KEY
        );
    // 退换货单导出权限
    String exportReturnExchange =
        StrUtil.blankToDefault(userToPermissionService.getUserPermissionCodeByUserIdAndType(
                userId, Constants.USER_PERMISSION_EXPORT_RETURN_EXCHANGE),
            Constants.EXPORT_OWM_PURCHASE_TYPE
        );
    //库存安全列表
    String inventorySafety =
        StrUtil.blankToDefault(userToPermissionService.getUserPermissionCodeByUserIdAndType(
            userId, Constants.USER_PERMISSION_INVENTORY_SAFETY),com.xhiot.boot.core.common.constants.Constants.NO
        );
    //查看权限内全部组织订单
    String allOrgOrders =
        StrUtil.blankToDefault(userToPermissionService.getUserPermissionCodeByUserIdAndType(
            userId, Constants.USER_PERMISSION_VIEW_ALL_ORGANIZATION),com.xhiot.boot.core.common.constants.Constants.NO
        );
    // 组装拆卸单导出权限
    String exportAssembleDisassemble = StrUtil.blankToDefault(
        userToPermissionService.getUserPermissionCodeByUserIdAndType(userId,
            Constants.USER_PERMISSION_EXPORT_ASSEMBLY_DISASSEMBLY),
        Constants.EXPORT_OWM_PURCHASE_TYPE);
    // 6.7.0 进项票导出需求注释
//    String exportInputInvoice =
//        StrUtil.blankToDefault(userToPermissionService.getUserPermissionCodeByUserIdAndType(
//            userId, Constants.USER_PERMISSION_EXPORT_INPUT_INVOICE),Constants.NOT_EXPORT_IMPORT_KEY
//        );
    PermissionType permissionType =
        permissionTypeRepository.findFirstByUserIdAndTypeAndState(userId,
            Constants.USER_PERMISSION_PURCHASE_APPLY_ORDER_PURCHASER, Constants.STATE_OK);
    // 分配组织部门
    List<UserToGroupTableDTO> userToGroupTableList = new ArrayList<>();
    List<UserToGroup> userGroupList =
        CollUtil.emptyIfNull(userToGroupDao.getUserToGroupList(userId));
    userGroupList.stream()
        .filter(
            userToGroup ->
                !StringUtils.isNullOrEmpty(userToGroup.getGroupId())
                    && !StringUtils.isNullOrEmpty(userToGroup.getDeptId()))
        .forEach(
            userToGroup -> {
              String groupId = userToGroup.getGroupId();
              Group group =
                  groupRepository
                      .findById(groupId)
                      .orElseThrow(() -> CheckException.noFindException(Group.class, groupId));
              String deptId = userToGroup.getDeptId();
              Group dept =
                  groupRepository
                      .findById(deptId)
                      .orElseThrow(() -> CheckException.noFindException(Group.class, deptId));
              userToGroupTableList.add(new UserToGroupTableDTO(userToGroup, group, dept));
            });
    // 组织名称
    List<String> userGroupNameList = new ArrayList<>();
    List<String> departNameList = new ArrayList<>();
    for (UserToGroup userToGroup : userGroupList) {
      String groupId = userToGroup.getGroupId();
      String deptId = userToGroup.getDeptId();
      Group group =
          groupService.get(groupId, () -> CheckException.noFindException(Group.class, groupId));
      Group dept =
          groupService.get(deptId, () -> CheckException.noFindException(Group.class, deptId));
      userGroupNameList.add(group.getName());
      departNameList.add(dept.getName());
    }
    UserDetailDTO userDetailDTO =
        userDetailMapper.sourceToTarget(
            user,
            checkRelationList,
            contractDataScope,
            inquiryDataScope,
            blockSupplier,
            updateSupplier,
          supplierOrderDataScope,
            purchaseApplyDataScope,
            importPriceLibrary,
            invoiceDataScope,
            priceLibrary,
            exportApplyForOrder,
            exportSupplierOrder,
            updateLeader,
            exportFinancialVoucher,
            exportPaymentApply,
            exportWarehouseAndReturnOrder,
            returnExchangeDataScope,
            exportInventory,
            exportReturnExchange,
            inventorySafety,
            allOrgOrders,
            exportAssembleDisassemble,
            userToGroupTableList);
    if (!user.getRoleList().contains(Constants.ROLEMAP_GENERALMANAGER)) {
      userDetailDTO.setInvitationCode(
          user.getGroup() != null ? user.getGroup().getGroupCode() + user.getMobile() : "");
    }
    userDetailDTO.setUserGroup(String.join(",", userGroupNameList));
    userDetailDTO.setDepart(String.join(",", departNameList));
    if (permissionType == null) {
      userDetailDTO.setPurchaseApplyOperationPermissions(
          PurchaseApplyOperationPermissionsEnum.PURCHASER.getKey());
    }else {
      userDetailDTO.setPurchaseApplyOperationPermissions(permissionType.getPermissionCode());
    }
    return userDetailDTO;
  }

  @Override
  public Boolean hasImportOrExportPermission(String userId,String type) {
    String permissionCodeByUserIdAndType =
        userToPermissionService.getUserPermissionCodeByUserIdAndType(userId, type);
    if (Constants.USER_PERMISSION_EXPORT_APPLY_FOR_ORDER.equals(type)
    ||Constants.USER_PERMISSION_EXPORT_SUPPLIER_ORDER.equals(type)){
      return Constants.YES.equals(permissionCodeByUserIdAndType) ||Constants.EXPORT_DEPT_TYPE.equals(permissionCodeByUserIdAndType);
    }
    return Constants.YES.equals(permissionCodeByUserIdAndType);
  }

  @Override
  public List<PurchaserDataDTO> getOperatorList(String groupErpCode) {
    List<PurchaserDataDTO> dataDTOS = new ArrayList<>();
    Group curGroupByErpCode = groupDao.getCurGroupByErpCode(groupErpCode);
    if(curGroupByErpCode!=null){
      List<User> operatorList = userDao.getOperatorList(curGroupByErpCode.getId());
      for (User user : operatorList) {
        PurchaserDataDTO dto = new PurchaserDataDTO();
        dto.setPurchaserId(user.getId());
        dto.setPurchaserName(user.getRealName());
        dto.setDeptName(user.getDepart());
        dto.setDeptCode(user.getDepartCode());
        dataDTOS.add(dto);
      }
    }
    return dataDTOS;
  }

  @Override
  public void addMailMeeting(String mails, String userId) {
    if (StringUtils.isNullOrEmpty(mails)) {
      throw new CheckException("请输入邮箱");
    }
    if (StringUtils.isNullOrEmpty(userId)) {
      throw new CheckException("请输入用户id");
    }
    User user =
        repository
            .findById(userId)
            .orElseThrow(() -> CheckException.noFindException(User.class, userId));
    if (user == null) {
      throw new CheckException("用户不存在");
    }
    user.setMeetingMails(mails);
    repository.save(user);
  }

  @Override
  public List<String> getMeetingMailDetail(String userId) {
    if (StringUtils.isNullOrEmpty(userId)) {
      throw new CheckException("请输入用户id");
    }
    User user =
        repository
            .findById(userId)
            .orElseThrow(() -> CheckException.noFindException(User.class, userId));
    if (user == null) {
      throw new CheckException("用户不存在");
    }
    String mails = user.getMeetingMails();
    List<String> res = new ArrayList<>();
    if (!StringUtils.isNullOrEmpty(mails)) {
      String[] emails = mails.split(",");
      for (String email : emails) {
        res.add(email);
      }
    }
    return res;
  }

  @SneakyThrows
  @Override
  public void sendMeetingMail() {

    XSSFWorkbook workbook = new XSSFWorkbook();
    XSSFSheet sheet =
        (XSSFSheet)
            ExcelUtil.createSheet(workbook, "会议列表", Arrays.asList(50, 30, 50, 30, 30, 50, 60));
    CellStyle baseStyle = ExcelUtil.getBaseStyle(workbook);
    CellStyle titleStyle = ExcelUtil.getTitleStyle(workbook);
    XSSFRow title = sheet.createRow(0);
    ExcelUtil.createCell(title, 0, "会议名称", titleStyle, false, false);
    ExcelUtil.createCell(title, 1, "参会地点", titleStyle, false, false);
    ExcelUtil.createCell(title, 2, "参会人员", titleStyle, false, false);
    ExcelUtil.createCell(title, 3, "参会部门", titleStyle, false, false);
    ExcelUtil.createCell(title, 4, "会议时间", titleStyle, false, false);
    ExcelUtil.createCell(title, 5, "供应商名称", titleStyle, false, false);
    ExcelUtil.createCell(title, 6, "会议附件链接", titleStyle, false, false);
    List<Meeting> meetingList = meetingDao.getMeetingListByLastWeek();
    if (meetingList != null && meetingList.size() > 0) {
      for (int i = 1; i < meetingList.size(); i++) {
        Meeting meet = meetingList.get(i);
        XSSFRow row = sheet.createRow(i);
        ExcelUtil.createCell(
            row,
            0,
            !StringUtils.isNullOrEmpty(meet.getName()) ? meet.getName() : "",
            baseStyle,
            false,
            false);
        ExcelUtil.createCell(
            row,
            1,
            !StringUtils.isNullOrEmpty(meet.getPlace()) ? meet.getPlace() : "",
            baseStyle,
            false,
            false);
        ExcelUtil.createCell(
            row,
            2,
            !StringUtils.isNullOrEmpty(meet.getPersonnel()) ? meet.getPersonnel() : "",
            baseStyle,
            false,
            false);
        ExcelUtil.createCell(
            row,
            3,
            !StringUtils.isNullOrEmpty(meet.getDepart()) ? meet.getDepart() : "",
            baseStyle,
            false,
            false);

        ExcelUtil.createCell(
            row,
            4,
            meet.getMeettime() > 0
                ? DateUtils.formatTimeStampToNormalDateTime(meet.getMeettime())
                : "",
            baseStyle,
            false,
            false);
        ExcelUtil.createCell(
            row,
            5,
            meet.getSupplier() != null
                    && !StringUtils.isNullOrEmpty(meet.getSupplier().getEnterpriseName())
                ? meet.getSupplier().getEnterpriseName()
                : "",
            baseStyle,
            false,
            false);
        // 会议附件链接
        List<com.xhgj.srm.jpa.entity.File> files = fileDao.getFileListBySId(meet.getId(), "");
        String fileUrls = "";
        if (files != null && files.size() > 0) {
          for (com.xhgj.srm.jpa.entity.File file : files) {
            String fileUrl = baseUrl + file.getUrl();
            fileUrls += fileUrl + "\n";
          }
        }
        ExcelUtil.createCell(row, 6, fileUrls, baseStyle, false, false);
      }
    }

    String fileName =
        "meeting" + DateUtils.formatTimeStampToNormalDate(System.currentTimeMillis()) + ".xlsx";
    String path = tempUrl;
    // 判断文件夹是否存在
    File parent = new File(path);
    if (!parent.exists()) {
      parent.mkdirs();
    }
    File file = new File(path, fileName);
    try(OutputStream os = new FileOutputStream(file);) {
      workbook.write(os);
      os.flush();
      User user = userDao.getUserByName("admin");
      if (user != null & !StringUtils.isNullOrEmpty(user.getMeetingMails())) {
        BootMailUtil.sendAttachmentsMail(user.getMeetingMails(), "SRM本周会议邮件，请查收", "SRM本周会议邮件", file);
      }
    }
  }

  @Override
  public void updateOAPersonInfo(String mdmId) {
    JSONObject usersJson = httpUtil.getOAUserInfoById(mdmId);
    if (usersJson != null) {
      String id = usersJson.containsKey("id") ? usersJson.getString("id") : "";
      String email = usersJson.containsKey("email") ? usersJson.getString("email") : "";
      String mobile = usersJson.containsKey("mobile") ? usersJson.getString("mobile") : "";
      User user = userDao.getUserByMdmId(id);
      if (user != null) {
        if (!StringUtils.isNullOrEmpty(email)) {
          user.setMail(email);
        }
        if (!StringUtils.isNullOrEmpty(mobile)) {
          user.setMobile(mobile);
          user.setName(mobile);
        }
        repository.save(user);
      }
    }
  }

  @Override
  public List<PurchaserDataDTO> getPurchaserList(String userId, String groupErpCode) {
    if (StringUtils.isNullOrEmpty(userId)) {
      throw new CheckException("用户id为空");
    }
    if (StringUtils.isNullOrEmpty(userId)) {
      throw new CheckException("组织的ERP编码为空");
    }
    User u =
        repository
            .findById(userId)
            .orElseThrow(() -> CheckException.noFindException(User.class, userId));
    List<PurchaserDataDTO> purchaserDataDTOList = new ArrayList<>();
    List<User> users = new ArrayList<>();
    if (u.getRoleList().contains(Constants.ROLEMAP_GENERALMANAGER)) {
      List<User> ulist = userDao.getUserByOrg(u.getGroupErpCode());
      if (ulist != null && ulist.size() > 0) {
        for (int i = 0; i < ulist.size(); i++) {
          User user = ulist.get(i);
          if (!StringUtils.isNullOrEmpty(user.getErpId())) {
            users.add(user);
          }
        }
      }
    } else if (u.getRoleList().contains(Constants.SUPPLIER_USER_ROLE_ADMIN)) {
      Group group = u.getGroup();
      if (group != null) {
        List<Group> curGroupList = new ArrayList<>();
        curGroupList.add(group);
        List<Group> groupList =
            groupDao.getGroupByParentIds(group.getGroupCode(), group.getParentIds());
        if (groupList != null) {
          for (Group useGroup : groupList) {
            if (!StringUtils.isNullOrEmpty(useGroup.getParentIds())) {
              String[] parentIds = useGroup.getParentIds().split(",");
              if (parentIds != null && parentIds.length > 0) {
                if (group.getId().equals(parentIds[0])) {
                  curGroupList.add(useGroup);
                }
              }
            }
          }
        }
        if (curGroupList != null && curGroupList.size() > 0) {
          for (Group useGroup : curGroupList) {
            List<User> userList = userDao.getUserByDepartId(useGroup.getId());
            if (userList != null && userList.size() > 0) {
              for (int i = 0; i < userList.size(); i++) {
                User user = userList.get(i);
                if (!StringUtils.isNullOrEmpty(user.getErpId())) {
                  users.add(user);
                }
              }
            }
          }
        }
      }
    }
    if (users != null && users.size() > 0) {
      for (User user : users) {
        PurchaserDataDTO purchaserDataDTO = new PurchaserDataDTO();
        purchaserDataDTO.setPurchaserId(user.getId());
        purchaserDataDTO.setPurchaserName(
            !StringUtils.isNullOrEmpty(user.getRealName()) ? user.getRealName() : "");
        purchaserDataDTO.setDeptName(
            u.getGroup() != null && !StringUtils.isNullOrEmpty(u.getGroup().getName())
                ? u.getGroup().getName()
                : "");
        purchaserDataDTO.setDeptCode(
            u.getGroup() != null && !StringUtils.isNullOrEmpty(u.getGroup().getErpCode())
                ? u.getGroup().getErpCode()
                : "");
        purchaserDataDTOList.add(purchaserDataDTO);
      }
    }
    return purchaserDataDTOList;
  }

  @Override
  public String getUserErpIdsByDepart(User u, String splitStr, String groupCode) {
    Assert.notNull(u, "用户为空");
    Assert.notEmpty(splitStr, "分隔符为空");
    StringBuilder users = new StringBuilder();
    Group group = groupDao.getCurGroupByErpCode(groupCode);
    Group depart = null;
    if (group != null) {
      UserToGroup userToGroup =
          userToGroupDao.getUserToGroupByUserIdAndGroupId(u.getId(), group.getId());
      if (userToGroup != null) {
        String departId = userToGroup.getDeptId();
        depart =
            groupRepository
                .findById(departId)
                .orElseThrow(() -> CheckException.noFindException(Group.class, departId));
      }
    }
    if (depart != null) {
      if (u.getRoleList().contains(Constants.SUPPLIER_USER_ROLE_ADMIN)) {
        List<Group> groups = groupDao.getGroupByParentIds("", depart.getId());
        groups.add(depart);
        StringBuilder finalUsers = users;
        groups.forEach(
            g -> {
              List<User> userList = userDao.getUserByDepartId(g.getId());
              if (CollUtil.isNotEmpty(userList)) {
                userList.forEach(
                    otherUser -> {
                      if (!StringUtils.isNullOrEmpty(otherUser.getErpId())) {
                        finalUsers.append(otherUser.getErpId()).append(splitStr);
                      }
                    });
              }
            });
      }
    }
    users.append(u.getErpId()).append(splitStr);
    if (users.length() > 0) {
      users = new StringBuilder(users.substring(0, users.length() - 1));
    }
    return users.toString();
  }

  @Override
  public String getUserIdsByDepart(User u, String splitStr) {
    Assert.notNull(u, "用户为空");
    Assert.notEmpty(splitStr, "分隔符为空");
    StringBuilder users = new StringBuilder();
    Group userGroup = groupDao.getCurGroupByErpCode(u.getDepartCode());
    if (userGroup != null) {
      if (u.getRoleList().contains(Constants.SUPPLIER_USER_ROLE_ADMIN)) {
        List<Group> groups = groupDao.getGroupByParentIds("", userGroup.getId());
        groups.add(userGroup);
        for (Group group : groups) {
          List<User> userList = userDao.getUserByDepartId(group.getId());
          if (CollUtil.isNotEmpty(userList)) {
            for (User otherUser : userList) {
              users.append("'").append(otherUser.getId()).append("'").append(splitStr);
            }
          }
        }
      } else if (u.getRoleList().contains(Constants.SUPPLIER_USER_ROLE_ORDINARY)) {
        users.append("'").append(u.getId()).append("'").append(splitStr);
      }
    } else {
      users.append("'").append(u.getId()).append("'").append(splitStr);
    }
    if (users.length() > 0) {
      users = new StringBuilder(users.substring(0, users.length() - 1));
    }
    return users.toString();
  }

  @Override
  public void batchUpdateUserDepart() {
    List<User> userList = userDao.getPurchaseUser();
    if (CollUtil.isNotEmpty(userList)) {
      for (User user : userList) {
        String groupId = user.getGroup() != null ? user.getGroup().getId() : "";
        if (!StringUtils.isNullOrEmpty(groupId)) {
          Group group =
              groupRepository
                  .findById(groupId)
                  .orElseThrow(() -> CheckException.noFindException(Group.class, groupId));
          user.setDepart(group.getName());
          user.setDepartCode(group.getErpCode());
          repository.save(user);
        }
      }
    }
  }

  @Override
  public User getUserById(String userId) {
    Assert.notEmpty(userId);
    return get(
        userId,
        () -> {
          throw new CheckException(Constants.INTERFACE_REQUEST_FAILED);
        });
  }

  @SneakyThrows
  @Override
  public void importUser(MultipartFile file, String userId) {
    Assert.notNull(file);
    Assert.notEmpty(userId);
    User user = getUserById(userId);
    String savePath = importExcelUtil.saveExcel(file);
    Mission mission =
        missionService.createMission(
            user, "导入-用户导入", Constants.PLATFORM_TYPE_AFTER, savePath, file.getOriginalFilename());
    batchTaskMqSender.toHandleBatchTask(
        mission.getId(), JSON.toJSONString(user), Constants_Batch.BATCH_TASK_USER_IN);
  }

  @Override
  public void exportUser(String userId, List<String> userIds) {
    Assert.notEmpty(userId);
    User user = getUserById(userId);
    Mission mission =
        missionService.createMission(user, "导出-用户导出", Constants.PLATFORM_TYPE_AFTER, null, null);
    HashMap<String, Object> params = new HashMap<>(3);
    params.put("userId", user.getId());
    params.put("userIds", CollUtil.emptyIfNull(userIds));
    batchTaskMqSender.toHandleBatchTask(
        mission.getId(), JSON.toJSONString(params), Constants_Batch.BATCH_TASK_USER_OUT);
  }

  @Override
  public User getByCode(String code) {
    Assert.notEmpty(code);
    return userDao.getUserByCode(code);
  }
  public User getByErpCode(String code) {
    Assert.notEmpty(code);
    return repository.findFirstByErpIdAndState(code, Constants.STATE_OK);
  }

  @Override
  public String getUserDescById(String userId, String groupId) {
    if(StrUtil.isEmpty(userId)){
      return null;
    }
    User user = getUserById(userId);
    if (StringUtils.isNullOrEmpty(groupId)) {
      throw new CheckException("不能正常获取人员【" + user.getRealName() + "】的组织，请联系管理员");
    }
    UserToGroup utg = userToGroupService.getByUserAndGroupId(userId, groupId);
    if (utg == null) {
      Group group =
          groupService.get(groupId, () -> CheckException.noFindException(Group.class, groupId));
      throw new CheckException(
          "用户【" + user.getRealName() + "】与组织【" + group.getName() + "】未分配好关系，请核实或联系管理员！");
    }
    String deptId = utg.getDeptId();
    List<String> deptNames = groupService.getDeptNames(deptId);
    deptNames.add(user.getRealName());
    return CollUtil.join(deptNames, " -> ");
  }

  @Override
  public boolean checkExistUserCode(String code) {
    return userDao.checkExistUserCode(code);
  }

  @Override
  public void synUserGroup(String userIds) {
    List<User> userList = userDao.getUserByIds(null);
    if (CollUtil.isNotEmpty(userList)) {
      userList.forEach(
          user -> {
            String groupId = user.getGroup() != null ? user.getGroup().getId() : "";
            String groupErpCode = StringUtils.emptyIfNull(user.getGroupErpCode());
            if (!StringUtils.isNullOrEmpty(groupId) && !StringUtils.isNullOrEmpty(groupErpCode)) {
              Group group = groupDao.getCurGroupByErpCode(user.getGroupErpCode());
              if (group != null) {
                UserToGroup userToGroup = new UserToGroup();
                userToGroup.setGroupId(group.getId());
                userToGroup.setUserId(user.getId());
                userToGroup.setCreateTime(System.currentTimeMillis());
                userToGroup.setDeptId(groupId);
                userToGroup.setState(Constants.STATE_OK);
                userToGroupRepository.save(userToGroup);
              }
              if (!user.getRoleList().equals(Constants.ROLE_ADMINISTRATOR)) {
                user.setRole(Constants.SUPPLIER_USER_ROLE_ORDINARY);
                repository.save(user);
              }
            }
          });
    }
  }

  @Override
  public boolean hasOperatePermission(
      String userId, String resourceOwnerId, String permissionType) {
    Assert.isTrue(Constants.USER_PERMISSIONS_DATA_TYPE.containsKey(permissionType));
    // 如果不传入用户 id，则认为没有权限
    if (StrUtil.isEmpty(userId)) {
      return false;
    }
    // 如果目标资源没有所属人，则认为谁都可以操作
    if (StrUtil.isEmpty(resourceOwnerId)) {
      return true;
    }
    // 1. 获取需要判断权限用户的授权范围编码
    // （获取【需要判断权限的用户】能操作的授权范围，如【自己所在部门】）
    String sourcePermissionCode =
        userToPermissionService.getUserPermissionCodeByUserIdAndType(userId, permissionType);
    // 如果该用户的授权范围为空，则认为其不能操作
    if (StrUtil.isBlank(sourcePermissionCode)) {
      return false;
    }
    // 2. 获取目标资源的所属者授权范围的用户 id 列表
    // （根据第一步获取到的授权范围，获取【目标资源所属者】相关的用户 id 列表，如【目标资源所属者】同部门下的所有用户）
    List<String> permissionUserIdList =
        CollUtil.emptyIfNull(
            userToPermissionService.getUserIdList(sourcePermissionCode, resourceOwnerId));
    // 3. 如果用户属于目标资源所属用户的授权范围，则代表可以操作
    // （如果【需要判断权限的用户】属于【目标资源所属者】的同部门下，则代表有操作权限）
    return permissionUserIdList.contains(userId);
  }

  @Override
  public long synUserMdmIdByUserMdmIdIsNull() {
    List<User> userList =
        CollUtil.emptyIfNull(
            repository.getAllByStateAndMdmIdOrMdmIdIsNull(Constants.STATE_OK, StrUtil.EMPTY));
    userList.stream()
        .filter(Objects::nonNull)
        .forEach(
            user -> {
              String mdmId = httpUtil.getOaPersonMdmIdByMobile(user.getMobile());
              user.setMdmId(mdmId);
              save(user);
            });
    return userList.size();
  }

  @Override
  public String getNameById(String userId) {
    if (StrUtil.isBlank(userId)) {
      return "";
    }
    User user = get(userId, () -> CheckException.noFindException(User.class, userId));
    return user.getRealName();
  }

  @Override
  public String getNameByErpCode(String erpCode) {
    if (StrUtil.isBlank(erpCode)) {
      return "";
    }
    User user = getByCode(erpCode);
    if (user == null) {
      return "";
    }
    return user.getRealName();
  }

  @Override
  public boolean isAdmin(User user) {
    Assert.notNull(user);
    return Objects.equals(user.getRealName(), Constants.ADMIN_REAL_NAME) && Objects.equals(
        user.getName(), Constants.USERNAME);
  }

  @Override
  public List<UserToGroupTableDTO> getUserDept(String groupCode, Boolean containsNoErp, User user) {
    List<UserToGroupTableDTO> userToGroupTableList = new ArrayList<>();
    List<UserToGroup> userGroupList =
        CollUtil.emptyIfNull(userToGroupDao.getUserToGroupList(user.getId()));
    userGroupList.stream()
        //过滤掉不是该组织下的部门
        .filter(
            userToGroup ->{
              if (StrUtil.isNotBlank(userToGroup.getGroupId())
                  && StrUtil.isNotBlank(userToGroup.getDeptId())) {
                Group group =
                    groupRepository
                        .findById(userToGroup.getGroupId())
                        .orElseThrow(() -> CheckException.noFindException(Group.class, userToGroup.getGroupId()));
                  return Objects.equals(group.getCode(), groupCode);
              }
              return false;
            })
        //过滤掉没有erp编码的部门
        .filter(userToGroup -> {
          if (Boolean.TRUE.equals(containsNoErp)) {
            return true;
          }
          String deptId = userToGroup.getDeptId();
          Group dept =
              groupRepository
                  .findById(deptId)
                  .orElseThrow(() -> CheckException.noFindException(Group.class, deptId));
          return StrUtil.isNotBlank(dept.getErpCode());
        })
        .forEach(
            userToGroup -> {
              String groupId = userToGroup.getGroupId();
              Group group =
                  groupRepository
                      .findById(groupId)
                      .orElseThrow(() -> CheckException.noFindException(Group.class, groupId));
              String deptId = userToGroup.getDeptId();
              Group dept =
                  groupRepository
                      .findById(deptId)
                      .orElseThrow(() -> CheckException.noFindException(Group.class, deptId));
              userToGroupTableList.add(new UserToGroupTableDTO(userToGroup, group, dept));
            });
    return userToGroupTableList;
  }

  @Override
  public Boolean hasPermissionByTypeAndCode(String userId, String permissionType,
      String permissionCode) {
    User user = Optional.ofNullable(get(userId))
        .orElseThrow(() -> CheckException.noFindException(User.class, userId));
    if (this.isAdmin(user)) {
      return true;
    }
    Set<String> permissionCodes =
        permissionTypeRepository.findAllByUserIdAndTypeAndState(userId, permissionType,
                Constants.STATE_OK).stream().map(PermissionType::getPermissionCode)
            .collect(Collectors.toSet());
    return permissionCodes.contains(permissionCode);
  }

  @Override
  public List<Group> getDepartmentInPurchaseOrder(String jobNumber, String userGroup) {
    if (StrUtil.isBlank(jobNumber) || StrUtil.isBlank(userGroup)) {
      return Collections.emptyList();
    }
    User user = getByCode(jobNumber);
    if (user == null) {
      throw new CheckException("srm系统没有此采购，请联系管理员同步");
    }
    Group group =
        groupRepository.findFirstByErpCodeAndState(userGroup, Constants.STATE_OK);
    if (group == null) {
      throw new CheckException("采购订单采购组织不存在");
    }
    List<UserToGroup> userToGroups =
        userToGroupRepository.findAllByUserIdAndGroupIdAndState(user.getId(), group.getId(),
            Constants.STATE_OK);
    if (CollUtil.isEmpty(userToGroups)) {
      return Collections.emptyList();
    }
    return userToGroups.stream().map(userToGroup -> {
      return groupRepository.findById(userToGroup.getDeptId())
          .orElseThrow(() -> new CheckException("采购员部门信息异常"));
    }).filter(group1 -> {
      return StrUtil.isNotBlank(group1.getErpCode()); //过滤掉没有erpCode的部门
    }).collect(Collectors.toList());

  }

  @Override
  public String getPermissionByType(String userId, String type) {
    PermissionType permissionType =
        permissionTypeRepository.findFirstByUserIdAndTypeAndState(userId, type, Constants.STATE_OK);
    return ObjectUtil.isNull(permissionType) ? null : permissionType.getPermissionCode();
  }

  @Override
  public String getExportReturnExchangePermission(String userId) {
    User user = Optional.ofNullable(get(userId))
        .orElseThrow(() -> CheckException.noFindException(User.class, userId));
    String code = getPermissionByType(userId, Constants.USER_PERMISSION_INVENTORY_SAFETY);
    if (user != null && user.getRoleList().contains(Constants.SUPPLIER_USER_ROLE_ADMIN)) {
      code = Constants.ALLOW_EXPORT_KEY;
    }
    return StrUtil.blankToDefault(code, Constants.NOT_EXPORT_IMPORT_KEY);
  }

  @Override
  public String getAllOrgOrdersPermission(String userId) {
    User user = Optional.ofNullable(get(userId))
        .orElseThrow(() -> CheckException.noFindException(User.class, userId));
    String code = getPermissionByType(userId, Constants.USER_PERMISSION_VIEW_ALL_ORGANIZATION);
    if (user != null && (user.getRoleList().contains(Constants.SUPPLIER_USER_ROLE_ADMIN)
        || Constants.USERNAME.equals(user.getName()))) {
      code = Constants.ALLOW_EXPORT_KEY;
    }
    return StrUtil.blankToDefault(code, Constants.NOT_EXPORT_IMPORT_KEY);
  }
}
