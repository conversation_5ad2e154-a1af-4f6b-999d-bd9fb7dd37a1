package com.xhgj.srm.api.config;/**
 * @since 2025/5/22 14:05
 */

import com.xhgj.srm.api.filter.FormFilter;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * <AUTHOR>
 */
@Configuration
public class WebFilterConfig implements WebMvcConfigurer {

  // 添加Filter配置
  @Bean
  public FilterRegistrationBean<FormFilter> formFilterRegistration() {
    FilterRegistrationBean<FormFilter> registration = new FilterRegistrationBean<>();
    registration.setFilter(new FormFilter());
    registration.addUrlPatterns("/*");
    registration.setName("formFilter");
    registration.setOrder(1);
    return registration;
  }

}
