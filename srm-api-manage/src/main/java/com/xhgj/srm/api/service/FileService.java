package com.xhgj.srm.api.service;

import com.xhgj.srm.api.dto.FileDTO;
import com.xhgj.srm.api.dto.supplier.SupplierFileDTO;
import com.xhgj.srm.jpa.entity.File;
import com.xhiot.boot.framework.jpa.service.BootBaseService;
import java.util.List;
import java.util.Optional;
import lombok.SneakyThrows;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR>
 * @since 2021/3/1 18:46
 */
public interface FileService extends BootBaseService<File, String> {

  /**
   * 添加附件
   *
   * @since 10:32 2019/9/25
   */
  void addFile(String cid, String filesall, String filetype);

  /**
   * 创建（组织内）供应商附件（先删后增）
   *
   * @param relationId 关联 id
   * @param files 参数对象列表
   * @param fileType 附件类型：{@link com.xhgj.srm.common.Constants#FILE_TYPE_TO_NAME};{@link
   *     com.xhgj.srm.common.Constants#SUPPLIER_AGREEMENT_FILE_TYPE_LIST}
   */
  void addSupplierFile(String relationId, List<SupplierFileDTO> files, String fileType);

  /**
   * 创建（组织内）供应商品牌附件（先删后增）
   * @param relationId
   * @param files
   * @param fileType
   */
  void addSupplierBrandFile(String relationId, List<SupplierFileDTO> files,
      String fileType);

  /**
   *
   * @param relationId
   * @param files
   * @param fileType
   */
  void addSupplierEvaluationTableFile(String relationId, List<SupplierFileDTO> files,
      String fileType);

  /**
   * 将供应商的协议文件复制到目标供应商副本
   *
   * @param supplierId 供应商 id
   * @param supplierFbId 目标供应商副本 id
   */
  void copySupplierAgreeFileToSupplierFb(String supplierId, String supplierFbId);
  /**
   * 将供应商的附件复制到目标供应商副本
   *
   * @param supplierId 供应商 id
   * @param supplierFbId 目标供应商副本 id
   */
  void copySupplierFileToSupplierFb(String supplierId, String supplierFbId);

  /**
   * 删除供应商资质证件照的附件
   * @param relationId
   */
  @Transactional
  void deleteInformationAllFile(String relationId);

  /**
   * 删除供应商的附件(除协议)
   *
   * @param supplierId 供应商 id
   * @since 16:51 2019/8/28
   */
  void deleteAllFileExxy(String supplierId);

  void updateSupplierFilesByFb(String supplierId, String fbid);

  void updateSupplierFbFilesByFb(String oldFbId, String fbid);

  void updateSupplierFbXyByFb(String oldFbId, String fbid);

  /**
   * 根据合同的 id 获得合同文件的数量
   *
   * @param contractSignedSupplierIdList 合同 id 的集合
   */
  long getContractFileCountByContractId(List<String> contractSignedSupplierIdList);

  /**
   * 根据组织下供应商的 id 集合获得该集合中有协议的数量
   *
   * @param myResponsibleSupplierIdList 组织下供应商 id 的集合 必传
   */
  long getNumberOfSuppliersWithAgreementBySupplierInGroupIds(
      List<String> myResponsibleSupplierIdList);

  /**
   * 根据关联 id 和关联类型获取附件列表
   *
   * @param relationId 关联 id
   * @param relationType 关联类型
   */
  List<File> getFileListByIdAndType(String relationId, String relationType);

  @SneakyThrows
  byte[] downloadZipMoreFile(List<String> fileIdList);

  /**
   * 保存文件
   * @param contractFile
   * @param userId
   * @param relationId
   * @param relationType
   */
  void saveFile(FileDTO contractFile, String userId, String relationId,
      String relationType);

  /**
   * 根据关联id和关联类型统计数量
   * @param relationId 关联id
   * @param relationType  关联类型
   * @return 统计数量
   */
  int countNumByRelationIdAndRelationType(String relationId, String relationType);

  /**
   * 获取文件半路经
   * @param ids id集合
   * @return 文件半路经
   */
  List<String> getUrls(List<String> ids);

  /**
   * 查询第一个根据关联id和关联类型
   * @param relationId 关联id
   * @param relationType 关联类型
   * @return Optional<File>
   */
  Optional<File> findFirstByRelationIdAndRelationType(String relationId, String relationType);

  void delete(String relationId, String relationType);

  void association(String id, String relationId, String relationType);

  /**
   * 根据id和类型获取文件集合
   * @param id
   * @param fileTypeLandingMerchantProductQualification
   * @return
   */
  List<File> getFileList(String id, String fileTypeLandingMerchantProductQualification);

  /**
   * 根据关联ids和关联类型获取附件列表(取最新一条)
   * @param contractIds
   * @param fileType
   * @return
   */
  List<File> findFirstByRelationIdInAndRelationType(List<String> contractIds, String fileType);
}
