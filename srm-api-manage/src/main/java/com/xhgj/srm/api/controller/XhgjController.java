package com.xhgj.srm.api.controller;

import com.xhgj.srm.api.service.XhgjService;
import com.xhgj.srm.request.dto.edge.CountryDTO;
import com.xhgj.srm.request.dto.edge.IndustryDTO;
import com.xhgj.srm.request.dto.edge.ProvinceCityDTO;
import com.xhiot.boot.mvc.base.ResultBean;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @since 2022/8/8 15:01
 */
@Api(tags = {"信息化接口"})
@RestController
@RequestMapping("xhgj")
public class XhgjController {
  @Autowired private XhgjService xhgjService;

  @ApiOperation(value = "获取所有国家信息")
  @GetMapping("getAllCountry")
  public ResultBean<List<CountryDTO>> getAllCountry() {
    return new ResultBean<>(xhgjService.getAllCountry());
  }

  @ApiOperation(value = "获取所有行业信息")
  @GetMapping("getAllIndustry")
  public ResultBean<List<IndustryDTO>> getAllIndustry() {
    return new ResultBean<>(xhgjService.getAllIndustry());
  }

  @ApiOperation(value = "获取所有省市信息")
  @GetMapping("getAllProvinceCity")
  public ResultBean<List<ProvinceCityDTO>> getAllProvinceCity() {
    return new ResultBean<>(xhgjService.getAllProvinceCity());
  }

  @ApiOperation(value = "获取所有省市信息包含全国")
  @GetMapping("getAllProvinceCityWithCountry")
  public ResultBean<List<ProvinceCityDTO>> getAllProvinceCityWithCountry() {
    return new ResultBean<>(xhgjService.getAllProvinceCityWithCountry());
  }
}
