package com.xhgj.srm.api.dto.purchase.order;

import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.StrUtil;
import com.xhgj.srm.common.enums.WarehouseEnum;
import com.xhgj.srm.common.utils.supplierorder.BigDecimalUtil;
import com.xhgj.srm.jpa.entity.PurchaseApplyForOrder;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import lombok.Data;

/**
 * Created by Geng Shy on 2023/12/12
 */
@Data
public class PurchaseApplyForOrderProductDetailsVO {

  @ApiModelProperty("id")
  private String id;
  @ApiModelProperty("物料编码")
  private String productCode;
  @ApiModelProperty("品牌")
  private String brand;
  @ApiModelProperty("物料名称")
  private String productName;
  @ApiModelProperty("型号规格")
  private String model;
  @ApiModelProperty("基本单位")
  private String unit;
  @ApiModelProperty("描述")
  private String describe;
  @ApiModelProperty("申请数量")
  private BigDecimal applyForNumber;
  @ApiModelProperty("仓库")
  private String warehouse;
  @ApiModelProperty("计划需求日期")
  private Long planDemandDate;
  @ApiModelProperty("主图")
  private String mainImageUrl;
  @ApiModelProperty("序号")
  private Integer serialNumber;
  @ApiModelProperty("物料行备注")
  private String materialLineRemarks;
  @ApiModelProperty("销售单价")
  private BigDecimal salesUnitPrice;
  @ApiModelProperty("销售需求数量")
  private BigDecimal salesDemandQuantity;
  @ApiModelProperty("MPM参考结算价")
  private BigDecimal mpmReferenceSettlementPrice;
  @ApiModelProperty("类目名称")
  private String cateName;

  @ApiModelProperty("采购员编码")
  private String empCode;

  @ApiModelProperty("采购员名称")
  private String empName;

  @ApiModelProperty("产品经理名称")
  private String productManagerName;



  public static PurchaseApplyForOrderProductDetailsVO getInstance(
      PurchaseApplyForOrder purchaseApplyForOrder, String pictureUrl) {
    Assert.notNull(purchaseApplyForOrder);
    PurchaseApplyForOrderProductDetailsVO vo = new PurchaseApplyForOrderProductDetailsVO();
    vo.id = purchaseApplyForOrder.getId();
    vo.productCode = purchaseApplyForOrder.getProductCode();
    WarehouseEnum warehouseEnum = WarehouseEnum.fromCode(purchaseApplyForOrder.getWarehouse());
    vo.warehouse = warehouseEnum != null ?
        warehouseEnum.getName() : "";
    vo.brand = purchaseApplyForOrder.getBrand();
    vo.productName = purchaseApplyForOrder.getProductName();
    vo.model = purchaseApplyForOrder.getModel();
    vo.unit = StrUtil.emptyIfNull(purchaseApplyForOrder.getUnitName());
    vo.applyForNumber = purchaseApplyForOrder.getApplyForNumber();
    vo.planDemandDate = purchaseApplyForOrder.getPlanDemandDate();
    vo.serialNumber = purchaseApplyForOrder.getSerialNumber();
    vo.materialLineRemarks = purchaseApplyForOrder.getMaterialLineRemarks();
    vo.describe = purchaseApplyForOrder.getMaterialDescription();
    vo.salesUnitPrice = BigDecimalUtil.formatForStandard(purchaseApplyForOrder.getSalesUnitPrice());
    vo.salesDemandQuantity =
        BigDecimalUtil.formatForStandard(purchaseApplyForOrder.getSalesDemandQuantity());
    vo.mpmReferenceSettlementPrice =
        BigDecimalUtil.formatForStandard(purchaseApplyForOrder.getMpmReferenceSettlementPrice());
    vo.mainImageUrl = pictureUrl;
    return vo;
  }
}
