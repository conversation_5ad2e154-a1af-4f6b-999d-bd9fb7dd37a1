package com.xhgj.srm.api.dto.filing;

import com.xhgj.srm.jpa.dto.BaseDefaultSearchSchemeForm;
import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.NotBlank;
import lombok.Data;
import java.util.HashMap;
import java.util.Map;

@Data
public class FilingPageQuery implements BaseDefaultSearchSchemeForm {

  @NotBlank(message = "登录人id不能为空")
  @ApiModelProperty("登录人id")
  private String userId;
  @ApiModelProperty(value = "组织编码", required = true)
  @NotBlank(message = "组织编码 必传")
  private String userGroup;
  @ApiModelProperty("报备单号")
  private String filingNo;
  @ApiModelProperty("报备单状态(1--报备中,2--已派单,3--已撤回)")
  private String fillingState;
  @ApiModelProperty("供应商名称")
  private String supplierName;
  @ApiModelProperty("平台")
  private String platform;
  @ApiModelProperty("客户单位")
  private String customer;
  @ApiModelProperty("报备金额")
  private String price;
  @ApiModelProperty("报备日期(起始)")
  private String filingStartTime;
  @ApiModelProperty("报备日期(截止)")
  private String filingEndTime;
  @ApiModelProperty("到期日(起始)")
  private String filingArriveStartTime;
  @ApiModelProperty("到期日(截止)")
  private String filingArriveEndTime;
  @ApiModelProperty("客户订单号")
  private String orderNo;
  @ApiModelProperty("报备类型")
  private String filingType;
  @ApiModelProperty("对接销售名称")
  private String dockingSalesName;
  @ApiModelProperty("备注")
  private String remark;
  @ApiModelProperty("方案id")
  private String schemeId;
  @ApiModelProperty(value = "当前页", example = "1")
  private Integer pageNo;
  @ApiModelProperty(value = "每页展示数量", example = "10")
  private Integer pageSize;

  public Integer getPageNo() {
    return pageNo == null ? 1 : pageNo;
  }

  public Integer getPageSize() {
    return pageSize == null ? 10 : pageSize;
  }

  public Map<String,Object> toQueryMap(String userIds) {
    Map<String,Object> map = new HashMap<>();
    map.put("filingNo",filingNo);
    map.put("state",fillingState);
    map.put("supplierName",supplierName);
    map.put("platform",platform);
    map.put("customer",customer);
    map.put("price",price);
    map.put("startTime",filingStartTime);
    map.put("endTime",filingEndTime);
    map.put("arriveStartTime",filingArriveStartTime);
    map.put("arriveEndTime",filingArriveEndTime);
    map.put("orderNo",orderNo);
    map.put("filingType",filingType);
    map.put("dockingSalesName",dockingSalesName);
    map.put("remark",remark);
    map.put("pageNo",getPageNo());
    map.put("pageSize",getPageSize());
    map.put("userIds",userIds);
    return map;
  }
}
