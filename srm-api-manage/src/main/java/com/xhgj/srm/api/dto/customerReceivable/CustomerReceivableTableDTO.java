package com.xhgj.srm.api.dto.customerReceivable;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import com.xhgj.srm.jpa.entity.CustomerReceivable;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/** <AUTHOR> @ClassName CustomerReceivableTableDTO */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CustomerReceivableTableDTO {

  @ApiModelProperty("大票项目编号")
  private String projectNo;

  @ApiModelProperty("发票号码")
  private String invoiceNo;

  @ApiModelProperty("发票日期")
  private String invoiceTime;

  @ApiModelProperty("价税合计")
  private String price;

  public CustomerReceivableTableDTO(CustomerReceivable customerReceivable) {
    this.projectNo = customerReceivable.getProjectNo();
    this.invoiceNo = customerReceivable.getInvoiceNo();
    this.price = customerReceivable.getPrice().toPlainString();
    this.invoiceTime =
        customerReceivable.getInvoiceTime() != null
            ? DateUtil.format(
                new Date(customerReceivable.getInvoiceTime()),
                DatePattern.NORM_DATETIME_MINUTE_PATTERN)
            : "";
  }
}
