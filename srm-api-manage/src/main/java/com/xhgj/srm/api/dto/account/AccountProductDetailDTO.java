package com.xhgj.srm.api.dto.account;

import com.xhgj.srm.common.utils.supplierorder.BigDecimalUtil;
import com.xhgj.srm.jpa.entity.OrderAccountProductDetail;
import com.xhiot.boot.core.common.util.StringUtils;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import lombok.Data;

@Data
public class AccountProductDetailDTO {

    @ApiModelProperty("物料编码")
    private String code;
    @ApiModelProperty("品牌")
    private String brand;
    @ApiModelProperty("商品名称")
    private String name;
    @ApiModelProperty("型号")
    private String model;
    @ApiModelProperty("数量")
    private BigDecimal num;
    @ApiModelProperty("单位")
    private String unit;
    @ApiModelProperty("单价")
    private BigDecimal price;
    @ApiModelProperty("客户订单号")
    private String orderNo;


    public AccountProductDetailDTO(OrderAccountProductDetail orderAccountProductDetail) {
        this.code = StringUtils.emptyIfNull(orderAccountProductDetail.getCode());
        this.brand = StringUtils.emptyIfNull(orderAccountProductDetail.getBrand());
        this.name = StringUtils.emptyIfNull(orderAccountProductDetail.getName());
        this.model = StringUtils.emptyIfNull(orderAccountProductDetail.getModel());
        this.num = BigDecimalUtil.formatForStandard(orderAccountProductDetail.getNum());
        this.unit = StringUtils.emptyIfNull(orderAccountProductDetail.getUnit());
        this.price = orderAccountProductDetail.getPrice();
        this.orderNo = StringUtils.emptyIfNull(orderAccountProductDetail.getOrderNo());
    }
}
