package com.xhgj.srm.api.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.exceptions.ExceptionUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.lang.Pair;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.CharUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.google.common.collect.ArrayListMultimap;
import com.google.common.collect.Multimap;
import com.xhgj.srm.api.dto.FileDTO;
import com.xhgj.srm.api.dto.purchase.order.PurchaseOrderLargeTicketInfoDTO;
import com.xhgj.srm.api.factory.MapStructFactory;
import com.xhgj.srm.api.service.SupplierTemplateFieldService;
import com.xhgj.srm.api.factory.SupplierOrderFactory;
import com.xhgj.srm.api.service.VerifyConfigService;
import com.xhgj.srm.common.Constants_OmsLargeTicket;
import com.xhgj.srm.common.dto.PlatformLargeTicketProjectDTO;
import com.xhgj.srm.common.enums.SettleCurrency;
import com.xhgj.srm.dto.InputInvoiceOrderWithDetail;
import com.xhgj.srm.api.dto.PurchaseOrderListDTO;
import com.xhgj.srm.api.dto.PurchaseOrderPageQuery;
import com.xhgj.srm.api.dto.PurchaseOrderProductListDTO;
import com.xhgj.srm.api.dto.PurchaseOrderProductTableHeaderQuery;
import com.xhgj.srm.api.dto.PurchaseOrderTableHeaderQuery;
import com.xhgj.srm.api.dto.SupplierOrderInternalRemarkParam;
import com.xhgj.srm.api.dto.UpdateSupplierOrderBaseInfoDTO;
import com.xhgj.srm.api.dto.order.AddOrderAnnexParam.AnnexParam;
import com.xhgj.srm.api.dto.purchase.order.AddPurchaseOrderDeliveryParam;
import com.xhgj.srm.api.dto.purchase.order.AddPurchaseOrderReturnParam;
import com.xhgj.srm.api.dto.purchase.order.AddPurchaseOrderReturnParam.ProductDetail;
import com.xhgj.srm.api.dto.purchase.order.ConsignmentReturnOrderParam;
import com.xhgj.srm.api.dto.purchase.order.ProductDetailParam;
import com.xhgj.srm.api.dto.purchase.order.PurchaseOrderContractVO;
import com.xhgj.srm.api.dto.purchase.order.PurchaseOrderDetailedVO;
import com.xhgj.srm.api.dto.purchase.order.PurchaseOrderInvoiceProductVO;
import com.xhgj.srm.api.dto.purchase.order.PurchaseOrderInvoiceVO;
import com.xhgj.srm.api.dto.purchase.order.PurchaseOrderProductDetailedReturnVO;
import com.xhgj.srm.api.dto.purchase.order.PurchaseOrderProductDetailedVO;
import com.xhgj.srm.api.dto.purchase.order.PurchaseOrderReturnVO;
import com.xhgj.srm.api.dto.purchase.order.PurchaseOrderWarehousingEntryInfoVO;
import com.xhgj.srm.api.dto.purchase.order.ShippingAndWarehousingInformationVO;
import com.xhgj.srm.api.dto.purchase.order.UpdateNotesParam;
import com.xhgj.srm.api.dto.purchase.order.UpdateProductDetailParam;
import com.xhgj.srm.api.dto.purchase.order.UpdateSupplierContactParam;
import com.xhgj.srm.api.dto.purchase.order.UpdateWarehouseLogisticsParam;
import com.xhgj.srm.api.dto.supplierorder.CancelPurchaseOrderListDTO;
import com.xhgj.srm.api.dto.supplierorder.CancelPurchaseOrderParam;
import com.xhgj.srm.api.dto.supplierorder.CancelPurchaseOrderParam.PurchaseOrderProduct;
import com.xhgj.srm.api.dto.supplierorder.ExportPurchaseOrderProductParams;
import com.xhgj.srm.api.dto.supplierorder.OutBoundDeliveryPrams;
import com.xhgj.srm.api.dto.supplierorder.PuchaseOrderAddParams;
import com.xhgj.srm.api.dto.supplierorder.PuchaseOrderAddParams.EntrustProduct;
import com.xhgj.srm.api.dto.supplierorder.PuchaseOrderAddParams.supplierProduct;
import com.xhgj.srm.api.dto.supplierorder.PurchaseOrderInvoiceRelation;
import com.xhgj.srm.api.dto.supplierorder.PurchaseOrderPaymentTermsPageVO;
import com.xhgj.srm.api.dto.supplierorder.PurchaseOrderPaymentTermsParam;
import com.xhgj.srm.api.dto.supplierorder.PurchaseOrderPrepaidApplicationPreInfoDTO;
import com.xhgj.srm.api.dto.supplierorder.PurchaseOrderProductSearchForm;
import com.xhgj.srm.api.dto.supplierorder.PurchaseOrderWarehousingDTO;
import com.xhgj.srm.api.dto.supplierorder.RetreatWarehouseDTO;
import com.xhgj.srm.api.dto.supplierorder.SupplierOrderCountDTO;
import com.xhgj.srm.api.dto.supplierorder.UnCancelPurchaseOrderDTO;
import com.xhgj.srm.api.dto.supplierorder.UserScreeningConditionParam;
import com.xhgj.srm.api.dto.supplierorder.WarehouseEntryListParams;
import com.xhgj.srm.api.event.ExportFiledTemplateEventPublisher;
import com.xhgj.srm.api.service.ConfigShortMessageService;
import com.xhgj.srm.api.service.FileService;
import com.xhgj.srm.api.service.MissionService;
import com.xhgj.srm.api.service.NoticeCenterService;
import com.xhgj.srm.api.service.PaymentApplyRecordService;
import com.xhgj.srm.api.service.PermissionTypeService;
import com.xhgj.srm.api.service.PurchaseApplyForOrderService;
import com.xhgj.srm.api.service.PurchaseOrderPaymentTermsService;
import com.xhgj.srm.api.service.PurchaseOrderService;
import com.xhgj.srm.api.service.SearchSchemeService;
import com.xhgj.srm.api.service.SupplierOrderDetailService;
import com.xhgj.srm.api.service.SupplierOrderToFormService;
import com.xhgj.srm.api.service.SupplierService;
import com.xhgj.srm.api.service.SupplierUserService;
import com.xhgj.srm.api.service.UserService;
import com.xhgj.srm.jpa.repository.PaymentApplyRecordRepository;
import com.xhgj.srm.jpa.sharding.enums.VersionEnum;
import com.xhgj.srm.jpa.sharding.util.ShardingContext;
import com.xhgj.srm.request.dto.oms.PlatformLargeTicketParam;
import com.xhgj.srm.request.service.third.oms.OMSService;
import com.xhgj.srm.request.vo.BaseXhgjRes;
import com.xhgj.srm.util.ImportExcelUtil;
import com.xhgj.srm.api.utils.ManageSecurityUtil;
import com.xhgj.srm.common.Constants;
import com.xhgj.srm.common.Constants_Batch;
import com.xhgj.srm.common.Constants_FileRelationType;
import com.xhgj.srm.common.Constants_order;
import com.xhgj.srm.common.config.SrmConfig;
import com.xhgj.srm.common.constants.Constants_LockName;
import com.xhgj.srm.common.constants.Constants_Sap;
import com.xhgj.srm.common.enums.BooleanEnum;
import com.xhgj.srm.common.enums.LogicalOperatorsEnums;
import com.xhgj.srm.common.enums.NoticeCenterType;
import com.xhgj.srm.common.enums.PaymentApplyTypeEnums;
import com.xhgj.srm.common.enums.PaymentAuditStateEnum;
import com.xhgj.srm.common.enums.PurchaseApplicationTypeEnum;
import com.xhgj.srm.common.enums.PurchaseOrderTypeEnum;
import com.xhgj.srm.common.enums.SimpleBooleanEnum;
import com.xhgj.srm.common.enums.VoucherTypeEnum;
import com.xhgj.srm.common.enums.WarehouseEnum;
import com.xhgj.srm.common.enums.purchase.order.PurchaseOrderFilterTypeEnum;
import com.xhgj.srm.common.enums.purchase.order.PurchaseOrderProductFilterTypeEnum;
import com.xhgj.srm.common.enums.supplierorder.SupplierOrderFormExecutionStatusEnum;
import com.xhgj.srm.common.enums.supplierorder.SupplierOrderFormStatus;
import com.xhgj.srm.common.enums.supplierorder.SupplierOrderFormType;
import com.xhgj.srm.common.enums.supplierorder.SupplierOrderState;
import com.xhgj.srm.common.utils.FileUtil;
import com.xhgj.srm.common.utils.OrderNumUtil;
import com.xhgj.srm.common.utils.ParallelProcessUtil;
import com.xhgj.srm.common.utils.SAPToolUtils;
import com.xhgj.srm.common.utils.dingding.DingUtils;
import com.xhgj.srm.common.utils.supplierorder.BigDecimalUtil;
import com.xhgj.srm.jpa.annotations.DefaultSearchScheme;
import com.xhgj.srm.jpa.dao.FileDao;
import com.xhgj.srm.jpa.dao.PaymentApplyRecordDao;
import com.xhgj.srm.jpa.dao.PurchaseOrderPaymentTermsDao;
import com.xhgj.srm.jpa.dao.SupplierOrderDao;
import com.xhgj.srm.jpa.dao.SupplierOrderDetailDao;
import com.xhgj.srm.jpa.dao.SupplierOrderToFormDao;
import com.xhgj.srm.jpa.dto.RetreatWarehousePageDTO;
import com.xhgj.srm.jpa.dto.WarehousingDTO;
import com.xhgj.srm.jpa.dto.permission.MergeUserPermission;
import com.xhgj.srm.jpa.dto.permission.OperatorPermission;
import com.xhgj.srm.jpa.dto.permission.SearchPermission;
import com.xhgj.srm.jpa.dto.purchase.order.PurchaseOrderOutBoundDeliveryStatistics;
import com.xhgj.srm.jpa.dto.purchase.order.PurchaseOrderPaymentTermsDaoParam;
import com.xhgj.srm.jpa.dto.purchase.order.PurchaseOrderPaymentTermsStatistics;
import com.xhgj.srm.jpa.dto.purchase.order.PurchaseOrderProductStatistics;
import com.xhgj.srm.jpa.dto.purchase.order.PurchaseOrderStatistics;
import com.xhgj.srm.jpa.dto.purchase.order.PurchaseOrderWarehousingStatistics;
import com.xhgj.srm.jpa.entity.File;
import com.xhgj.srm.jpa.entity.FinancialVoucher;
import com.xhgj.srm.jpa.entity.Group;
import com.xhgj.srm.jpa.entity.InputInvoiceOrder;
import com.xhgj.srm.jpa.entity.Mission;
import com.xhgj.srm.jpa.entity.PaymentApplyDetail;
import com.xhgj.srm.jpa.entity.PaymentApplyRecord;
import com.xhgj.srm.jpa.entity.PurchaseApplyForOrder;
import com.xhgj.srm.jpa.entity.PurchaseOrderPaymentTerms;
import com.xhgj.srm.jpa.entity.SearchScheme;
import com.xhgj.srm.jpa.entity.Supplier;
import com.xhgj.srm.jpa.entity.SupplierOrder;
import com.xhgj.srm.jpa.entity.SupplierOrderDetail;
import com.xhgj.srm.jpa.entity.SupplierOrderProduct;
import com.xhgj.srm.jpa.entity.SupplierOrderToForm;
import com.xhgj.srm.jpa.entity.User;
import com.xhgj.srm.jpa.repository.FinancialVoucherRepository;
import com.xhgj.srm.jpa.repository.GroupRepository;
import com.xhgj.srm.jpa.repository.OrderInvoiceRelationRepository;
import com.xhgj.srm.jpa.repository.PaymentApplyDetailRepository;
import com.xhgj.srm.jpa.repository.PurchaseApplyForOrderRepository;
import com.xhgj.srm.jpa.repository.PurchaseOrderPaymentTermsRepository;
import com.xhgj.srm.jpa.repository.SupplierOrderDetailRepository;
import com.xhgj.srm.jpa.repository.SupplierOrderProductRepository;
import com.xhgj.srm.jpa.repository.SupplierOrderRepository;
import com.xhgj.srm.jpa.repository.SupplierOrderToFormRepository;
import com.xhgj.srm.jpa.repository.SupplierRepository;
import com.xhgj.srm.jpa.repository.VerifyConfigRepository;
import com.xhgj.srm.mission.common.MissionTypeEnum;
import com.xhgj.srm.mission.dispatcher.MissionDispatcher;
import com.xhgj.srm.request.dto.mdm.NameAndCodeDTO;
import com.xhgj.srm.request.dto.oms.SalesOrderListDTO;
import com.xhgj.srm.request.service.third.erp.sap.SapPurchaseOrderRequest;
import com.xhgj.srm.request.service.third.erp.sap.dto.MM_075Param;
import com.xhgj.srm.request.service.third.erp.sap.dto.MM_075Param.PurchaseOrderReturnDATADTO;
import com.xhgj.srm.request.service.third.erp.sap.dto.MM_075Param.PurchaseOrderReturnDATADTO.PurchaseOrderReturnHEADDTO;
import com.xhgj.srm.request.service.third.erp.sap.dto.MM_075Result;
import com.xhgj.srm.request.service.third.erp.sap.dto.ReceiptOrReturnReversalResult.RETURNDTO;
import com.xhgj.srm.request.service.third.erp.sap.dto.ReceiptVoucherSynchronizationParam;
import com.xhgj.srm.request.service.third.erp.sap.dto.ReceiptVoucherSynchronizationParam.DataInfo;
import com.xhgj.srm.request.service.third.erp.sap.dto.ReceiptVoucherSynchronizationParam.Head;
import com.xhgj.srm.request.service.third.erp.sap.dto.ReceiptVoucherSynchronizationParam.Item;
import com.xhgj.srm.request.service.third.erp.sap.dto.ReceiptVoucherSynchronizationResult;
import com.xhgj.srm.request.service.third.erp.sap.dto.ReceiptVoucherSynchronizationResult.ReturnMessage;
import com.xhgj.srm.request.service.third.erp.sap.dto.SAPReversalDTO;
import com.xhgj.srm.request.service.third.erp.sap.dto.UpdatePurchaseOrderSapParam;
import com.xhgj.srm.request.service.third.erp.sap.dto.UpdatePurchaseOrderSapParam.UpdatePurchaseOrderDATADTO;
import com.xhgj.srm.request.service.third.erp.sap.dto.UpdatePurchaseOrderSapParam.UpdatePurchaseOrderDATADTO.UpdatePurchaseOrderHEADDTO;
import com.xhgj.srm.request.service.third.erp.sap.dto.UpdatePurchaseOrderSapParam.UpdatePurchaseOrderDATADTO.UpdatePurchaseOrderHEADDTO.ITEMDTO;
import com.xhgj.srm.request.service.third.erp.sap.dto.UpdatePurchaseOrderSapParam.UpdatePurchaseOrderDATADTO.UpdatePurchaseOrderHEADDTO.ITEMDTO.WWDTO;
import com.xhgj.srm.request.service.third.erp.sap.dto.UpdatePurchaseOrderSapResult.UpdatePurchaseOrderRETURNDTO;
import com.xhgj.srm.request.service.third.oms.OmsRequest;
import com.xhgj.srm.request.service.third.sap.SAPService;
import com.xhgj.srm.request.service.third.xhgj.XhgjPersonRequest;
import com.xhgj.srm.request.service.third.xhgj.XhgjSMSRequest;
import com.xhgj.srm.sender.mq.sender.BatchTaskMqSender;
import com.xhgj.srm.service.ShareInputInvoiceService;
import com.xhgj.srm.service.SharePermissionTypeService;
import com.xhgj.srm.service.SharePurchaseOrderService;
import com.xhiot.boot.core.common.exception.CheckException;
import com.xhiot.boot.core.common.util.DateUtils;
import com.xhiot.boot.core.common.util.StringUtils;
import com.xhiot.boot.framework.jpa.repository.BootBaseRepository;
import com.xhiot.boot.framework.web.util.PageResultBuilder;
import com.xhiot.boot.mvc.base.PageResult;
import com.xhiot.boot.upload.config.UploadConfig;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import javax.annotation.Resource;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.jboss.jandex.Main;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.DefaultTransactionDefinition;
import org.springframework.web.multipart.MultipartFile;

/**
 * @ClassName PuchaseOrderServiceImpl <AUTHOR> @Date 2023/12/13 11:24
 */
@Service
@Slf4j
public class PurchaseOrderServiceImpl implements PurchaseOrderService {

  @Resource private SupplierOrderRepository supplierOrderRepository;
  @Resource private SupplierOrderToFormService supplierOrderToFormService;
  @Resource private SupplierOrderDetailService supplierOrderDetailService;
  @Resource private FileService fileService;
  @Resource private UploadConfig uploadConfig;
  @Resource private UserService userService;
  @Resource private DingUtils dingUtils;
  @Resource private SupplierOrderToFormDao supplierOrderToFormDao;
  @Resource private PurchaseApplyForOrderService purchaseApplyForOrderService;
  @Autowired private RedissonClient redissonClient;
  @Autowired private SupplierOrderDao supplierOrderDao;
  @Autowired private SearchSchemeService searchSchemeService;
  @Autowired private SupplierOrderDetailDao supplierOrderDetailDao;
  @Autowired private NoticeCenterService noticeCenterService;
  @Autowired private XhgjSMSRequest xhgjSMSRequest;
  @Autowired private SupplierOrderToFormRepository supplierOrderToFormRepository;
  @Autowired private SupplierOrderDetailRepository supplierOrderDetailRepository;
  @Autowired private SupplierOrderProductRepository supplierOrderProductRepository;
  @Autowired private PurchaseApplyForOrderRepository purchaseApplyForOrderRepository;
  @Autowired private ConfigShortMessageService configShortMessageService;
  @Autowired private SupplierUserService supplierUserService;
  @Autowired private PlatformTransactionManager platformTransactionManager;
  @Autowired private FileDao fileDao;
  @Autowired private SapPurchaseOrderRequest sapPurchaseOrderRequest;
  @Autowired private SupplierRepository supplierRepository;
  @Resource
  private OrderInvoiceRelationRepository orderInvoiceRelationRepository;
  @Resource
  private GroupRepository groupRepository;
  @Resource
  private PurchaseOrderPaymentTermsRepository purchaseOrderPaymentTermsRepository;
  @Resource
  private PurchaseOrderPaymentTermsService purchaseOrderPaymentTermsService;
  @Resource
  private PurchaseOrderPaymentTermsDao purchaseOrderPaymentTermsDao;
  @Resource
  private FinancialVoucherRepository financialVoucherRepository;
  @Resource
  private OmsRequest omsRequest;
  @Resource
  private XhgjPersonRequest xhgjPersonRequest;
  @Resource
  private PaymentApplyRecordDao paymentApplyRecordDao;
  @Resource
  private PaymentApplyDetailRepository paymentApplyDetailRepository;
  @Resource
  private PermissionTypeService permissionTypeService;
  @Resource
  private SrmConfig srmConfig;
  @Resource
  private ImportExcelUtil importExcelUtil;
  @Resource
  private MissionService missionService;
  @Resource
  private BatchTaskMqSender batchTaskMqSender;
  @Resource(name = "IoIntensiveThreadPool")
  private ThreadPoolTaskExecutor ioIntensiveThreadPool;
  @Autowired private SupplierService supplierService;
  @Autowired
  private ExportFiledTemplateEventPublisher templateEventPublisher;
  @Autowired private ManageSecurityUtil manageSecurityUtil;
  @Autowired private PaymentApplyRecordService paymentApplyRecordService;
  @Resource SAPService sapService;
  @Resource SharePurchaseOrderService sharePurchaseOrderService;
  @Resource private ApplicationContext applicationContext;
  @Autowired private MissionDispatcher missionDispatcher;
  @Resource
  ShareInputInvoiceService shareInputInvoiceService;
  @Resource
  SharePermissionTypeService sharePermissionTypeService;
  @Autowired private VerifyConfigService verifyConfigService;
  @Resource
  SupplierOrderFactory supplierOrderFactory;
  @Resource
  OMSService omsService;

  @Override
  public BootBaseRepository<SupplierOrder, String> getRepository() {
    return supplierOrderRepository;
  }

  @Override
  public PurchaseOrderDetailedVO getPurchaseOrderDetailed(String id) {
    Assert.notEmpty(id);
    SupplierOrder supplierOrder =
        get(id, () -> CheckException.noFindException(SupplierOrder.class, id));
    List<PurchaseOrderPaymentTerms> purchaseOrderPaymentTerms =
        purchaseOrderPaymentTermsRepository.findAllByPurchaseOrderIdAndState(supplierOrder.getId(),
            Constants.STATE_OK);
    PurchaseOrderDetailedVO purchaseOrderDetailedVO =
        new PurchaseOrderDetailedVO(supplierOrder, purchaseOrderPaymentTerms);
    String supplierId = supplierOrder.getSupplierId();
    if(!StringUtils.isNullOrEmpty(supplierId)){
      supplierRepository.findById(supplierId).ifPresent(supplier->{
        purchaseOrderDetailedVO.setSupType(StrUtil.emptyIfNull(supplier.getSupType()));
        purchaseOrderDetailedVO.setOpenSupplierOrder(BooleanUtil.isTrue(supplier.getOpenSupplierOrder()));
      });
    }
    SupplierOrderToForm supplierOrderToForm =
        Optional.ofNullable(
                supplierOrderToFormService.getDetailedBySupplierOrderId(supplierOrder.getId()))
            .orElseThrow(() -> new CheckException("【" + id + "】未找到该订单的订单明细，请联系管理员"));
    List<SupplierOrderDetail> detailList =
        supplierOrderDetailService.getByOrderToFormId(supplierOrderToForm.getId());
    AtomicReference<Map<String, PurchaseApplyForOrder>> purchaseApplyIdMapPurchaseOrder = new AtomicReference<>(new HashMap<>());
    List<File> annexcontract = new ArrayList<>();
    List<File> annex = new ArrayList<>();
    List<FinancialVoucher> financialVoucherList = new ArrayList<>();
    Multimap<String, BigDecimal> returnDetailIdMapReturnCount = ArrayListMultimap.create();
    CompletableFuture<Void> completableFuture =
        CompletableFuture.allOf(CompletableFuture.runAsync(()-> CollUtil.emptyIfNull(
                supplierOrderToFormService.getReturnOrCancelFormByTypeAndAndStatusSupplierOrderId(
                    SupplierOrderFormType.RETURN,
                    supplierOrder.getId(),
                    ListUtil.toList(SupplierOrderFormStatus.ERP_ASSESS.getStatus())))
            .forEach(
                supplierOrderToForm1 -> CollUtil.emptyIfNull(
                        supplierOrderDetailService.getByOrderToFormId(supplierOrderToForm1.getId()))
                    .forEach(
                        supplierOrderDetail -> {
                          String detailedId = supplierOrderDetail.getDetailedId();
                          BigDecimal stockOutputQty = supplierOrderDetail.getStockOutputQty();
                          returnDetailIdMapReturnCount.put(detailedId, stockOutputQty);
                        }))) ,
            CompletableFuture.runAsync(()->{
              List<String> purchaseApplyForOrderIdList =
                  detailList.stream().map(SupplierOrderDetail::getPurchaseApplyForOrderId)
                      .filter(StrUtil::isNotBlank).distinct().collect(Collectors.toList());
              if (CollUtil.isNotEmpty(purchaseApplyForOrderIdList)) {
                List<PurchaseApplyForOrder> purchaseApplyForOrders =
                    purchaseApplyForOrderService.getAllByIdList(purchaseApplyForOrderIdList);
                purchaseApplyIdMapPurchaseOrder.set(purchaseApplyForOrders.stream().collect(
                    Collectors.toMap(PurchaseApplyForOrder::getId, Function.identity(), (k1, k2) -> k1)));
              }
            }),
            CompletableFuture.runAsync(()->{
              annexcontract.addAll(fileService.getFileListByIdAndType(
                  supplierOrder.getId(), Constants_FileRelationType.ORDER_CONTRACT));
            }),
            CompletableFuture.runAsync(()->{
              annex.addAll(
                  fileService.getFileListByIdAndType(
                      supplierOrder.getId(), Constants_FileRelationType.ORDER_ANNEX));
            }),
            CompletableFuture.runAsync(()->{
              //发票过账凭证
              financialVoucherList.addAll(
                  financialVoucherRepository.getByVoucherTypeAndPurchaseOrderNoAndState(
                      VoucherTypeEnum.INVOICE_POSTING.getKey(), supplierOrder.getCode(),
                      Constants.STATE_OK));
            })
            );
    try {
      completableFuture.get();
    } catch (Exception e) {
      log.error(ExceptionUtil.stacktraceToString(e,-1));
      throw new CheckException("查询异常");
    }
    List<PurchaseOrderProductDetailedVO> purchaseOrderProductDetails =
        CollUtil.emptyIfNull(detailList)
            .parallelStream()
            .map(
                supplierOrderDetail -> {
                  PurchaseOrderProductDetailedVO vo =
                      new PurchaseOrderProductDetailedVO(supplierOrderDetail);
                  Integer unitDigit = supplierOrderDetail.getSupplierOrderProduct().getUnitDigit();
                  String orderDetailId = supplierOrderDetail.getId();
                  BigDecimal stockOutputQty = supplierOrderDetail.getStockOutputQty();
                  BigDecimal returnQty = supplierOrderDetail.getReturnQty();
                  BigDecimal num = supplierOrderDetail.getNum();
                  vo.setProductDetailedVO(
                      getPurchaseOrderProductDetailed(supplierOrderDetail.getId()));
                  BigDecimalUtil.setScaleBigDecimalHalfUp(returnQty, unitDigit);
                  // 在 ERP 审核中的退库数量前台不展示
                  if (CollUtil.isNotEmpty(returnDetailIdMapReturnCount.get(orderDetailId))) {
                    BigDecimal returnSumNumCount =
                        returnDetailIdMapReturnCount.get(orderDetailId).stream()
                            .reduce(NumberUtil::add)
                            .orElse(BigDecimal.ZERO);
                    stockOutputQty = NumberUtil.sub(stockOutputQty, returnSumNumCount);
                    returnQty = NumberUtil.sub(returnQty, returnSumNumCount);
                  }
                  vo.setStockOutputQty(
                      BigDecimalUtil.setScaleBigDecimalHalfUp(stockOutputQty, 3));
                  BigDecimal taxRate = supplierOrderDetail.getTaxRate();
                  String nakedProductPrice = getNakedPrice(supplierOrderDetail.getPrice(), taxRate);
                  if (StrUtil.isNotBlank(nakedProductPrice)) {
                    if (num != null && NumberUtil.isGreaterOrEqual(num, BigDecimal.ZERO)) {
                      // 不含税金额
                      vo.setNakedTotalPrice(
                          BigDecimalUtil.setScaleBigDecimalHalfUp(NumberUtil.mul(num,
                              new BigDecimal(nakedProductPrice)), 2).toPlainString());
                      // 税额
                      vo.setTaxPrice(getTaxPrice(new BigDecimal(vo.getNakedTotalPrice()),
                          taxRate));
                    }
                    // 不含税单价
                    vo.setNakedProductPrice(nakedProductPrice);
                  }
                  vo.setReturnQty(BigDecimalUtil.setScaleBigDecimalHalfUp(returnQty, 3));
                  String purchaseApplyForOrderId = supplierOrderDetail.getPurchaseApplyForOrderId();
                  if (StrUtil.isNotBlank(purchaseApplyForOrderId)) {
                    Optional.ofNullable(
                        purchaseApplyIdMapPurchaseOrder.get().get(purchaseApplyForOrderId)).ifPresent(purchaseApplyOrder -> {
                      vo.setPurchaseApplyForOrderNo(
                          String.format("%s-%s", purchaseApplyOrder.getApplyForOrderNo(),
                              Optional.ofNullable(purchaseApplyOrder.getSerialNumber())
                                  .map(Convert::toStr).orElse("")
                          ));
                      vo.setPurchaseApplyForOrderId(purchaseApplyOrder.getId());
                      vo.setPurchaseApplyForOrder(purchaseApplyOrder);
                    });
                  }
                  //海外类型添加运费供应商编码
                  if (StrUtil.equals(purchaseOrderDetailedVO.getSupType(),
                      Constants.SUPPLIERTYPE_ABROAD) && (StrUtil.isNotEmpty(
                      vo.getFreightSupplierId()) || StrUtil.isNotEmpty(vo.getTariffSupplierId()))) {
                    Optional.ofNullable(supplierService.get(vo.getFreightSupplierId()))
                        .ifPresent(supplier -> {
                          vo.setFreightSupplierCode(supplier.getMdmCode());
                        });
                    Optional.ofNullable(supplierService.get(vo.getTariffSupplierId()))
                        .ifPresent(supplier -> {
                          vo.setTariffSupplierCode(supplier.getMdmCode());
                        });
                  }
                  return vo;
                })
            .collect(Collectors.toList());
    // 关联发票数据
    purchaseOrderDetailedVO.setPurchaseOrderInvoiceRelationList(
        getInvoiceNumber(supplierOrder.getId()));
    purchaseOrderDetailedVO.setCanInvoicedNum(BigDecimalUtil.formatForStandard(supplierOrder.getTotalStockInputQty()));
    purchaseOrderDetailedVO.setInvoiceTotalNum(BigDecimalUtil.formatForStandard(getInvoiceOpenNum(supplierOrder.getId())));
    purchaseOrderDetailedVO.setDetailedProductDTOList(purchaseOrderProductDetails);
    purchaseOrderDetailedVO.setAnnexDTOs(annex, uploadConfig.getUploadPath());
    purchaseOrderDetailedVO.setContractFiles(annexcontract,uploadConfig.getUploadPath());
    if(CollUtil.isNotEmpty(financialVoucherList)){
      BigDecimal payableAmount =
          financialVoucherList.stream().filter(financialVoucher -> financialVoucher.getRelatedAmount() != null).map(FinancialVoucher::getRelatedAmount)
              .reduce(BigDecimal.ZERO, BigDecimal::add);
      BigDecimal paidAmount = financialVoucherList.stream().filter(financialVoucher -> financialVoucher.getWithdrawnAmount() != null).map(FinancialVoucher::getWithdrawnAmount)
          .reduce(BigDecimal.ZERO, BigDecimal::add);
      BigDecimal returnAmount =
          financialVoucherList.stream().filter(financialVoucher -> financialVoucher.getRefundAmount() != null).map(FinancialVoucher::getRefundAmount)
              .reduce(BigDecimal.ZERO, BigDecimal::add);
      //应付金额
      purchaseOrderDetailedVO.setPayableAmount(payableAmount);
      //已提款金额
      purchaseOrderDetailedVO.setPaidAmount(paidAmount);
      //退款金额
      purchaseOrderDetailedVO.setReturnAmount(returnAmount);
      //应付金额
      purchaseOrderDetailedVO.setPayableBalance(payableAmount.subtract(paidAmount).add(returnAmount));
    }
    //寄售转自有订单赋值
    if (StrUtil.isNotEmpty(purchaseOrderDetailedVO.getConsignmentToOwnedCode())) {
      Optional.ofNullable(
              supplierOrderRepository.findFirstByCodeAndState(supplierOrder.getConsignmentToOwnedCode(),
                  Constants.STATE_OK))
          .ifPresent(order -> {
            purchaseOrderDetailedVO.setConsignmentToOwnedId(order.getId());
            purchaseOrderDetailedVO.setConsignmentToOwnedState(order.getOrderState());
          });
    }
    return purchaseOrderDetailedVO;
  }

  private List<PurchaseOrderInvoiceRelation> getInvoiceNumber(String supplierOrderId) {
    // bb
  /*  List<InputInvoiceOrder> orderInvoiceRelations =
        orderInvoiceRelationRepository.findAllByOrderCodesAndState(purchaseOrderCode,
            Constants.STATE_OK);
    return CollUtil.emptyIfNull(orderInvoiceRelations).stream()
        .map(PurchaseOrderInvoiceRelation::new).collect(Collectors.toList());*/
      List<PurchaseOrderInvoiceRelation> list = new ArrayList<>();
      List<SupplierOrderToForm> supplierOrderToFormList = supplierOrderToFormService.getByTypeAndSupplierOrderId(
        SupplierOrderFormType.WAREHOUSING, supplierOrderId);
      if(CollUtil.isNotEmpty(supplierOrderToFormList)){
        for (SupplierOrderToForm supplierOrderToForm : supplierOrderToFormList){
          List<SupplierOrderDetail> shipProductDTOList =
              supplierOrderDetailService
                  .getByOrderToFormId(supplierOrderToForm.getId());
          for (SupplierOrderDetail supplierOrderDetail : shipProductDTOList){
            List<InputInvoiceOrderWithDetail> orderInvoiceRelationListByDetailIds =
                shareInputInvoiceService.getOrderInvoiceRelationListByDetailIdsRef(Collections.singletonList(supplierOrderDetail.getId()));
            List<PurchaseOrderInvoiceRelation> purchaseOrderInvoiceRelationList = orderInvoiceRelationListByDetailIds.stream().map(
                item -> new PurchaseOrderInvoiceRelation(
                    item.getInputInvoiceOrder())).collect(Collectors.toList());
            list.addAll(purchaseOrderInvoiceRelationList);
          }
        }
      }
    return list.stream().distinct().collect(Collectors.toList());
  }

  private BigDecimal getInvoiceOpenNum(String purchaseOrderId) {
    List<SupplierOrderToForm> supplierOrderToForms =
        supplierOrderToFormRepository.findBySupplierOrderIdAndTypeAndState(purchaseOrderId,
            SupplierOrderFormType.WAREHOUSING.getKey(), Constants.STATE_OK);
    return CollUtil.emptyIfNull(supplierOrderToForms).stream().map(
        supplierOrderToForm -> supplierOrderDetailRepository.getAllByOrderToFormIdAndStateOrderBySortNumAsc(
                supplierOrderToForm.getId(), Constants.STATE_OK).stream()
            .map(SupplierOrderDetail::getInvoicedNum).filter(Objects::nonNull)
            .reduce(BigDecimal.ZERO, BigDecimal::add)).reduce(BigDecimal.ZERO, BigDecimal::add);
  }

  @Override
  public List<PurchaseOrderInvoiceVO> getPurchaseOrderInvoiceInfo(String id) {
    Assert.notEmpty(id);
    SupplierOrder supplierOrder =
        get(id, () -> CheckException.noFindException(SupplierOrder.class, id));
    return CollUtil.emptyIfNull(
            supplierOrderToFormService.getByTypeAndSupplierOrderId(
                SupplierOrderFormType.DELIVER, supplierOrder.getId()))
        .stream()
        .map(
            supplierOrderToForm -> {
              PurchaseOrderInvoiceVO vo = new PurchaseOrderInvoiceVO();
              vo.setProductVoucher(supplierOrderToForm.getProductVoucher());
              vo.setId(supplierOrderToForm.getId());
              vo.setDeliveryTime(supplierOrderToForm.getTime());
              vo.setLogisticsCompany(
                  StrUtil.emptyIfNull(supplierOrderToForm.getLogisticsCompany()));
              vo.setState(supplierOrderToForm.getStatus());
              vo.setLogisticsCode(supplierOrderToForm.getLogisticsCode());
              vo.setTrackNum(StrUtil.emptyIfNull(supplierOrderToForm.getTrackNum()));
              vo.setWarehousing(
                  ObjectUtil.equals(Boolean.TRUE, supplierOrderToForm.getWarehousing()));
              SupplierOrderToForm invoiceOrder =
                  supplierOrderToFormRepository.findFirstByDeliverFormIdAndState(
                      supplierOrderToForm.getId(), Constants.STATE_OK);
              if (invoiceOrder != null && SupplierOrderFormStatus.REVERSAL.getStatus()
                  .equals(invoiceOrder.getStatus())) {
                vo.setWarehousingEntryReversal(true);
              }else {
                vo.setWarehousingEntryReversal(false);
              }
              List<PurchaseOrderInvoiceProductVO> shipProductDTOList =
                  supplierOrderDetailService.getByOrderToFormId(supplierOrderToForm.getId())
                      .stream().map(supplierOrderDetail -> {
                        String detailedId = supplierOrderDetail.getDetailedId();
                        //检查detailedId是否为空
                        if (!StringUtils.isNullOrEmpty(detailedId)) {
                          Optional<SupplierOrderDetail> supplierOrderDetailOptional =
                              supplierOrderDetailRepository.findById(detailedId);
                          if (supplierOrderDetailOptional.isPresent()) {
                            //明细
                            SupplierOrderDetail supplierOrderDetail1 = supplierOrderDetailOptional.get();
                            supplierOrderDetail.setWaitQty(supplierOrderDetail1.getWaitQty());
                          }
                        }
                        return supplierOrderDetail;
                      }).map(PurchaseOrderInvoiceProductVO::new).collect(Collectors.toList());
              vo.setShipProductDTOList(shipProductDTOList);
              return vo;
            }).collect(Collectors.toList());
  }
  @Override
  @Transactional(rollbackFor = Exception.class)
  public void cancelInvoiceForm(String orderToFormId) {
    Assert.notBlank(orderToFormId);
    SupplierOrderToForm supplierOrderToForm =
        supplierOrderToFormService.get(
            orderToFormId,
            () -> CheckException.noFindException(SupplierOrderToForm.class, orderToFormId));
    // 修改此时单据状态
    supplierOrderToForm.setStatus(SupplierOrderFormStatus.REVOKE.getStatus());
    supplierOrderToFormService.save(supplierOrderToForm);
    String supplierOrderId = supplierOrderToForm.getSupplierOrderId();
    SupplierOrder supplierOrder =
        get(
            supplierOrderId,
            () -> CheckException.noFindException(SupplierOrder.class, supplierOrderId));
    // 只有直发订单且发货单未入库的才可以撤销发货单
    if (!(ObjectUtil.notEqual(Boolean.TRUE, supplierOrderToForm.getWarehousing()))) {
      throw new CheckException("该订单已入库无法进行撤销！");
    }
    supplierOrder.setOrderShipWaitStockState(
        supplierOrderToFormService.existStatusBySupplierIdAndStatusAndState(
            SupplierOrderFormType.RETURN,
            ListUtil.toList(SupplierOrderFormStatus.WAIT_RECEIPT),
            supplierOrderId));
    supplierOrder.setOrderReturnState(
        supplierOrderToFormService.existStatusBySupplierIdAndStatusAndState(
            SupplierOrderFormType.RETURN,
            ListUtil.toList(
                SupplierOrderFormStatus.ERP_ASSESS,
                SupplierOrderFormStatus.RETURN,
                SupplierOrderFormStatus.RECEIPT,
                SupplierOrderFormStatus.RETURN_COMPLETE,
                SupplierOrderFormStatus.ERP_REJECT),
            supplierOrderId));
    List<SupplierOrderDetail> shipDetailList =
        supplierOrderDetailService.getByOrderToFormId(supplierOrderToForm.getId());
    // 将发货数量加回到物料详情中
    for (SupplierOrderDetail supplierOrderDetail : shipDetailList) {
      // 物料明细
      SupplierOrderDetail detailed = supplierOrderDetail.getDetailed();
      SupplierOrderProduct supplierOrderProduct = detailed.getSupplierOrderProduct();
      Integer unitDigit = supplierOrderProduct.getUnitDigit();
      // 旧发货数量
      BigDecimal oldShipQty = supplierOrderDetail.getShipQty();
      // 明细行待发货
      BigDecimal waitQty = detailed.getWaitQty();
      detailed.setWaitQty(
          BigDecimalUtil.setScaleBigDecimalHalfUpAndLessThanZeroReturnZero(
              NumberUtil.add(waitQty, oldShipQty), unitDigit));
      detailed.setShipQty(
          BigDecimalUtil.setScaleBigDecimalHalfUpAndLessThanZeroReturnZero(
              NumberUtil.sub(detailed.getShipQty(), oldShipQty), unitDigit));
      // 重新计算待入库数量
      detailed.setWaitStockInputQty(
          BigDecimalUtil.setScaleBigDecimalHalfUpAndLessThanZeroReturnZero(
              NumberUtil.sub(
                  detailed.getShipQty(), detailed.getStockInputQty(), detailed.getStockOutputQty()),
              supplierOrderProduct.getUnitDigit()));
      supplierOrderDetailService.save(detailed);
    }
    if (!supplierOrderToFormService.existByTypeAndSupplierOrderIdAndExcludeStatusNotIn(
        ListUtil.toList(SupplierOrderFormType.WAREHOUSING, SupplierOrderFormType.DELIVER),
        supplierOrderId, ListUtil.toList(SupplierOrderFormStatus.REVERSAL.getStatus(),
            SupplierOrderFormStatus.REVOKE.getStatus()))) {
      supplierOrder.setOrderState(SupplierOrderState.WAIT.getOrderState());
    }
    save(supplierOrder);
  }

  @Override
  @Transactional(rollbackFor = Exception.class)
  public void purchaseOrderDelivery(AddPurchaseOrderDeliveryParam deliveryParam,
      boolean isConfirmReceipt) {
    String supplierOrderId = deliveryParam.getId();
    SupplierOrder supplierOrder = get(supplierOrderId,
        () -> CheckException.noFindException(SupplierOrder.class, supplierOrderId));
    if (supplierOrder.getOrderState().equals(SupplierOrderState.PENDING.getKey())) {
      throw new CheckException("该订单已挂起!");
    }
    if (!StrUtil.equals(PurchaseOrderTypeEnum.CONSIGNMENT_TO_OWNED.getKey(),
        supplierOrder.getOrderType()) && !supplierOrder.getDirectShipment()) {
      throw new CheckException("非直发的订单不支持此操作!");
    }
    if (getShipmentStatus(supplierOrderId)) {
      throw new CheckException("该订单已全部发货");
    }
    if (StrUtil.length(deliveryParam.getTrackNum()) > 60) {
      throw new CheckException("物流单号最多输入60字，否则无法正常销售发货，多个发物流单号请分开入库!");
    }
    if (deliveryParam.getProductDetailList().size() > 400) {
      throw new CheckException("您好，您只能勾选400行物料进行操作");
    }
    List<ProductDetailParam> productDetailList = deliveryParam.getProductDetailList();
    // 检查订单是否超发
    checkShipmentQuantity(productDetailList);
    // 订单在前台发货提交时，如果订单入库进度>=1，则订单状态更新为已完成。
    if (NumberUtil.isGreaterOrEqual(
        supplierOrder.getTotalStockInputQty(), supplierOrder.getTotalNum())) {
      supplierOrder.setOrderState(SupplierOrderState.COMPLETE.getOrderState());
    } else {
      supplierOrder.setOrderState(SupplierOrderState.IN_PROGRESS.getOrderState());
    }
    // 发货时该订单自动确认
    supplierOrder.setOrderConfirmState(false);
    if (supplierOrder.getConfirmTime() == null) {
      supplierOrder.setConfirmTime(System.currentTimeMillis());
    }
    if (supplierOrder.getFirstShipTime() == null) {
      supplierOrder.setFirstShipTime(System.currentTimeMillis());
    }
    save(supplierOrder);
    // 新增发货单
    SupplierOrderToForm supplierOrderForm = generateShipmentOrder(supplierOrderId, deliveryParam);
    productDetailList.forEach(
        productDetailParam -> {
          productDetailParam.setDetailId(productDetailParam.getId());
        });
    // 新增发货单明细
    supplierOrderDetailService.saveOrderDetail(supplierOrderForm.getId(), productDetailList, null);
    // 需要发送钉钉卡片通知给对应的采购员
    //     v6.2.1取消后台发送通知
    //    String purchaseCode = supplierOrder.getPurchaseCode();
    //    sendDingMsgToPurchase(purchaseCode, supplierOrder, supplierOrderForm);
    // 维护订单明细
    for (ProductDetailParam productDetailParam : productDetailList) {
      // 订单明细
      SupplierOrderDetail supplierOrderDetail =
          supplierOrderDetailService.get(
              productDetailParam.getId(),
              () ->
                  CheckException.noFindException(
                      SupplierOrderDetail.class, productDetailParam.getId()));
      BigDecimal shipQty = supplierOrderDetail.getShipQty();
      BigDecimal waitQty = supplierOrderDetail.getWaitQty();
      supplierOrderDetail.setShipQty(
          shipQty == null
              ? productDetailParam.getDeliveryQty()
              : shipQty.add(productDetailParam.getDeliveryQty()));
      supplierOrderDetail.setWaitQty(NumberUtil.sub(waitQty, productDetailParam.getDeliveryQty()));
      supplierOrderDetailService.save(supplierOrderDetail);
    }
    // 处理完成发货时间
    if (getShipmentStatus(supplierOrderId)) {
      supplierOrder.setCompleteShipTime(System.currentTimeMillis());
      save(supplierOrder);
    }
    // 入库
    if (isConfirmReceipt) {
      confirmReceipt(supplierOrderForm.getId());
    }
  }

  private SupplierOrderToForm generateShipmentOrder(String supplierOrderId,
      AddPurchaseOrderDeliveryParam deliveryParam) {
    // 新增发货单
    SupplierOrderToForm supplierOrderForm =
        supplierOrderToFormService.createSupplierOrderForm(supplierOrderId,
            SupplierOrderFormType.DELIVER);
    String logisticsCompany = deliveryParam.getLogisticsCompany();
    supplierOrderForm.setLogisticsCompany(logisticsCompany);
    String logisticsCode = deliveryParam.getLogisticsCode();
    supplierOrderForm.setLogisticsCode(logisticsCode);
    supplierOrderForm.setCode(deliveryParam.getCode());
    String trackNum = deliveryParam.getTrackNum();
    supplierOrderForm.setTrackNum(trackNum);
    supplierOrderForm.setStatus(SupplierOrderFormStatus.WAIT_RECEIPT.getStatus());
    BigDecimal num =
        deliveryParam.getProductDetailList().stream().map(ProductDetailParam::getDeliveryQty)
            .reduce(BigDecimal.ZERO, BigDecimal::add);
    supplierOrderForm.setNum(num);
    return supplierOrderToFormService.save(supplierOrderForm);
  }

  private SupplierOrderToForm generateWarehousingEntry(String supplierOrderId,
      SupplierOrderToForm supplierOrderToForm, String orderToFormId) {
    SupplierOrderToForm supplierOrderForm =
        supplierOrderToFormService.createSupplierOrderForm(
            supplierOrderId, SupplierOrderFormType.WAREHOUSING);
    String logisticsCompany = supplierOrderToForm.getLogisticsCompany();
    supplierOrderForm.setLogisticsCompany(logisticsCompany);
    String logisticsCode = supplierOrderToForm.getLogisticsCode();
    supplierOrderForm.setLogisticsCode(logisticsCode);
    supplierOrderForm.setCode(supplierOrderToForm.getCode());
    String trackNum = supplierOrderToForm.getTrackNum();
    supplierOrderForm.setTrackNum(trackNum);
    supplierOrderForm.setDeliverFormId(orderToFormId);
    final String order_source = "SRM";
    supplierOrderForm.setSource(order_source);
    return supplierOrderToFormService.save(supplierOrderForm);
  }

  private void sendDingMsgToPurchase(String purchaseCode, SupplierOrder supplierOrder,
      SupplierOrderToForm supplierOrderForm) {
    if (StrUtil.isBlank(purchaseCode) || supplierOrder == null || supplierOrderForm == null) {
      return;
    }
    Optional.ofNullable(userService.getByCode(purchaseCode)).ifPresent(user -> {
      String mobile = user.getMobile();
      if (StrUtil.isBlank(mobile)) {
        return;
      }
      try {
        dingUtils.sendStokeInPutNotice(supplierOrder.getCode(), supplierOrder.getSupplierName(),
            supplierOrderForm.getTime(), supplierOrderForm.getLogisticsCompany(), mobile);
        log.info("【" + supplierOrder.getId() + "】该订单通知采购员 erp 审核成功");
      } catch (Exception e) {
        log.error(ExceptionUtil.stacktraceToString(e, -1));
      }
    });
  }

  /**
   * 检查发货数量
   */
  private void checkShipmentQuantity(List<ProductDetailParam> productDetailParams) {
    for (ProductDetailParam param : productDetailParams) {
      SupplierOrderDetail detail = supplierOrderDetailService.get(param.getId(),
          () -> CheckException.noFindException(SupplierOrderDetail.class, param.getId()));
      BigDecimal waitQty = detail.getWaitQty();
      BigDecimal deliveryQty = param.getDeliveryQty();
      if (NumberUtil.isGreater(deliveryQty, waitQty)) {
        throw new CheckException("订单超发！");
      }
    }

  }

  /**
   * 保存物流信息
   *
   * @param supplierOrderForm 供应商订单单据
   */
  private void saveExpressInfo(SupplierOrderToForm supplierOrderForm) {
    String logisticsCompany = supplierOrderForm.getLogisticsCompany();
    String trackNum = supplierOrderForm.getTrackNum();
    String logisticsCode = supplierOrderForm.getLogisticsCode();
    if (StrUtil.isAllNotBlank(logisticsCompany, trackNum, logisticsCode)) {
      supplierOrderToFormService.save(supplierOrderForm);
    }
  }

  /**
   * 统计订单是否全部发货
   * @param supplierOrderId 采购订单id
   */
  private boolean getShipmentStatus(String supplierOrderId) {
    SupplierOrderToForm supplierOrderToForm =
        supplierOrderToFormService.getDetailedBySupplierOrderId(supplierOrderId);
    BigDecimal waitTotal =
        getTotalDetailWaitQtyBySupplierOrderToFormId(supplierOrderToForm.getId());
    return NumberUtil.isGreaterOrEqual(BigDecimal.ZERO, waitTotal);
  }

  /**
   * 获取单据表单下 待发数量 之和
   *
   * @param orderToFormId 单据表单id
   * @return
   */
  private BigDecimal getTotalDetailWaitQtyBySupplierOrderToFormId(String orderToFormId) {
    return supplierOrderDetailService.getByOrderToFormId(orderToFormId).stream()
        .map(SupplierOrderDetail::getWaitQty)
        .reduce(BigDecimal.ZERO, BigDecimal::add);
  }

  private boolean hasUnDeliverySupplierOrder(String supplierOrderId) {
    SupplierOrderToForm supplierOrderToForm =
        supplierOrderToFormService.getDetailedBySupplierOrderId(supplierOrderId);
    BigDecimal waitTotal =
        getTotalDetailWaitQtyBySupplierOrderToFormId(supplierOrderToForm.getId());
    return waitTotal.compareTo(new BigDecimal(0)) > 0;
  }

  private void setSupplierOrderDetailShipQty(
      Map<String, BigDecimal> map, SupplierOrderDetail supplierOrderDetail) {
    if (!map.containsKey(supplierOrderDetail.getId())) {
      return;
    }
    BigDecimal waitQty = supplierOrderDetail.getWaitQty();
    BigDecimal shipQty = map.get(supplierOrderDetail.getId());
    waitQty = NumberUtil.sub(waitQty, shipQty);
    // 原发货量
    BigDecimal oriShipQty = supplierOrderDetail.getShipQty();
    supplierOrderDetail.setShipQty(shipQty.add(oriShipQty));
    supplierOrderDetail.setWaitQty(waitQty);
    // 待入库数量（已经发货的数量 - 采购入库数量 - 退货数量）
    BigDecimal waitStockInputQty =
        BigDecimalUtil.setScaleBigDecimalHalfUp(
            NumberUtil.sub(
                supplierOrderDetail.getShipQty(),
                supplierOrderDetail.getStockInputQty(),
                supplierOrderDetail.getStockOutputQty()),
            supplierOrderDetail.getSupplierOrderProduct().getUnitDigit());
    supplierOrderDetail.setWaitStockInputQty(
        NumberUtil.isLess(waitStockInputQty, BigDecimal.ZERO)
            ? BigDecimal.ZERO
            : waitStockInputQty);
    supplierOrderDetailService.save(supplierOrderDetail);
  }

  @Override
  public void confirmReceipt(String orderToFormId) {
    //发货单
    SupplierOrderToForm supplierOrderToForm = supplierOrderToFormService.get(orderToFormId,
        () -> CheckException.noFindException(SupplierOrderToForm.class, orderToFormId));
    RLock lock = null;
    RLock shareLock = null;
    try {
      // 修改为根据订单id
      lock = redissonClient.getLock(Constants_LockName.PURCHASE_ORDER_CONFIRM_RECEIPT
          + supplierOrderToForm.getSupplierOrderId());
      shareLock = redissonClient.getLock(StrUtil.format(Constants_LockName.PURCHASE_ORDER_UPDATE_LOCK, supplierOrderToForm.getSupplierOrderId()));
      lock.lock();
      shareLock.lock();
      PurchaseOrderServiceImpl proxy =
          applicationContext.getBean(PurchaseOrderServiceImpl.class);
      proxy.confirmReceiptTransactional(orderToFormId);
    } catch (CheckException checkException) {
      throw checkException;
    } catch (Exception e) {
      throw new CheckException("未知异常，请联系管理员！");
    } finally {
      if (lock != null) {
        lock.unlock();
      }
      if (shareLock != null) {
        shareLock.unlock();
      }
    }
  }

  @Transactional(rollbackFor = Exception.class)
  public void confirmReceiptTransactional(String orderToFormId) {
    // 查最新数据
    SupplierOrderToForm supplierOrderInvoice = supplierOrderToFormService.get(orderToFormId,
        () -> CheckException.noFindException(SupplierOrderToForm.class, orderToFormId));
    if (!Objects.equals(supplierOrderInvoice.getType(), SupplierOrderFormType.DELIVER.getType())) {
      throw new CheckException("发货单异常，请联系管理员！");
    }
    if (Objects.equals(Boolean.TRUE, supplierOrderInvoice.getWarehousing())) {
      throw new CheckException("该发货单已经入库");
    }
    supplierOrderInvoice.setWarehousing(true);
    supplierOrderToFormService.save(supplierOrderInvoice);
    SupplierOrder supplierOrder = get(supplierOrderInvoice.getSupplierOrderId(),
        () -> CheckException.noFindException(SupplierOrder.class,
            supplierOrderInvoice.getSupplierOrderId()));
    if (!StrUtil.equals(PurchaseOrderTypeEnum.CONSIGNMENT_TO_OWNED.getKey(),
        supplierOrder.getOrderType())&&Boolean.FALSE.equals(supplierOrder.getDirectShipment())) {
      throw new CheckException("该订单非厂家直发，无法入库");
    }
    String random = RandomUtil.randomString(7);
    log.info(StrUtil.format(random + "采购订单入库：{}，发货单发货数量为：{}，订单已经入库数量：{}", supplierOrder.getCode(),
        supplierOrderInvoice.getNum(), supplierOrder.getTotalStockInputQty()));
    supplierOrder.setTotalStockInputQty(
        supplierOrder.getTotalStockInputQty() == null ? supplierOrderInvoice.getNum()
            : supplierOrder.getTotalStockInputQty().add(supplierOrderInvoice.getNum()));
    log.info(StrUtil.format(random + "采购订单入库：{}，订单已经入库数量：{}", supplierOrder.getCode(),
        supplierOrder.getTotalStockInputQty()));
    // 新增入库单
    SupplierOrderToForm supplierOrderForm =
        generateWarehousingEntry(supplierOrder.getId(), supplierOrderInvoice, orderToFormId);
    //发货单明细
    List<SupplierOrderDetail> supplierOrderDetails =
        supplierOrderDetailService.getByOrderToFormId(orderToFormId);
    if (CollUtil.isEmpty(supplierOrderDetails)) {
      throw new CheckException("数据异常，前联系管理员！");
    }
    List<ProductDetailParam> productDetailParams = supplierOrderDetails.stream().map(
        supplierOrderDetail -> new ProductDetailParam(supplierOrderDetail.getSupplierOrderProduct(),
            supplierOrderDetail)).collect(Collectors.toList());
    //入库单明细
    List<SupplierOrderDetail> supplierOrderDetailList =
        supplierOrderDetailService.saveOrderDetail(
            supplierOrderForm.getId(), productDetailParams,
            supplierOrderInvoice.getSupplierOrderId());
    //维护订单明细
    for (SupplierOrderDetail supplierOrderDetail : CollUtil.emptyIfNull(supplierOrderDetailList)) {
      String detailedId = supplierOrderDetail.getDetailedId();
      SupplierOrderDetail detail = supplierOrderDetailService.get(detailedId,
          () -> CheckException.noFindException(SupplierOrderDetail.class, detailedId));
      detail.setRemainQty(
          NumberUtil.sub(detail.getRemainQty(), supplierOrderDetail.getStockInputQty()));
      detail.setStockInputQty(
          NumberUtil.add(supplierOrderDetail.getStockInputQty(), detail.getStockInputQty()));
      detail.setWaitStockInputQty(
          NumberUtil.sub(detail.getWaitStockInputQty(), supplierOrderDetail.getStockInputQty()));
      detail.setSettleQty(
          NumberUtil.add(detail.getSettleQty(), supplierOrderDetail.getStockInputQty()));
      supplierOrderDetailService.save(supplierOrderDetail);
    }
    //维护入库数量和金额
    BigDecimal sumNum = CollUtil.emptyIfNull(supplierOrderDetailList).stream()
        .map(SupplierOrderDetail::getStockInputQty).filter(Objects::nonNull)
        .reduce(BigDecimal.ZERO, BigDecimal::add);
    BigDecimal sumPrice =
        CollUtil.emptyIfNull(supplierOrderDetailList).stream().map(supplierOrderDetail -> {
          BigDecimal deliveryQty = supplierOrderDetail.getStockInputQty();
          BigDecimal productPrice = supplierOrderDetail.getPrice();
          return NumberUtil.mul(deliveryQty, productPrice);
        }).reduce(BigDecimal.ZERO, BigDecimal::add);
    supplierOrderForm.setNum(sumNum);
    supplierOrderForm.setReturnPrice(sumPrice);
    supplierOrderToFormService.save(supplierOrderForm);
    boolean warehouse =
        supplierOrderToFormService.confirmThatAllItemsAreStoredInTheWarehouse(supplierOrder);
    supplierOrder.setOrderShipWaitStockState(!warehouse);
    save(supplierOrder);
    receiptVoucherSync(supplierOrder, supplierOrderForm, supplierOrderDetailList);
    // 设置入库进度
    setStockProgress(supplierOrder);
  }

  private void receiptVoucherSync(SupplierOrder supplierOrder,
      SupplierOrderToForm supplierOrderToForm, List<SupplierOrderDetail> supplierOrderDetails) {
    supplierOrderDetails.forEach(supplierOrderDetail -> {
      Optional<SupplierOrderDetail> supplierOrderDetail1 =
          supplierOrderDetailRepository.findById(supplierOrderDetail.getDetailedId());
      supplierOrderDetail1.ifPresent(supplierOrderDetail::setDetailed);
    });
    User user = manageSecurityUtil.getSrmUserDetails().getUser();
    ReceiptVoucherSynchronizationParam param =
        ReceiptVoucherSynchronizationParam.buildAnInstance(supplierOrder, supplierOrderToForm,
            supplierOrderDetails,user);
    ReceiptVoucherSynchronizationResult result;
    try {
      result = sapService.sapMaterialVoucherWithLockGroup(param);
    } catch (CheckException e){
      throw e;
    }catch (Exception e) {
      log.error(ExceptionUtil.stacktraceToString(e));
      throw new CheckException("SAP系统响应异常，请联系管理员！");
    }
    List<ReturnMessage> returnMessages = result.getReturnMessages();
    for (ReturnMessage returnMessage : returnMessages) {
      String lineItem = returnMessage.getLineItem();
      String documentNumber = returnMessage.getDocumentNumber();
      String purchaseOrderLineItems = returnMessage.getPurchaseOrderLineItems();
      supplierOrderToForm.setProductVoucher(documentNumber);
      if (StrUtil.isNotBlank(purchaseOrderLineItems)) {
        Integer purchaseOrderLineItems1 = Integer.valueOf(purchaseOrderLineItems);
        Optional<SupplierOrderDetail> first =
            supplierOrderDetails.stream().filter(supplierOrderDetail -> {
              return Objects.equals(supplierOrderDetail.getSortNum(), purchaseOrderLineItems1);
            }).findFirst();
        first.ifPresent(supplierOrderDetail -> {
          supplierOrderDetail.setBatchNo(returnMessage.getCharge());
          supplierOrderDetail.setSapRowId(lineItem);
          supplierOrderDetailRepository.save(supplierOrderDetail);
        });
      }
    }
    supplierOrderToFormRepository.save(supplierOrderToForm);
  }

  public String setStockProgress(SupplierOrder supplierOrder) {
    List<SupplierOrderToForm> supplierOrderToForms =
        supplierOrderToFormRepository.findBySupplierOrderIdAndTypeAndState(supplierOrder.getId(),
            SupplierOrderFormType.DETAILED.getType(), Constants.STATE_OK);
    if (CollUtil.isEmpty(supplierOrderToForms)) {
      return StrUtil.EMPTY;
    }
    BigDecimal totalCancel =
        supplierOrderDetailDao.getTotalCancel(SupplierOrderFormType.CANCEL.getKey(),
            supplierOrder.getId());
    BigDecimal totalReturn =
        supplierOrderDetailDao.getTotalReturn(SupplierOrderFormType.RETURN.getKey(),
            supplierOrder.getId());
    BigDecimal productCount = NumberUtil.sub(supplierOrder.getTotalNum(), totalCancel, totalReturn);
    BigDecimal inventoryQuantity = getInventoryQuantity(supplierOrder.getId());
    supplierOrder.setTotalStockInputQty(inventoryQuantity);
    String originStockProgress = supplierOrder.getStockProgress();
    String stockProgress = supplierOrder.makeAndSetStockProgress(supplierOrder.getTotalStockInputQty(), productCount);
    if (NumberUtil.isGreaterOrEqual(supplierOrder.getTotalStockInputQty(), productCount)) {
      supplierOrder.setOrderState(SupplierOrderState.COMPLETE.getOrderState());
    }
    save(supplierOrder);
    if (!Objects.equals(originStockProgress, stockProgress)) {
      if (StrUtil.isBlank(originStockProgress)) {
        log.warn("采购订单" + supplierOrder.getCode() + "初始化入库进度成功，" + originStockProgress + "-->" + stockProgress +
            "，总入库数量：" + supplierOrder.getTotalStockInputQty());
      }else {
        log.warn("采购订单" + supplierOrder.getCode() + "修改入库进度成功，" + originStockProgress + "-->" + stockProgress +
            "，总入库数量：" + supplierOrder.getTotalStockInputQty());
      }
    }else {
      log.warn("采购订单" + supplierOrder.getCode() + "修改入库进度失败，" + originStockProgress + "-->"
          + stockProgress + "，总入库数量：" + supplierOrder.getTotalStockInputQty());
    }
    checkShipWaitStockState(supplierOrder);
    return supplierOrder.getStockProgress();
  }

  /**
   * @param purchaseOrder 采购地那个的
   * 检查采购订单发货单是否全部入库状态
   */
  public void checkShipWaitStockState(SupplierOrder purchaseOrder) {
    List<SupplierOrderToForm> supplierOrderToForms =
        supplierOrderToFormRepository.findBySupplierOrderIdAndTypeAndState(purchaseOrder.getId(),
            SupplierOrderFormType.DELIVER.getType(), Constants.STATE_OK);
    // 过滤掉已撤销的发货单
    supplierOrderToForms = supplierOrderToForms.stream().filter(item ->
            !SupplierOrderFormStatus.REVOKE.getStatus().equals(item.getStatus())
        ).collect(Collectors.toList());
    if (CollUtil.isEmpty(supplierOrderToForms)) {
      purchaseOrder.setOrderShipWaitStockState(false);
      return;
    }
    AtomicBoolean isWarehousing = new AtomicBoolean(false);
    // 厂直发且未入库--红点显示
    if (Boolean.TRUE.equals(purchaseOrder.getDirectShipment())) {
      // 只有其中有一个入库单未入库，那么订单就是未入库状态
      for (SupplierOrderToForm supplierOrderToForm : supplierOrderToForms) {
        if (supplierOrderToForm.getWarehousing() == null ||
            Boolean.FALSE.equals(supplierOrderToForm.getWarehousing())
        ) {
          isWarehousing.set(true);
          break;
        }
      }
    }
    purchaseOrder.setOrderShipWaitStockState(isWarehousing.get());
    save(purchaseOrder);
  }



  public BigDecimal getInventoryQuantity(String supplierOrderId) {
    List<SupplierOrderToForm> supplierOrderToForms =
        supplierOrderToFormRepository.findBySupplierOrderIdAndTypeAndState(supplierOrderId,
            SupplierOrderFormType.DETAILED.getType(), Constants.STATE_OK);
    if (CollUtil.isEmpty(supplierOrderToForms)) {
      return BigDecimal.ZERO;
    }
    BigDecimal totalStockInputQty = BigDecimal.ZERO;
    for (SupplierOrderToForm supplierOrderToForm : supplierOrderToForms) {
      List<SupplierOrderDetail> supplierOrderDetails =
          supplierOrderDetailRepository.findByOrderToFormIdAndState(supplierOrderToForm.getId(),
              Constants.STATE_OK);
      for (SupplierOrderDetail supplierOrderDetail : CollUtil.emptyIfNull(supplierOrderDetails)) {
        BigDecimal stockInputQty = supplierOrderDetail.getStockInputQty();
        if (stockInputQty != null) {
          BigDecimal sub = NumberUtil.sub(stockInputQty, supplierOrderDetail.getStockOutputQty());
          totalStockInputQty = totalStockInputQty.add(sub);
        }
      }
    }
    return totalStockInputQty;
  }



  private void sendDingMessage(SupplierOrder supplierOrder, SupplierOrderToForm supplierOrderForm) {
    if (!supplierOrder.getDirectShipment()) {
      return;
    }
    String purchaseCode = supplierOrder.getPurchaseCode();
    if (StrUtil.isBlank(purchaseCode)) {
      return;
    }
    Optional.ofNullable(userService.getByCode(purchaseCode))
        .ifPresent(
            user -> {
              String mobile = user.getMobile();
              if (StrUtil.isBlank(mobile)) {
                return;
              }
              try {
                dingUtils.sendStokeInPutNotice(
                    supplierOrder.getCode(),
                    supplierOrder.getSupplierName(),
                    supplierOrderForm.getTime(),
                    supplierOrderForm.getLogisticsCompany(),
                    mobile);
                log.info("【" + supplierOrder.getId() + "】该订单通知采购员 erp 审核成功");
              } catch (Exception e) {
                log.error(ExceptionUtil.stacktraceToString(e, -1));
              }
            });
  }

  @Override
  public SupplierOrderCountDTO getSupplierOrderFormCountById(String purchaseOrderId) {
    return new SupplierOrderCountDTO(
        supplierOrderToFormDao.getSumSupplierOrderFormByTypeAndStatus(
            SupplierOrderFormType.DELIVER.getKey(), purchaseOrderId, null),
        supplierOrderToFormDao.getSumSupplierOrderFormByTypeAndStatus(
            SupplierOrderFormType.RETURN.getKey(), purchaseOrderId, null),
        supplierOrderToFormDao.getSumSupplierOrderFormByTypeAndStatus(
            SupplierOrderFormType.CANCEL.getKey(), purchaseOrderId, null),
        supplierOrderToFormDao.getSumSupplierOrderFormByTypeAndStatus(
            SupplierOrderFormType.WAREHOUSING.getKey(), purchaseOrderId, null)
    );
  }

  @Override
  @DefaultSearchScheme(searchType = Constants.SEARCH_TYPE_GET_PAGE_SUPPLIER_ORDER_PAGE)
  public SupplierOrderCountDTO getSupplierOrderCount(User user, PurchaseOrderPageQuery form) {
    String currUser = getCurrUser(user);
    SearchPermission searchPermission =
        sharePermissionTypeService.getSearchPermission(user, form.getUserGroup(),
            Constants.USER_PERMISSION_SUPPLIER_ORDER, false, false, false,form.getIsViewAllOrganization());
    MergeUserPermission mergeUserPermission =
        sharePermissionTypeService.mergePermission(searchPermission, new OperatorPermission());
    form.setPageNo(1);
    form.setPageSize(1);
    form.setOrderState(SupplierOrderState.WAIT);
    long waitCount =
        supplierOrderDao.findPurchaseOrderPageRef(form.toQueryMap(mergeUserPermission)).getTotalElements();
    form.setOrderState(SupplierOrderState.IN_PROGRESS);
    long progressCount =
        supplierOrderDao.findPurchaseOrderPageRef(form.toQueryMap(mergeUserPermission)).getTotalElements();
    form.setOrderState(SupplierOrderState.NOT_REVIEWED);
    long notReviewed =
        supplierOrderDao.findPurchaseOrderPageRef(form.toQueryMap(mergeUserPermission)).getTotalElements();
    SupplierOrderCountDTO supplierOrderCountDTO = new SupplierOrderCountDTO();
    supplierOrderCountDTO.setWaitCount(waitCount);
    supplierOrderCountDTO.setInProgressCount(progressCount);
    supplierOrderCountDTO.setNotReviewedCount(notReviewed);
    return supplierOrderCountDTO;
  }

  @Override
  @DefaultSearchScheme(searchType = Constants.SEARCH_TYPE_GET_PAGE_SUPPLIER_ORDER_PAGE)
  public SupplierOrderCountDTO getOrderProductCount(User user, PurchaseOrderProductSearchForm form) {
    String createMan = user.getId();
    SearchPermission searchPermission =
        sharePermissionTypeService.getSearchPermission(user, form.getUserGroup(),
            Constants.USER_PERMISSION_SUPPLIER_ORDER, false, false, false,form.getIsViewAllOrganization());
    MergeUserPermission mergeUserPermission =
        sharePermissionTypeService.mergePermission(searchPermission, new OperatorPermission());
    form.setPageNo(1);
    form.setPageSize(1);
    form.setOrderState(SupplierOrderState.WAIT);
    long waitCount =
        supplierOrderDetailDao.findPurchaseOrderDetailPageRef(form.toQueryMap(mergeUserPermission)).getTotalElements();
    form.setOrderState(SupplierOrderState.IN_PROGRESS);
    long progressCount =
        supplierOrderDetailDao.findPurchaseOrderDetailPageRef(form.toQueryMap(mergeUserPermission)).getTotalElements();
    form.setOrderState(SupplierOrderState.NOT_REVIEWED);
    long notReviewed =
        supplierOrderDetailDao.findPurchaseOrderDetailPageRef(form.toQueryMap(mergeUserPermission)).getTotalElements();
    SupplierOrderCountDTO supplierOrderCountDTO = new SupplierOrderCountDTO();
    supplierOrderCountDTO.setWaitCount(waitCount);
    supplierOrderCountDTO.setInProgressCount(progressCount);
    supplierOrderCountDTO.setNotReviewedCount(notReviewed);
    return supplierOrderCountDTO;
  }

  /**
   *
   * @param user 用户
   * @param query 查询参数 必传
   * @param toPageable 分页参数 必传
   * @deprecated 请使用 {@link #getPagePurchaseOrderPageRef(PurchaseOrderPageQuery)}
   * @return
   */
  @Override
  @Deprecated
  public PageResult<PurchaseOrderListDTO> getPagePurchaseOrderPage(
      User user, PurchaseOrderPageQuery query, Pageable toPageable) {
    String currUser = getCurrUser(user);
    String purchaseId = user.getId();
    String createMan = user.getId();
    List<String> userNameList;
    if (user.getRoleList().contains(Constants.SUPPLIER_USER_ROLE_ADMIN) || user.getRoleList().contains(Constants.ROLE_ADMINISTRATOR) ) {
      purchaseId = StrUtil.EMPTY;
      createMan = StrUtil.EMPTY;
      userNameList = null;
    }else{
      // 用户权限下采购名称集合
      userNameList =
          permissionTypeService.getConcatNumUserNameList(
              user.getId(),
              Constants.USER_PERMISSION_SUPPLIER_ORDER,
              ListUtil.toList(Constants.SUPPLIER_USER_ROLE_ORDINARY));
    }
    // 查询方案
    String schemeId = query.getSchemeId();
    String code = query.getOrderCode();
    SupplierOrderState orderState = query.getOrderState();
    Long startCreateTime = query.getStartCreateTime();
    Long endCreateTime = query.getEndCreateTime();
    String supplierName = query.getSupplierName();
    Boolean directShipment = query.getDirectShipment();
    Boolean confirmState = query.getConfirmState();
    Boolean cancelState = query.getCancelState();
    Boolean returnState = query.getReturnState();
    Boolean refuseState = query.getRefuseState();
    Boolean shipWaitStock = query.getShipWaitStock();
    String purchaseGroupName = query.getPurchaseGroupName();
    String receiveMan = query.getReceiveMan();
    String supplierOpenInvoiceState = query.getSupplierOpenInvoiceState();
    Boolean selectUnReceipt = query.getSelectUnReceipt();
    Boolean pending = query.getPending();
    Boolean reject = query.getReject();
    Boolean staging = query.getStaging();
    Boolean unaudited = query.getUnaudited();
    String purchaseDept = query.getPurchaseDept();
    String purchaseMan = query.getPurchaseMan();
    String salesOrderNo = query.getSalesOrderNo();
    String largeTicketProjectNumbers = query.getLargeTicketProjectNumbers();
    String largeTicketProjectName = query.getLargeTicketProjectName();
    Boolean freeState = query.getFreeState();
    Boolean selfState = query.getSelfState();

    //创建人
    String creater = query.getCreateMan();
    //修改人
    String updateMan = query.getUpdateMan();
    //修改时间 开始
    Long startUpdateTime = query.getStartUpdateTime();
    //修改时间 结束
    Long endUpdateTime = query.getEndUpdateTime();
    //审核时间 开始
    Long startAuditTime = query.getStartAuditTime();
    //审核时间 结束
    Long endAuditTime = query.getEndAuditTime();
    LogicalOperatorsEnums numOperators = query.getOrderNumOperators();
    BigDecimal num = query.getOrderNum();
    BigDecimal price = query.getOrderPrice();
    LogicalOperatorsEnums priceOperators = query.getOrderPriceOperators();
    Boolean scp = query.getScp();
    String allScp = query.getAllScp();
    String orderType = query.getOrderType();
    Boolean counteractState = query.getWriteOffState();
    Boolean loss = query.getLoss();
    if (StrUtil.isBlank(schemeId)) {
      SearchScheme search =
          searchSchemeService.getDefaultSearchScheme(
              user.getId(), Constants.SEARCH_TYPE_GET_PAGE_SUPPLIER_ORDER_PAGE);
      if (search != null) {
        schemeId = search.getId();
      }
    }
    if (StrUtil.isNotEmpty(schemeId)) {
      SearchScheme search = searchSchemeService.get(schemeId);
      if (search != null && StrUtil.isNotEmpty(search.getContent())) {
        PurchaseOrderPageQuery supplierOrderPageQuery =
            JSON.parseObject(search.getContent(), new TypeReference<PurchaseOrderPageQuery>() {});
        if (supplierOrderPageQuery != null) {
          code = StrUtil.blankToDefault(code,supplierOrderPageQuery.getOrderCode());
          orderState = ObjectUtil.defaultIfNull(orderState,supplierOrderPageQuery.getOrderState());
          startCreateTime = ObjectUtil.defaultIfNull(startCreateTime,supplierOrderPageQuery.getStartCreateTime());
          endCreateTime = ObjectUtil.defaultIfNull(endCreateTime,supplierOrderPageQuery.getEndCreateTime());
          supplierName = ObjectUtil.defaultIfNull(supplierName,supplierOrderPageQuery.getSupplierName());
          directShipment = ObjectUtil.defaultIfNull(directShipment,supplierOrderPageQuery.getDirectShipment());
          confirmState = ObjectUtil.defaultIfNull(confirmState,supplierOrderPageQuery.getConfirmState());
          cancelState = ObjectUtil.defaultIfNull(cancelState,supplierOrderPageQuery.getCancelState());
          returnState = ObjectUtil.defaultIfNull(returnState,supplierOrderPageQuery.getReturnState());
          shipWaitStock = ObjectUtil.defaultIfNull(shipWaitStock,supplierOrderPageQuery.getShipWaitStock());
          purchaseGroupName = StrUtil.blankToDefault(purchaseGroupName,supplierOrderPageQuery.getPurchaseGroupName());
          receiveMan = StrUtil.blankToDefault(receiveMan,supplierOrderPageQuery.getReceiveMan());
          selectUnReceipt = ObjectUtil.defaultIfNull(selectUnReceipt,supplierOrderPageQuery.getSelectUnReceipt());
          supplierOpenInvoiceState = StrUtil.blankToDefault(supplierOpenInvoiceState,supplierOrderPageQuery.getSupplierOpenInvoiceState());
          reject = ObjectUtil.defaultIfNull(reject,supplierOrderPageQuery.getReject());
          pending = ObjectUtil.defaultIfNull(pending,supplierOrderPageQuery.getPending());
          staging = ObjectUtil.defaultIfNull(staging,supplierOrderPageQuery.getStaging());
          unaudited = ObjectUtil.defaultIfNull(unaudited,supplierOrderPageQuery.getUnaudited());
          purchaseDept = StrUtil.blankToDefault(purchaseDept,supplierOrderPageQuery.getPurchaseDept());
          purchaseMan = StrUtil.blankToDefault(purchaseMan,supplierOrderPageQuery.getPurchaseMan());
          salesOrderNo = StrUtil.blankToDefault(salesOrderNo,supplierOrderPageQuery.getSalesOrderNo());
          largeTicketProjectNumbers =
              StrUtil.blankToDefault(largeTicketProjectNumbers,supplierOrderPageQuery.getLargeTicketProjectNumbers());
          largeTicketProjectName =
              StrUtil.blankToDefault(largeTicketProjectName,supplierOrderPageQuery.getLargeTicketProjectName());
          freeState = ObjectUtil.defaultIfNull(freeState,supplierOrderPageQuery.getFreeState());
          selfState = ObjectUtil.defaultIfNull(selfState,supplierOrderPageQuery.getSelfState());
          creater = ObjectUtil.defaultIfNull(creater, supplierOrderPageQuery.getCreateMan());
          updateMan = ObjectUtil.defaultIfNull(updateMan, supplierOrderPageQuery.getUpdateMan());
          startUpdateTime = ObjectUtil.defaultIfNull(startUpdateTime
              , supplierOrderPageQuery.getStartUpdateTime());
          endUpdateTime = ObjectUtil.defaultIfNull(endUpdateTime,
              supplierOrderPageQuery.getEndUpdateTime());
          startAuditTime = ObjectUtil.defaultIfNull(startUpdateTime
              , supplierOrderPageQuery.getStartAuditTime());
          endAuditTime = ObjectUtil.defaultIfNull(endUpdateTime,
              supplierOrderPageQuery.getEndAuditTime());
          numOperators = ObjectUtil.defaultIfNull(numOperators,
              supplierOrderPageQuery.getOrderNumOperators());
          num = ObjectUtil.defaultIfNull(num,
              supplierOrderPageQuery.getOrderNum());
          price = ObjectUtil.defaultIfNull(price,
              supplierOrderPageQuery.getOrderPrice());
          priceOperators = ObjectUtil.defaultIfNull(priceOperators,
              supplierOrderPageQuery.getOrderPriceOperators());
          scp = ObjectUtil.defaultIfNull(scp,
              supplierOrderPageQuery.getScp());
          allScp = StrUtil.blankToDefault(allScp,
              supplierOrderPageQuery.getAllScp());
          orderType = StrUtil.blankToDefault(orderType, supplierOrderPageQuery.getOrderType());
          counteractState = ObjectUtil.defaultIfNull(counteractState,
              supplierOrderPageQuery.getWriteOffState());
          loss = ObjectUtil.defaultIfNull(loss, supplierOrderPageQuery.getLoss());
        }
      }
    }
    SupplierOrderFormType supplierOrderFormType = null;
    SupplierOrderFormStatus supplierOrderFormStatus = null;
    if (Boolean.TRUE.equals(selectUnReceipt)) {
      supplierOrderFormType = SupplierOrderFormType.DELIVER;
      supplierOrderFormStatus = SupplierOrderFormStatus.WAIT_RECEIPT;
    }
    boolean isAdmin = userService.isAdmin(user);
    Page<SupplierOrder> purchaseOrderPage =
        supplierOrderDao.findPurchaseOrderPage(purchaseId, createMan, code, orderState,
            startCreateTime, endCreateTime, supplierName, directShipment, confirmState, cancelState,
            returnState, refuseState, freeState, selfState, shipWaitStock, pending, staging,
            unaudited, reject, purchaseDept, purchaseMan, purchaseGroupName, receiveMan,
            supplierOpenInvoiceState, supplierOrderFormType, supplierOrderFormStatus, salesOrderNo,
            largeTicketProjectNumbers, largeTicketProjectName, userNameList, isAdmin, creater,
            updateMan, startUpdateTime, endUpdateTime, startAuditTime, endAuditTime,numOperators,
            num,priceOperators,price,scp,allScp,orderType, counteractState, loss, toPageable);
    if (purchaseOrderPage.getContent().size() < Runtime.getRuntime().availableProcessors() * 1.5) {
      return PageResultBuilder.buildPageResult(purchaseOrderPage, supplierOrder -> {
        PurchaseOrderListDTO supplierOrderListDTO = new PurchaseOrderListDTO(supplierOrder);
        supplierOrderListDTO.setUnReceipt(
            supplierOrderToFormService.countByFormTypeAndFormStatus(supplierOrder.getId(),
                SupplierOrderFormType.DELIVER, SupplierOrderFormStatus.WAIT_RECEIPT) > 0);
        if (StrUtil.isNotEmpty(supplierOrder.getCreateMan())) {
          supplierOrderListDTO.setCreateMan(
              userService.getNameById(supplierOrder.getCreateMan()));
        }
        if (StrUtil.isNotEmpty(supplierOrder.getUpdateMan())) {
          supplierOrderListDTO.setUpdateMan(
              userService.getNameById(supplierOrder.getUpdateMan()));
        }
        List<File> fileList = fileService.getFileListByIdAndType(supplierOrder.getId(),
            Constants_FileRelationType.ORDER_CONTRACT);
        supplierOrderListDTO.setContractFiles(buildContractFiles(fileList,uploadConfig.getUploadPath()));
        if (SupplierOrderState.STAGING.getOrderState().equals(supplierOrder.getOrderState())
        || SupplierOrderState.REJECT.getOrderState().equals(supplierOrder.getOrderState())) {
          supplierOrderListDTO.setDeleteStorage(("admin".equals(user.getName())||
              currUser.equals(supplierOrder.getPurchaseMan())
          ));
        }else{
          supplierOrderListDTO.setDeleteStorage(false);
        }
        supplierOrderListDTO.setWriteOffState(supplierOrderToFormService.getExistReversal(supplierOrder.getId()));
        return supplierOrderListDTO;
      });
    }
    List<List<PurchaseOrderListDTO>> vos =
        ParallelProcessUtil.simpleParallelProcessOfCollection(purchaseOrderPage.getContent(),
            ioIntensiveThreadPool, supplierOrder -> {
              PurchaseOrderListDTO supplierOrderListDTO = new PurchaseOrderListDTO(supplierOrder);
              supplierOrderListDTO.setUnReceipt(
                  supplierOrderToFormService.countByFormTypeAndFormStatus(supplierOrder.getId(),
                      SupplierOrderFormType.DELIVER, SupplierOrderFormStatus.WAIT_RECEIPT) > 0);
              if (StrUtil.isNotEmpty(supplierOrder.getCreateMan())) {
                supplierOrderListDTO.setCreateMan(
                    userService.getNameById(supplierOrder.getCreateMan()));
              }
              if (StrUtil.isNotEmpty(supplierOrder.getUpdateMan())) {
                supplierOrderListDTO.setUpdateMan(
                    userService.getNameById(supplierOrder.getUpdateMan()));
              }
              List<File> fileList = fileService.getFileListByIdAndType(supplierOrder.getId(),
                  Constants_FileRelationType.ORDER_CONTRACT);
              supplierOrderListDTO.setContractFiles(buildContractFiles(fileList,uploadConfig.getUploadPath()));
              if (SupplierOrderState.STAGING.getOrderState().equals(supplierOrder.getOrderState())
              || SupplierOrderState.REJECT.getOrderState().equals(supplierOrder.getOrderState())) {
                supplierOrderListDTO.setDeleteStorage(("admin".equals(user.getName())||
                    currUser.equals(supplierOrder.getPurchaseMan())
                ));
              }else{
                supplierOrderListDTO.setDeleteStorage(false);
              }
              supplierOrderListDTO.setWriteOffState(supplierOrderToFormService.getExistReversal(supplierOrder.getId()));
              return supplierOrderListDTO;
            });
    List<PurchaseOrderListDTO> results =
        ParallelProcessUtil.flattenTwoDimensionalList(vos);
    return PageResultBuilder.buildPageResult(purchaseOrderPage, results);
  }

  @Override
  @DefaultSearchScheme(searchType = Constants.SEARCH_TYPE_GET_PAGE_SUPPLIER_ORDER_PAGE)
  public PageResult<PurchaseOrderListDTO> getPagePurchaseOrderPageRef(
      PurchaseOrderPageQuery form) {
    User user = manageSecurityUtil.getSrmUserDetails().getUser();
    String currUser = getCurrUser(user);
    SearchPermission searchPermission =
        sharePermissionTypeService.getSearchPermission(user, form.getUserGroup(),
            Constants.USER_PERMISSION_SUPPLIER_ORDER, false, false, false,form.getIsViewAllOrganization());
    MergeUserPermission mergeUserPermission =
        sharePermissionTypeService.mergePermission(searchPermission, new OperatorPermission());
    Page<SupplierOrder> page =
        supplierOrderDao.findPurchaseOrderPageRef(form.toQueryMap(mergeUserPermission));
    if (page.getContent().size() < Runtime.getRuntime().availableProcessors() * 1.5) {
      return PageResultBuilder.buildPageResult(page, supplierOrder -> {
        PurchaseOrderListDTO supplierOrderListDTO = new PurchaseOrderListDTO(supplierOrder);
        supplierOrderListDTO.setUnReceipt(
            supplierOrderToFormService.countByFormTypeAndFormStatus(supplierOrder.getId(),
                SupplierOrderFormType.DELIVER, SupplierOrderFormStatus.WAIT_RECEIPT) > 0);
        if (StrUtil.isNotEmpty(supplierOrder.getCreateMan())) {
          supplierOrderListDTO.setCreateMan(
              userService.getNameById(supplierOrder.getCreateMan()));
        }
        if (StrUtil.isNotEmpty(supplierOrder.getUpdateMan())) {
          supplierOrderListDTO.setUpdateMan(
              userService.getNameById(supplierOrder.getUpdateMan()));
        }
        List<File> fileList = fileService.getFileListByIdAndType(supplierOrder.getId(),
            Constants_FileRelationType.ORDER_CONTRACT);
        supplierOrderListDTO.setContractFiles(buildContractFiles(fileList,uploadConfig.getUploadPath()));
        if (SupplierOrderState.STAGING.getOrderState().equals(supplierOrder.getOrderState())
            || SupplierOrderState.REJECT.getOrderState().equals(supplierOrder.getOrderState())) {
          supplierOrderListDTO.setDeleteStorage(("admin".equals(user.getName())||
              currUser.equals(supplierOrder.getPurchaseMan())
          ));
        }else{
          supplierOrderListDTO.setDeleteStorage(false);
        }
        supplierOrderListDTO.setWriteOffState(supplierOrderToFormService.getExistReversal(supplierOrder.getId()));
        return supplierOrderListDTO;
      });
    }
    List<List<PurchaseOrderListDTO>> vos =
        ParallelProcessUtil.simpleParallelProcessOfCollection(page.getContent(),
            ioIntensiveThreadPool, supplierOrder -> {
              PurchaseOrderListDTO supplierOrderListDTO = new PurchaseOrderListDTO(supplierOrder);
              supplierOrderListDTO.setUnReceipt(
                  supplierOrderToFormService.countByFormTypeAndFormStatus(supplierOrder.getId(),
                      SupplierOrderFormType.DELIVER, SupplierOrderFormStatus.WAIT_RECEIPT) > 0);
              if (StrUtil.isNotEmpty(supplierOrder.getCreateMan())) {
                supplierOrderListDTO.setCreateMan(
                    userService.getNameById(supplierOrder.getCreateMan()));
              }
              if (StrUtil.isNotEmpty(supplierOrder.getUpdateMan())) {
                supplierOrderListDTO.setUpdateMan(
                    userService.getNameById(supplierOrder.getUpdateMan()));
              }
              List<File> fileList = fileService.getFileListByIdAndType(supplierOrder.getId(),
                  Constants_FileRelationType.ORDER_CONTRACT);
              supplierOrderListDTO.setContractFiles(buildContractFiles(fileList,uploadConfig.getUploadPath()));
              if (SupplierOrderState.STAGING.getOrderState().equals(supplierOrder.getOrderState())
                  || SupplierOrderState.REJECT.getOrderState().equals(supplierOrder.getOrderState())) {
                supplierOrderListDTO.setDeleteStorage(("admin".equals(user.getName())||
                    currUser.equals(supplierOrder.getPurchaseMan())
                ));
              }else{
                supplierOrderListDTO.setDeleteStorage(false);
              }
              supplierOrderListDTO.setWriteOffState(supplierOrderToFormService.getExistReversal(supplierOrder.getId()));
              return supplierOrderListDTO;
            });
    List<PurchaseOrderListDTO> results =
        ParallelProcessUtil.flattenTwoDimensionalList(vos);
    return PageResultBuilder.buildPageResult(page, results);
  }

  @Override
  @DefaultSearchScheme(searchType = Constants.SEARCH_TYPE_GET_PAGE_SUPPLIER_ORDER_PAGE)
  public PurchaseOrderStatistics getPagePurchaseOrderStatisticsForOrder(
      PurchaseOrderPageQuery form) {
    User user = manageSecurityUtil.getSrmUserDetails().getUser();
    SearchPermission searchPermission =
        sharePermissionTypeService.getSearchPermission(user, form.getUserGroup(),
            Constants.USER_PERMISSION_SUPPLIER_ORDER, false, false, false,form.getIsViewAllOrganization());
    MergeUserPermission mergeUserPermission =
        sharePermissionTypeService.mergePermission(searchPermission, new OperatorPermission());
    return supplierOrderDao.findPurchaseOrderStatistics2(form.toQueryMap(mergeUserPermission));
  }

  private static SupplierOrderProduct saveProduct(supplierProduct supplierProduct) {
    SupplierOrderProduct supplierOrderProduct = new SupplierOrderProduct();
    supplierOrderProduct.setCode(supplierProduct.getProductCode());
    supplierOrderProduct.setBrand(supplierProduct.getBrand());
    supplierOrderProduct.setName(supplierProduct.getProductName());
    supplierOrderProduct.setManuCode(supplierProduct.getManuCode());
    supplierOrderProduct.setUnit(supplierProduct.getUnit());
    supplierOrderProduct.setUnitCode(supplierProduct.getUnitCode());
    supplierOrderProduct.setUnitDigit(3);
    supplierOrderProduct.setSalesman(supplierProduct.getSalesman());
    supplierOrderProduct.setFollowUpPersonName(supplierProduct.getFollowUpPersonName());
    supplierOrderProduct.setBusinessCompanyName(supplierProduct.getBusinessCompanyName());
    supplierOrderProduct.setMakeManName(supplierProduct.getMakeManName());
    supplierOrderProduct.setSoldToParty(supplierProduct.getSoldToParty());
    return supplierOrderProduct;
  }

  @Override
  @DefaultSearchScheme(searchType = Constants.USER_PERMISSION_SUPPLIER_ORDER)
  public PurchaseOrderProductStatistics getPagePurchaseOrderStatisticsForProduct(PurchaseOrderProductSearchForm form) {
    User user = manageSecurityUtil.getSrmUserDetails().getUser();
    SearchPermission searchPermission =
        sharePermissionTypeService.getSearchPermission(user, form.getUserGroup(),
            Constants.USER_PERMISSION_SUPPLIER_ORDER, false, false, false,form.getIsViewAllOrganization());
    MergeUserPermission mergeUserPermission =
        sharePermissionTypeService.mergePermission(searchPermission, new OperatorPermission());
    return supplierOrderDetailDao.getPagePurchaseOrderStatistics2(form.toQueryMap(mergeUserPermission));
  }

  private List<FileDTO> buildContractFiles(Collection<File> annex, String baseUrl) {
    ArrayList<FileDTO> fileDTOS = new ArrayList<>();
    for (File file : annex) {
      FileDTO fileDTO = new FileDTO();
      fileDTO.setId(file.getId());
      fileDTO.setUrl(file.getUrl());
      fileDTO.setName(file.getName());
      fileDTO.setBaseUrl(baseUrl);
      if (StrUtil.isNotBlank(file.getDescription())) {
        String[] split = StrUtil.split(file.getDescription(), ".");
        String fileType = ArrayUtil.get(split, -1);
        fileDTO.setType(fileType);
      }
      fileDTOS.add(fileDTO);
    }
    return fileDTOS;
  }

  /**
   * 处理query
   */
  @DefaultSearchScheme(searchType = Constants.USER_PERMISSION_SUPPLIER_ORDER)
  public PurchaseOrderProductSearchForm handlerPurchaseOrderProductPageQuery(PurchaseOrderProductSearchForm query) {
    return query;
  }

  /**
   *
   * @param exportPurchaseOrderParams
   */
  @Override
  public void exportPurchaseOrderProduct(ExportPurchaseOrderProductParams exportPurchaseOrderParams) {
    Assert.notNull(exportPurchaseOrderParams);
    // 设置userGroup
    if (exportPurchaseOrderParams.getQuery() != null) {
      exportPurchaseOrderParams.getQuery().setUserGroup(exportPurchaseOrderParams.getUserGroup());
    }
    // 存储模版导出信息
    templateEventPublisher.publish(this,exportPurchaseOrderParams,
        Constants_Batch.BATCH_TASK_EXPORT_SUPPLIER_ORDER);
    // 解决同一类调用注解不生效问题
    PurchaseOrderServiceImpl proxy = applicationContext.getBean(PurchaseOrderServiceImpl.class);
    PurchaseOrderProductSearchForm newQuery = proxy.handlerPurchaseOrderProductPageQuery(exportPurchaseOrderParams.getQuery());
    exportPurchaseOrderParams.setQuery(newQuery);
    User user = manageSecurityUtil.getSrmUserDetails().getUser();
    // 手动管理事务
    DefaultTransactionDefinition definition = new DefaultTransactionDefinition();
    TransactionStatus transaction = platformTransactionManager.getTransaction(definition);
    Map<String, Object> queryMap = new HashMap<>();
    List<String> exportIds = new ArrayList<>();
    List<String> ids = exportPurchaseOrderParams.getIds();
    Mission mission;
    Map<String, Object> params = new HashMap<>();
    try {
      boolean exportAll = false;
      if (CollUtil.isNotEmpty(ids)) {
        exportIds.addAll(ids);
        if (BeanUtil.isNotEmpty(newQuery) && BooleanUtil.isTrue(newQuery.getIsHistoricalOrder())) {
          params.put("isHistoricalOrder", true);
        }
      }else if (BeanUtil.isNotEmpty(newQuery)) {
        newQuery.setPageNo(1);
        newQuery.setPageSize(Integer.MAX_VALUE);
        SearchPermission searchPermission =
            sharePermissionTypeService.getSearchPermission(user, exportPurchaseOrderParams.getUserGroup(),
                Constants.USER_PERMISSION_SUPPLIER_ORDER, false, false, false);
        MergeUserPermission mergeUserPermission =
            sharePermissionTypeService.mergePermission(searchPermission, new OperatorPermission());
        queryMap = newQuery.toQueryMap(mergeUserPermission);
      } else {
        exportAll = true;
      }
      mission = missionService.createMission(user, "导出-采购单物料信息", Constants.PLATFORM_TYPE_AFTER, null,
          null);
      params.put("userId", user.getId());
      params.put("ids", exportIds);
      params.put("queryMap", queryMap);
      params.put("exportAll", exportAll);
      params.put("exportField", CollUtil.emptyIfNull(exportPurchaseOrderParams.getSelectFieldList()));
      params.put("selectSupplier",
          StrUtil.isNotBlank(newQuery.mGetQueryMapWithUnifiedFormValueStr(queryMap, "supplierName")));
      platformTransactionManager.commit(transaction);
    }catch (Exception e) {
      platformTransactionManager.rollback(transaction);
      throw e;
    }
    batchTaskMqSender.toHandleBatchTask(mission.getId(), JSON.toJSONString(params),
          Constants_Batch.BATCH_TASK_EXPORT_SUPPLIER_ORDER_PRODUCT_DETAIL);
    }

  private static String getCurrUser(User user) {
    String removeStr = "xhgj00";
    String removeStr2 = "XHGJ00";
    String currUser = StrUtil.removeAny(user.getCode(), removeStr, removeStr2) + user.getRealName();
    return currUser;
  }

  private String generateReturnReason(String returnReason, String returnReasonDetails) {
    if (StrUtil.isNotBlank(returnReason) && StrUtil.isNotBlank(returnReasonDetails)) {
      return "【" + returnReason + "】 " + returnReasonDetails;
    }
    if (StrUtil.isBlank(returnReason) && StrUtil.isBlank(returnReasonDetails)) {
      return StrUtil.EMPTY;
    }
    if (StrUtil.isNotBlank(returnReason) && StrUtil.isBlank(returnReasonDetails)) {
      return "【" + returnReason + "】";
    }
    throw new CheckException("入参不合法");
  }

  @Override
  @Transactional(rollbackFor = Exception.class)
  public void addReturnOrder(AddPurchaseOrderReturnParam param) {
    verifyReturnOrderWarehouse(param);
    SupplierOrder supplierOrder = supplierOrderRepository.findById(param.getId()).orElse(null);
    User user = manageSecurityUtil.getSrmUserDetails().getUser();
    if (supplierOrder == null) {
      throw new CheckException("订单不存在！");
    }
    Supplier supplier = supplierRepository.findById(supplierOrder.getSupplierId())
        .orElseThrow(() -> new CheckException("供应商信息不存在！"));
    Boolean oneTimeSupplier = supplier.isOneTimeSupplier();
    Supplier supplierInvoice = supplier;
    if (!oneTimeSupplier) {
      String invoicingParty = supplierOrder.getInvoicingParty();
      supplierInvoice =
          supplierRepository.findFirstByEnterpriseNameAndState(invoicingParty, Constants.STATE_OK)
              .orElseThrow(() -> new CheckException("供应商信息不存在！"));
    }
    param.setReturnReason(generateReturnReason(param.getReturnReason(),
        param.getReturnReasonDetails()));
    supplierOrder.setOrderReturnState(true);
    //是否是已开票
    Boolean isOpen = false;
    // 新增退货单
    SupplierOrderToForm supplierOrderForm =
        supplierOrderToFormService.createSupplierOrderForm(
            param.getId(), SupplierOrderFormType.RETURN);
    String logisticsCompany = param.getLogisticsCompany();
    supplierOrderForm.setLogisticsCompany(logisticsCompany);
    String logisticsCode = param.getLogisticsCode();
    supplierOrderForm.setLogisticsCode(logisticsCode);
    supplierOrderForm.setCode(param.getCode());
    String trackNum = param.getTrackNum();
    supplierOrderForm.setTrackNum(trackNum);
    supplierOrderForm.setStatus(SupplierOrderFormStatus.RETURN.getStatus());
    supplierOrderForm.setReturnReason(param.getReturnReason());
    supplierOrderForm.setReturnWarehouse(param.getReturnWarehouse());
    supplierOrderForm.setConsignee(param.getConsignee());
    supplierOrderForm.setReceiveAddress(param.getReceiveAddress());
    //是否需要开红票
    supplierOrderForm.setNeedRedTicket(param.getNeedRedTicket());
    supplierOrderForm.setSapReturnNumber(supplierOrder.getCode());

    SupplierOrderToForm orderToForm = supplierOrderToFormService.save(supplierOrderForm);
    List<ProductDetail> productDetailList = param.getProductDetailList();
    BigDecimal returnNum = BigDecimal.ZERO;
    BigDecimal returnAmount = BigDecimal.ZERO;
    List<Item> itemList = new ArrayList<>();
    List<ITEMDTO> itemdtos = new ArrayList<>();
    List<PurchaseOrderReturnHEADDTO.ITEMDTO> itemdtoList = new ArrayList<>();
    List<SupplierOrderDetail> supplierOrderDetailList = new ArrayList<>();
    //是否厂家直发 0否 1是
    boolean directShipment = WarehouseEnum.HAI_NING_DIRECT_SALES.getCode().equals(supplierOrderForm.getReturnWarehouse());
    // 记录每个物料的最初退库数量
    Map<String,BigDecimal> originDetailToOutput = new HashMap<>();
    for (ProductDetail productDetail : productDetailList) {
      returnNum = returnNum.add(productDetail.getReturnNum());
      SupplierOrderDetail orderDetail =
          supplierOrderDetailRepository.findById(productDetail.getId()).orElse(null);
      if (orderDetail == null) {
        continue;
      }
      originDetailToOutput.putIfAbsent(orderDetail.getId(),
          orderDetail.getStockOutputQty());
      SupplierOrderDetail orderDetailBase =
          orderDetail.getDetailed();
      if (orderDetailBase == null) {
        continue;
      }
      if(orderDetail.getInvoicedNum()!=null && orderDetail.getInvoicedNum().compareTo(BigDecimal.ZERO) == 1){
        isOpen = true;
      }
      SupplierOrderDetail supplierOrderDetail =
          saveSupplierOrderDetail(supplierOrderForm, productDetail, orderDetail, orderDetailBase,
              supplierOrder.getId(),param.getNeedRedTicket(),supplierOrder.getOrderType());
      supplierOrderDetailList.add(supplierOrderDetail);
      orderDetail.setInvoicableNum(orderDetail.getInvoicableNum().subtract(productDetail.getReturnNum()));
      orderDetail.setReturnQty(BigDecimalUtil.setScaleBigDecimalHalfUp(
          orderDetail.getReturnQty() == null ? productDetail.getReturnNum()
              : orderDetail.getReturnQty().add(productDetail.getReturnNum()), 3));
      // 使用最初获得的退库数量。防止同时退库同一物料，退库数量叠加
      BigDecimal baseOutputQty =
          Optional.ofNullable(originDetailToOutput.get(orderDetail.getId()))
              .orElse(BigDecimal.ZERO);
      orderDetail.setStockOutputQty(
          (BigDecimalUtil.setScaleBigDecimalHalfUp(baseOutputQty.add(productDetail.getReturnNum()),
              3)));
      orderDetail.setPurchaseOrderId(supplierOrder.getId());
      supplierOrderDetailRepository.save(orderDetail);

      orderDetailBase.setReturnQty(BigDecimalUtil.setScaleBigDecimalHalfUp(
          orderDetailBase.getReturnQty() == null ? productDetail.getReturnNum()
              : orderDetailBase.getReturnQty().add(productDetail.getReturnNum()), 3));
      orderDetailBase.setStockOutputQty(BigDecimalUtil.setScaleBigDecimalHalfUp(
          orderDetailBase.getStockOutputQty() == null ? productDetail.getReturnNum()
              : orderDetailBase.getStockOutputQty().add(productDetail.getReturnNum()), 3));
      orderDetailBase.setSettleQty(BigDecimalUtil.setScaleBigDecimalHalfUp(
          orderDetailBase.getStockInputQty().subtract(productDetail.getReturnNum()), 3));
      orderDetailBase.setRemainQty(BigDecimalUtil.setScaleBigDecimalHalfUp(orderDetailBase.getRemainQty(),3));
      orderDetailBase.setPurchaseOrderId(supplierOrder.getId());
      supplierOrderDetailRepository.save(orderDetailBase);
      //入库单id
      returnAmount = returnAmount.add(orderDetail.getPrice().multiply(productDetail.getReturnNum()));
      String orderToFormId = orderDetail.getOrderToFormId();
      SupplierOrderToForm supplierOrderToForm =
          supplierOrderToFormRepository.findById(orderToFormId).orElse(null);
      if(supplierOrderToForm!=null){
        supplierOrderToForm.setReturnPrice(supplierOrderToForm.getReturnPrice().subtract(orderDetail.getPrice().multiply(productDetail.getReturnNum())));
        supplierOrderToForm.setNum(supplierOrderToForm.getNum() == null ?
            productDetail.getReturnNum() :
            supplierOrderToForm.getNum().subtract(productDetail.getReturnNum()));
        supplierOrderToFormRepository.save(supplierOrderToForm);
      }
      //构建sap参数 ZFM_MM_031
      Item item = new Item();
      item.setPurchaseOrderNumber(supplierOrder.getCode());
      item.setPurchaseOrderLineItemNo(orderDetailBase.getSortNum().toString());
      item.setMaterialNumber(orderDetail.getSupplierOrderProduct().getCode());
      item.setQuantity(productDetail.getReturnNum().setScale(3).toPlainString());
      item.setBaseUnitOfMeasure(orderDetailBase.getSupplierOrderProduct().getUnitCode());
      item.setFactoryCode(supplierOrder.getGroupCode());
      item.setWarehouseLocation(supplierOrderForm.getReturnWarehouse());
      item.setReferenceMaterialDocumentNumber(supplierOrderToForm.getProductVoucher());
      item.setReferenceMaterialDocumentLineNumber(orderDetail.getSapRowId());
      itemList.add(item);
      //构建sap参数 MM_021
      ITEMDTO itemdto = new ITEMDTO();
      //默认赋值空的参数
      itemdto.setBanfn(StrUtil.EMPTY);
      itemdto.setBnfpo(StrUtil.EMPTY);
      itemdto.setCharX(StrUtil.EMPTY);
      itemdto.setElikz(StrUtil.EMPTY);
      itemdto.setKnttp(StrUtil.EMPTY);
      itemdto.setKostl(StrUtil.EMPTY);
      itemdto.setLoekz(StrUtil.EMPTY);
      itemdto.setMatkl(StrUtil.EMPTY);
      itemdto.setZgsje(StrUtil.EMPTY);
      itemdto.setZjsj(StrUtil.EMPTY);
      itemdto.setZyyje(StrUtil.EMPTY);
      itemdto.setZzfjf(StrUtil.EMPTY);
      itemdto.setZzkhddh_01(StrUtil.EMPTY);
      itemdto.setZZPOSNR(StrUtil.EMPTY);
      itemdto.setZZVBELN(StrUtil.EMPTY);
      itemdto.setZzwlsl(StrUtil.EMPTY);
      itemdto.setZzxmbh_01(StrUtil.EMPTY);
      itemdto.setEbelp(productDetail.getRowNo());
      itemdto.setMatnr(orderDetail.getSupplierOrderProduct().getCode());
      itemdto.setTxz01(orderDetail.getSupplierOrderProduct().getName());
      itemdto.setMeins(orderDetail.getSupplierOrderProduct().getUnitCode());
      itemdto.setMenge(supplierOrderDetail.getStockOutputQty().stripTrailingZeros().toPlainString());
      itemdto.setWerks(supplierOrder.getGroupCode());
      itemdto.setLgort(supplierOrderForm.getReturnWarehouse());
      itemdto.setAplfz(DateUtils.formatTimeStampToStr(System.currentTimeMillis(),
          DatePattern.PURE_DATE_PATTERN));
      itemdto.setMwskz(Constants.TAX_RATE_TYPE_NUM.get(orderDetail.getTaxRate()));
      BigDecimal price = orderDetail.getPrice();
      Pair<BigDecimal, Integer> convertSapPrice = SAPToolUtils.convertSapPrice(price, 2);
      itemdto.setNetpr(convertSapPrice.getKey().toPlainString());
      itemdto.setPeinh(convertSapPrice.getValue().toString());
      itemdto.setBprme(orderDetail.getSupplierOrderProduct().getUnitCode());
      itemdto.setZmfbs(StrUtil.equals(orderDetail.getFreeState(), SimpleBooleanEnum.YES.getKey()) ?
          Constants_Sap.CONFIRM_IDENTIFICATION : "");
      itemdto.setPstyp(orderDetail.getProjectType());
      itemdto.setRetpo("X");
      itemdto.setCharX(orderDetail.getBatchNo());
      String freightSupplierId = orderDetailBase.getFreightSupplierId();
      if (StrUtil.isNotBlank(freightSupplierId)) {
        Optional.ofNullable(supplierService.get(freightSupplierId)).ifPresent(
            supplierTemp -> itemdto.setYfgys(supplierTemp.getMdmCode())
        );
      }
      String tariffSupplierId = orderDetailBase.getTariffSupplierId();
      if (StrUtil.isNotBlank(tariffSupplierId)) {
        Optional.ofNullable(supplierService.get(tariffSupplierId)).ifPresent(
            supplierTemp -> itemdto.setGsgys(supplierTemp.getMdmCode())
        );
      }
      itemdtos.add(itemdto);
      //构建sap参数 MM_075
      PurchaseOrderReturnHEADDTO.ITEMDTO itemReturndto = new PurchaseOrderReturnHEADDTO.ITEMDTO();
      itemReturndto.setEbelp(productDetail.getRowNo());
      itemReturndto.setMatnr(orderDetail.getSupplierOrderProduct().getCode());
      itemReturndto.setMeins(orderDetail.getSupplierOrderProduct().getUnitCode());
      itemReturndto.setMenge(supplierOrderDetail.getStockOutputQty().stripTrailingZeros().toPlainString());
      itemReturndto.setWerks(supplierOrder.getGroupCode());
      itemReturndto.setLgort(supplierOrderForm.getReturnWarehouse());
      itemReturndto.setAplfz(DateUtil.format(DateUtil.date(), "yyyyMMdd"));
      itemReturndto.setMwskz(Constants.TAX_RATE_TYPE_NUM.get(orderDetail.getTaxRate().stripTrailingZeros()));
      itemReturndto.setNetpr(convertSapPrice.getKey().toPlainString());
      itemReturndto.setPeinh(convertSapPrice.getValue().toString());
      itemReturndto.setBprme(orderDetail.getSupplierOrderProduct().getUnitCode());
      itemReturndto.setRetpo("X");
      itemReturndto.setCharX(orderDetail.getBatchNo());
      itemdtoList.add(itemReturndto);
    }
    BigDecimal oldTotalProgress =
        new BigDecimal(
            StrUtil.subAfter(supplierOrder.getStockProgress(), CharUtil.SLASH, true));
    BigDecimal newTotalProgress =
        new BigDecimal(
            StrUtil.subBefore(supplierOrder.getStockProgress(), CharUtil.SLASH, true));
    supplierOrder.makeAndSetStockProgress(NumberUtil.sub(newTotalProgress, returnNum), NumberUtil.sub(oldTotalProgress, returnNum));
    supplierOrder.setTotalStockInputQty(NumberUtil.sub(supplierOrder.getTotalStockInputQty(),returnNum));
    if(supplierOrder.getFinalPrice()!=null && supplierOrder.getFinalPrice().compareTo(BigDecimal.ZERO) == 1){
      supplierOrder.setFinalPrice(NumberUtil.sub(supplierOrder.getFinalPrice(),returnAmount));
    }
    supplierOrder.setCancelReturnPrice(NumberUtil.add(supplierOrder.getCancelReturnPrice(),
        returnAmount));
    supplierOrderRepository.save(supplierOrder);
    // TODO 退货后调用SAP物料凭证创建接口
    //当前时间 例如20240108
    String dateTime =
        DateUtils.formatTimeStampToStr(System.currentTimeMillis(), DatePattern.PURE_DATE_PATTERN);
    ReceiptVoucherSynchronizationParam sapParam = new ReceiptVoucherSynchronizationParam();
    DataInfo dataInfo = new DataInfo();
    Head head = new Head();
    head.setMaterialPostingDate(dateTime);
    head.setVoucherDate(dateTime);
    head.setMovementType("122");
    head.setLogisticsCompany(StrUtil.nullToEmpty(param.getLogisticsCode()));
    head.setLogisticsNumber(StrUtil.nullToEmpty(param.getTrackNum()));
    head.setSrmId(orderToForm.getId());
    head.setTextRemark(param.getReturnReason());
    //TODO 退库逻辑修改 待sap提供字段补充接口文档
    if(directShipment){
      // 6.3.0 直销库退货时，仓库执行状态修改为无需仓库执行
      supplierOrderForm.setExecutionStatus(SupplierOrderFormExecutionStatusEnum.NO_NEED_EXECUTION.getKey());
      if(!isOpen){
        //厂直发订单，没有已开票数量的入库单物料退货 原逻辑原接口
        head.setZxkbj("");
        // 提交此退货单的采购的姓名、收件人、收件地址;
        head.setOperator(user.getRealName());
        head.setConsignee(StrUtil.nullToEmpty(param.getConsignee()));
        head.setConsigneeAddress(StrUtil.nullToEmpty(param.getReceiveAddress()));
      }else {
        //厂直发订单，有已开票数量的入库单物料退货 sap新接口
        MM_075Param mm075Param = new MM_075Param();
        PurchaseOrderReturnDATADTO purchaseOrderReturnDATADTO = new PurchaseOrderReturnDATADTO();
        PurchaseOrderReturnHEADDTO purchaseOrderReturnHEADDTO = new PurchaseOrderReturnHEADDTO();
        purchaseOrderReturnHEADDTO.setEbeln("20"+RandomUtil.randomNumbers(8));
        purchaseOrderReturnHEADDTO.setBsart(StrUtil.equals(supplierOrder.getOrderType(),
            PurchaseOrderTypeEnum.INITIAL_PURCHASE.getKey())? PurchaseOrderTypeEnum.INITIAL_PURCHASE.getKey():"Z050");
        purchaseOrderReturnHEADDTO.setEkorg(supplierOrder.getGroupCode());
        purchaseOrderReturnHEADDTO.setEkgrp(supplierOrder.getPurchaseDeptCode());
        purchaseOrderReturnHEADDTO.setBukrs(supplierOrder.getGroupCode());
        if(Constants.SUPPLIER_TYPE_PROVISIONAL.equals(supplier.getSupType())){
          purchaseOrderReturnHEADDTO.setName1(supplier.getEnterpriseName());
          purchaseOrderReturnHEADDTO.setCity1("一次性供应商");
          purchaseOrderReturnHEADDTO.setCountry("CN");
        }
        purchaseOrderReturnHEADDTO.setZttwb(param.getReturnReason());
        purchaseOrderReturnHEADDTO.setBedat(DateUtil.format(new Date(),DatePattern.PURE_DATE_PATTERN));
        purchaseOrderReturnHEADDTO.setLifnr(supplier.getMdmCode());
        purchaseOrderReturnHEADDTO.setLifn2(supplierInvoice.getMdmCode());
        purchaseOrderReturnHEADDTO.setWkurs(supplierOrder.getOrderRate());
        final String payment_iterm = "0001";
        purchaseOrderReturnHEADDTO.setZterm(payment_iterm);
        purchaseOrderReturnHEADDTO.setWaers(supplierOrder.getMoneyCode());
        //增加申请人和工号
        purchaseOrderReturnHEADDTO.setZycgdh(supplierOrder.getCode());
        purchaseOrderReturnHEADDTO.setAfnam(user.getRealName());
        purchaseOrderReturnHEADDTO.setBednr(user.getCode());
        purchaseOrderReturnHEADDTO.setItem(itemdtoList);
        purchaseOrderReturnDATADTO.setHead(purchaseOrderReturnHEADDTO);
        mm075Param.setData(purchaseOrderReturnDATADTO);
        MM_075Result result = sapService.sapDirectReturnWithError(mm075Param);
        supplierOrderForm.setProductVoucher(result.getReturnMessages().get(0).getProductVoucher());
        //sap退库单采购订单号
        supplierOrderForm.setSapReturnNumber(result.getReturnMessages().get(0).getEBELN());
        supplierOrderToFormRepository.save(supplierOrderForm);
        return;
      }
    }else {
      // 6.3.0 非直销库退货时，仓库执行状态修改为未执行
      supplierOrderForm.setExecutionStatus(SupplierOrderFormExecutionStatusEnum.PENDING_EXECUTION.getKey());
      if(!isOpen){
        //非厂直发订单，没有已开票数量的入库单物料退货 增加新字段
        head.setZxkbj("X");
        // 提交此退货单的采购的姓名、收件人、收件地址;
        head.setOperator(user.getRealName());
        head.setConsignee(StrUtil.nullToEmpty(param.getConsignee()));
        head.setConsigneeAddress(StrUtil.nullToEmpty(param.getReceiveAddress()));
      }else {
        //非厂直发订单，有已开票数量的入库单物料退货 增加新字段 调用MM021接口
        UpdatePurchaseOrderSapParam returnParam =
            buildNewPurchaseOrderParam(supplierOrder, itemdtos, supplier, supplierInvoice,
                orderToForm.getId(), false, null);
        returnParam
            .getData()
            .getHead()
            .setZttwb(
                param.getReturnReason()
                    + " 【收件人】"
                    + param.getConsignee()
                    + " 【收件地址】"
                    + param.getReceiveAddress());
        UpdatePurchaseOrderRETURNDTO mm_021Result =
            sapService.sapPurchaseOrderWithAlarm(returnParam, "");
        supplierOrderForm.setSapReturnNumber(mm_021Result.getEbeln());
        supplierOrderToFormRepository.save(supplierOrderForm);
        return;
      }

    }
    head.setItems(itemList);
    dataInfo.setHead(head);
    sapParam.setData(dataInfo);
    if(!isOpen){
      ReceiptVoucherSynchronizationResult result = sapService.sapMaterialVoucherWithLockGroup(sapParam);
      List<ReturnMessage> returnMessages = result.getReturnMessages();
      for (ReturnMessage returnMessage : returnMessages) {
        String lineItem = returnMessage.getLineItem();
        String documentNumber = returnMessage.getDocumentNumber();
        String purchaseOrderLineItems = returnMessage.getPurchaseOrderLineItems();
        supplierOrderForm.setProductVoucher(documentNumber);
        if (StrUtil.isNotBlank(purchaseOrderLineItems)) {
          Integer purchaseOrderLineItems1 = Integer.valueOf(purchaseOrderLineItems);
          Optional<SupplierOrderDetail> first =
              supplierOrderDetailList.stream().filter(supplierOrderDetail -> {
                return Objects.equals(supplierOrderDetail.getSortNum(), purchaseOrderLineItems1);
              }).findFirst();
          first.ifPresent(supplierOrderDetail -> {
            supplierOrderDetail.setBatchNo(returnMessage.getCharge());
            supplierOrderDetail.setSapRowId(lineItem);
            supplierOrderDetail.setSortNum(purchaseOrderLineItems1);
            supplierOrderDetailRepository.save(supplierOrderDetail);
          });
        }
        supplierOrderToFormRepository.save(supplierOrderForm);
      }
    }

  }

  /**
   * 校验退货仓库选择
   * v6.3.0 新增退货单的仓库增加限制。新增退货单时，仓库只允许选择成品库、直销库、样品库
   * @param param
   */
  private void verifyReturnOrderWarehouse(AddPurchaseOrderReturnParam param) {
    // 判断是否只选择了成品库、直销库、样品库
    if (StrUtil.isNotBlank(param.getReturnWarehouse())) {
      if (!WarehouseEnum.HAI_NING_DIRECT_SALES.getCode().equals(param.getReturnWarehouse())
          && !WarehouseEnum.FINISHED_PRODUCTS.getCode().equals(param.getReturnWarehouse())
          && !WarehouseEnum.HAI_NING_FINISHED_PRODUCTS.getCode().equals(param.getReturnWarehouse())
          && !WarehouseEnum.SAMPLE_SHIPMENT_CODE.getCode().equals(param.getReturnWarehouse())) {
        throw new CheckException("退货仓库只能选择成品库、直销库、样品库");
      }
    }
  }

  @Override
  @Transactional(rollbackFor = Exception.class)
  public String consignmentReturnOrder(ConsignmentReturnOrderParam param) {
    SupplierOrder supplierOrder = supplierOrderRepository.findById(param.getOrderId())
        .orElseThrow(() -> CheckException.noFindException(SupplierOrder.class, param.getOrderId()));
    User user = manageSecurityUtil.getSrmUserDetails().getUser();
    //如果寄售订单下面有未冲销退货单时，点击退货按钮报错“寄售订单下面已经有退货单了，无法转自有自动退货”
    supplierOrderToFormRepository.findBySupplierOrderIdAndTypeAndStateAndStatus(param.getOrderId(),
            SupplierOrderFormType.RETURN.getType(), Constants.STATE_OK,
            SupplierOrderFormStatus.RETURN.getStatus()).stream().findAny()
        .ifPresent(supplierOrderToForm -> {
          throw new CheckException("寄售订单下面已经有退货单了，无法转自有自动退货");
        });

    Supplier supplier = supplierRepository.findById(supplierOrder.getSupplierId())
        .orElseThrow(() -> new CheckException("供应商信息不存在！"));
    Boolean oneTimeSupplier = supplier.isOneTimeSupplier();
    Supplier supplierInvoice = supplier;
    if (!oneTimeSupplier) {
      String invoicingParty = supplierOrder.getInvoicingParty();
      supplierInvoice =
          supplierRepository.findFirstByEnterpriseNameAndState(invoicingParty, Constants.STATE_OK)
              .orElseThrow(() -> new CheckException("供应商信息不存在！"));
    }
    supplierOrder.setOrderReturnState(true);
    // 新增退货单
    SupplierOrderToForm supplierOrderForm =
        supplierOrderToFormService.createSupplierOrderForm(
            param.getOrderId(), SupplierOrderFormType.RETURN);
    supplierOrderForm.setStatus(SupplierOrderFormStatus.RETURN.getStatus());
    //退库原因
    supplierOrderForm.setReturnReason("寄售转自有退货");
    supplierOrderForm.setExecutionStatus(SupplierOrderFormExecutionStatusEnum.NO_NEED_EXECUTION.getKey());
    //退库仓库
//    String returnWarehouse =
//        supplierOrderDetailRepository.findById(param.getOrderDetailIds().get(0))
//            .map(SupplierOrderDetail::getDetailed).map(SupplierOrderDetail::getWarehouse)
//            .orElse(StrUtil.EMPTY);
    // 临时需求2024年7月19日09:35:20 -
    supplierOrderForm.setReturnWarehouse(WarehouseEnum.LEND_OUT_SHIPMENT_CODE.getCode());
    //是否需要开红票
    supplierOrderForm.setNeedRedTicket(Constants.STATE_NO);
    supplierOrderForm.setSapReturnNumber(supplierOrder.getCode());
    supplierOrderToFormService.save(supplierOrderForm);

    //总入库数量
    BigDecimal returnNum = BigDecimal.ZERO;
    BigDecimal returnAmount = BigDecimal.ZERO;
    List<PurchaseOrderReturnHEADDTO.ITEMDTO> itemdtoList = new ArrayList<>();
    List<SupplierOrderDetail> supplierOrderDetailList = new ArrayList<>();
    Integer index = 1;
    for (String orderDetailId : param.getOrderDetailIds()) {
      SupplierOrderDetail orderDetail =
          supplierOrderDetailRepository.findById(orderDetailId).orElse(null);
      if (orderDetail == null) {
        continue;
      }
      SupplierOrderDetail orderDetailBase =
          orderDetail.getDetailed();
      if (orderDetailBase == null) {
        continue;
      }
      returnNum = returnNum.add(orderDetail.getStockInputQty());
      SupplierOrderDetail supplierOrderDetail =
          saveSupplierOrderConsignmentDetail(supplierOrderForm, orderDetail, orderDetailBase,
              supplierOrder.getId());
      supplierOrderDetailList.add(supplierOrderDetail);
      orderDetail.setInvoicableNum(NumberUtil.sub(orderDetail.getInvoicableNum(),orderDetail.getStockInputQty()));
      orderDetail.setReturnQty(
          BigDecimalUtil.setScaleBigDecimalHalfUp(orderDetail.getStockInputQty(), 3));
      orderDetail.setStockOutputQty(
          BigDecimalUtil.setScaleBigDecimalHalfUp(orderDetail.getStockInputQty(), 3));
      orderDetail.setPurchaseOrderId(supplierOrder.getId());
      supplierOrderDetailRepository.save(orderDetail);
      orderDetailBase.setStockOutputQty(
          NumberUtil.add(orderDetailBase.getStockOutputQty(), orderDetail.getStockOutputQty()));
      orderDetailBase.setReturnQty(
          NumberUtil.add(orderDetailBase.getReturnQty(), orderDetail.getStockOutputQty()));
      orderDetailBase.setSettleQty(NumberUtil.sub(orderDetailBase.getStockInputQty(),orderDetail.getStockOutputQty()));
      supplierOrderDetailRepository.save(orderDetailBase);
      //入库单id
      returnAmount = returnAmount.add(orderDetail.getPrice().multiply(orderDetail.getStockInputQty()));
      String orderToFormId = orderDetail.getOrderToFormId();
      SupplierOrderToForm supplierOrderToForm =
          supplierOrderToFormRepository.findById(orderToFormId).orElse(null);
      if(supplierOrderToForm!=null){
        supplierOrderToForm.setReturnPrice(NumberUtil.sub(supplierOrderToForm.getReturnPrice(),
            NumberUtil.mul(orderDetail.getPrice(), orderDetail.getStockInputQty())));
        supplierOrderToForm.setNum(
            supplierOrderToForm.getNum() == null ? orderDetail.getStockInputQty()
                : NumberUtil.sub(supplierOrderToForm.getNum(), orderDetail.getStockInputQty()));
        supplierOrderToFormRepository.save(supplierOrderToForm);
      }
      BigDecimal price = orderDetail.getPrice();
      Pair<BigDecimal, Integer> convertSapPrice = SAPToolUtils.convertSapPrice(price, 2);
      //构建sap参数 MM_075
      PurchaseOrderReturnHEADDTO.ITEMDTO itemReturnDto = new PurchaseOrderReturnHEADDTO.ITEMDTO();
      itemReturnDto.setEbelp(index.toString());
      index++;
      itemReturnDto.setMatnr(orderDetail.getSupplierOrderProduct().getCode());
      itemReturnDto.setMeins(orderDetail.getSupplierOrderProduct().getUnitCode());
      itemReturnDto.setMenge(orderDetail.getStockInputQty().stripTrailingZeros().toPlainString());
      itemReturnDto.setWerks(supplierOrder.getGroupCode());
      itemReturnDto.setLgort(supplierOrderForm.getReturnWarehouse());
      itemReturnDto.setAplfz(DateUtil.format(DateUtil.date(), "yyyyMMdd"));
      itemReturnDto.setMwskz(Constants.TAX_RATE_TYPE_NUM.get(orderDetail.getTaxRate().stripTrailingZeros()));
      itemReturnDto.setNetpr(convertSapPrice.getKey().toPlainString());
      itemReturnDto.setPeinh(convertSapPrice.getValue().toString());
      itemReturnDto.setBprme(orderDetail.getSupplierOrderProduct().getUnitCode());
      itemReturnDto.setCharX(orderDetail.getBatchNo());
      itemReturnDto.setRetpo("X");
      //寄售转自有时传递K
      itemReturnDto.setPstyp("K");
      itemdtoList.add(itemReturnDto);
    }
    BigDecimal oldTotalProgress =
        new BigDecimal(
            StrUtil.subAfter(supplierOrder.getStockProgress(), CharUtil.SLASH, true));
    BigDecimal newTotalProgress =
        new BigDecimal(
            StrUtil.subBefore(supplierOrder.getStockProgress(), CharUtil.SLASH, true));
    supplierOrder.makeAndSetStockProgress(NumberUtil.sub(newTotalProgress, returnNum), NumberUtil.sub(oldTotalProgress, returnNum));
    supplierOrder.setTotalStockInputQty(NumberUtil.sub(supplierOrder.getTotalStockInputQty(),returnNum));
    if(supplierOrder.getFinalPrice()!=null && supplierOrder.getFinalPrice().compareTo(BigDecimal.ZERO) == 1){
      supplierOrder.setFinalPrice(NumberUtil.sub(supplierOrder.getFinalPrice(),returnAmount));
    }
    supplierOrder.setCancelReturnPrice(NumberUtil.add(supplierOrder.getCancelReturnPrice(),
        returnAmount));
    supplierOrderRepository.save(supplierOrder);

    MM_075Param mm075Param = new MM_075Param();
    PurchaseOrderReturnDATADTO purchaseOrderReturnDATADTO = new PurchaseOrderReturnDATADTO();
    PurchaseOrderReturnHEADDTO purchaseOrderReturnHEADDTO = new PurchaseOrderReturnHEADDTO();
    purchaseOrderReturnHEADDTO.setEbeln("21"+RandomUtil.randomNumbers(8));
    purchaseOrderReturnHEADDTO.setBsart("Z050");
    purchaseOrderReturnHEADDTO.setEkorg(supplierOrder.getGroupCode());
    purchaseOrderReturnHEADDTO.setEkgrp(supplierOrder.getPurchaseDeptCode());
    purchaseOrderReturnHEADDTO.setBukrs(supplierOrder.getGroupCode());
    if(Constants.SUPPLIER_TYPE_PROVISIONAL.equals(supplier.getSupType())){
      purchaseOrderReturnHEADDTO.setName1(supplier.getEnterpriseName());
      purchaseOrderReturnHEADDTO.setCity1("一次性供应商");
    }
    purchaseOrderReturnHEADDTO.setCountry("CN");
    purchaseOrderReturnHEADDTO.setZttwb(
        "寄售转自有SRM自动退货，操作人：{" + param.getUserName() + "}");
    purchaseOrderReturnHEADDTO.setBedat(DateUtil.format(new Date(),DatePattern.PURE_DATE_PATTERN));
    purchaseOrderReturnHEADDTO.setLifnr(supplier.getMdmCode());
    purchaseOrderReturnHEADDTO.setLifn2(supplierInvoice.getMdmCode());
    purchaseOrderReturnHEADDTO.setWkurs(supplierOrder.getOrderRate());
    final String payment_iterm = "0001";
    purchaseOrderReturnHEADDTO.setZterm(payment_iterm);
    purchaseOrderReturnHEADDTO.setWaers(supplierOrder.getMoneyCode());
    //增加申请人和工号
    purchaseOrderReturnHEADDTO.setZycgdh(supplierOrder.getCode());
    purchaseOrderReturnHEADDTO.setAfnam(user.getRealName());
    purchaseOrderReturnHEADDTO.setBednr(user.getCode());
    purchaseOrderReturnHEADDTO.setItem(itemdtoList);
    purchaseOrderReturnDATADTO.setHead(purchaseOrderReturnHEADDTO);
    mm075Param.setData(purchaseOrderReturnDATADTO);
    MM_075Result result = sapService.sapDirectReturnWithError(mm075Param);
    String productVoucher = result.getReturnMessages().get(0).getProductVoucher();
    supplierOrderForm.setProductVoucher(productVoucher);
    //sap退库单采购订单号
    supplierOrderForm.setSapReturnNumber(result.getReturnMessages().get(0).getEBELN());
    supplierOrderToFormRepository.save(supplierOrderForm);
    return productVoucher;
  }

  private SupplierOrderDetail saveSupplierOrderConsignmentDetail(
      SupplierOrderToForm supplierOrderForm, SupplierOrderDetail orderDetail,
      SupplierOrderDetail orderDetailBase, String purchaseOrderId) {
    SupplierOrderDetail supplierOrderDetail = new SupplierOrderDetail();
    supplierOrderDetail.setOrderToFormId(supplierOrderForm.getId());
    supplierOrderDetail.setState(Constants.STATE_OK);
    supplierOrderDetail.setCreateTime(System.currentTimeMillis());
    supplierOrderDetail.setOrderProductId(orderDetail.getOrderProductId());
    supplierOrderDetail.setSupplierOrderProduct(orderDetail.getSupplierOrderProduct());
    supplierOrderDetail.setSortNum(orderDetail.getSortNum());
    supplierOrderDetail.setDetailedId(orderDetailBase.getId());
    //退库数量取入库数量
    supplierOrderDetail.setStockOutputQty(orderDetail.getStockInputQty());
    supplierOrderDetail.setInWareHouseId(orderDetail.getOrderToFormId());
    supplierOrderDetail.setReturnQty(orderDetail.getStockInputQty());
    supplierOrderDetail.setBatchNo(orderDetail.getBatchNo());

    supplierOrderDetail.setTotalPrice(orderDetail.getPrice().multiply(orderDetail.getStockInputQty()));
    supplierOrderDetail.setPrice(orderDetailBase.getPrice());
    //退库单价
    supplierOrderDetail.setReturnPrice(orderDetailBase.getPrice());
    //本次退库金额=单价*本次退库数量
    supplierOrderDetail.setReturnAmount(BigDecimalUtil.setScaleBigDecimalHalfUp(
        NumberUtil.mul(orderDetail.getStockInputQty(), orderDetailBase.getPrice()), 2)
    );
    //退库单添加采购订单id
    supplierOrderDetail.setPurchaseOrderId(purchaseOrderId);
    supplierOrderDetail.setTaxRate(orderDetail.getTaxRate());
    supplierOrderDetail.setProductRate(orderDetail.getProductRate());
    supplierOrderDetailRepository.save(supplierOrderDetail);
    return supplierOrderDetail;

  }

  private SupplierOrderDetail saveSupplierOrderDetail(SupplierOrderToForm supplierOrderForm,
      ProductDetail productDetail, SupplierOrderDetail orderDetail,
      SupplierOrderDetail orderDetailBase,String purchaseOrderId,String needRedTicket,
      String orderType) {
    SupplierOrderDetail supplierOrderDetail = new SupplierOrderDetail();
    supplierOrderDetail.setTaxRate(orderDetail.getTaxRate());
    supplierOrderDetail.setProductRate(orderDetail.getProductRate());
    supplierOrderDetail.setOrderToFormId(supplierOrderForm.getId());
    supplierOrderDetail.setState(Constants.STATE_OK);
    supplierOrderDetail.setCreateTime(System.currentTimeMillis());
    supplierOrderDetail.setOrderProductId(orderDetail.getOrderProductId());
    supplierOrderDetail.setSupplierOrderProduct(orderDetail.getSupplierOrderProduct());
    // 明细id
    supplierOrderDetail.setSortNum(orderDetail.getSortNum());
    supplierOrderDetail.setDetailedId(orderDetailBase.getId());
    supplierOrderDetail.setStockOutputQty(productDetail.getReturnNum());
    supplierOrderDetail.setInWareHouseId(productDetail.getInWareHouseId());
    supplierOrderDetail.setInWareHouseName(productDetail.getInWareHouseName());
    supplierOrderDetail.setReturnQty(productDetail.getReturnNum());
    supplierOrderDetail.setBatchNo(
        StrUtil.equals(PurchaseOrderTypeEnum.INITIAL_PURCHASE.getKey(), orderType)
            ? productDetail.getBatchNumber() : orderDetail.getBatchNo());
    supplierOrderDetail.setStockOutputQty(productDetail.getReturnNum());
    BigDecimal subtract =
        orderDetail.getStockInputQty().subtract(NumberUtil.add(productDetail.getReturnNum(),
            orderDetail.getStockOutputQty()));
    if (NumberUtil.isLess(subtract,BigDecimal.ZERO)) {
      throw new CheckException("退库数量超过可退货数量");
    }
    supplierOrderDetail.setStockInputQty(subtract);
    supplierOrderDetail.setTotalPrice(orderDetail.getPrice().multiply(productDetail.getReturnNum()));
    supplierOrderDetail.setPrice(orderDetailBase.getPrice());
    //退库单价
    supplierOrderDetail.setReturnPrice(productDetail.getReturnPrice());
    //本次退库金额=行退库单价*本次退库数量
    supplierOrderDetail.setReturnAmount(
        productDetail.getReturnAmount() != null
            ? productDetail.getReturnAmount()
            : BigDecimalUtil.setScaleBigDecimalHalfUp(
                NumberUtil.mul(productDetail.getReturnNum(), productDetail.getReturnPrice()), 2)
    );
    //退库单添加采购订单id
    supplierOrderDetail.setPurchaseOrderId(purchaseOrderId);
    if (StrUtil.equals(needRedTicket, Constants.NEED_RED_TICKET)) {
      supplierOrderDetail.setOpenRedInvoice(true);
    }
    supplierOrderDetailRepository.save(supplierOrderDetail);
    return supplierOrderDetail;
  }

  @Override
  public List<PurchaseOrderReturnVO> purchaseOrderReturn(String id) {
    Assert.notEmpty(id);
    SupplierOrder supplierOrder =
        get(id, () -> CheckException.noFindException(SupplierOrder.class, id));
    return CollUtil.emptyIfNull(
            supplierOrderToFormService.getByTypeAndSupplierOrderId(
                SupplierOrderFormType.RETURN, supplierOrder.getId()))
        .stream()
        .map(
            supplierOrderToForm -> {
              PurchaseOrderReturnVO vo = new PurchaseOrderReturnVO();
              vo.setId(supplierOrderToForm.getId());
              vo.setTime(supplierOrderToForm.getTime());
              vo.setProductVoucher(supplierOrderToForm.getProductVoucher());
              vo.setProductVoucherYear(supplierOrderToForm.getProductVoucherYear());
              vo.setSapReversalNo(supplierOrderToForm.getSapReversalNo());
              vo.setReturnReason(supplierOrderToForm.getReturnReason());
              vo.setLogisticsCompany(supplierOrderToForm.getLogisticsCompany());
              vo.setLogisticsCode(supplierOrderToForm.getLogisticsCode());
              vo.setTrackNum(supplierOrderToForm.getTrackNum());
              // 设置退货的仓库执行状态
              vo.setExecutionStatus(supplierOrderToForm.getExecutionStatus());
              // 设置退货的仓库执行状态值
              Optional.ofNullable(SupplierOrderFormExecutionStatusEnum.fromKey(supplierOrderToForm.getExecutionStatus()))
                  .ifPresent(executionStatusEnum -> vo.setExecutionStatusValue(executionStatusEnum.getValue()));
              vo.setReturnWarehouse(supplierOrderToForm.getReturnWarehouse());
              vo.setConsignee(supplierOrderToForm.getConsignee());
              vo.setReceiveAddress(supplierOrderToForm.getReceiveAddress());
              //是否需要开红票
              vo.setNeedRedTicket(StrUtil.emptyToDefault(supplierOrderToForm.getNeedRedTicket(), Constants.STATE_NO));
              vo.setSapReturnNumber(StrUtil.emptyIfNull(supplierOrderToForm.getSapReturnNumber()));
              List<PurchaseOrderProductDetailedReturnVO> shipProductDTOList =
                  supplierOrderDetailService.getByOrderToFormId(supplierOrderToForm.getId())
                      .stream().map(supplierOrderDetail -> {
                        String orderDetailId = supplierOrderDetail.getId();
                        List<InputInvoiceOrderWithDetail> inputInvoiceOrderWithDetails =
                            shareInputInvoiceService.getOrderInvoiceRelationListByDetailIdsRef(
                                Collections.singletonList(orderDetailId));
                        List<PurchaseOrderInvoiceRelation> purchaseOrderInvoiceRelationList =
                            inputInvoiceOrderWithDetails.stream().map(
                                    item -> new PurchaseOrderInvoiceRelation(item.getInputInvoiceOrder()))
                                .collect(Collectors.toList());
                        PurchaseOrderProductDetailedReturnVO productDetailedReturnVO =
                            new PurchaseOrderProductDetailedReturnVO(supplierOrderDetail);
                        //关联发票号
                        productDetailedReturnVO.setPurchaseOrderInvoiceRelationList(purchaseOrderInvoiceRelationList);
                        return productDetailedReturnVO;
                      }).collect(Collectors.toList());
              vo.setReturnProductDTOList(shipProductDTOList);
              vo.setState(supplierOrderToForm.getStatus());
              return vo;
            })
        .collect(Collectors.toList());
  }

  @DefaultSearchScheme(searchType = Constants.SEARCH_TYPE_GET_PAGE_SUPPLIER_ORDER_PAGE)
  @Override
  public PageResult<PurchaseOrderProductListDTO> getPagePurchaseOrderProductPageRef(
      PurchaseOrderProductSearchForm form) {
    User user = manageSecurityUtil.getSrmUserDetails().getUser();
    String currUser = getCurrUser(user);
    SearchPermission searchPermission =
        sharePermissionTypeService.getSearchPermission(user, form.getUserGroup(),
            Constants.USER_PERMISSION_SUPPLIER_ORDER, false, false, false,form.getIsViewAllOrganization());
    MergeUserPermission mergeUserPermission =
        sharePermissionTypeService.mergePermission(searchPermission, new OperatorPermission());
    Page<SupplierOrderDetail> page =
        supplierOrderDetailDao.findPurchaseOrderDetailPageRef(form.toQueryMap(mergeUserPermission));
    List<SupplierOrderDetail> content = page.getContent();
    if (CollUtil.isEmpty(content)) {
      return PageResultBuilder.buildPageResult(page, Collections.emptyList());
    }
    // 1.获取orderToFormId
    List<String> orderToFormIds =
        content.stream().map(SupplierOrderDetail::getOrderToFormId).distinct()
            .collect(Collectors.toList());
    orderToFormIds.add("-1");
    // 1.1 批量获取SupplierOrderToForm
    Map<String, SupplierOrderToForm> id2SupplierOrderToForm =
        supplierOrderToFormRepository.findAllById(orderToFormIds).stream()
            .collect(Collectors.toMap(SupplierOrderToForm::getId, Function.identity()));
    // 2.获取supplierOrderId
    List<String> supplierOrderIds =
        id2SupplierOrderToForm.values().stream().map(SupplierOrderToForm::getSupplierOrderId).distinct()
            .collect(Collectors.toList());
    supplierOrderIds.add("-1");
    Map<String, SupplierOrder> id2SupplierOrder =
        supplierOrderRepository.findAllById(supplierOrderIds).stream()
            .collect(Collectors.toMap(SupplierOrder::getId, Function.identity()));
    // 3.sap查询缓存
    Map<String,Map<String,String>> groupCodeMapSapStockAddr = new ConcurrentHashMap<>();
    // 4.采购申请
    List<String> applyIds = content.stream().map(SupplierOrderDetail::getPurchaseApplyForOrderId)
        .filter(StrUtil::isNotBlank).distinct().collect(Collectors.toList());
    Map<String, PurchaseApplyForOrder> id2PurchaseApplyForOrder =
        purchaseApplyForOrderRepository.findAllById(applyIds).stream()
            .collect(Collectors.toMap(PurchaseApplyForOrder::getId, Function.identity()));
    // 5.是否存在冲销
    Map<String, Boolean> id2ExistReversal =
        supplierOrderToFormService.getExistReversalBatch(supplierOrderIds);
    // 6.supplierOrderProduct --- supplierOrderDetail带有supplierOrderProduct
    //  todo 去除supplierOrderDetail的supplierOrderProduct
//    List<String> productIds = content.stream().map(SupplierOrderDetail::getOrderProductId)
//        .distinct().collect(Collectors.toList());
//    productIds.add("-1");
//    Map<String, SupplierOrderProduct> id2SupplierProduct =
//        supplierOrderProductRepository.findAllById(productIds).stream()
//            .collect(Collectors.toMap(SupplierOrderProduct::getId, Function.identity()));

    // 拼接数据
    // todo 拼接待优化的数据
    List<List<PurchaseOrderProductListDTO>> lists =
        ParallelProcessUtil.simpleParallelProcessOfCollection(page.getContent(),
            ioIntensiveThreadPool, supplierOrderDetail -> {
              SupplierOrderProduct supplierOrderProduct =
                  Optional.ofNullable(supplierOrderDetail.getSupplierOrderProduct()).orElseThrow(
                      () -> new CheckException(
                          "当前订单明细" + supplierOrderDetail.getId() + "的物料信息查询不到！"));
              SupplierOrderToForm supplierOrderToForm =
                  id2SupplierOrderToForm.get(supplierOrderDetail.getOrderToFormId());
              String supplierOrderId = Optional.ofNullable(supplierOrderToForm)
                  .map(SupplierOrderToForm::getSupplierOrderId).orElseThrow(
                      () -> new CheckException(
                          "当前订单明细" + supplierOrderDetail.getId() + "的form表单查询不到！"));
              SupplierOrder supplierOrder = id2SupplierOrder.get(supplierOrderId);
              Optional.ofNullable(supplierOrderDao.get(supplierOrderId)).orElseThrow(
                  () -> new CheckException(
                      "当前订单明细" + supplierOrderDetail.getId() + "的采购订单查询不到！"));
              PurchaseOrderProductListDTO dto =
                  new PurchaseOrderProductListDTO(supplierOrder, supplierOrderProduct,
                      supplierOrderDetail);
              Boolean writeOff =
                  Optional.ofNullable(id2ExistReversal.get(supplierOrderId)).orElse(false);
              dto.setWriteOffState(writeOff);
              if (SupplierOrderState.STAGING.getOrderState().equals(supplierOrder.getOrderState())
                  || SupplierOrderState.REJECT.getOrderState()
                  .equals(supplierOrder.getOrderState())) {
                dto.setDeleteStorage(("admin".equals(user.getName()) || currUser
                    .equals(supplierOrder.getPurchaseMan())));
              } else {
                dto.setDeleteStorage(false);
              }
              if (StrUtil.isNotEmpty(supplierOrderDetail.getPurchaseApplyForOrderId())) {
                PurchaseApplyForOrder purchaseApplyForOrder =
                    id2PurchaseApplyForOrder.get(supplierOrderDetail.getPurchaseApplyForOrderId());
                if (purchaseApplyForOrder != null) {
                  //采购申请单号
                  dto.setPurchaseApplyCode(purchaseApplyForOrder.getApplyForOrderNo());
                  // 7.1.2数据处理后，统一product存储
                  //业务员
//                  dto.setSalesman(purchaseApplyForOrder.getSalesman());
                  //跟单员
//                  dto.setFollowUpPersonName(purchaseApplyForOrder.getFollowUpPersonName());
                  dto.setApplyForType(purchaseApplyForOrder.getApplyForType());
//                  dto.setBusinessCompanyName(purchaseApplyForOrder.getBusinessCompanyName());
//                  dto.setMakeManName(purchaseApplyForOrder.getMakeManName());
                  dto.setIsWorryOrder(purchaseApplyForOrder.getIsWorryOrder());
//                  dto.setSoldToParty(purchaseApplyForOrder.getSoldToParty());
                }
              }
              //todo 优化是否预付款
              dto.setPrePay(supplierOrderDao.getPrePay(supplierOrder.getCode()));
              // todo 优化fileList
              List<File> fileList = fileService.getFileListByIdAndType(supplierOrder.getId(),
                  Constants_FileRelationType.ORDER_CONTRACT);
              //是否上传合同
              dto.setUploadContract(CollUtil.isNotEmpty(fileList) ? true : false);
              //合同文件
              dto.setContractFiles(buildContractFiles(fileList, uploadConfig.getUploadPath()));
              String groupCode = supplierOrder.getGroupCode();
              if (StrUtil.isNotBlank(groupCode)) {
                Map<String, String> stockAddr = groupCodeMapSapStockAddr.get(groupCode);
                if (stockAddr == null) {
                  Map<String, String> sapStockAddr = MapUtil.emptyIfNull(
                      CollUtil.emptyIfNull(getSAPStockAddr(null, null, null, groupCode)).stream()
                          .collect(
                              Collectors.toMap(NameAndCodeDTO::getCode, NameAndCodeDTO::getName,
                                  (v1, v2) -> v1)));
                  groupCodeMapSapStockAddr.put(groupCode, sapStockAddr);
                  dto.setWarehouse(sapStockAddr.getOrDefault(
                      StrUtil.emptyIfNull(supplierOrderDetail.getWarehouse()), StrUtil.EMPTY));
                } else {
                  dto.setWarehouse(stockAddr.getOrDefault(
                      StrUtil.emptyIfNull(supplierOrderDetail.getWarehouse()), StrUtil.EMPTY));
                }
              }
              // todo 优化是否未签收
              dto.setUnReceipt(supplierOrderToFormService.countByFormTypeAndFormStatus(
                  supplierOrderDetail.getId(), SupplierOrderFormType.DELIVER,
                  SupplierOrderFormStatus.WAIT_RECEIPT) > 0);
              //自采单关联的销售订单的信息
              // todo 7.1.2需要优化的逻辑
              dto.setSalesman(supplierOrderProduct.getSalesman());
              dto.setFollowUpPersonName(supplierOrderProduct.getFollowUpPersonName());
              dto.setBusinessCompanyName(supplierOrderProduct.getBusinessCompanyName());
              dto.setMakeManName(supplierOrderProduct.getMakeManName());
              dto.setSoldToParty(supplierOrderProduct.getSoldToParty());
              return dto;
            });
    List<PurchaseOrderProductListDTO> purchaseOrderProductListDTOS =
        ParallelProcessUtil.flattenTwoDimensionalList(lists);
    return PageResultBuilder.buildPageResult(page, purchaseOrderProductListDTOS);
  }

  /**
   * 仓库校验
   * 新增标准采购订单时，只能选择成品库、直销库;新增寄售采购订单时，只能选择样品库、直销库;
   */
  private void checkWarehouse(PuchaseOrderAddParams params) {
    if (Objects.equals(params.getOrderType(), PurchaseOrderTypeEnum.SAP.getKey())) {
      params.getProductList().stream().forEach(item -> {
        String warehouse = item.getWarehouse();
        boolean isFinishedProducts =
            Objects.equals(WarehouseEnum.FINISHED_PRODUCTS.getCode(), warehouse);
        boolean isHaiNingDirectSales =
                Objects.equals(WarehouseEnum.HAI_NING_DIRECT_SALES.getCode(), warehouse);
        boolean haiNingFinishedProducts =
                Objects.equals(WarehouseEnum.HAI_NING_FINISHED_PRODUCTS.getCode(), warehouse);
        if (!isFinishedProducts && !isHaiNingDirectSales && !haiNingFinishedProducts) {
          throw new CheckException("订单类型与所选仓库不匹配，请修改！");
        }
      });
    }
    if (Objects.equals(params.getOrderType(), PurchaseOrderTypeEnum.CONSIGNMENT.getKey())) {
      params.getProductList().stream().forEach(item -> {
        String warehouse = item.getWarehouse();
        boolean isSampleShippingCode =
                Objects.equals(WarehouseEnum.SAMPLE_SHIPMENT_CODE.getCode(), warehouse);
        boolean isHaiNingDirectSales =
                Objects.equals(WarehouseEnum.HAI_NING_DIRECT_SALES.getCode(), warehouse);
        if (!isSampleShippingCode && !isHaiNingDirectSales) {
          throw new CheckException("订单类型与所选仓库不匹配，请修改！");
        }
      });
    }
  }

  /**
   * 校验除免费行外，税率一致性
   * @param params
   */
  private void checkRateWithoutFreeLine(PuchaseOrderAddParams params) {
    List<supplierProduct> productList = params.getProductList();
    if (CollUtil.isEmpty(productList)) {
      return;
    }
    // 过滤掉免费行
    productList = productList.stream()
        .filter(item -> !Constants.YES.equals(item.getFreeState()))
        .collect(Collectors.toList());
    BigDecimal orginRate = null;
    // 校验税率一致性
    for (supplierProduct product : productList) {
      BigDecimal taxRate = product.getTaxRate()  == null ? BigDecimal.ZERO : product.getTaxRate();
      if (orginRate == null) {
        orginRate = taxRate;
      }
      if (taxRate.compareTo(orginRate) != 0) {
        throw new CheckException("订单物料行的税率必须一致，请检查订单税率");
      }
    }
  }

  private void checkAddOrUpdatePurchaseParams(PuchaseOrderAddParams params) {
    String supplierId = params.getSupplierId();
    Supplier supplier = null;
    if (StrUtil.isNotBlank(supplierId)) {
      supplier = Optional.ofNullable(supplierService.get(supplierId))
          .orElseThrow(() -> new CheckException("供应商信息不存在"));
    }
    // 非自采、赠品、寄售、外海供应商(海外订单) && 未勾选亏本订单
    if ((BooleanUtil.isTrue(params.getSelfState()) || BooleanUtil.isTrue(params.getFreeState())
        || PurchaseOrderTypeEnum.CONSIGNMENT.getKey().equals(params.getOrderType())
        || Constants.SUPPLIERTYPE_ABROAD.equals(supplier.getSupType()) || StrUtil.equals(
        PurchaseOrderTypeEnum.OVER_SEAS.getKey(),params.getOrderType()))) {
      return;
    }
    if (!BooleanUtil.isTrue(params.getLoss())) {
      List<String> rowError = new ArrayList<>();
      for (int i = 0; i < params.getProductList().size(); i++) {
        supplierProduct supplierProduct = params.getProductList().get(i);
        // 亏本：(结算单价/(1+物料税率))<=去税单价 去税单价 = 含税单价/（1+订单税率） 原币=含税单价
        // 去税单价
        BigDecimal unitPriceExcludingTax =
            NumberUtil.div(supplierProduct.getPrice(),
                NumberUtil.add(BigDecimal.ONE, NumberUtil.null2Zero(supplierProduct.getTaxRate())));
        if (NumberUtil.isLessOrEqual(NumberUtil.div(NumberUtil.null2Zero(supplierProduct.getSettlementPrice()),
                NumberUtil.add(BigDecimal.ONE, NumberUtil.null2Zero(supplierProduct.getProductRate()))),
            unitPriceExcludingTax)) {
          rowError.add((i + 1) + "行");
        }
        // 项目类别与是否免费校验
        if (ObjectUtil.equals(Constants.PROJECT_TYPE_JS, supplierProduct.getProjectType())
            && !ObjectUtil.equals(SimpleBooleanEnum.NO.getKey(), supplierProduct.getFreeState())) {
          throw new CheckException("项目类别为“寄售”时，是否免费只能是“否”");
        }
      }
      if (CollUtil.isNotEmpty(rowError)) {
        throw new CheckException(CollUtil.join(rowError, "、")
            + "，去税结算单价小于去税单价，无法提交。如需做亏本订单请勾选亏本订单");
      }
    }
  }

  private void checkUpdatePurchaseParams(PuchaseOrderAddParams params, SupplierOrder supplierOrder) {
    boolean firstSubmitFlag = supplierOrderFactory.checkSupplierOrderFirstSubmitFlag(params.getSaveType(),
        supplierOrder.getOrderState());
    // 提交时校验且非暂存订单
    if (firstSubmitFlag) {
      // 首次提交校验
      supplierOrderFactory.checkSupplierOrderFirstSubmit(supplierOrder.getId(),
          CollUtil.emptyIfNull(params.getProductList()).size(),
          params.getSupplierName(),
          params.getSupplierId(),
          supplierOrder.getSupplierName(),
          supplierOrder.getSupplierId());
    }
  }

  @Override
  @Transactional(rollbackFor = Exception.class)
  public String addPurchaseOrder(PuchaseOrderAddParams params) {
    if(StrUtil.isEmpty(params.getSupplierId())){
      throw new CheckException("供应商不能为空！");
    }
    // #check 自采订单的是否走scp限定为否
    supplierOrderFactory.checkSelfScp(params);
    checkWarehouse(params);
    // 销售订单号
    Set<String> saleOrderNo = new HashSet<>();
    // 客户订单号
    Set<String> customerOrderCode = new HashSet<>();
    // 项目名称
    Set<String> projectName = new HashSet<>();
    // 项目编号
    Set<String> projectNo = new HashSet<>();
    // 售达方
    Set<String> soldToPartySet = new HashSet<>();
    // 业务员
    Set<String> salesmanSet = new HashSet<>();
    // 检验除了免费单外的物料税率和税率一致性
    checkRateWithoutFreeLine(params);
    // 检验采购订单付款条件
    purchaseOrderPaymentTermsService.checkPurchaseOrderPaymentTerms(params.getPaymentTerms());
    SupplierOrder supplierOrder = new SupplierOrder();
    if (StrUtil.isNotEmpty(params.getId())) {
      // 供应商订单
      supplierOrder = supplierOrderRepository.findById(params.getId()).orElse(null);
      if (supplierOrder == null) {
        throw new CheckException("订单不存在！");
      }
      // 如果有取消订单不可进行编辑
      if (supplierOrderToFormService.countByTypeAndSupplierOrderIdAndState(
          SupplierOrderFormType.CANCEL, supplierOrder.getId())>0) {
        throw new CheckException("该订单存在取消单无法进行编辑");
      }
      checkUpdatePurchaseParams(params, supplierOrder);
      //删除订单信息
      supplierOrderFactory.delSupplierOrderInfo(supplierOrder.getId());
      supplierOrder.setUpdateMan(params.getUserId());
      supplierOrder.setUpdateTime(System.currentTimeMillis());
      // 获取销售订单号
      String salesOrderNo = StrUtil.join(",", CollUtil.emptyIfNull(params.getProductList()).stream()
          .map(supplierProduct -> StrUtil.subPre(supplierProduct.getSalesOrderNo(),10)).collect(Collectors.toList()));
      // 获取项目编号
      String projectNoJoin = StrUtil.join(",",
          CollUtil.emptyIfNull(params.getProductList()).stream()
              .map(supplierProduct::getProjectNo).collect(Collectors.toList()));
      String soldToParty = StrUtil.join(",",
          CollUtil.emptyIfNull(params.getProductList()).stream()
              .map(supplierProduct::getSoldToParty).collect(Collectors.toList()));
      String salesman = StrUtil.join(",",
          CollUtil.emptyIfNull(params.getProductList()).stream()
              .map(supplierProduct::getSalesman).collect(Collectors.toList()));
      supplierOrder.setSoldToParty(soldToParty);
      supplierOrder.setSalesman(salesman);
      supplierOrder.setSaleOrderNo(salesOrderNo);
      supplierOrder.setProjectNo(projectNoJoin);
      // 修改后的订单金额不能小于已预付金额
      List<PaymentApplyRecord> applyRecordList = new ArrayList<>();
      // 获取审核中预付款申请记录
      List<PaymentApplyRecord> examineList =
          paymentApplyRecordDao.getListByParam(supplierOrder.getCode(),
              Constants.PAYMENT_TYPE_ADVANCE, Constants.PAYMENT_APPLY_EXAMINE);
      // 获取审核通过的预付款申请记录
      List<PaymentApplyRecord> passList =
          paymentApplyRecordDao.getListByParam(supplierOrder.getCode(),
              Constants.PAYMENT_TYPE_ADVANCE, Constants.PAYMENT_APPLY_PASS);
      // 获取审核驳回预付款申请记录
      List<PaymentApplyRecord> unPassList =
          paymentApplyRecordDao.getListByParam(supplierOrder.getCode(),
              Constants.PAYMENT_TYPE_ADVANCE, Constants.PAYMENT_APPLY_UNPASS);
      List<PaymentApplyRecord> advanceReversal =
          paymentApplyRecordDao.getListByParam(supplierOrder.getCode(),
              PaymentApplyTypeEnums.ADVANCE_REVERSAL.getKey(), Constants.PAYMENT_APPLY_UNPASS);
      applyRecordList.addAll(examineList);
      applyRecordList.addAll(passList);
      applyRecordList.addAll(unPassList);
      List<String> ids =
          applyRecordList.stream().map(PaymentApplyRecord::getId).collect(Collectors.toList());
      List<PaymentApplyDetail> detailList =
          paymentApplyDetailRepository.findByPaymentApplyRecordIdIn(ids);
      BigDecimal paymentApplyPrice =
          detailList.stream().map(PaymentApplyDetail::getApplyAdvancePrice)
              .reduce(BigDecimal.ZERO, BigDecimal::add);
      List<String> advanceReversalIds =
          applyRecordList.stream().map(PaymentApplyRecord::getId).collect(Collectors.toList());
      BigDecimal advanceReversalPrice =
          CollUtil.emptyIfNull(paymentApplyDetailRepository.findByPaymentApplyRecordIdIn(advanceReversalIds)).stream().map(PaymentApplyDetail::getApplyAdvancePrice)
              .reduce(BigDecimal.ZERO, BigDecimal::add);
      //实际预付金额
      BigDecimal actualPrepaidAmount = getActualPrepaidAmount(supplierOrder.getCode());
      List<FinancialVoucher> financialVouchers =
          financialVoucherRepository.findAllByPurchaseOrderNoAndState(supplierOrder.getCode(),
              Constants.STATE_OK);
      AtomicReference<BigDecimal> refundAmount = new AtomicReference<>(BigDecimal.ZERO);
      CollUtil.emptyIfNull(financialVouchers).forEach(financialVoucher -> {
        if (VoucherTypeEnum.ADVANCE_CHARGE.getKey().equals(financialVoucher.getVoucherType())) {
          refundAmount.set(NumberUtil.add(refundAmount.get(), financialVoucher.getRefundAmount()));
        }
      });
      // 修改后的订单金额不能小于已预付金额
      if (NumberUtil.isGreater(actualPrepaidAmount, params.getTotalAmountIncludingTax())) {
        throw new CheckException("订单已预付"+actualPrepaidAmount.stripTrailingZeros().toPlainString()+
            "元，修改后的订货金额不能小于已预付金额!");
      }
    } else {
      // 生成唯一订单号
      supplierOrder.setCode(OrderNumUtil.getInstance().getSeq());
      supplierOrder.setOrderCreateTime(System.currentTimeMillis());
      supplierOrder.setCreateTime(System.currentTimeMillis());
      supplierOrder.setCreateMan(params.getUserId());
    }
    checkAddOrUpdatePurchaseParams(params);
    //保存订单信息
    SupplierOrder supplierOrderAdd = savaSupplierOrder(params, supplierOrder);
    //保存附件
    fileDao.deleteByRelationIdAndRelationType(supplierOrder.getId(),Constants_FileRelationType.ORDER_ANNEX);
    int max_annex_num = 20;
    int count = fileService.countNumByRelationIdAndRelationType(supplierOrder.getId(),
        Constants_FileRelationType.ORDER_ANNEX);
    if (count > max_annex_num) {
      throw new CheckException("超出订单最大附件数量");
    }
    List<AnnexParam> fileList = params.getFileList();
    for (AnnexParam annexParam : fileList) {
      File file = annexParam.buildFile(supplierOrder.getId());
      file.setRelationType(Constants_FileRelationType.ORDER_ANNEX);
      fileService.save(file);
    }
    //采购合同
    List<AnnexParam> fileContract = params.getFileContract();
    if(CollUtil.isNotEmpty(fileContract)){
      for (AnnexParam annexParam : fileContract) {
        File file = new File();
        file.setUrl(annexParam.getNewFilePath());
        file.setState(Constants.STATE_OK);
        file.setDescription(annexParam.getFilename());
        file.setName(annexParam.getFilename());
        file.setRelationType(Constants_FileRelationType.ORDER_CONTRACT);
        file.setRelationId(supplierOrder.getId());
        file.setCreateTime(System.currentTimeMillis());
        fileService.save(file);
      }
    }
    List<String> delDetailIdList = params.getDelDetailIdList();
    if(CollUtil.isNotEmpty(delDetailIdList)){
      for (String s : delDetailIdList) {
        SupplierOrderDetail supplierOrderDetail =
            supplierOrderDetailRepository.findById(s).orElse(null);
        if(supplierOrderDetail!=null && StrUtil.isNotEmpty(supplierOrderDetail.getPurchaseApplyForOrderId())
            &&Constants.STATE_OK.equals(supplierOrderDetail.getState())){
          PurchaseApplyForOrder purchaseApplyForOrder =
              purchaseApplyForOrderRepository
                  .findById(supplierOrderDetail.getPurchaseApplyForOrderId())
                  .orElse(null);
          if(purchaseApplyForOrder!=null){
            purchaseApplyForOrder.setOrderGoodsNumber(purchaseApplyForOrder.getOrderGoodsNumber().subtract(supplierOrderDetail.getNum()));
            if(NumberUtil.isLess(purchaseApplyForOrder.getOrderGoodsNumber(),
                purchaseApplyForOrder.getApplyForNumber())){
              purchaseApplyForOrder.setOrderGoodsState(SimpleBooleanEnum.YES.getKey());
            }
            purchaseApplyForOrderRepository.save(purchaseApplyForOrder);
          }
          supplierOrderDetail.setState(Constants.STATE_DELETE);
          supplierOrderDetailRepository.save(supplierOrderDetail);
        }
      }
    }
    List<ITEMDTO> itemdtos = new ArrayList<>();
    // 物料信息
    List<supplierProduct> productList = params.getProductList();
    if (CollUtil.isNotEmpty(productList)) {
      SupplierOrderToForm supplierOrderToForm = new SupplierOrderToForm();
      supplierOrderToForm.setSupplierOrderId(supplierOrderAdd.getId());
      supplierOrderToForm.setType(SupplierOrderFormType.DETAILED.getType());
      supplierOrderToForm.setCreateTime(System.currentTimeMillis());
      supplierOrderToForm.setState(Constants.STATE_OK);
      supplierOrderToForm.setNum(new BigDecimal("0"));
      SupplierOrderToForm orderToForm = supplierOrderToFormRepository.save(supplierOrderToForm);
      for (supplierProduct supplierProduct : productList) {
        // 物料
        SupplierOrderProduct supplierOrderProduct = saveProduct(supplierProduct);
        supplierOrderProductRepository.save(supplierOrderProduct);
        // 订单明细
        SupplierOrderDetail supplierOrderDetail =
            buildSupplierOrderDetail(orderToForm, supplierProduct, supplierOrderProduct,
                params.getSelfState(),params.getOrderType());
        // 判断订货状态
        PurchaseApplyForOrder purchaseApplyForOrder = new PurchaseApplyForOrder();
        if (StrUtil.isNotEmpty(supplierProduct.getPurchaseApplyForOrderId())) {
          supplierOrderDetail.setPurchaseApplyForOrderId(
              supplierProduct.getPurchaseApplyForOrderId());
          purchaseApplyForOrder =
              purchaseApplyForOrderRepository
                  .findById(supplierProduct.getPurchaseApplyForOrderId())
                  .orElse(null);
          if (purchaseApplyForOrder != null) {
            supplierOrderDetail.setSapRowId(purchaseApplyForOrder.getRowId());
            supplierOrderDetail.setSalesOrderNo(purchaseApplyForOrder.getSaleOrderNo() + "-" + purchaseApplyForOrder.getSaleOrderProductRowId());
            if(StrUtil.isNotEmpty(purchaseApplyForOrder.getSaleOrderNo())){
              saleOrderNo.add(StrUtil.subPre(purchaseApplyForOrder.getSaleOrderNo(),10));
            }
            if(StrUtil.isNotEmpty(purchaseApplyForOrder.getCustomerOrderNumber())){
              customerOrderCode.add(purchaseApplyForOrder.getCustomerOrderNumber());
            }
            if(StrUtil.isNotEmpty(purchaseApplyForOrder.getProjectNo())){
              supplierOrderDetail.setProjectNo(purchaseApplyForOrder.getProjectNo());
              projectNo.add(purchaseApplyForOrder.getProjectNo());
            }
            if(StrUtil.isNotEmpty(purchaseApplyForOrder.getProjectName())){
              supplierOrderDetail.setProjectName(purchaseApplyForOrder.getProjectName());
              projectName.add(purchaseApplyForOrder.getProjectName());
            }
            if (StrUtil.isNotBlank(purchaseApplyForOrder.getSoldToParty())) {
              soldToPartySet.add(purchaseApplyForOrder.getSoldToParty());
            }
            if (StrUtil.isNotBlank(purchaseApplyForOrder.getSalesman())) {
              salesmanSet.add(purchaseApplyForOrder.getSalesman());
            }
          }
          if (purchaseApplyForOrder != null && supplierProduct.getNum() != null) {
            // 计算订单数量  采购数量
            BigDecimal currentOrderGoodsNumber = Optional.ofNullable(purchaseApplyForOrder.getOrderGoodsNumber()).orElse(BigDecimal.ZERO);
            BigDecimal supplierProductNum = supplierProduct.getNum();
            BigDecimal totalOrderGoods = supplierProductNum.add(currentOrderGoodsNumber);
            BigDecimal applyForNumber = purchaseApplyForOrder.getApplyForNumber();
            Boolean isConsignmentToOwned = PurchaseOrderTypeEnum.CONSIGNMENT_TO_OWNED.getKey().equals(params.getOrderType());
            // 非寄售转自有单子才进行校验
            if (!isConsignmentToOwned) {
              // 2024年10月23日15:02:07 关联借出申请的采购订单提交时增加校验 - 订单仓库是否与借出申请单上物料行的仓库一致
              // 2024年11月5日10:32:16 寄售转自有不需要校验
              if (PurchaseApplicationTypeEnum.LEND.getCode().equals(purchaseApplyForOrder.getApplyForType())) {
                String purchaseApplyForOrderWarehouse = purchaseApplyForOrder.getWarehouse();
                String warehouse = supplierProduct.getWarehouse();
                if (!StrUtil.equals(purchaseApplyForOrderWarehouse, warehouse)) {
                  throw new CheckException("您关联的采购申请仓库与订单仓库不一致，请修改");
                }
              }
              if (supplierProductNum.compareTo(applyForNumber) > 0) {
                throw new CheckException(supplierProduct.getProductName() + " 的订货数量大于采购申请的可订货数量或申请数量，请检查");
              }
              if (totalOrderGoods.compareTo(applyForNumber) > 0) {
                throw new CheckException(supplierProduct.getProductName() + " 的订货数量大于可订货数量，请检查");
              }
              if (totalOrderGoods.compareTo(applyForNumber) == 0) {
                purchaseApplyForOrder.setOrderGoodsState(SimpleBooleanEnum.NO.getKey());
              }
              if (totalOrderGoods.compareTo(applyForNumber) < 0) {
                purchaseApplyForOrder.setOrderGoodsState(SimpleBooleanEnum.YES.getKey());
              }
              purchaseApplyForOrder.setOrderGoodsNumber(
                  purchaseApplyForOrder.getOrderGoodsNumber() == null
                      ? supplierProduct.getNum()
                      : supplierProduct
                          .getNum()
                          .add(purchaseApplyForOrder.getOrderGoodsNumber()));
              purchaseApplyForOrder.setPushDownState(Constants.STATE_OK);
              purchaseApplyForOrderRepository.save(purchaseApplyForOrder);
            }
          }
        }else{
          if(StrUtil.isNotEmpty(supplierProduct.getSalesOrderNo())){
            saleOrderNo.add(StrUtil.subPre(supplierProduct.getSalesOrderNo(),10));
          }
          if(StrUtil.isNotEmpty(supplierProduct.getProjectNo())){
            projectNo.add(supplierProduct.getProjectNo());
          }
        }
        SupplierOrderDetail orderDetail = supplierOrderDetailRepository.save(supplierOrderDetail);
        ITEMDTO itemdto = new ITEMDTO();
        itemdto.setCharX(StrUtil.EMPTY);
        itemdto.setElikz(StrUtil.EMPTY);
        itemdto.setKnttp(StrUtil.EMPTY);
        itemdto.setLoekz(StrUtil.EMPTY);
        itemdto.setMatkl(StrUtil.EMPTY);
        itemdto.setRetpo(StrUtil.EMPTY);
        itemdto.setZZPOSNR(StrUtil.EMPTY);
        itemdto.setZZVBELN(StrUtil.EMPTY);
        itemdto.setZgsje(StrUtil.EMPTY);
        itemdto.setZyyje(StrUtil.EMPTY);
        itemdto.setZzf1(StrUtil.EMPTY);
        if(params.getSaveType() == Constants.SAP_INVOICE_SUBMIT){
          if (StrUtil.isNotBlank(supplierProduct.getSalesOrderNo())) {
            String[] split = StrUtil.split(supplierProduct.getSalesOrderNo(), "-");
            if (split.length == 2) {
              itemdto.setZZPOSNR(split[1]);
              itemdto.setZZVBELN(split[0]);
            }
          }
          itemdto.setEbelp(orderDetail.getSortNum().toString());
          itemdto.setMatnr(supplierOrderProduct.getCode());
          itemdto.setTxz01(supplierOrderProduct.getName());
          itemdto.setMeins(supplierOrderProduct.getUnitCode());
          itemdto.setMenge(orderDetail.getNum().toPlainString());
          itemdto.setWerks(supplierOrder.getGroupCode());
          itemdto.setLgort(orderDetail.getWarehouse());
          itemdto.setAplfz(DateUtils.formatTimeStampToStr(orderDetail.getPurchaseDeliverTime(),
              DatePattern.PURE_DATE_PATTERN));
          itemdto.setMwskz(Constants.TAX_RATE_TYPE_NUM.get(orderDetail.getTaxRate()));
          BigDecimal price = orderDetail.getPrice();
          Pair<BigDecimal, Integer> convertSapPrice = SAPToolUtils.convertSapPrice(price, 2);
          /*// 此行价税合计 保留两位小数
          itemdto.setNetpr(NumberUtil.null2Zero(orderDetail.getTotalAmountIncludingTax())
              .setScale(2,RoundingMode.HALF_UP).toPlainString());
          // 此行订货数量
          itemdto.setPeinh(NumberUtil.null2Zero(orderDetail.getNum())
              .setScale(3,RoundingMode.HALF_UP).toPlainString());*/
          itemdto.setNetpr(convertSapPrice.getKey().toPlainString());
          itemdto.setPeinh(convertSapPrice.getValue().toString());
          itemdto.setBprme(supplierOrderProduct.getUnitCode());
          if(supplierProduct.getTariffAmount() !=null){
            itemdto.setZgsje(supplierProduct.getTariffAmount().toPlainString());
          }
          if(supplierProduct.getFreight() !=null){
            itemdto.setZyyje(supplierProduct.getFreight().toString());
          }
          if(supplierProduct.getIncidentalAmount() !=null){
            itemdto.setZzf1(supplierProduct.getIncidentalAmount().toString());
          }
          itemdto.setZjsj(BooleanUtil.isTrue(supplierOrder.getSelfState())?"0":orderDetail.getSettlementPrice().toPlainString());
          //寄售转自有订单传对应批号
          if (StrUtil.equals(params.getOrderType(),
              PurchaseOrderTypeEnum.CONSIGNMENT_TO_OWNED.getKey())) {
            itemdto.setCharX(supplierProduct.getBatchNo());
          }
          itemdto.setZmfbs(StrUtil.equals(orderDetail.getFreeState(),SimpleBooleanEnum.YES.getKey()) ?
              Constants_Sap.CONFIRM_IDENTIFICATION : "");
          itemdto.setPstyp(orderDetail.getProjectType());
          itemdto.setKostl("");
          itemdto.setBanfn(purchaseApplyForOrder.getApplyForOrderNo());
          itemdto.setBnfpo(purchaseApplyForOrder.getRowId());
          //关税供应商和运费供应商没值的时候传空
          itemdto.setYfgys(StrUtil.EMPTY);
          itemdto.setGsgys(StrUtil.EMPTY);
          itemdto.setZfgys(StrUtil.EMPTY);
          String freightSupplierId = orderDetail.getFreightSupplierId();
          if (StrUtil.isNotBlank(freightSupplierId)) {
            Optional.ofNullable(supplierService.get(freightSupplierId)).ifPresent(
                supplierTemp -> itemdto.setYfgys(supplierTemp.getMdmCode())
            );
          }
          String tariffSupplierId = orderDetail.getTariffSupplierId();
          if (StrUtil.isNotBlank(tariffSupplierId)) {
            Optional.ofNullable(supplierService.get(tariffSupplierId)).ifPresent(
                supplierTemp -> itemdto.setGsgys(supplierTemp.getMdmCode())
            );
          }
          //6.8.2杂费供应商
          String incidentalSupplierId = orderDetail.getIncidentalSupplierId();
          if (StrUtil.isNotBlank(incidentalSupplierId)) {
            Optional.ofNullable(supplierService.get(incidentalSupplierId)).ifPresent(
                supplierTemp -> itemdto.setZfgys(supplierTemp.getMdmCode())
            );
          }
          itemdto.setZzxmbh_01(String.join(",", projectNo));
          itemdto.setZzfjf(NumberUtil.null2Zero(supplierProduct.getSurcharge()).toPlainString());
          itemdto.setZzwlsl(NumberUtil.null2Zero(supplierProduct.getProductRate()).toPlainString());

          itemdtos.add(itemdto);
        }
        List<WWDTO> wwdtos = new ArrayList<>();
        if (CollUtil.isNotEmpty(supplierProduct.getEntrustProductList())) {
          for (EntrustProduct entrustProduct : supplierProduct.getEntrustProductList()) {
            SupplierOrderProduct supplierOrderProductEntrust = saveProductEntrust(entrustProduct);
            SupplierOrderProduct productChile =
                supplierOrderProductRepository.save(supplierOrderProductEntrust);
            SupplierOrderDetail supplierOrderDetailChild = new SupplierOrderDetail();
            supplierOrderDetailChild.setOrderProductId(productChile.getId());
            supplierOrderDetailChild.setState(Constants.STATE_OK);
            supplierOrderDetailChild.setSupplierOrderProduct(productChile);
            supplierOrderDetailChild.setDescription(entrustProduct.getDescription());
            supplierOrderDetailChild.setNum(entrustProduct.getNum());
            supplierOrderDetailChild.setEntrustDetailId(orderDetail.getId());
            supplierOrderDetailRepository.save(supplierOrderDetailChild);
            if(params.getSaveType() == Constants.SAP_INVOICE_SUBMIT){
              WWDTO wwdto = new WWDTO();
              wwdto.setEbelp(orderDetail.getSortNum().toString());
              wwdto.setWlzj(productChile.getCode());
              wwdto.setWerks(supplierOrder.getGroupCode());
              wwdto.setMenge(entrustProduct.getNum().toPlainString());
              wwdtos.add(wwdto);
            }
          }
        }
        itemdto.setWw(wwdtos);
      }
    }
    supplierOrderAdd.setCustomerOrderCode(String.join(",", customerOrderCode));
    supplierOrderAdd.setSaleOrderNo(String.join(",", saleOrderNo));
    supplierOrderAdd.setProjectNo(String.join(",", projectNo));
    supplierOrderAdd.setProjectName(String.join(",", projectName));
    supplierOrderAdd.setSoldToParty(String.join(",", soldToPartySet));
    supplierOrderAdd.setSalesman(String.join(",", salesmanSet));
    supplierOrderRepository.save(supplierOrderAdd);
    // 发送短信，站内消息通知  产品说暂时去掉 20240314
    //    if (StrUtil.isNotEmpty(params.getId())) {
    //      sendUpdateOrderReceive(supplierOrder);
    //    }
    // 提交SAP审核
    if(params.getSaveType() == Constants.SAP_INVOICE_SUBMIT ){
      Supplier supplier = supplierRepository.findById(supplierOrder.getSupplierId())
          .orElseThrow(() -> new CheckException("供应商信息不存在！"));
      Boolean oneTimeSupplier = supplier.isOneTimeSupplier();
      Supplier supplierInvoice = supplier;
      if (!oneTimeSupplier) {
        String invoicingParty = supplierOrder.getInvoicingParty();
        supplierInvoice =
            supplierRepository.findFirstByEnterpriseNameAndState(invoicingParty, Constants.STATE_OK)
                .orElseThrow(() -> new CheckException("供应商信息不存在！"));
      }
      // 校验供应商配置
      verifyConfigService.verifyAll(supplier, supplierOrder.getGroupCode());

      boolean supervisorApproval = false;
      //如果有项目类别是标准&&关联采购申请是空&&是否免费为“否”&&关联销售订单号是空，调用MM021接口时，ZZSP_01主管审批传递X
      if (StrUtil.equals(params.getOrderType(), PurchaseOrderTypeEnum.SAP.getKey())
          && CollUtil.isNotEmpty(params.getProductList())) {
        supervisorApproval = params.getProductList().stream().anyMatch(
            item -> StrUtil.isEmpty(item.getPurchaseApplyForOrderId()) && StrUtil.equals(
                item.getFreeState(), Constants.STATE_NO)&&StrUtil.isEmpty(item.getSalesOrderNo()));
      }
      List<ITEMDTO> resultItemDTOS = itemdtos.stream().peek(itemdto -> {
        itemdto.setZzxmbh_01(
            StrUtil.isNotEmpty(supplierOrderAdd.getProjectNo()) ? supplierOrderAdd.getProjectNo()
                : StrUtil.EMPTY);
        itemdto.setZzkhddh_01(StrUtil.isNotEmpty(supplierOrderAdd.getCustomerOrderCode())
            ? supplierOrderAdd.getCustomerOrderCode() : StrUtil.EMPTY);
      }).collect(Collectors.toList());
      UpdatePurchaseOrderSapParam param =
          buildPurchaseOrderParam(supplierOrder, resultItemDTOS, supplier, supplierInvoice,null,
              supervisorApproval,params.getPaymentTermsStr(), params.getFileList());
      // 自动审核的单据状态需要直接变成待履约
      if (BooleanUtil.isTrue(supplierOrder.getSelfState())) {
        if (SupplierOrderState.UNAUDITED.getOrderState().equals(supplierOrder.getOrderState())) {
          supplierOrder.setOrderState(SupplierOrderState.WAIT.getOrderState());
        }
        supplierOrder.setFinalPrice(BigDecimal.ZERO);
      }
      // 强制先保存订单信息，防止保存到数据库异常
      supplierOrderDetailRepository.flush();
      supplierOrderRepository.flush();
      supplierOrderProductRepository.flush();
      sapService.sapPurchaseOrderWithAlarm(param, "");
    }
    //保存到原寄售转自有订单号
    if (StrUtil.equals(params.getOrderType(), PurchaseOrderTypeEnum.CONSIGNMENT_TO_OWNED.getKey())
        && StrUtil.isNotEmpty(params.getConsignmentToOwnedCode())) {
      Optional.ofNullable(
          supplierOrderRepository.findFirstByCodeAndState(params.getConsignmentToOwnedCode(),
              Constants.STATE_OK)).ifPresent(oldOrder -> {
        oldOrder.setConsignmentToOwnedCode(supplierOrderAdd.getCode());
        supplierOrderRepository.save(oldOrder);
      });
    }
    return supplierOrderAdd.getId();
  }

  private void checkPurchaseOrderGroup(PuchaseOrderAddParams params, SupplierOrder supplierOrder) {
    Group group =
        groupRepository.findFirstByErpCodeAndState(params.getPurchaseDeptCode(), Constants.STATE_OK);
    if (group == null) throw new CheckException("采购部门不存在！");
    //修改的情况
    if (StrUtil.isNotBlank(supplierOrder.getGroupCode())) {
      if (!Objects.equals(supplierOrder.getGroupCode(), group.getGroupCode())) {
        throw new CheckException("采购部门不属于该采购单的采购组织");
      }
    }
    //新增的情况
    if (!Objects.equals(params.getUserGroup(), group.getGroupCode())) {
      throw new CheckException("采购部门不属于该采购组织");
    }
  }

  private SupplierOrder savaSupplierOrder(PuchaseOrderAddParams params,
      SupplierOrder supplierOrder) {
    checkPurchaseOrderGroup(params, supplierOrder);
    supplierOrder.setOrderState(SupplierOrderState.UNAUDITED.getOrderState());
    if (params.getSaveType() == 1) {
      supplierOrder.setOrderState(SupplierOrderState.STAGING.getOrderState());
    }
    supplierOrder.setSupplierId(params.getSupplierId());
    supplierOrder.setSupplierName(params.getSupplierName());
    if (StrUtil.isBlank(supplierOrder.getGroupCode())) {
      supplierOrder.setGroupCode(params.getUserGroup());
    }
    if (StrUtil.isBlank(supplierOrder.getGroupName())) {
      supplierOrder.setGroupName(params.getGroupName());
    }
    supplierOrder.setPurchaseTime(System.currentTimeMillis());
    // 编码判断是否厂家直发
    if (CollUtil.isNotEmpty(params.getProductList())) {
      if (StrUtil.equals(
          params.getProductList().stream().findFirst().get().getWarehouse(), WarehouseEnum.HAI_NING_DIRECT_SALES.getCode())) {
        supplierOrder.setDirectShipment(true);
      } else {
        supplierOrder.setDirectShipment(false);
      }
    }
    // 价税合计总和
    supplierOrder.setPrice(params.getTotalAmountIncludingTax());
    supplierOrder.setReceiveMobile(params.getReceiveMobile());
    supplierOrder.setReceiveMan(params.getReceiveMan());
    supplierOrder.setPurchaseMan(params.getPurchaseMan());
    supplierOrder.setReceiveAddress(params.getReceiveAddress());
    supplierOrder.setOrderConfirmState(true);
    supplierOrder.setOrderCancelState(false);
    supplierOrder.setOrderReturnState(false);
    supplierOrder.setOrderShipWaitStockState(false);
    supplierOrder.makeAndSetStockProgress(BigDecimal.ZERO, params.getTotalNum());
    supplierOrder.setTotalNum(params.getTotalNum());
    supplierOrder.setState(Constants.STATE_OK);
    supplierOrder.setMark(params.getMark());
    supplierOrder.setRefuseState(Constants.STATE_NO);
    supplierOrder.setSupplierOpenInvoiceState(Constants.ORDER_INVOICE_STATE_NOT_DONE);
    supplierOrder.setOrderType(params.getOrderType());
    supplierOrder.setPurchaseDept(params.getPurchaseDept());
    supplierOrder.setPurchaseDeptCode(params.getPurchaseDeptCode());
    supplierOrder.setPurchaseCode("xhgj00"+ params.getPurchaseMan().substring(0,4));
    supplierOrder.setInvoicingParty(params.getInvoicingParty());
    supplierOrder.setMoneyCode(params.getMoneyCode());
    supplierOrder.setOrderRate(params.getOrderRate());
    supplierOrder.setSupContacts(params.getSupContacts());
    supplierOrder.setSupMobile(params.getSupMobile());
    supplierOrder.setSupEmail(params.getSupEmail());
    supplierOrder.setSupFax(params.getSupFax());
    supplierOrder.setInvoiceType(params.getInvoiceType());
    supplierOrder.setFreight(params.getFreight());
    supplierOrder.setFreeState(params.getFreeState());
    supplierOrder.setSelfState(params.getSelfState());
    if (params.getSaveType() == 2) {
      supplierOrder.setPaymentTermsStr(params.getPaymentTermsStr());
    }
    supplierOrder.setLoss(params.getLoss());
    supplierOrder.setCauseOfLoss(params.getCauseOfLoss());
    boolean scp = BooleanUtil.isTrue(params.getScp());
    supplierOrder.setScp(scp);
    if (!scp) {
      supplierOrder.setOrderConfirmState(false);
    }
    SupplierOrder supplierOrderAdd = supplierOrderRepository.save(supplierOrder);
    purchaseOrderPaymentTermsService.savePurchaseOrderPaymentTerms(params.getPaymentTerms(),
        supplierOrderAdd.getId());
    return supplierOrderAdd;
  }

  private UpdatePurchaseOrderSapParam buildPurchaseOrderParam(SupplierOrder supplierOrder,
      List<ITEMDTO> itemdtos, Supplier supplier, Supplier supplierInvoice, String srmId,
      boolean supervisorApproval,String paymentTermsStr,
      List<AnnexParam> fileList) {
    UpdatePurchaseOrderSapParam param = new UpdatePurchaseOrderSapParam();
    UpdatePurchaseOrderDATADTO datadto = new UpdatePurchaseOrderDATADTO();
    UpdatePurchaseOrderHEADDTO headdto = new UpdatePurchaseOrderHEADDTO();
    headdto.setSRMID(StrUtil.EMPTY);
    headdto.setSpras(StrUtil.EMPTY);
    headdto.setName1(StrUtil.EMPTY);
    headdto.setCity1(StrUtil.EMPTY);
    headdto.setCountry(StrUtil.EMPTY);
    headdto.setEbeln(supplierOrder.getCode());
    headdto.setBsart(supplierOrder.getOrderType());
    headdto.setEkorg(supplierOrder.getGroupCode());
    headdto.setEkgrp(supplierOrder.getPurchaseDeptCode());
    headdto.setBukrs(supplierOrder.getGroupCode());
    if(Constants.SUPPLIER_TYPE_PROVISIONAL.equals(supplier.getSupType())){
      headdto.setName1(supplier.getEnterpriseName());
      headdto.setCity1("一次性供应商");
      headdto.setCountry("CN");
    }
    File file = fileDao.getFirstFileByRIdAndRType(supplierOrder.getId(),
        Constants_FileRelationType.ORDER_CONTRACT);
 /*   // todo 给产品测试用，暂时拿掉
    headdto.setZttwb(srmConfig.getContractFileH5Url()+"?id="+supplierOrder.getId());*/
    if (file != null) {
      headdto.setZttwb("【点击查看合同："+srmConfig.getContractFileH5Url()+"?id="+supplierOrder.getId()+"】"+supplierOrder.getMark());
    }
    if (file == null) {
      headdto.setZttwb("未上传合同");
    }
    headdto.setBedat(DateUtil.format(new Date(),DatePattern.PURE_DATE_PATTERN));
    headdto.setLifnr(supplier.getMdmCode());
    headdto.setLifn2(supplierInvoice.getMdmCode());
    //20250210 创建、修改采购订单时，如果WAERS货币码是JPY，汇率传递订单汇率*100
    headdto.setWkurs(StrUtil.equals(SettleCurrency.PRE004.getKey(), supplierOrder.getMoneyCode())
        ? NumberUtil.mul(NumberUtil.toBigDecimal(supplierOrder.getOrderRate()), BigDecimal.valueOf(100)).toString()
        : supplierOrder.getOrderRate());
    final String payment_iterm = "0001";
    headdto.setZterm(payment_iterm);
    headdto.setWaers(supplierOrder.getMoneyCode());
    //    headdto.setSpras(Constants_Sap.DEFAULT_ATTRIBUTE_VALUES);
    if (BooleanUtil.isTrue(supplierOrder.getSelfState())) {
      headdto.setZsp("X");
    }else{
      headdto.setZsp("");
    }
    headdto.setZcdoa("Y");
    headdto.setAfnam(supplierOrder.getPurchaseMan().substring(4,
        supplierOrder.getPurchaseMan().length()));
    headdto.setBednr(supplierOrder.getPurchaseCode());
    //如果有项目类别是标准&&关联采购申请是空，调用MM021接口时，ZZSP_01主管审批传递X,默认传空
    headdto.setZzsp_01(supervisorApproval ? Constants_Sap.CONFIRM_IDENTIFICATION : StrUtil.EMPTY);
    headdto.setZterm_01(StrUtil.isNotEmpty(paymentTermsStr) ? paymentTermsStr : StrUtil.EMPTY);
    headdto.setKufix(Constants_Sap.CONFIRM_IDENTIFICATION);
    if (StrUtil.isNotEmpty(srmId)) {
      headdto.setSRMID(srmId);
    }
    headdto.setItem(itemdtos);
    datadto.setHead(headdto);
    param.setData(datadto);
    return param;
  }

  private UpdatePurchaseOrderSapParam buildNewPurchaseOrderParam(SupplierOrder supplierOrder,
      List<ITEMDTO> itemdtos, Supplier supplier, Supplier supplierInvoice, String srmId,
      boolean supervisorApproval,String paymentTermsStr) {
    UpdatePurchaseOrderSapParam param = new UpdatePurchaseOrderSapParam();
    UpdatePurchaseOrderDATADTO datadto = new UpdatePurchaseOrderDATADTO();
    UpdatePurchaseOrderHEADDTO headdto = new UpdatePurchaseOrderHEADDTO();
    headdto.setSRMID(StrUtil.EMPTY);
    headdto.setSpras(StrUtil.EMPTY);
    headdto.setName1(StrUtil.EMPTY);
    headdto.setCity1(StrUtil.EMPTY);
    headdto.setCountry(StrUtil.EMPTY);
    headdto.setEbeln("21"+ RandomUtil.randomNumbers(8));
    headdto.setBsart(StrUtil.equals(supplierOrder.getOrderType(),
        PurchaseOrderTypeEnum.INITIAL_PURCHASE.getKey())? PurchaseOrderTypeEnum.INITIAL_PURCHASE.getKey():"Z050");
    headdto.setEkorg(supplierOrder.getGroupCode());
    headdto.setEkgrp(supplierOrder.getPurchaseDeptCode());
    headdto.setBukrs(supplierOrder.getGroupCode());
    headdto.setZycgdh(supplierOrder.getCode());
    if(Constants.SUPPLIER_TYPE_PROVISIONAL.equals(supplier.getSupType())){
      headdto.setName1(supplier.getEnterpriseName());
      headdto.setCity1("一次性供应商");
    }
    /*// todo 给产品测试用，暂时拿掉
    headdto.setZttwb(srmConfig.getContractFileH5Url()+"?id="+supplierOrder.getId());*/
    headdto.setZttwb(
        "【点击查看合同："
            + srmConfig.getContractFileH5Url()
            + "?id="
            + supplierOrder.getId()
            + "】"
            + supplierOrder.getMark());
    headdto.setBedat(DateUtil.format(new Date(), DatePattern.PURE_DATE_PATTERN));
    headdto.setLifnr(supplier.getMdmCode());
    headdto.setLifn2(supplierInvoice.getMdmCode());
    headdto.setCountry("CN");
    headdto.setWkurs(supplierOrder.getOrderRate());
    final String payment_iterm = "0001";
    headdto.setZterm(payment_iterm);
    headdto.setWaers(supplierOrder.getMoneyCode());
    //    headdto.setSpras(Constants_Sap.DEFAULT_ATTRIBUTE_VALUES);
    // 如果是自采单提交时自动审批字段传 X
    if (BooleanUtil.isTrue(supplierOrder.getSelfState())) {
      headdto.setZsp("X");
    }else{
      headdto.setZsp("");
    }
    headdto.setAfnam(supplierOrder.getPurchaseMan().substring(4,
        supplierOrder.getPurchaseMan().length()));
    headdto.setBednr(supplierOrder.getPurchaseCode());
    //如果有项目类别是标准&&关联采购申请是空，调用MM021接口时，ZZSP_01主管审批传递X,默认传空
    headdto.setZzsp_01(supervisorApproval ? Constants_Sap.CONFIRM_IDENTIFICATION : StrUtil.EMPTY);
    headdto.setZterm_01(StrUtil.isNotEmpty(paymentTermsStr) ? paymentTermsStr : StrUtil.EMPTY);
    if (StrUtil.isNotEmpty(srmId)) {
      headdto.setSRMID(srmId);
    }
    headdto.setZthlx(Constants_Sap.RETURN_ORDER_TYPE_ORIGINAL);
    headdto.setItem(itemdtos);
    datadto.setHead(headdto);
    param.setData(datadto);
    return param;
  }


  private static SupplierOrderProduct saveProductEntrust(EntrustProduct entrustProduct) {
    SupplierOrderProduct supplierOrderProductEntrust = new SupplierOrderProduct();
    supplierOrderProductEntrust.setCode(entrustProduct.getProductCode());
    supplierOrderProductEntrust.setBrand(entrustProduct.getBrand());
    supplierOrderProductEntrust.setName(entrustProduct.getProductName());
    supplierOrderProductEntrust.setManuCode(entrustProduct.getManuCode());
    supplierOrderProductEntrust.setUnit(entrustProduct.getUnit());
    supplierOrderProductEntrust.setUnitDigit(3);
    return supplierOrderProductEntrust;
  }

  @Override
  public void receiptOrReturnReversal(String orderToFormId) {
    Assert.notBlank(orderToFormId);
    SupplierOrderToForm supplierOrderToForm =
        supplierOrderToFormService.get(
            orderToFormId,
            () -> CheckException.noFindException(SupplierOrderToForm.class, orderToFormId));
    if (SupplierOrderFormStatus.REVERSAL.getStatus().equals(supplierOrderToForm.getStatus())) {
      throw new CheckException("单据已经冲销，无法重复操作");
    }
    String supplierOrderId = supplierOrderToForm.getSupplierOrderId();
    RLock lock =
        redissonClient.getLock(
            Constants_LockName.PURCHASE_ORDER_RECEIPT_OR_RETURN_REVERSAL + supplierOrderId);
    RLock lockGroup =
        redissonClient.getLock(
            Constants_LockName.LOCK_GROUP_PURCHASE_ADD_RETURN_AND_REVERSAL + supplierOrderId);
    DefaultTransactionDefinition defaultTransactionDefinition = new DefaultTransactionDefinition();
    TransactionStatus transactionStatus =
        platformTransactionManager.getTransaction(defaultTransactionDefinition);
    supplierOrderToForm.setStatus(SupplierOrderFormStatus.REVERSAL.getStatus());
    supplierOrderToFormService.save(supplierOrderToForm);
    try {
      if (lock.tryLock(
          Constants_LockName.WAIT_LOCK_TIME,
          Constants_LockName.LEASE_LOCK_MAX_TIME,
          TimeUnit.SECONDS) &&
          lockGroup.tryLock(
              Constants_LockName.WAIT_LOCK_TIME,
              Constants_LockName.LEASE_LOCK_MAX_TIME,
              TimeUnit.SECONDS)
      ) {
        String type = supplierOrderToForm.getType();
        String warehousingType = SupplierOrderFormType.WAREHOUSING.getType();
        String returnType = SupplierOrderFormType.RETURN.getType();
        if (!StrUtil.containsAny(type, warehousingType, returnType)) {
          throw new CheckException("该类型单据无法进行冲销");
        }
        SupplierOrder supplierOrder =
            get(
                supplierOrderId,
                () -> CheckException.noFindException(SupplierOrder.class, supplierOrderId));
        String wareHouse = supplierOrderToForm.getReturnWarehouse();
        boolean directShipment;
        if (StrUtil.isNotBlank(wareHouse)) {
          directShipment = WarehouseEnum.HAI_NING_DIRECT_SALES.getCode().equals(supplierOrderToForm.getReturnWarehouse());
        }else{
          directShipment = supplierOrder.getDirectShipment();
        }
        //是否厂家直发 0否 1是
        // 单据明细
        List<SupplierOrderDetail> supplierOrderDetails =
            supplierOrderDetailService.getByOrderToFormId(supplierOrderToForm.getId());

        boolean reversalSAP = false;
        // 入库冲销处理
        if (warehousingType.equals(type)) {
          receiptReversal(supplierOrder, supplierOrderToForm, supplierOrderDetails);
          reversalSAP = true;
        }
        // 退库库冲销处理
        if (returnType.equals(type)) {
          returnReversal(supplierOrder, supplierOrderToForm, supplierOrderDetails);
          reversalSAP = true;
        }
        if (reversalSAP) {
          // 调用 SAP 逻辑
          SAPReversalDTO sapReversalDTO = reversalSAP(supplierOrderToForm.getProductVoucher(), directShipment, supplierOrderToForm.getCreateTime());
          String reversalNo = sapReversalDTO.getReversalNo();
          // SAP 冲销单号赋值
          // 厂直发单子才会返回冲销单号
          if (directShipment) {
            if (StrUtil.isBlank(reversalNo)) {
              log.info("厂直发订单冲销sap未返回冲销凭证号，单据id：" + supplierOrderToForm.getId());
              throw new CheckException("SAP系统出现异常，请联系管理员！");
            }
            supplierOrderToForm.setSapReversalNo(reversalNo);
            // 单据状态变成已冲销
            supplierOrderToForm.setStatus(SupplierOrderFormStatus.REVERSAL.getStatus());
          }
          if (!directShipment) {
            // 没有返回冲销凭证号状态改为冲销中
            supplierOrderToForm.setStatus(SupplierOrderFormStatus.REVERSAL_IN_PROGRESS.getStatus());
          }
        }
        supplierOrderToFormService.save(supplierOrderToForm);
        // 判断订单状态 如果不存在发货单和入库单需要将该订单状态改为 待履约
        if (!supplierOrderToFormService.existByTypeAndSupplierOrderIdAndExcludeStatusNotIn(
            ListUtil.toList(SupplierOrderFormType.WAREHOUSING, SupplierOrderFormType.DELIVER),
            supplierOrderId, ListUtil.toList(SupplierOrderFormStatus.REVERSAL.getStatus(),
                SupplierOrderFormStatus.REVOKE.getStatus()))) {
          supplierOrder.setOrderState(SupplierOrderState.WAIT.getOrderState());
        }
        //入库单冲销订单状态改成完成
        if (StrUtil.subBefore(supplierOrder.getStockProgress(), '/', true).trim()
            .equals(StrUtil.subAfter(supplierOrder.getStockProgress(), '/', true).trim())) {
          supplierOrder.setOrderState(SupplierOrderState.COMPLETE.getOrderState());
        }
        save(supplierOrder);
      } else {
        throw new CheckException("该订单有其他单据正在冲销，请稍后再进行操作");
      }
      platformTransactionManager.commit(transactionStatus);
    } catch (CheckException e) {
      platformTransactionManager.rollback(transactionStatus);
      throw e;
    } catch (Exception e) {
      platformTransactionManager.rollback(transactionStatus);
      log.error(ExceptionUtil.stacktraceToString(e, -1));
      throw new CheckException("未知异常请联系管理员");
    } finally {
      lock.unlock();
      lockGroup.unlock();
    }
  }

  private SAPReversalDTO reversalSAP(String productVoucher, boolean directShipment, Long createTime) {
    DateTime date = DateTime.of(System.currentTimeMillis());
    RETURNDTO returndto = sapPurchaseOrderRequest.receiptOrReturnReversal(productVoucher,
        DateUtil.format(date, DatePattern.PURE_DATE_PATTERN), directShipment, createTime);
    SAPReversalDTO sapReversalDTO = new SAPReversalDTO();
    sapReversalDTO.setReversalNo(returndto.getBelnr());
    return sapReversalDTO;
  }

  /**
   * 退库单冲销逻辑
   *
   * @param supplierOrderToForm 入库单
   * @param supplierOrderDetails 入库单明细
   */
  private void returnReversal(
      SupplierOrder supplierOrder,
      SupplierOrderToForm supplierOrderToForm,
      List<SupplierOrderDetail> supplierOrderDetails) {
    Assert.notEmpty(supplierOrderDetails);
    Assert.notNull(supplierOrderToForm);
    // 校验入库单冲销条件
    vaildteReturnReversal(supplierOrderToForm);
    // 冲销需要更新采购明细行的退库数量减少
    for (SupplierOrderDetail supplierOrderDetail : supplierOrderDetails) {
      String inWareHouseId = supplierOrderDetail.getInWareHouseId();
      // 入库单的orderToForm回退num
      supplierOrderToFormRepository.findById(inWareHouseId).ifPresent(inOrderForm -> {
        inOrderForm.setNum(inOrderForm.getNum().add(supplierOrderDetail.getStockOutputQty()));
        inOrderForm.setReturnPrice(inOrderForm.getReturnPrice().add(supplierOrderDetail.getReturnAmount()));
        supplierOrderToFormRepository.save(inOrderForm);
      });
      List<InputInvoiceOrderWithDetail> orderInvoiceRelationListByDetailIds =
          shareInputInvoiceService.getOrderInvoiceRelationListByDetailIdsRef(
              Collections.singletonList(supplierOrderDetail.getId()));
      orderInvoiceRelationListByDetailIds.forEach(inputInvoiceOrderWithDetail -> {
        if (!Constants.ORDER_INVOICE_STATE_OFFSET.equals(inputInvoiceOrderWithDetail.getInputInvoiceOrder().getInvoiceState())) {
          throw new CheckException("有未冲销的发票，请先冲销发票");
        }
      });

      SupplierOrderDetail detailed = supplierOrderDetail.getDetailed();
      if (detailed != null) {
        String detailedId = detailed.getId();
        // 退库数量减少
        detailed.setStockOutputQty(
            NumberUtil.sub(detailed.getStockOutputQty(), supplierOrderDetail.getStockOutputQty()));
        detailed.setReturnQty(
            NumberUtil.sub(detailed.getReturnQty(), supplierOrderDetail.getStockOutputQty()));
        // 入库单的退库数量减少（通过关联的入库单 id 和明细行 id 获取到行数据）
        SupplierOrderDetail wareHouseDetailed =
            supplierOrderDetailService.getFormIdAndDetailedId(inWareHouseId, detailedId);
        if (wareHouseDetailed != null) {
          wareHouseDetailed.setReturnQty(NumberUtil.sub(
              wareHouseDetailed.getReturnQty(), supplierOrderDetail.getStockOutputQty()));
          wareHouseDetailed.setStockOutputQty(
              NumberUtil.sub(
                  wareHouseDetailed.getStockOutputQty(), supplierOrderDetail.getStockOutputQty()));
          //加回可开票数量=冲销的退库单数量
          wareHouseDetailed.setInvoicableNum(
              NumberUtil.add(NumberUtil.toBigDecimal(wareHouseDetailed.getInvoicableNum()),
                  supplierOrderDetail.getStockOutputQty()));
          supplierOrderDetailService.save(wareHouseDetailed);
        }
        // 实际交货数量（入库数量-退库数量）
        detailed.setSettleQty(
            NumberUtil.sub(detailed.getStockInputQty(), detailed.getStockOutputQty()));
        // 总入库数量增加
        supplierOrder.setTotalStockInputQty(
            NumberUtil.add(
                supplierOrder.getTotalStockInputQty(), supplierOrderDetail.getStockOutputQty()));
        supplierOrder.setCancelReturnPrice(NumberUtil.sub(supplierOrder.getCancelReturnPrice(),
            NumberUtil.mul(supplierOrderDetail.getStockOutputQty(),supplierOrderDetail.getReturnPrice())));
        supplierOrder.setFinalPrice(NumberUtil.add(supplierOrder.getFinalPrice(),
            NumberUtil.mul(supplierOrderDetail.getStockOutputQty(),supplierOrderDetail.getReturnPrice())));
        // 更新入库进度
        setStockProgress(supplierOrder);
        // 待入库数量（已经发货的数量 - 采购入库数量 - 退库数量）
        detailed.setWaitStockInputQty(
            NumberUtil.sub(
                detailed.getShipQty(), detailed.getStockInputQty(), detailed.getStockOutputQty()));
        supplierOrderDetailService.save(detailed);
      }
    }
  }

  /**
   * 入库单冲销逻辑
   *
   * @param supplierOrderToForm 入库单
   * @param supplierOrderDetails 入库单明细
   */
  private void receiptReversal(
      SupplierOrder supplierOrder,
      SupplierOrderToForm supplierOrderToForm,
      List<SupplierOrderDetail> supplierOrderDetails) {
    Assert.notEmpty(supplierOrderDetails);
    Assert.notNull(supplierOrderToForm);
    // 校验入库单冲销条件
    vaildteReceiptReversal(supplierOrderToForm);
    //非厂直发订单，不执行订单数量变更操作
    if (supplierOrder.getDirectShipment()) {
    // 冲销需要更新采购明细行的入库数量、剩余入库数量、实际交货数量
    for (SupplierOrderDetail supplierOrderDetail : supplierOrderDetails) {
      SupplierOrderDetail detailed = supplierOrderDetail.getDetailed();
      if (detailed != null) {
        // 校验入库单内有物料已开票数量>0时，无法冲销；
        // ● 入库单内有物料的关联发票号时，无法冲销，除非关联的发票号是（已冲销）状态
        validReceiptReversalParam(detailed);
        // 入库数量(冲销需要减少入库数量)
        detailed.setStockInputQty(
            NumberUtil.sub(detailed.getStockInputQty(), supplierOrderDetail.getStockInputQty()));
        // 待发货数量增加
        detailed.setWaitQty(
            NumberUtil.add(detailed.getWaitQty(), supplierOrderDetail.getStockInputQty()));
        // 发货数量减少
        BigDecimal shipQty =
            NumberUtil.sub(detailed.getShipQty(), supplierOrderDetail.getStockInputQty());
        detailed.setShipQty(NumberUtil.isLess(shipQty, BigDecimal.ZERO) ? BigDecimal.ZERO : shipQty);
        // 重新计算剩余入库数量（订货数量-取消数量-入库数量）
        detailed.setRemainQty(NumberUtil.sub(detailed.getNum(), detailed.getCancelQty(), detailed.getStockInputQty()));
        // 实际交货数量（入库数量-退库数量）
        detailed.setSettleQty(
            NumberUtil.sub(detailed.getStockInputQty(), detailed.getStockOutputQty()));
        // 待入库数量（已经发货的数量 - 采购入库数量 - 退货数量）
        detailed.setWaitStockInputQty(
            NumberUtil.sub(detailed.getShipQty(), detailed.getStockInputQty(), detailed.getStockOutputQty()));
        supplierOrderDetailService.save(detailed);
        // 总入库数量也减少
        supplierOrder.setTotalStockInputQty(NumberUtil.sub(supplierOrder.getTotalStockInputQty(),
            supplierOrderDetail.getStockInputQty()));
        save(supplierOrder);
        // 更新入库进度
        setStockProgress(supplierOrder);
      }
    }
  }
    // 更新订单状态（如果是完成的订单需要变成履约中）
    if (SupplierOrderState.COMPLETE.getOrderState().equals(supplierOrder.getOrderState())) {
      supplierOrder.setOrderState(SupplierOrderState.IN_PROGRESS.getOrderState());
    }
    // 冲销了就会有发货单需要入库
    supplierOrder.setOrderShipWaitStockState(Boolean.TRUE);
    // 修改入库进度和总入库数量
    save(supplierOrder);
    // 需要将原来的发货单的的入库状态修改为待入库
    String deliverFormId = supplierOrderToForm.getDeliverFormId();
    if (StrUtil.isBlank(deliverFormId)&&BooleanUtil.isTrue(supplierOrder.getDirectShipment())) {
      throw new CheckException("该入库单没有绑定发货单，请联系管理员");
    }
    if (StrUtil.isNotBlank(deliverFormId)) {
      SupplierOrderToForm deliverForm =
          supplierOrderToFormService.get(
              deliverFormId,
              () -> CheckException.noFindException(SupplierOrderToForm.class, deliverFormId));
      // 发货单入库状态改为 false
      deliverForm.setWarehousing(Boolean.FALSE);
      // 发货单变为已撤销状态
      deliverForm.setStatus(SupplierOrderFormStatus.REVOKE.getStatus());
      supplierOrderToFormService.save(deliverForm);
    }
  }

  /**
   * 校验入库冲销
   * @param detailed 物料明细
   */
  private void validReceiptReversalParam(SupplierOrderDetail detailed) {
    // 开票数量
    BigDecimal invoicedNum = detailed.getInvoicedNum();
    if (invoicedNum != null && NumberUtil.isGreater(invoicedNum,BigDecimal.ZERO)){
      throw new CheckException("此入库单已经有财务凭证，请先联系财务冲销财务凭证");
    }
    // 开票状态校验
    String orderDetailId = detailed.getId();
    List<InputInvoiceOrderWithDetail> orderInvoiceRelationListByDetailIds =
        shareInputInvoiceService.getOrderInvoiceRelationListByDetailIdsRef(
            Collections.singletonList(orderDetailId));
    orderInvoiceRelationListByDetailIds.forEach(item -> {
      String invoiceState = item.getInputInvoiceOrder().getInvoiceState();
      // 入库单内有物料的关联发票号时，无法冲销，除非关联的发票号是（已冲销）状态
      if (!invoiceState.equals(Constants.PURCHASE_ORDER_INVOICE_RELATION_INVOICE_STATE_REVERSAL)) {
        throw new CheckException("此入库单已经有财务凭证，请先联系财务冲销财务凭证");
      }
    });
  }

  /**
   * 校验入库单冲销条件
   *
   * @param supplierOrderToForm 入库单
   */
  private void vaildteReceiptReversal(SupplierOrderToForm supplierOrderToForm) {
    Assert.notNull(supplierOrderToForm);
    vailedBaseReversal(supplierOrderToForm, SupplierOrderFormType.WAREHOUSING);
    // 入库单没有退库数量才能冲销，查询该单据是否有入库数量
    if (supplierOrderDetailService.existByWarehousingFormId(supplierOrderToForm.getId())) {
      throw new CheckException("入库单下有关联的退库单，请先冲销退库单再冲销入库单");
    }
  }

  /**
   * 校验入库单冲销条件
   *
   * @param supplierOrderToForm 入库单
   */
  private void vaildteReturnReversal(SupplierOrderToForm supplierOrderToForm) {
    Assert.notNull(supplierOrderToForm);
    vailedBaseReversal(supplierOrderToForm, SupplierOrderFormType.RETURN);
  }

  /**
   * 冲销基础条件
   *
   * @param supplierOrderToForm
   * @param vailedType
   */
  private static void vailedBaseReversal(
      SupplierOrderToForm supplierOrderToForm, SupplierOrderFormType vailedType) {
    if (ObjectUtil.notEqual(vailedType.getType(), supplierOrderToForm.getType())) {
      throw new CheckException("单据类型非法");
    }
    String sapReversalNo = supplierOrderToForm.getSapReversalNo();
    if (StrUtil.isNotBlank(sapReversalNo)) {
      throw new CheckException("单据已被冲销，无需重复操作");
    }
    // 入库单冲销条件
    // 1.入库单已有SAP返回的数据（物料凭证号、物料凭证年度）时展示冲销按钮
    String productVoucher = supplierOrderToForm.getProductVoucher();
    if (StrUtil.isBlank(productVoucher)) {
      throw new CheckException("入库单的 SAP 数据缺失，无法进行冲销");
    }
  }

  private SupplierOrderDetail buildSupplierOrderDetail(SupplierOrderToForm orderToForm,
      supplierProduct supplierProduct, SupplierOrderProduct supplierOrderProduct,
      boolean selfState,String orderType) {
    SupplierOrderDetail supplierOrderDetail = new SupplierOrderDetail();
    //寄售转自有订单
    if (StrUtil.equals(orderType, PurchaseOrderTypeEnum.CONSIGNMENT_TO_OWNED.getKey())) {
      if (StrUtil.isEmpty(supplierProduct.getBatchNo())) {
        throw new CheckException("批号为空不允许提交");
      }
      supplierOrderDetail.setBatchNo(supplierProduct.getBatchNo());
    }
    supplierOrderDetail.setOrderProductId(supplierOrderProduct.getId());
    supplierOrderDetail.setSupplierOrderProduct(supplierOrderProduct);
    supplierOrderDetail.setOrderToFormId(orderToForm.getId());
    supplierOrderDetail.setSapRowId(supplierProduct.getSapRowId());
    supplierOrderDetail.setDeliverTime(supplierProduct.getSupplierDeliverTime());
    supplierOrderDetail.setPurchaseDeliverTime(supplierProduct.getPurchaseDeliverTime());
    supplierOrderDetail.setNum(BigDecimalUtil.setScaleBigDecimalHalfUp(supplierProduct.getNum(),3));
    supplierOrderDetail.setSortNum(supplierProduct.getIndexNum());
    supplierOrderDetail.setWaitQty(BigDecimalUtil.setScaleBigDecimalHalfUp(supplierProduct.getNum(),3));
    supplierOrderDetail.setShipQty(BigDecimalUtil.setScaleBigDecimalHalfUp(new BigDecimal("0"),3));
    supplierOrderDetail.setReturnQty(
        BigDecimalUtil.setScaleBigDecimalHalfUp(new BigDecimal("0"), 3));
    supplierOrderDetail.setCancelQty(new BigDecimal("0"));
    supplierOrderDetail.setStockInputQty(new BigDecimal("0"));
    supplierOrderDetail.setWaitStockInputQty(BigDecimalUtil.setScaleBigDecimalHalfUp(supplierProduct.getNum(),3));
    supplierOrderDetail.setRemainQty(BigDecimalUtil.setScaleBigDecimalHalfUp(supplierProduct.getNum(),3));
    supplierOrderDetail.setStockOutputQty(new BigDecimal("0"));
    supplierOrderDetail.setSettleQty(new BigDecimal("0"));
    BigDecimal currShipPrice =
        NumberUtil.mul(supplierOrderDetail.getNum(), supplierProduct.getPrice());
    supplierOrderDetail.setTotalPrice(currShipPrice);
    supplierOrderDetail.setMark(supplierProduct.getMark());
    supplierOrderDetail.setTaxRate(supplierProduct.getTaxRate());
    supplierOrderDetail.setCreateTime(System.currentTimeMillis());
    supplierOrderDetail.setState(Constants.STATE_OK);
    supplierOrderDetail.setPrice(supplierProduct.getPrice());
    supplierOrderDetail.setDescription(supplierProduct.getDescription());
    supplierOrderDetail.setWarehouse(supplierProduct.getWarehouse());
    supplierOrderDetail.setWarehouseName(supplierProduct.getWarehouseName());
    supplierOrderDetail.setTotalAmountIncludingTax(
        supplierProduct.getTotalAmountIncludingTax());
    supplierOrderDetail.setFreeState(supplierProduct.getFreeState());
    supplierOrderDetail.setProjectType(supplierProduct.getProjectType());
    supplierOrderDetail.setProductRate(supplierProduct.getProductRate());
    supplierOrderDetail.setMarkupCoefficient(supplierProduct.getMarkupCoefficient());
    supplierOrderDetail.setTransferPrice(supplierProduct.getTransferPrice());
    supplierOrderDetail.setSurcharge(supplierProduct.getSurcharge());
    supplierOrderDetail.setSettlementPrice(Optional.ofNullable(supplierProduct.getSettlementPrice()).orElse(BigDecimal.ZERO));
    supplierOrderDetail.setTotalSettlementPrice(Optional.ofNullable(supplierProduct.getTotalSettlementPrice()).orElse(BigDecimal.ZERO));
    supplierOrderDetail.setTariff(supplierProduct.getTariff());
    supplierOrderDetail.setFreight(supplierProduct.getFreight());
    supplierOrderDetail.setPaymentAmount(supplierProduct.getPaymentAmount());
    supplierOrderDetail.setTariffAmount(supplierProduct.getTariffAmount());
    String freightSupplierId = supplierProduct.getFreightSupplierId();
    if (StrUtil.isNotBlank(freightSupplierId)) {
      Supplier supplier = Optional.ofNullable(supplierService.get(freightSupplierId))
          .orElseThrow(() -> new CheckException("费用供应商 id 非法!"));
      supplierOrderDetail.setFreightSupplierId(supplier.getId());
      supplierOrderDetail.setFreightSupplierName(supplier.getEnterpriseName());
    }
    String tariffSupplierId = supplierProduct.getTariffSupplierId();
    if (StrUtil.isNotBlank(tariffSupplierId)) {
      Supplier supplier = Optional.ofNullable(supplierService.get(tariffSupplierId))
          .orElseThrow(() -> new CheckException("关税供应商 id 非法!"));
      supplierOrderDetail.setTariffSupplierId(supplier.getId());
      supplierOrderDetail.setTariffSupplierName(supplier.getEnterpriseName());
    }
    //6.8.2增加杂费供应商
    supplierOrderDetail.setIncidentalAmount(supplierProduct.getIncidentalAmount());
    String incidentalSupplierId = supplierProduct.getIncidentalSupplierId();
    if (StrUtil.isNotBlank(incidentalSupplierId)) {
      Supplier supplier = Optional.ofNullable(supplierService.get(incidentalSupplierId))
          .orElseThrow(() -> new CheckException("杂费供应商 id 非法!"));
      supplierOrderDetail.setIncidentalSupplierId(supplier.getId());
      supplierOrderDetail.setIncidentalSupplierName(supplier.getEnterpriseName());
    }
    //自采订单特有的字段
    if (selfState) {
      supplierOrderDetail.setSalesOrderNo(supplierProduct.getSalesOrderNo());
      supplierOrderDetail.setProjectNo(supplierProduct.getProjectNo());
      supplierOrderDetail.setProjectName(supplierProduct.getProjectName());
      // 自采订单的结算单价和结算总价存0
      supplierOrderDetail.setSettlementPrice(BigDecimal.ZERO);
      supplierOrderDetail.setTotalSettlementPrice(BigDecimal.ZERO);
    }
    return supplierOrderDetail;
  }

  /**
   * 构建 code 的 map
   *
   * @param orderCode 采购订单号
   */
  private Map<String, String> buildCodeMap(String orderCode) {
    return MapUtil.of("code", orderCode);
  }

  /**
   * 发送更新订单的站内通知
   *
   * @param supplierOrder 供应商订单
   */
  private void sendUpdateOrderReceive(SupplierOrder supplierOrder) {
    saveNoticeCenter(
        NoticeCenterType.UPDATE_ORDER_INFO,
        StrUtil.format(Constants_order.ORDER_NOTCIE_UPDATE_ORDER_INFO, supplierOrder.getCode()),
        supplierOrder.getId(),
        supplierOrder.getSupplierId());
  }


  /**
   * 保存消息中心
   *
   * @param type 消息类型
   * @param supplierOrderId 订单id
   * @param supplierId 供应商id
   */
  private void saveNoticeCenter(
      NoticeCenterType type, String content, String supplierOrderId, String supplierId) {
    log.info(
        "新增前台消息通知内容："
            + "type:"
            + type
            + "content:"
            + content
            + "supplierOrderId:"
            + supplierOrderId
            + "supplierId:"
            + supplierId);
    noticeCenterService.save(type, content, supplierOrderId, supplierId);
  }

  @Override
  public List<PurchaseOrderProductDetailedVO> getPurchaseOrderProductDetailed(String id) {
    return CollUtil.emptyIfNull(
            supplierOrderDetailRepository.findAllByEntrustDetailIdAndState(id, Constants.STATE_OK))
        .stream()
        .map(PurchaseOrderProductDetailedVO::new)
        .collect(Collectors.toList());
  }

  @Override
  public List<PurchaseOrderWarehousingEntryInfoVO> getPurchaseOrderWarehousingEntryInfo(String id) {
    Assert.notEmpty(id);
    SupplierOrder supplierOrder =
        get(id, () -> CheckException.noFindException(SupplierOrder.class, id));
    return CollUtil.emptyIfNull(
            supplierOrderToFormService.getByTypeAndSupplierOrderId(
                SupplierOrderFormType.WAREHOUSING, supplierOrder.getId()))
        .stream()
        .map(
            supplierOrderToForm -> {
              PurchaseOrderWarehousingEntryInfoVO vo = new PurchaseOrderWarehousingEntryInfoVO();
              vo.setShippingOrderId(supplierOrderToForm.getDeliverFormId());
              vo.setId(supplierOrderToForm.getId());
              vo.setWarehousingTime(supplierOrderToForm.getTime());
              vo.setLogisticsCompany(
                  StrUtil.emptyIfNull(supplierOrderToForm.getLogisticsCompany()));
              vo.setState(supplierOrderToForm.getStatus());
              vo.setLogisticsCode(supplierOrderToForm.getLogisticsCode());
              vo.setTrackNum(StrUtil.emptyIfNull(supplierOrderToForm.getTrackNum()));
              vo.setSource(supplierOrderToForm.getSource());
              vo.setWarehousing(
                  ObjectUtil.equals(Boolean.TRUE, supplierOrderToForm.getWarehousing()));
              List<PurchaseOrderInvoiceProductVO> shipProductDTOList =
                  supplierOrderDetailService
                      .getByOrderToFormId(supplierOrderToForm.getId())
                      .stream()
                      .map(
                          supplierOrderDetail -> {
                            String orderDetailId = supplierOrderDetail.getId();
                            List<InputInvoiceOrderWithDetail> orderInvoiceRelationListByDetailIds =
                                shareInputInvoiceService.getOrderInvoiceRelationListByDetailIdsRef(Collections.singletonList(orderDetailId));
                            List<PurchaseOrderInvoiceRelation> purchaseOrderInvoiceRelationList = orderInvoiceRelationListByDetailIds.stream().map(
                                item -> new PurchaseOrderInvoiceRelation(
                                    item.getInputInvoiceOrder())).collect(Collectors.toList());
                            PurchaseOrderInvoiceProductVO purchaseOrderInvoiceProductVO =
                                new PurchaseOrderInvoiceProductVO(supplierOrderDetail);
                            purchaseOrderInvoiceProductVO.setPurchaseOrderInvoiceRelationList(purchaseOrderInvoiceRelationList);
                            supplierOrderDetailRepository.findById(supplierOrderDetail.getDetailedId()).ifPresent(baseDetail -> {
                              BigDecimal settlementPrice = Optional.ofNullable(baseDetail.getSettlementPrice()).orElse(BigDecimal.ZERO);
                              purchaseOrderInvoiceProductVO.setSettlementPrice(settlementPrice.stripTrailingZeros().toPlainString());
                            });
                            return purchaseOrderInvoiceProductVO;
                          })
                      .collect(Collectors.toList());
              vo.setShipProductDTOList(shipProductDTOList);
              vo.setProductVoucherNo(supplierOrderToForm.getProductVoucher());
              vo.setProductVoucherAnnual(supplierOrderToForm.getProductVoucherYear());
              vo.setSapReversalNo(supplierOrderToForm.getSapReversalNo());
              return vo;
            })
        .collect(Collectors.toList());
  }

  @Override
  @Transactional(rollbackFor = Exception.class)
  public void cancelPurchaseOrder(CancelPurchaseOrderParam param) {
    String purchaseOrderId = param.getPurchaseOrderId();
    SupplierOrder supplierOrder =
        get(
            purchaseOrderId,
            () -> CheckException.noFindException(SupplierOrder.class, purchaseOrderId));
    if (BooleanUtil.isTrue(supplierOrder.getDirectShipment())) {
      supplierOrderToFormService
          .getByTypeAndStatusAndSupplierOrderId(SupplierOrderFormType.DELIVER, supplierOrder.getId())
          .forEach(
              supplierOrderToForm -> {
                if (!BooleanUtil.isTrue(supplierOrderToForm.getWarehousing())) {
                  throw new CheckException("存在未入库的发货单，请先入库，若发货单不需要入库请撤销");
                }
              });
    }
    // TODO 待确认-根据进度 - 判断订单状态问题
    // 根据订单id和类型去获取表单信息
    SupplierOrderToForm supplierOrderToForm =
        supplierOrderToFormRepository.getFirstByTypeAndSupplierOrderIdAndState(
            SupplierOrderFormType.CANCEL.getType(), purchaseOrderId, Constants.STATE_OK);
    // 如果该订单取消类型表单为空则创建取消类型表单
    if (ObjectUtil.isEmpty(supplierOrderToForm)) {
      supplierOrderToForm = new SupplierOrderToForm();
      supplierOrderToForm.setSupplierOrderId(supplierOrder.getId());
      supplierOrderToForm.setType(SupplierOrderFormType.CANCEL.getType());
      supplierOrderToForm.setCreateTime(System.currentTimeMillis());
      supplierOrderToForm.setState(Constants.STATE_OK);
      supplierOrderToFormRepository.save(supplierOrderToForm);
    }
    // 根据表单生成新取消单
    List<PurchaseOrderProduct> purchaseProductList = param.getPurchaseProductList();
    if (CollUtil.isEmpty(purchaseProductList)) {
      throw new CheckException("取消订货-请先选择需要取消的物料！");
    }
    // 该订单总取消数量
    BigDecimal cancelAllCount = ObjectUtil.defaultIfNull(supplierOrderToForm.getNum(),BigDecimal.ZERO);
    // TODO 调用SAP接口
    UpdatePurchaseOrderSapParam updatePurchaseOrderSapParam = new UpdatePurchaseOrderSapParam();
    List<ITEMDTO> itemList = new ArrayList<>();
    for (PurchaseOrderProduct purchaseOrderProduct : purchaseProductList) {
      //  采购明细id-变更采购明细的订货数量
      String purchaseOrderDetailId = purchaseOrderProduct.getPurchaseOrderDetailId();
      // 查询订单明细信息
      SupplierOrderDetail supplierOrderDetail =
          Optional.ofNullable(supplierOrderDetailService.get(purchaseOrderDetailId))
              .orElseThrow(
                  () -> new CheckException("该详情id" + purchaseOrderDetailId + "无法查询采购订单详情信息！"));
      // 修改订单明细信息 - 取消订货数量需要更新 - 待入库数量变为 0 待发数量减少为0
      BigDecimal remainQty = supplierOrderDetail.getRemainQty();
      // 最终结算金额
      supplierOrderDetail.setCancelQty(remainQty);
      supplierOrderDetail.setWaitQty(BigDecimal.ZERO);
      supplierOrderDetail.setRemainQty(BigDecimal.ZERO);
      supplierOrderDetailRepository.save(supplierOrderDetail);
      BigDecimal stockInputQty = supplierOrderDetail.getStockInputQty();
      BigDecimal stockOutputQty = supplierOrderDetail.getStockOutputQty();
      BigDecimal settleQty = supplierOrderDetail.getSettleQty();
      BigDecimal num = supplierOrderDetail.getNum();
      BigDecimal price = supplierOrderDetail.getPrice();
      // 保存取消订单明细
      SupplierOrderDetail supplierCancelOrderDetail = new SupplierOrderDetail();
      // 物料 id 的字段对应的实体是不能变化的 insertable = false, updatable = false
      supplierCancelOrderDetail.setProductRate(supplierOrderDetail.getProductRate());
      supplierCancelOrderDetail.setTaxRate(supplierOrderDetail.getTaxRate());
      supplierCancelOrderDetail.setOrderProductId(supplierOrderDetail.getOrderProductId());
      supplierCancelOrderDetail.setDetailedId(supplierOrderDetail.getId());
      supplierCancelOrderDetail.setOrderToFormId(supplierOrderToForm.getId());
      supplierCancelOrderDetail.setSapRowId(supplierOrderDetail.getSapRowId());
      supplierCancelOrderDetail.setErpRowNum(supplierOrderDetail.getErpRowNum());
      supplierCancelOrderDetail.setDeliverTime(supplierOrderDetail.getDeliverTime());
      supplierCancelOrderDetail.setNum(num);
      supplierCancelOrderDetail.setWaitQty(supplierOrderDetail.getWaitQty());
      supplierCancelOrderDetail.setShipQty(supplierOrderDetail.getShipQty());
      supplierCancelOrderDetail.setReturnQty(supplierOrderDetail.getReturnQty());
      // 取消订货数量 = 订货数量 -  入库数量  也就是剩余入库数量
      supplierCancelOrderDetail.setCancelQty(remainQty);
      cancelAllCount = NumberUtil.add(cancelAllCount,remainQty);
      // 该物料的退货/取消金额
      BigDecimal currReturnOrCancelPrice = NumberUtil.mul(remainQty, price);
      supplierOrder.setCancelReturnPrice(
          NumberUtil.add(supplierOrder.getCancelReturnPrice(), currReturnOrCancelPrice));
      supplierCancelOrderDetail.setStockInputQty(supplierOrderDetail.getStockInputQty());
      supplierCancelOrderDetail.setWaitStockInputQty(supplierOrderDetail.getWaitStockInputQty());
      supplierCancelOrderDetail.setRemainQty(supplierOrderDetail.getRemainQty());
      supplierCancelOrderDetail.setStockOutputQty(supplierOrderDetail.getStockOutputQty());
      supplierCancelOrderDetail.setSettleQty(settleQty);
      supplierCancelOrderDetail.setTotalPrice(supplierOrderDetail.getTotalPrice());
      supplierCancelOrderDetail.setMark(supplierOrderDetail.getMark());
      supplierCancelOrderDetail.setPrice(supplierOrderDetail.getPrice());

      supplierCancelOrderDetail.setSalesOrderNo(supplierOrderDetail.getSalesOrderNo());
      supplierCancelOrderDetail.setDescription(supplierOrderDetail.getDescription());
      supplierCancelOrderDetail.setPurchaseApplyForOrderId(
          supplierOrderDetail.getPurchaseApplyForOrderId());
      supplierCancelOrderDetail.setWarehouse(supplierOrderDetail.getWarehouse());
      supplierCancelOrderDetail.setTotalAmountIncludingTax(
          supplierOrderDetail.getTotalAmountIncludingTax());
      supplierCancelOrderDetail.setFreeState(supplierOrderDetail.getFreeState());
      supplierCancelOrderDetail.setProjectType(supplierOrderDetail.getProjectType());
      supplierCancelOrderDetail.setCreateTime(System.currentTimeMillis());
      supplierCancelOrderDetail.setSortNum(supplierOrderDetail.getSortNum());
      supplierCancelOrderDetail.setState(Constants.STATE_OK);
      supplierOrderDetailRepository.save(supplierCancelOrderDetail);
      // 构建sap接口
      buildCancelPurchaseItemDTOList(itemList, supplierOrderDetail.getSortNum(),
          ObjectUtil.defaultIfNull(supplierOrderDetail.getShipQty(),BigDecimal.ZERO),
          ObjectUtil.defaultIfNull(supplierOrderDetail.getStockInputQty(),BigDecimal.ZERO),
           supplierOrder.getOrderType(), remainQty);
      //采购申请订货状态
      String purchaseApplyForOrderId = supplierOrderDetail.getPurchaseApplyForOrderId();
      if(StrUtil.isNotEmpty(purchaseApplyForOrderId)){
        PurchaseApplyForOrder purchaseApplyForOrder =
            purchaseApplyForOrderRepository.findById(purchaseApplyForOrderId).orElse(null);
        if(num.compareTo(settleQty) > 0){
          purchaseApplyForOrder.setOrderGoodsState(SimpleBooleanEnum.YES.getKey());
        }
        BigDecimal orderGoodsNumber = purchaseApplyForOrder.getOrderGoodsNumber();
        orderGoodsNumber =
            BigDecimalUtil.setScaleBigDecimalHalfUp(orderGoodsNumber, 2).subtract(remainQty);
        purchaseApplyForOrder.setOrderGoodsNumber(orderGoodsNumber);
        purchaseApplyForOrderRepository.save(purchaseApplyForOrder);
      }
    }
    // 更新取消单总数量
    supplierOrderToForm.setNum(cancelAllCount);
    supplierOrderToFormRepository.save(supplierOrderToForm);
    // 入库进度 采购入库数量/总数量-取消数量-退货数量
    // TODO  调用sap后更新订单状态 如果入库进度=1或0，订单状态更新为已完成
    // 调用SAP接口
    buildCancelPurchaseOrder(updatePurchaseOrderSapParam,supplierOrder.getCode(),itemList);
    sapService.sapPurchaseOrderWithAlarm(updatePurchaseOrderSapParam, "");
    // 是否应该更新入库进度
    setStockProgress(supplierOrder);
    supplierOrder.setOrderCancelState(Boolean.TRUE);
    save(supplierOrder);
    // 交货进度
    // 【初始值】
    // ● 初始为未完成
    // 【变更节点】
    // ● 此行物料的剩余入库数量=0时，交货状态变更为已完成；
    // ● 剩余入库数量=订货数量-取消订货数量-已入库数量
  }

  /**
   * 构建SAP参数Item
   */
  private void buildCancelPurchaseItemDTOList(List<ITEMDTO> itemList, Integer sortNum,
      BigDecimal shipQty, BigDecimal stockInputQty ,String orderType, BigDecimal remain) {
    ITEMDTO itemdto = new ITEMDTO();
    List<WWDTO> wwDtoList = new ArrayList<>();
    WWDTO wwdto = new WWDTO();
    wwdto.setEbelp("@");
    wwdto.setMenge("@");
    wwdto.setWerks("@");
    wwdto.setWlzj("@");
    wwDtoList.add(wwdto);
    itemdto.setWw(wwDtoList);
    itemdto.setEbelp(StrUtil.toString(sortNum));
    itemdto.setMatnr("@");
    itemdto.setTxz01("@");
    itemdto.setMeins("@");
    itemdto.setMenge("@");
    itemdto.setWerks("@");
    itemdto.setLgort("@");
    itemdto.setAplfz("@");
    itemdto.setMatkl("@");
    itemdto.setMwskz("@");
    itemdto.setNetpr("@");
    itemdto.setPeinh("@");
    itemdto.setBprme("@");
    itemdto.setZgsje("@");
    itemdto.setZyyje("@");
    itemdto.setZjsj("@");
    itemdto.setCharX("@");
    itemdto.setRetpo("@");
    itemdto.setZmfbs("@");
    itemdto.setPstyp("@");
    itemdto.setKnttp("@");
    itemdto.setKostl("@");
    itemdto.setBanfn("@");
    itemdto.setBnfpo("@");
    itemdto.setGsgys("@");
    itemdto.setYfgys("@");
    if (PurchaseOrderTypeEnum.INITIAL_PURCHASE.getKey().equals(orderType)) {
      itemdto.setGsgys(null);
      itemdto.setYfgys(null);
    }
    //    if (NumberUtil.isGreater(shipQty,BigDecimal.ZERO)||NumberUtil.isGreater(stockInputQty,BigDecimal.ZERO)){
    //      itemdto.setElikz("X");
    //    }
    //    取消物料行调用MM021接口的时候，无论是否发货都在ELIKZ内传X
    //（原先的逻辑要保留，后续有可能还要该回去，现在是因为SAP有一批不太对的数据）
    itemdto.setElikz("X");
    itemdto.setLoekz("@");
    // 2025年3月21日13:18:21 采购订单取消/反取消时传递，取消时传取消订货数量。反取消时传递0
    itemdto.setZqxsl(remain.stripTrailingZeros().toPlainString());
//    if (NumberUtil.equals(shipQty,BigDecimal.ZERO)&&NumberUtil.equals(stockInputQty,
//        BigDecimal.ZERO)){
//      itemdto.setLoekz("X");
//    }
    itemList.add(itemdto);
  }

  /** 构建SAP参数Item */
  private void buildUnCancelPurchaseItemDTOList(List<ITEMDTO> itemList, Integer sortNum, BigDecimal shipQty) {
    ITEMDTO itemdto = new ITEMDTO();
    List<WWDTO> wwDtoList = new ArrayList<>();
    WWDTO wwdto = new WWDTO();
    wwdto.setEbelp("@");
    wwdto.setMenge("@");
    wwdto.setWerks("@");
    wwdto.setWlzj("@");
    wwDtoList.add(wwdto);
    itemdto.setWw(wwDtoList);
    itemdto.setEbelp(StrUtil.toString(sortNum));
    itemdto.setMatnr("@");
    itemdto.setTxz01("@");
    itemdto.setMeins("@");
    itemdto.setMenge("@");
    itemdto.setWerks("@");
    itemdto.setLgort("@");
    itemdto.setAplfz("@");
    itemdto.setMatkl("@");
    itemdto.setMwskz("@");
    itemdto.setNetpr("@");
    itemdto.setPeinh("@");
    itemdto.setBprme("@");
    itemdto.setZgsje("@");
    itemdto.setZyyje("@");
    itemdto.setZjsj("@");
    itemdto.setCharX("@");
    itemdto.setRetpo("@");
    itemdto.setZmfbs("@");
    itemdto.setPstyp("@");
    itemdto.setKnttp("@");
    itemdto.setKostl("@");
    itemdto.setBanfn("@");
    itemdto.setBnfpo("@");
    itemdto.setYfgys("@");
    itemdto.setGsgys("@");
    if (NumberUtil.isGreater(shipQty,BigDecimal.ZERO)){
      itemdto.setElikz("");
      itemdto.setLoekz("@");
    }else {
      itemdto.setElikz("@");
      itemdto.setLoekz("");
    }
    itemdto.setElikz(StrUtil.EMPTY);
    itemdto.setLoekz(StrUtil.EMPTY);
    // 2025年3月21日13:18:21 采购订单取消/反取消时传递，取消时传取消订货数量。反取消时传递0
    itemdto.setZqxsl(BigDecimal.ZERO.stripTrailingZeros().toPlainString());
    itemList.add(itemdto);
  }

  /** 构建取消订货SAP参数 */
  private void buildCancelPurchaseOrder(UpdatePurchaseOrderSapParam updatePurchaseOrderSapParam,
      String orderCode, List<ITEMDTO> itemList) {
    // 数据参数
    UpdatePurchaseOrderDATADTO dataDTO = new UpdatePurchaseOrderDATADTO();
    UpdatePurchaseOrderHEADDTO headDTO = new UpdatePurchaseOrderHEADDTO();
    headDTO.setItem(itemList);
    headDTO.setEbeln(orderCode);
    headDTO.setBsart("@");
    headDTO.setEkorg("@");
    headDTO.setEkgrp("@");
    headDTO.setBukrs("@");
    headDTO.setZttwb("@");
    headDTO.setBedat("@");
    headDTO.setLifnr("@");
    headDTO.setLifn2("@");
    headDTO.setWkurs("@");
    headDTO.setZterm("@");
    headDTO.setWaers("@");
    headDTO.setSpras("@");
    headDTO.setZsp("X");
    headDTO.setAfnam("@");
    headDTO.setBednr("@");
    dataDTO.setHead(headDTO);
    updatePurchaseOrderSapParam.setData(dataDTO);
  }

  @Override
  public List<CancelPurchaseOrderListDTO> getCancelPurchaseOrderList(String purchaseOrderId) {
    // 根据订单id和类型去获取表单信息
    SupplierOrderToForm supplierOrderToForm =
        supplierOrderToFormRepository.getFirstByTypeAndSupplierOrderIdAndState(
            SupplierOrderFormType.CANCEL.getType(), purchaseOrderId, Constants.STATE_OK);
    if (ObjectUtil.isNotEmpty(supplierOrderToForm)) {
      return CollUtil.emptyIfNull(
              supplierOrderDetailService.getByOrderToFormId(
                  supplierOrderToForm.getId()))
          .stream()
          .map(
              supplierOrderDetail ->
                  new CancelPurchaseOrderListDTO(purchaseOrderId, supplierOrderDetail))
          .collect(Collectors.toList());
    } else {
      return ListUtil.empty();
    }
  }

  @Override
  @Transactional(rollbackFor = Exception.class)
  public void unCancelPurchaseOrder(UnCancelPurchaseOrderDTO param) {
    String purchaseOrderId = param.getPurchaseOrderId();
    String purchaseOrderDetailId = param.getPurchaseOrderDetailId();
    // 获取取消单信息
    SupplierOrderDetail supplierOrderDetail =
        Optional.ofNullable(supplierOrderDetailService.get(purchaseOrderDetailId))
            .orElseThrow(() -> new CheckException("反取消订单" + purchaseOrderDetailId + "不存在"));
    // 非厂直发订单反向取消，如果是“否”就提示由于WMS仓库系统限制，目前关闭非厂直发订单的反取消功能，有问题请咨询仓库同事
    if (!WarehouseEnum.HAI_NING_DIRECT_SALES.getCode().equals(supplierOrderDetail.getWarehouse())) {
      throw new CheckException("由于WMS仓库系统限制，目前关闭非厂直发订单的反取消功能，有问题请咨询仓库同事");
    }
    UpdatePurchaseOrderSapParam updatePurchaseOrderSapParam = new UpdatePurchaseOrderSapParam();
    List<ITEMDTO> itemList = new ArrayList<>();
    // 获取订单明细信息
    SupplierOrderDetail detailed = supplierOrderDetail.getDetailed();
    BigDecimal cancelQty = supplierOrderDetail.getCancelQty();
    BigDecimal num = supplierOrderDetail.getNum();
    BigDecimal shipQty = supplierOrderDetail.getShipQty();
    BigDecimal settleQty = supplierOrderDetail.getSettleQty();
    detailed.setRemainQty(cancelQty);
    detailed.setWaitQty(NumberUtil.sub(num, shipQty));
    detailed.setCancelQty(BigDecimal.ZERO);
    // 反取消后 订单取消数量回滚
    supplierOrderDetailService.save(detailed);
    // 逻辑删除取消单
    supplierOrderDetail.setState(Constants.STATE_DELETE);
    supplierOrderDetailService.save(supplierOrderDetail);
    // 采购订单信息
    SupplierOrder supplierOrder =
        get(
            purchaseOrderId,
            () -> CheckException.noFindException(SupplierOrder.class, purchaseOrderId));
    // 查询采购单对应的取消单
    // 根据订单id和类型去获取表单信息
    SupplierOrderToForm supplierOrderToForm =
        supplierOrderToFormRepository.getFirstByTypeAndSupplierOrderIdAndState(
            SupplierOrderFormType.CANCEL.getType(), purchaseOrderId, Constants.STATE_OK);
    // 查询该采购单下面的取消单是否存在 - 为空修改取消标签状态
    List<SupplierOrderDetail> supplierCancelOrderDetailList =
        supplierOrderDetailService.getByOrderToFormId(supplierOrderToForm.getId());
    if(CollUtil.isEmpty(supplierCancelOrderDetailList)){
      supplierOrder.setOrderCancelState(Boolean.FALSE);
      supplierOrderToForm.setState(Constants.STATE_DELETE);
      supplierOrderToFormRepository.save(supplierOrderToForm);
    }
    // 获取订单发货数量 如果订单有发货数量，订单状态更新为履约中；如果没有发货数量，订单状态更新为待履约
    // 获取该订单下所以订单详情集合
    BigDecimal deliverNumber = supplierOrderDetailService.getPurchaseOrderShipQty(purchaseOrderId);
    if (NumberUtil.isGreater(deliverNumber, BigDecimal.ZERO)) {
      supplierOrder.setOrderState(SupplierOrderState.IN_PROGRESS.getOrderState());
    } else {
      supplierOrder.setOrderState(SupplierOrderState.WAIT.getOrderState());
    }
    // 该物料的退货/取消金额
    BigDecimal currReturnOrCancelPrice = NumberUtil.mul(cancelQty,
        supplierOrderDetail.getPrice());
    supplierOrder.setCancelReturnPrice(
        NumberUtil.sub(supplierOrder.getCancelReturnPrice(), currReturnOrCancelPrice));
    // 更新取消单总数量
    supplierOrderToForm.setNum(NumberUtil.sub(supplierOrderToForm.getNum(),cancelQty));
    supplierOrderToFormRepository.save(supplierOrderToForm);
    // 是否应该更新入库进度
    setStockProgress(supplierOrder);
    //采购申请订货状态
    String purchaseApplyForOrderId = supplierOrderDetail.getPurchaseApplyForOrderId();
    if(StrUtil.isNotEmpty(purchaseApplyForOrderId)){
      PurchaseApplyForOrder purchaseApplyForOrder =
          purchaseApplyForOrderRepository.findById(purchaseApplyForOrderId).orElse(null);
      if(num.compareTo(settleQty) < 1){
        purchaseApplyForOrder.setOrderGoodsState(SimpleBooleanEnum.NO.getKey());
      }
      BigDecimal orderGoodsNumber = purchaseApplyForOrder.getOrderGoodsNumber();
      // 2024年8月19日15:45:09修正历史遗留BUG 反取消数量取错
      orderGoodsNumber =
          BigDecimalUtil.setScaleBigDecimalHalfUp(orderGoodsNumber, 2).add(cancelQty);
      //如果退货数量加上已订货数量大于申请数量，则不允许反取消
      if (NumberUtil.isGreater(orderGoodsNumber, purchaseApplyForOrder.getApplyForNumber())) {
        throw new CheckException("反取消后对应采购申请的订货数量会超出申请数量！无法反取消");
      }
      purchaseApplyForOrder.setOrderGoodsNumber(orderGoodsNumber);
      //已订货数量>=申请数量，订货状态应该变成不可订货
      if (NumberUtil.isGreaterOrEqual(orderGoodsNumber,
          purchaseApplyForOrder.getApplyForNumber())) {
        purchaseApplyForOrder.setOrderGoodsState(Constants.STATE_NO);
      }
      purchaseApplyForOrderRepository.save(purchaseApplyForOrder);
    }
    // TODO 调用SAP接口
    // 构建sap接口
    buildUnCancelPurchaseItemDTOList(itemList, supplierOrderDetail.getSortNum(),
        supplierOrderDetail.getShipQty());
    buildCancelPurchaseOrder(updatePurchaseOrderSapParam,supplierOrder.getCode(),itemList);
    sapService.sapPurchaseOrderWithAlarm(updatePurchaseOrderSapParam, "");
  }

  @Override
  public void updateNotes(UpdateNotesParam param) {
    SupplierOrderDetail supplierOrderDetail =
        supplierOrderDetailService.get(
            param.getId(),
            () -> CheckException.noFindException(SupplierOrderDetail.class, param.getId()));
    supplierOrderDetail.setMark(param.getNotes());
    supplierOrderDetailService.save(supplierOrderDetail);
  }

  @Override
  public void updateShipForm(AddPurchaseOrderDeliveryParam deliveryParam) {
    String supplierOrderId = deliveryParam.getFormId();
    SupplierOrderToForm supplierOrderForm = supplierOrderToFormService.get(supplierOrderId,
        () -> CheckException.noFindException(SupplierOrderToForm.class, deliveryParam.getFormId()));
    if (CollUtil.isEmpty(deliveryParam.getProductDetailList())) {
      throw new CheckException("请勾选需要入库的物料");
    }
    Assert.notNull(deliveryParam);
    // 先校验删除行 id 和修改行 id 是否有重复
    Map<String, ProductDetailParam> shipIdMapUpdateShipFormDetail =
        deliveryParam.getProductDetailList().stream()
            .collect(Collectors.toMap(ProductDetailParam::getId, Function.identity(), (k1, k2) -> {
              throw new CheckException("修改退货单行 id 存在重复行，请联系管理员！");
            }));
    Set<String> shipIdSet = shipIdMapUpdateShipFormDetail.keySet();
    List<SupplierOrderDetail> originShipDetailList =
        supplierOrderDetailService.getByOrderToFormId(deliveryParam.getFormId());

    String logisticsCompany = deliveryParam.getLogisticsCompany();
    supplierOrderForm.setLogisticsCompany(logisticsCompany);
    String logisticsCode = deliveryParam.getLogisticsCode();
    supplierOrderForm.setLogisticsCode(logisticsCode);
    supplierOrderForm.setCode(deliveryParam.getCode());
    String trackNum = deliveryParam.getTrackNum();
    supplierOrderForm.setTrackNum(trackNum);
    supplierOrderToFormService.save(supplierOrderForm);
    // 新增发货单明细
    for (SupplierOrderDetail originShipDetail : originShipDetailList) {
      // 旧发货数量
      BigDecimal oldShipQty = originShipDetail.getShipQty();
      SupplierOrderDetail detailed = originShipDetail.getDetailed();
      // 明细行待发货
      BigDecimal waitQty = detailed.getWaitQty();
      // 本次可发货数量 = 明细行待发货数量 + 旧发货数量
      BigDecimal maxShipQty = NumberUtil.add(waitQty, oldShipQty);
      SupplierOrderProduct supplierOrderProduct = detailed.getSupplierOrderProduct();
      Integer unitDigit = supplierOrderProduct.getUnitDigit();
      BigDecimal price = detailed.getPrice();
      String shipId = originShipDetail.getId();
      BigDecimal num;
      // 修改退货单行
      if (shipIdSet.contains(shipId)) {
        ProductDetailParam productDetailParam = shipIdMapUpdateShipFormDetail.get(shipId);
        // 此次修改数量
        BigDecimal updateNum = productDetailParam.getDeliveryQty();
        if (NumberUtil.isLessOrEqual(updateNum, BigDecimal.ZERO)) {
          throw new CheckException(
              "物料入库【" + supplierOrderProduct.getCode() + "】数量不能小于 0 ");
        }
        if (NumberUtil.isGreater(updateNum, maxShipQty)) {
          throw new CheckException(
              "物料入库【" + supplierOrderProduct.getCode() + "】数量超过本次可发货数量");
        }
        originShipDetail.setShipQty(updateNum);
        num = BigDecimalUtil.setScaleBigDecimalHalfUp(NumberUtil.sub(oldShipQty, updateNum),
            unitDigit);
      } else {
        // 删除退货单行
        originShipDetail.setState(Constants.STATE_DELETE);
        originShipDetail.setShipQty(BigDecimal.ZERO);
        num = oldShipQty;
      }
      BigDecimal currShipPrice = NumberUtil.mul(originShipDetail.getShipQty(), price);
      originShipDetail.setTotalPrice(currShipPrice);
      supplierOrderDetailService.save(originShipDetail);
      detailed.setWaitQty(BigDecimalUtil.setScaleBigDecimalHalfUpAndLessThanZeroReturnZero(
          NumberUtil.add(waitQty, num), unitDigit));
      detailed.setShipQty(BigDecimalUtil.setScaleBigDecimalHalfUpAndLessThanZeroReturnZero(
          NumberUtil.sub(detailed.getShipQty(), num), unitDigit));
      // 重新计算待入库数量
      detailed.setWaitStockInputQty(
          BigDecimalUtil.setScaleBigDecimalHalfUpAndLessThanZeroReturnZero(
              NumberUtil.sub(detailed.getShipQty(), detailed.getStockInputQty(),
                  detailed.getReturnQty()), supplierOrderProduct.getUnitDigit()));
      supplierOrderDetailService.save(detailed);
    }
  }

  /**
   * 获取不含税价
   *
   * @param price 税价
   * @return 不含税价
   */
  private String getNakedPrice(BigDecimal price, BigDecimal taxRate) {
    if (price == null || taxRate == null) {
      return BigDecimal.ZERO.toPlainString();
    }
    BigDecimal nakedPrice =
        price.divide(
            BigDecimal.ONE.add(taxRate),
            10,
            RoundingMode.HALF_UP);
    return nakedPrice.toPlainString();
  }
  /**
   * 获取税额
   *
   * @param nakedPrice 不含税价
   * @return 税额
   */
  private String getTaxPrice(BigDecimal nakedPrice, BigDecimal taxRate) {
    return BigDecimalUtil.setScaleBigDecimalHalfUp(NumberUtil.mul(taxRate, nakedPrice), 2)
        .stripTrailingZeros().toPlainString();
  }

  @Override
  public ShippingAndWarehousingInformationVO getShippingAndWarehousingInformationVO(String id) {
    List<PurchaseOrderInvoiceVO> purchaseOrderInvoiceInfo = getPurchaseOrderInvoiceInfo(id);
    List<PurchaseOrderWarehousingEntryInfoVO> purchaseOrderWarehousingEntryInfo =
        getPurchaseOrderWarehousingEntryInfo(id);
    if (CollUtil.isNotEmpty(purchaseOrderInvoiceInfo) && CollUtil.isNotEmpty(
        purchaseOrderWarehousingEntryInfo)) {
      //发货单id->入库单集合下表map
      HashMap<String, String> invoiceIdToWarehousingIndex = new HashMap<>();
      for (int i = 0; i < purchaseOrderWarehousingEntryInfo.size(); i++) {
        PurchaseOrderWarehousingEntryInfoVO purchaseOrderWarehousingEntryInfoVO =
            purchaseOrderWarehousingEntryInfo.get(i);
        invoiceIdToWarehousingIndex.put(purchaseOrderWarehousingEntryInfoVO.getShippingOrderId(),
            String.valueOf(i + 1));
      }
      for (PurchaseOrderInvoiceVO purchaseOrderInvoiceVO : purchaseOrderInvoiceInfo) {
        purchaseOrderInvoiceVO.setWarehouseEntryNumber(
            invoiceIdToWarehousingIndex.getOrDefault(purchaseOrderInvoiceVO.getId(), ""));
      }
    }
    return new ShippingAndWarehousingInformationVO(purchaseOrderInvoiceInfo,
        purchaseOrderWarehousingEntryInfo);
  }

  private InputStream streamTran(ByteArrayOutputStream in){
    return new ByteArrayInputStream(in.toByteArray());
  }

  @Override
  @DefaultSearchScheme(searchType = Constants.SEARCH_TYPE_GET_PAGE_SUPPLIER_ORDER_PAGE)
  public PageResult<PurchaseOrderPaymentTermsPageVO> getPaymentTermsPage(User user,
      PurchaseOrderPaymentTermsParam param) {
    String purchaseId = user.getId();
    String createMan = user.getId();
    // 用户权限下采购名称集合
    if (user.getRoleList().contains(Constants.SUPPLIER_USER_ROLE_ADMIN) || user.getRoleList().contains(Constants.ROLE_ADMINISTRATOR)) {
      purchaseId = StrUtil.EMPTY;
      createMan = StrUtil.EMPTY;
    }
    if (Boolean.TRUE.equals(param.getSelectUnReceipt())) {
      param.setSupplierOrderFormType(SupplierOrderFormType.DELIVER);
      param.setSupplierOrderFormStatus(SupplierOrderFormStatus.WAIT_RECEIPT);
    }
    PurchaseOrderPaymentTermsDaoParam daoPageParam = MapStructFactory.INSTANCE.toPurchaseOrderPaymentTermsDaoParam(param);
    daoPageParam.setPurchaseId(purchaseId);
    daoPageParam.setCreateMan(createMan);
    if (StrUtil.isBlank(daoPageParam.getSortField()) && StrUtil.isBlank(
        daoPageParam.getSortType())) {
      final String default_sort_field = "baseDate";
      final String default_sort_type = "desc";
      daoPageParam.setSortField(default_sort_field);
      daoPageParam.setSortType(default_sort_type);
    }
    Page<PurchaseOrderPaymentTerms> purchaseOrderPaymentTermsPage =
        purchaseOrderPaymentTermsDao.findPurchaseOrderPaymentTermsPage(daoPageParam);
    if (purchaseOrderPaymentTermsPage.getContent().size() < ioIntensiveThreadPool.getMaxPoolSize()) {
      List<PurchaseOrderPaymentTermsPageVO> result =
          purchaseOrderPaymentTermsPage.getContent().stream()
              .map(this::convertPurchaseOrderPaymentTermsPageVO).collect(Collectors.toList());
      return PageResultBuilder.buildPageResult(purchaseOrderPaymentTermsPage, result);
    }
    long start = System.currentTimeMillis();
    List<List<PurchaseOrderPaymentTermsPageVO>> results =
        ParallelProcessUtil.simpleParallelProcessOfCollection(purchaseOrderPaymentTermsPage.getContent(),
            ioIntensiveThreadPool, this::convertPurchaseOrderPaymentTermsPageVO);
    List<PurchaseOrderPaymentTermsPageVO> resultVos =
        ParallelProcessUtil.flattenTwoDimensionalList(results);
    long end = System.currentTimeMillis();
    log.info("parallelProcessOfCollection cost time:{}", end - start);
    return PageResultBuilder.buildPageResult(purchaseOrderPaymentTermsPage, resultVos);
  }

  @Override
  @DefaultSearchScheme(searchType = Constants.SEARCH_TYPE_GET_PAGE_SUPPLIER_ORDER_PAGE)
  public PurchaseOrderPaymentTermsStatistics getPaymentTermsStatistics(User user, PurchaseOrderPaymentTermsParam param) {
    String purchaseId = user.getId();
    String createMan = user.getId();
    // 用户权限下采购名称集合
    if (user.getRoleList().contains(Constants.SUPPLIER_USER_ROLE_ADMIN) || user.getRoleList().contains(Constants.ROLE_ADMINISTRATOR)) {
      purchaseId = StrUtil.EMPTY;
      createMan = StrUtil.EMPTY;
    }
    if (Boolean.TRUE.equals(param.getSelectUnReceipt())) {
      param.setSupplierOrderFormType(SupplierOrderFormType.DELIVER);
      param.setSupplierOrderFormStatus(SupplierOrderFormStatus.WAIT_RECEIPT);
    }
    PurchaseOrderPaymentTermsDaoParam daoPageParam = MapStructFactory.INSTANCE.toPurchaseOrderPaymentTermsDaoParam(param);
    daoPageParam.setPurchaseId(purchaseId);
    daoPageParam.setCreateMan(createMan);
    if (StrUtil.isBlank(daoPageParam.getSortField()) && StrUtil.isBlank(
        daoPageParam.getSortType())) {
      final String default_sort_field = "baseDate";
      final String default_sort_type = "desc";
      daoPageParam.setSortField(default_sort_field);
      daoPageParam.setSortType(default_sort_type);
    }
    return purchaseOrderPaymentTermsDao.findPurchaseOrderPaymentTermsStatistics(daoPageParam);
  }

  private PurchaseOrderPaymentTermsPageVO convertPurchaseOrderPaymentTermsPageVO(
      PurchaseOrderPaymentTerms purchaseOrderPaymentTerms) {
    String purchaseOrderId = purchaseOrderPaymentTerms.getPurchaseOrderId();
    SupplierOrder supplierOrder = supplierOrderRepository.findById(purchaseOrderId)
        .orElseThrow(() -> new CheckException("数据异常，请联系管理员"));
    List<FinancialVoucher> financialVouchers =
        financialVoucherRepository.findAllByPurchaseOrderNoAndState(supplierOrder.getCode(),
            Constants.STATE_OK);
    BigDecimal withdrawnAmount =
        CollUtil.emptyIfNull(financialVouchers).stream().filter(financialVoucher -> {
          if (Objects.equals(VoucherTypeEnum.REFUND_VOUCHER, financialVoucher.getVoucherType())) {
            return false;
          }
          return true;
        }).map(financialVoucher -> Optional.ofNullable(financialVoucher.getVoucherPrice()).orElse(BigDecimal.ZERO)).reduce(BigDecimal.ZERO,
            BigDecimal::add);
    return PurchaseOrderPaymentTermsPageVO.of(purchaseOrderPaymentTerms, supplierOrder, withdrawnAmount);
  }

  private List<PurchaseOrderPaymentTerms> splitPurchaseOrderPaymentTerms(List<PurchaseOrderPaymentTerms> purchaseOrderPaymentTerms,
      int startIndex, int endIndex) {
    ArrayList<PurchaseOrderPaymentTerms> splitPurchaseOrderPaymentTerms =
        new ArrayList<>(endIndex - startIndex);
    for (int i = startIndex; i < endIndex; i++) {
      splitPurchaseOrderPaymentTerms.add(purchaseOrderPaymentTerms.get(i));
    }
    return splitPurchaseOrderPaymentTerms;
  }

  private List<PurchaseOrderPaymentTermsPageVO> parallelProcess(
      List<PurchaseOrderPaymentTerms> purchaseOrderPaymentTerms) {
    if (purchaseOrderPaymentTerms.size() <= 10) {
      return purchaseOrderPaymentTerms.stream().map(this::convertPurchaseOrderPaymentTermsPageVO)
          .collect(Collectors.toList());
    }
    final int number_of_partitions = 3;
    int oneSize = purchaseOrderPaymentTerms.size() / number_of_partitions;
    int residueElement = purchaseOrderPaymentTerms.size() % number_of_partitions;
    CompletableFuture<List<PurchaseOrderPaymentTermsPageVO>> vo1 =
        CompletableFuture.supplyAsync(() -> {
          List<PurchaseOrderPaymentTerms> purchaseOrderPaymentTerms1 =
              splitPurchaseOrderPaymentTerms(purchaseOrderPaymentTerms, 0, oneSize);
          return purchaseOrderPaymentTerms1.stream()
              .map(this::convertPurchaseOrderPaymentTermsPageVO).collect(Collectors.toList());
        }, ioIntensiveThreadPool);
    CompletableFuture<List<PurchaseOrderPaymentTermsPageVO>> vo2 =
        CompletableFuture.supplyAsync(() -> {
          List<PurchaseOrderPaymentTerms> purchaseOrderPaymentTerms1 =
              splitPurchaseOrderPaymentTerms(purchaseOrderPaymentTerms, oneSize, oneSize * 2);
          return purchaseOrderPaymentTerms1.stream()
              .map(this::convertPurchaseOrderPaymentTermsPageVO).collect(Collectors.toList());
        }, ioIntensiveThreadPool);
    CompletableFuture<List<PurchaseOrderPaymentTermsPageVO>> vo3 =
        CompletableFuture.supplyAsync(() -> {
          List<PurchaseOrderPaymentTerms> purchaseOrderPaymentTerms1 =
              splitPurchaseOrderPaymentTerms(purchaseOrderPaymentTerms, oneSize * 2, oneSize * 3);
          return purchaseOrderPaymentTerms1.stream()
              .map(this::convertPurchaseOrderPaymentTermsPageVO).collect(Collectors.toList());
        }, ioIntensiveThreadPool);
    List<PurchaseOrderPaymentTermsPageVO> residueElements = Collections.emptyList();
    if (residueElement != 0) {
      List<PurchaseOrderPaymentTerms> purchaseOrderPaymentTerms1 =
          splitPurchaseOrderPaymentTerms(purchaseOrderPaymentTerms, oneSize * 3,
              purchaseOrderPaymentTerms.size());
      residueElements =
          purchaseOrderPaymentTerms1.stream().map(this::convertPurchaseOrderPaymentTermsPageVO)
              .collect(Collectors.toList());
    }
    CompletableFuture<Void> voidCompletableFuture = CompletableFuture.allOf(vo1, vo2, vo3);
    voidCompletableFuture.join();
    return Stream.of(vo1.join(), vo2.join(), vo3.join(), residueElements)
        .flatMap(Collection::stream).collect(Collectors.toList());
  }

  @Override
  public PageResult<SalesOrderListDTO> getSalesOrderDetail(String salesOrgCode, String salesOrderNo,
      String projectNo, int pageNo, int pageSize) {
    PageResult<SalesOrderListDTO> salesOrderDetail;
    try {
      salesOrderDetail =
          omsRequest.getSalesOrderDetail(salesOrgCode, salesOrderNo, projectNo, pageNo, pageSize);
    } catch (RuntimeException e) {
      throw new CheckException("OMS系统出现异常,请稍候再试");
    }
    return salesOrderDetail;
  }

  @Override
  public List<NameAndCodeDTO> getSAPStockAddr(String name, String code, String sapComponentCode,
      String sapFactoryCode) {
    List<NameAndCodeDTO> sapStockAddr;
    try {
      sapStockAddr = xhgjPersonRequest.getSAPStockAddrByCondition(name, code, sapComponentCode,
          sapFactoryCode);
    } catch (RuntimeException e) {
      throw new CheckException("信息化人员信息服务异常,请稍候再试");
    }
    return sapStockAddr;
  }

  @Override
  public PurchaseOrderContractVO getContractFiles(String id) {
    SupplierOrder supplierOrder = supplierOrderRepository.findById(id)
        .orElseThrow(() -> new CheckException("订单不存在"));
    PurchaseOrderContractVO vo = new PurchaseOrderContractVO();
    List<File> annexcontract =
        fileService.getFileListByIdAndType(
            id, Constants_FileRelationType.ORDER_CONTRACT);
    ArrayList<FileDTO> fileDTOS = new ArrayList<>();
    for (File file : annexcontract) {
      FileDTO fileDTO = new FileDTO();
      fileDTO.setId(file.getId());
      fileDTO.setUrl(file.getUrl());
      fileDTO.setName(file.getName());
      fileDTO.setBaseUrl(uploadConfig.getUploadPath());
      if (StrUtil.isNotBlank(file.getDescription())) {
        String[] split = StrUtil.split(file.getDescription(), ".");
        String fileType = ArrayUtil.get(split, -1);
        fileDTO.setType(fileType);
      }
      fileDTOS.add(fileDTO);
    }
    vo.setContractFiles(fileDTOS);
    vo.setOrderNo(supplierOrder.getCode());
    vo.setRemake(supplierOrder.getMark());
    return vo;
  }

  @Override
  @SneakyThrows
  public void saveSupplierOrderExcel(MultipartFile file,String userId) {
    User user = userService.getUserById(userId);
    Map<String, Object> params = new HashMap<>();
    params.put("version", ShardingContext.getVersion());
    params.put("userId", userId);
    String savePath = importExcelUtil.saveExcel(file);
    Mission mission =
        missionService.createMission(
            user, "导入-采购订单", Constants.PLATFORM_TYPE_AFTER, savePath, file.getOriginalFilename());
    batchTaskMqSender.toHandleBatchTask(
        mission.getId(),
        JSON.toJSONString(user),
        Constants_Batch.BATCH_TASK_EXPORT_PURCHASE_ORDER);
  }

  @Override
  @Transactional
  public void updateSupplierContact(UpdateSupplierContactParam param) {
    Assert.notNull(param);
    SupplierOrder supplierOrder =
        get(param.getId(), () -> CheckException.noFindException(SupplierOrder.class, param.getId()));
    // 根据类型设置相应字段
    if (StrUtil.equals(param.getType(), Constants.STATE_NO)) {
      supplierOrder.setSupContacts(param.getSupContacts());
      supplierOrder.setSupMobile(param.getSupMobile());
      supplierOrder.setSupEmail(param.getSupEmail());
      supplierOrder.setSupFax(param.getSupFax());
    } else {
      supplierOrder.setReceiveMan(param.getReceiveMan());
      supplierOrder.setReceiveMobile(param.getReceiveMobile());
      supplierOrder.setReceiveAddress(param.getReceiveAddress());
    }
    supplierOrderDao.update(supplierOrder);
  }

  @Override
  @Transactional(rollbackFor = Exception.class)
  public void updateProductDetail(UpdateProductDetailParam param) {
    Assert.notNull(param);
    SupplierOrder supplierOrder =
        get(param.getId(), () -> CheckException.noFindException(SupplierOrder.class, param.getId()));
    if (StrUtil.equals(supplierOrder.getOrderState(),
        SupplierOrderState.UNAUDITED.getOrderState())) {
      throw new CheckException("审核中的订单不能修改明细，请耐心等待审核通过");
    }
    SupplierOrderToForm supplierOrderToForm =
        Optional.ofNullable(
                supplierOrderToFormService.getDetailedBySupplierOrderId(supplierOrder.getId()))
            .orElseThrow(() -> new CheckException("【" + supplierOrder.getId() + "】未找到该订单的订单明细，请联系管理员"));
    List<ITEMDTO> items = new ArrayList<>();
    //订货金额差值
    BigDecimal allDifferencePrice = BigDecimal.ZERO;
    // 物料信息
    List<UpdateProductDetailParam.ProductDetail> productList = param.getProductList();
      for (UpdateProductDetailParam.ProductDetail productDetail : productList) {
        SupplierOrderDetail detail =
            supplierOrderDetailRepository.findById(productDetail.getId()).orElse(null);
        if (detail==null) {
          continue;
        }
        if (NumberUtil.isGreater(detail.getStockInputQty(), productDetail.getNum())) {
          throw new CheckException("修改后的订货数量不能小于入库数量");
        }
        if (NumberUtil.isGreater(productDetail.getNum(), detail.getNum())) {
          throw new CheckException("您不能调大订货数量，如需调大请重新做单");
        }
        if (NumberUtil.equals(detail.getRemainQty(), BigDecimal.ZERO)) {
          throw new CheckException("交货已完成的物料行不允许修改明细");
        }
        //订货数量调整后，如果此行有关联采购申请，需要把采购申请的状态变为可订货，把已订货数量减去本次修改调小的订货数量
        if (NumberUtil.isGreater(NumberUtil.sub(detail.getNum(), productDetail.getNum()),
            BigDecimal.ZERO) && StrUtil.isNotEmpty(detail.getPurchaseApplyForOrderId())) {
          PurchaseApplyForOrder purchaseApplyForOrder =
              purchaseApplyForOrderRepository.findById(detail.getPurchaseApplyForOrderId())
                  .orElse((null));
          if (purchaseApplyForOrder != null) {
            //已订货数量减去本次修改调小的订货数量
            purchaseApplyForOrder.setOrderGoodsNumber(
                NumberUtil.sub(purchaseApplyForOrder.getOrderGoodsNumber(),
                    NumberUtil.sub(detail.getNum(), productDetail.getNum())));
            purchaseApplyForOrder.setOrderGoodsState(SimpleBooleanEnum.YES.getKey());
            purchaseApplyForOrderRepository.save(purchaseApplyForOrder);
          }
        }
        //待发数量=订货数量-已发数量
        detail.setWaitQty(NumberUtil.sub(productDetail.getNum(),detail.getShipQty()));
        //剩余入库数量
        detail.setRemainQty(NumberUtil.sub(productDetail.getNum(), detail.getCancelQty(),
            detail.getStockInputQty()));
        //价税合计=物料单价*数量
        detail.setTotalAmountIncludingTax(NumberUtil.mul(detail.getPrice(),productDetail.getNum()));
        //去税单价
        String nakedProductPrice = getNakedPrice(detail.getPrice(), detail.getTaxRate());
        //结算总价=结算单价*数量
        detail.setTotalSettlementPrice(BigDecimalUtil.setScaleBigDecimalHalfUp(
            NumberUtil.mul(productDetail.getNum(), detail.getSettlementPrice()), 2));
        //去掉约定交货日期
//        detail.setDeliverTime(productDetail.getSupplierDeliverTime());
        detail.setPurchaseDeliverTime(productDetail.getPurchaseDeliverTime());
        detail.setMark(productDetail.getMark());
        // v6.3.0版本线上问题，未设置更新的数据
        detail.setNum(BigDecimalUtil.setScaleBigDecimalHalfUp(productDetail.getNum(), 3));
        detail.setFreightSupplierId(productDetail.getFreightSupplierId());
        detail.setTariffSupplierId(productDetail.getTariffSupplierId());
        detail.setIncidentalSupplierId(productDetail.getIncidentalSupplierId());
        ITEMDTO itemdto = new ITEMDTO();
        itemdto.setEbelp(detail.getSortNum().toString());
        itemdto.setMenge(
            BigDecimalUtil.setScaleBigDecimalHalfUp(productDetail.getNum(), 3).toPlainString());
        itemdto.setAplfz(productDetail.getPurchaseDeliverTime() == null ? StrUtil.EMPTY
            : DateUtils.formatTimeStampToPureDate(productDetail.getPurchaseDeliverTime()));
        //海外类型添加运费供应商
        if (StrUtil.equals(param.getSupType(), Constants.SUPPLIERTYPE_ABROAD) || StrUtil.equals(
            param.getOrderType(), PurchaseOrderTypeEnum.OVER_SEAS.getKey())) {
          if (StrUtil.isNotBlank(productDetail.getFreightSupplierId()) || StrUtil.isNotBlank(
              productDetail.getTariffSupplierId()) || StrUtil.isNotBlank(
              productDetail.getIncidentalSupplierId())) {
            Optional.ofNullable(supplierService.get(productDetail.getFreightSupplierId()))
                .ifPresent(supplier -> {
                  itemdto.setYfgys(supplier.getMdmCode());
                  detail.setFreightSupplierId(supplier.getId());
                  detail.setFreightSupplierName(supplier.getEnterpriseName());
                });
            Optional.ofNullable(supplierService.get(productDetail.getTariffSupplierId()))
                .ifPresent(supplier -> {
                  itemdto.setGsgys(supplier.getMdmCode());
                  detail.setTariffSupplierId(supplier.getId());
                  detail.setTariffSupplierName(supplier.getEnterpriseName());
                });
            Optional.ofNullable(supplierService.get(productDetail.getIncidentalSupplierId()))
                .ifPresent(supplier -> {
                  itemdto.setZfgys(supplier.getMdmCode());
                  detail.setIncidentalSupplierId(supplier.getId());
                  detail.setIncidentalSupplierName(supplier.getEnterpriseName());
                });
          } else {
            itemdto.setYfgys(Constants_Sap.DEFAULT_ATTRIBUTE_VALUES);
            itemdto.setGsgys(Constants_Sap.DEFAULT_ATTRIBUTE_VALUES);
            itemdto.setZfgys(Constants_Sap.DEFAULT_ATTRIBUTE_VALUES);
          }
          itemdto.setZyyje(
              BigDecimalUtil.formatForStandard(productDetail.getFreight()).toPlainString());
          itemdto.setZgsje(
              BigDecimalUtil.formatForStandard(productDetail.getTariffAmount()).toPlainString());
          itemdto.setZjsj(
              BigDecimalUtil.formatForStandard(productDetail.getSettlementPrice()).toPlainString());
          itemdto.setZzf1(BigDecimalUtil.formatForStandard(productDetail.getIncidentalAmount())
              .toPlainString());
          detail.setTotalAmountIncludingTax(productDetail.getTotalAmountIncludingTax());
          detail.setPaymentAmount(productDetail.getPaymentAmount());
          detail.setTariffAmount(productDetail.getTariffAmount());
          detail.setIncidentalAmount(productDetail.getIncidentalAmount());
          detail.setProductRate(productDetail.getProductRate());
          detail.setSurcharge(productDetail.getSurcharge());
          detail.setFreight(productDetail.getFreight());
          detail.setSettlementPrice(
              Optional.ofNullable(productDetail.getSettlementPrice()).orElse(BigDecimal.ZERO));
          detail.setTotalSettlementPrice(
              Optional.ofNullable(productDetail.getTotalSettlementPrice()).orElse(BigDecimal.ZERO));
          //海外供应商添加货币汇率
          supplierOrder.setOrderRate(param.getOrderRate());
        }
        supplierOrderDetailDao.update(detail);
        List<WWDTO> wwDto = new ArrayList<>();
        WWDTO ww = new WWDTO();
        wwDto.add(ww);
        itemdto.setWw(wwDto);
        items.add(itemdto);
      }
    String detailFormId = supplierOrderToForm.getId();
    List<SupplierOrderDetail> supplierOrderDetails =
        CollUtil.emptyIfNull(supplierOrderDetailService.getByOrderToFormId(detailFormId));
    // 是否应该更新入库进度
    BigDecimal detailNum =
        supplierOrderDetails
            .stream()
            .map(SupplierOrderDetail::getNum)
            .reduce(NumberUtil::add)
            .orElse(BigDecimal.ZERO);
    BigDecimal totalPrice = supplierOrderDetails.stream()
        .map(detail -> NumberUtil.mul(detail.getNum(), detail.getPrice())).reduce(NumberUtil::add)
        .orElse(BigDecimal.ZERO);
    BigDecimal totalFreight = supplierOrderDetails.stream()
        .map(SupplierOrderDetail::getFreight).filter(Objects::nonNull).reduce(NumberUtil::add).orElse(BigDecimal.ZERO);
    supplierOrder.setTotalNum(detailNum);
    supplierOrder.setPrice(totalPrice);
    // 保存总运费
    supplierOrder.setFreight(totalFreight);
    BigDecimal stockInputQty = supplierOrder.getTotalStockInputQty();
    BigDecimal totalCancel =
        supplierOrderToFormService
            .getByTypeAndSupplierOrderId(SupplierOrderFormType.CANCEL, supplierOrder.getId())
            .stream()
            .map(SupplierOrderToForm::getNum)
            .reduce(NumberUtil::add)
            .orElse(BigDecimal.ZERO)
            .setScale(3, RoundingMode.HALF_UP);
    List<String> noStockProgressStatus =
        ListUtil.toList(
            SupplierOrderFormStatus.RETURN.getStatus(),
            SupplierOrderFormStatus.RETURN_COMPLETE.getStatus());
    BigDecimal totalReturn =
        supplierOrderToFormService
            .getByTypeAndSupplierOrderId(SupplierOrderFormType.RETURN, supplierOrder.getId())
            .stream()
            .filter(
                supplierOrderToForm1 ->
                    noStockProgressStatus.contains(supplierOrderToForm1.getStatus()))
            .map(SupplierOrderToForm::getNum)
            .reduce(NumberUtil::add)
            .orElse(BigDecimal.ZERO)
            .setScale(3, RoundingMode.HALF_UP);
    //分母
    BigDecimal progress = BigDecimalUtil.setScaleBigDecimalHalfUpAndLessThanZeroReturnZero(
        NumberUtil.sub(supplierOrder.getTotalNum(), totalCancel, totalReturn), 3);
    //修改后入库进度分子分母相等，将订单状态调整为已完成
    if (NumberUtil.equals(stockInputQty, progress)) {
      supplierOrder.setOrderState(SupplierOrderState.COMPLETE.getOrderState());
    }
    //入库进度：（入库数量合计-退库数量合计）/（订货数量合计-取消订货数量合计-退库数量合计），初始值为0/订单数量之和；
    //入库进度
    supplierOrder.makeAndSetStockProgress(stockInputQty, progress);
    //订货金额
    supplierOrder.setPrice(NumberUtil.sub(supplierOrder.getPrice(), allDifferencePrice));
    supplierOrderRepository.save(supplierOrder);
    // 构建sap-021接口(修改物料明细)
    try {
      sapService.sapPurchaseOrderWithAlarm(buildUpdateProductDetailParam(supplierOrder, items), "");
    } catch (Exception e) {
      throw new CheckException("调用 SAP-021 接口存在报错");
    }

  }


  private UpdatePurchaseOrderSapParam buildUpdateProductDetailParam(SupplierOrder supplierOrder,
      List<ITEMDTO> items) {
    UpdatePurchaseOrderSapParam param = new UpdatePurchaseOrderSapParam();
    UpdatePurchaseOrderDATADTO dataDto = new UpdatePurchaseOrderDATADTO();
    UpdatePurchaseOrderHEADDTO headDto = new UpdatePurchaseOrderHEADDTO();
    headDto.setEbeln(supplierOrder.getCode());
    headDto.setZsp(Constants_Sap.CONFIRM_IDENTIFICATION);
    headDto.setZcdoa("N");
    headDto.setItem(items);
    dataDto.setHead(headDto);
    param.setData(dataDto);
    return param;
  }

  @Override
  public byte[] printOutPurchaseOrderContract(String id) {
    byte[] bytes = sharePurchaseOrderService.downloadPurchaseOrderContract(id);
    ByteArrayInputStream inputStream = new ByteArrayInputStream(bytes);
    byte[] bytesOfPdf;
    try {
      bytesOfPdf = FileUtil.docConvertPdf(inputStream);
    } catch (Exception e) {
      log.error("生成pdf文件异常", e);
      throw new CheckException("生成pdf文件异常，请联系管理员！");
    }
    return bytesOfPdf;
  }

  //实际预付金额
  private BigDecimal getActualPrepaidAmount(String purchaseOrderCode) {
    List<FinancialVoucher> financialVouchers =
        financialVoucherRepository.getByVoucherTypeAndPurchaseOrderNoAndState(
            VoucherTypeEnum.ADVANCE_CHARGE.getKey(), purchaseOrderCode, Constants.STATE_OK);
    if (CollUtil.isEmpty(financialVouchers)) {
      return BigDecimal.ZERO;
    }
    financialVouchers = financialVouchers.stream().filter(
        financialVoucher -> !Objects.equals(financialVoucher.getPrepaidOffsetStatus(),
            Constants.STATE_OK)).collect(Collectors.toList());
    if (CollUtil.isEmpty(financialVouchers)) {
      return BigDecimal.ZERO;
    }
    BigDecimal relatedAmount = financialVouchers.stream().map(
        financialVoucher -> Optional.ofNullable(financialVoucher.getRelatedAmount())
            .orElse(BigDecimal.ZERO)).reduce(BigDecimal.ZERO, BigDecimal::add);
    BigDecimal refundAmount = financialVouchers.stream().map(
        financialVoucher -> Optional.ofNullable(financialVoucher.getRefundAmount())
            .orElse(BigDecimal.ZERO)).reduce(BigDecimal.ZERO, BigDecimal::add);
    return NumberUtil.sub(relatedAmount, refundAmount);
  }


  //获取订单应付款金额
  private BigDecimal getAmountPayable(String purchaseOrderCode) {
    BigDecimal result = BigDecimal.ZERO;
    List<FinancialVoucher> financialVouchers =
        financialVoucherRepository.getByVoucherTypeAndPurchaseOrderNoAndState(
            VoucherTypeEnum.INVOICE_POSTING.getKey(), purchaseOrderCode, Constants.STATE_OK);
    if (CollUtil.isEmpty(financialVouchers)) {
      return result;
    }
    //发票凭证金额
    BigDecimal invoiceAmount =
        financialVouchers.stream().map(FinancialVoucher::getRelatedAmount).filter(Objects::nonNull)
            .reduce(BigDecimal.ZERO, BigDecimal::add);
    //抵消预付金额
    BigDecimal offsetPrepaidAmount =
        financialVouchers.stream().map(FinancialVoucher::getOffsetPrepaidAmount)
            .filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
    result = NumberUtil.sub(invoiceAmount, offsetPrepaidAmount);
    return result;
  }

  @Override
  public List<PurchaseOrderPrepaidApplicationPreInfoDTO> getPrepaidApplicationPreInfoByIds(List<String> ids, String applyId) {
    if (CollUtil.isEmpty(ids)) {
      throw new CheckException("未指定订单");
    }
    //去重，多个物料合成一个订单
    ids = CollUtil.distinct(ids);
    List<SupplierOrder> supplierOrders = supplierOrderRepository.findAllByIdIn(new HashSet<>(ids));
    List<String> purchaseOrderNos = supplierOrders.stream().map(SupplierOrder::getCode).collect(Collectors.toList());
    return this.getPrepaidApplicationPreInfo(purchaseOrderNos, applyId);
  }

  @Override
  public List<PurchaseOrderPrepaidApplicationPreInfoDTO> getPrepaidApplicationPreInfo(List<String> purchaseOrderNos, String applyId) {
    //去重，多个物料合成一个订单
    purchaseOrderNos = CollUtil.distinct(purchaseOrderNos);
    List<SupplierOrder> purchaseOrders =
        supplierOrderRepository.findAllByCodeInAndState(purchaseOrderNos, Constants.STATE_OK);
    Map<String, SupplierOrder> id2PurchaseOrder = purchaseOrders.stream()
        .collect(Collectors.toMap(SupplierOrder::getCode, Function.identity(), (k1, k2) -> k1));
    // 查询审核中 + 驳回的预付申请详情
    Map<String, List<PaymentApplyDetail>> id2PaymentApplyDetails =
        paymentApplyDetailRepository.queryAllByApplyStateInAndSupplierOrderNoIn(
                ListUtil.toList(PaymentAuditStateEnum.PROCESSING.getKey(),
                    PaymentAuditStateEnum.REJECTED.getKey()), purchaseOrderNos, Constants.STATE_OK,
                PaymentApplyTypeEnums.ADVANCE.getKey()).stream()
            .collect(Collectors.groupingBy(PaymentApplyDetail::getSupplierOrderNo));
    // id2PaymentApplyDetails 过滤出本次编辑的applyId
    Map<String, PaymentApplyDetail> id2OriginPaymentApplyDetail =
        id2PaymentApplyDetails.values().stream().flatMap(Collection::stream).filter(
                paymentApplyDetail -> StrUtil.equals(applyId,
                    paymentApplyDetail.getPaymentApplyRecordId()))
            .collect(Collectors.toMap(PaymentApplyDetail::getSupplierOrderNo, Function.identity()));
    return purchaseOrderNos.stream().map(code -> {
      PurchaseOrderPrepaidApplicationPreInfoDTO dto =
          new PurchaseOrderPrepaidApplicationPreInfoDTO();
      PaymentApplyDetail paymentApplyDetail = id2OriginPaymentApplyDetail.get(code);
      SupplierOrder supplierOrder = id2PurchaseOrder.get(code);
      BigDecimal finalPrice = null;
      if (paymentApplyDetail != null) {
        dto.setId(supplierOrder.getId());
        dto.setEnterpriseName(paymentApplyDetail.getSupplierName());
        String financialVouchersId = paymentApplyDetail.getFinancialVouchersId();
        FinancialVoucher financialVoucher = financialVoucherRepository.findById(financialVouchersId)
            .orElseThrow(() -> new CheckException("财务凭证不存在"));
        finalPrice = getFinalPrice(code, financialVoucher);
        dto.setEnterpriseName(paymentApplyDetail.getSupplierName());
      }
      if (supplierOrder != null) {
        dto.setId(supplierOrder.getId());
        dto.setEnterpriseName(supplierOrder.getSupplierName());
        finalPrice = getFinalPrice(code, null);
      }
      dto.setCode(code);
      BigDecimal actualPrepaidAmount = getActualPrepaidAmount(code);
      dto.setActualPrepaidAmount(actualPrepaidAmount.stripTrailingZeros().toPlainString());
      BigDecimal amountPayable = getAmountPayable(code);
      dto.setAmountPayable(amountPayable.stripTrailingZeros().toPlainString());
      dto.setOrderSettlementAmount(
          finalPrice == null ? StrUtil.EMPTY : finalPrice.stripTrailingZeros().toPlainString());
      BigDecimal maximumPrePayableAmount =
          NumberUtil.sub(finalPrice, actualPrepaidAmount, amountPayable);
      dto.setAdvanceRelatedAmount(paymentApplyRecordService.getRelatedAmountByType(code,
          VoucherTypeEnum.ADVANCE_CHARGE.getKey()).stripTrailingZeros().toPlainString());
      dto.setPayableRelatedAmount(paymentApplyRecordService.getRelatedAmountByType(code,
          VoucherTypeEnum.ACCOUNTS_PAYABLE.getKey()).stripTrailingZeros().toPlainString());
      dto.setRefundRelatedAmount(paymentApplyRecordService.getRelatedAmountByType(code,
          VoucherTypeEnum.REFUND_VOUCHER.getKey()).stripTrailingZeros().toPlainString());
      BigDecimal applyingAdvancePrice = Optional.ofNullable(id2PaymentApplyDetails.get(code))
          .orElse(Collections.emptyList()) // 如果为null，则返回空List
          .stream()
          .map(PaymentApplyDetail::getApplyAdvancePrice)
          .reduce(BigDecimal.ZERO, BigDecimal::add);
      dto.setApplyingAdvancePrice(applyingAdvancePrice.toPlainString());
      // 最大可预付金额：=订单结算金额-实际预付金额-订单应付金额-此订单申请中的预付金额；
      maximumPrePayableAmount = maximumPrePayableAmount.subtract(applyingAdvancePrice);
      dto.setMaximumPrePayableAmount(maximumPrePayableAmount.stripTrailingZeros().toPlainString());
      BigDecimal finalMaximumPrePayableAmount = maximumPrePayableAmount;
      // 编辑时，需要减去原来的预付金额
      Optional.ofNullable(id2OriginPaymentApplyDetail.get(code))
          .ifPresent(origin -> {
            BigDecimal applyAdvancePrice = Optional.ofNullable(origin.getApplyAdvancePrice()).orElse(BigDecimal.ZERO);
            dto.setApplyAdvancePrice(applyAdvancePrice.toPlainString());
            dto.setMaximumPrePayableAmount(finalMaximumPrePayableAmount.add(applyAdvancePrice).stripTrailingZeros().toPlainString());
            dto.setApplyingAdvancePrice(applyingAdvancePrice.subtract(applyAdvancePrice).toPlainString());
          });
      return dto;
    }).collect(Collectors.toList());
  }

  private BigDecimal getFinalPrice(String purchaseOrderCode, FinancialVoucher originFinancialVoucher) {
    SupplierOrder supplierOrder =
        supplierOrderRepository.findFirstByCodeAndState(purchaseOrderCode, Constants.STATE_OK);
    if (supplierOrder != null) {
      return NumberUtil.sub(supplierOrder.getPrice(), supplierOrder.getCancelReturnPrice());
    }
    if (originFinancialVoucher != null && Boolean.TRUE.equals(originFinancialVoucher.getInitialOrder())) {
      return originFinancialVoucher.getOrderAmount();
    }
    return BigDecimal.ZERO;
  }

  @Override
  @DefaultSearchScheme(searchType = Constants.SEARCH_TYPE_GET_PAGE_SUPPLIER_ORDER_PAGE)
  public List<Object> getProductListByTableHeaderRef(PurchaseOrderProductTableHeaderQuery query) {
    User user = manageSecurityUtil.getSrmUserDetails().getUser();
    SearchPermission searchPermission =
        sharePermissionTypeService.getSearchPermission(user, query.getUserGroup(),
            Constants.USER_PERMISSION_SUPPLIER_ORDER, false, false, false, query.getIsViewAllOrganization());
    MergeUserPermission mergeUserPermission =
        sharePermissionTypeService.mergePermission(searchPermission, new OperatorPermission());
    List<Object> tableHeader = supplierOrderDetailDao.getProductListByTableHeaderRef(query.toQueryMap(mergeUserPermission));
    //数据转化
    List<Object> resultList =
        tableHeader.stream().filter(o -> {
          if (o instanceof String) {
            return StrUtil.isNotBlank((String) o);
          }
          return o != null;
        }).collect(Collectors.toList());
    if (StrUtil.equals(query.getFilterType(), PurchaseOrderProductFilterTypeEnum.PRE_PAY.getKey())
        || StrUtil.equals(query.getFilterType(),
        PurchaseOrderProductFilterTypeEnum.UPLOAD_CONTRACT.getKey()) || StrUtil.equals(
        query.getFilterType(), PurchaseOrderProductFilterTypeEnum.SCP.getKey())) {
      return resultList.stream().map(
          s -> StrUtil.equals(Constants.YES, (String)s) ? BooleanEnum.YES.getDescription()
              : BooleanEnum.NO.getDescription()).collect(Collectors.toList());
    }
    if (StrUtil.equals(query.getFilterType(), PurchaseOrderProductFilterTypeEnum.LOSS.getKey())) {
      return resultList.stream().map(
          s -> BooleanUtil.isTrue((Boolean) s) ? BooleanEnum.YES.getDescription()
              : BooleanEnum.NO.getDescription()).collect(Collectors.toList());
    }
    if (StrUtil.equals(query.getFilterType(), PurchaseOrderProductFilterTypeEnum.IS_WORRY_ORDER.getKey())) {
      return resultList.stream().filter(Objects::nonNull).map(
          s -> StrUtil.equals(Constants.IS_WORRY_ORDER,(String)s) ? BooleanEnum.YES.getDescription()
              : BooleanEnum.NO.getDescription()).collect(Collectors.toList());
    }
    PurchaseOrderProductFilterTypeEnum filterType = PurchaseOrderProductFilterTypeEnum.getByType(query.getFilterType());
    if (filterType == null) {
      return resultList;
    }
    switch (filterType) {
      case ORDER_STATUS:
        // 转换为列表一致的数据
        return resultList.stream().map(
            s -> SupplierOrderState.findValueByOrderStateWithoutError(Convert.toStr(s)))
            .filter(Objects::nonNull)
            .collect(Collectors.toList());
      case SUPPLIER_INVOICE:
        return resultList.stream()
            .map(s -> Constants.ORDER_SUPPLIER_INVOICE_STATE_TYPE_NEW.get(Convert.toStr(s)))
            .filter(Objects::nonNull).collect(Collectors.toList());
      case ORDER_TYPE:
      case PURCHASE_APPLY_TYPE:
      default:
        return resultList;
    }
  }

  @Override
  @DefaultSearchScheme(searchType = Constants.SEARCH_TYPE_GET_PAGE_SUPPLIER_ORDER_PAGE)
  public List<Object> getOrderListByTableHeaderRef(PurchaseOrderTableHeaderQuery query) {
    User user = manageSecurityUtil.getSrmUserDetails().getUser();
    SearchPermission searchPermission =
        sharePermissionTypeService.getSearchPermission(user, query.getUserGroup(),
            Constants.USER_PERMISSION_SUPPLIER_ORDER, false, false, false,query.getIsViewAllOrganization());
    MergeUserPermission mergeUserPermission =
        sharePermissionTypeService.mergePermission(searchPermission, new OperatorPermission());
    List<Object> tableHeader =
        supplierOrderDao.getOrderListByTableHeaderRef(query.toQueryMap(mergeUserPermission));
    //数据转化
    List<Object> resultList =
        tableHeader.stream().filter(o -> {
          if (o instanceof String) {
            return StrUtil.isNotBlank((String) o);
          }
          return o != null;
        }).collect(Collectors.toList());
    if (StrUtil.equals(query.getFilterType(), PurchaseOrderFilterTypeEnum.SCP.getKey())) {
      return resultList.stream().map(
          s -> StrUtil.equals(Constants.YES, (String)s) ? BooleanEnum.YES.getDescription()
              : BooleanEnum.NO.getDescription()).collect(Collectors.toList());
    }
    if (StrUtil.equals(query.getFilterType(), PurchaseOrderFilterTypeEnum.LOSS.getKey())) {
      return resultList.stream().map(
          s -> BooleanUtil.isTrue((Boolean) s) ? BooleanEnum.YES.getDescription()
              : BooleanEnum.NO.getDescription()).collect(Collectors.toList());
    }
    PurchaseOrderFilterTypeEnum filterType = PurchaseOrderFilterTypeEnum.getByType(query.getFilterType());
    if (filterType == null) {
      return resultList;
    }
    switch (filterType) {
      case ORDER_STATUS:
        // 转换为列表一致的数据
        return resultList.stream().map(
            s -> SupplierOrderState.findValueByOrderStateWithoutError(Convert.toStr(s)))
            .filter(Objects::nonNull)
            .collect(Collectors.toList());
      case IS_DIRECT_SHIPMENT:
        return resultList.stream().map(
            s -> BooleanUtil.isTrue((Boolean) s) ? BooleanEnum.YES.getDescription()
                : BooleanEnum.NO.getDescription()).collect(Collectors.toList());
      case SUPPLIER_INVOICE:
        return resultList.stream().map(s -> Constants.ORDER_SUPPLIER_INVOICE_STATE_TYPE_NEW.get(Convert.toStr(s)))
            .filter(Objects::nonNull)
            .collect(Collectors.toList());
      case ORDER_TYPE:
      default:
        return resultList;
    }
  }

  @Override
  @DefaultSearchScheme(searchType = Constants.GO_DOWN_ENTRY_PAGE)
  public PageResult<PurchaseOrderWarehousingDTO> warehouseWarrantPageRef(WarehouseEntryListParams param, User user) {
    String purchaseId = user.getId();
    String createMan = user.getId();
    List<String> userNameList;
    // 用户权限下采购名称集合
    if (user.getRoleList().contains(Constants.SUPPLIER_USER_ROLE_ADMIN) || user.getRoleList()
        .contains(Constants.ROLE_ADMINISTRATOR)) {
      purchaseId = StrUtil.EMPTY;
      createMan = StrUtil.EMPTY;
      userNameList = null;
    } else {
      // 用户权限下采购名称集合
      userNameList = permissionTypeService.getConcatNumUserNameList(user.getId(),
          Constants.USER_PERMISSION_SUPPLIER_ORDER,
          ListUtil.toList(Constants.SUPPLIER_USER_ROLE_ORDINARY));
    }
    PageResult<WarehousingDTO> pageResult =
        supplierOrderToFormDao.warehousingPageRef(param.toQueryMap(userNameList, purchaseId, createMan));
    List<WarehousingDTO> content = pageResult.getContent();
    List<String> detailIds =
        content.stream().map(WarehousingDTO::getDetailId).distinct().collect(Collectors.toList());
    List<InputInvoiceOrderWithDetail> inputInvoiceOrderWithDetailList =
        shareInputInvoiceService.getOrderInvoiceRelationListByDetailIdsRef(detailIds);
    List<PurchaseOrderWarehousingDTO> result = content.stream().map(warehousingDTO -> {
      PurchaseOrderWarehousingDTO purchaseOrderWarehousingDTO =
          new PurchaseOrderWarehousingDTO(warehousingDTO);
      // 根据detailId过滤出相应的inputInvoice
      List<InputInvoiceOrderWithDetail> filterInputInvoiceOrderWithDetail = inputInvoiceOrderWithDetailList.stream()
          .filter(item -> item.getDistinctDetailIds().contains(warehousingDTO.getDetailId()))
          .collect(Collectors.toList());
      List<PurchaseOrderInvoiceRelation> inputInvoiceOrders = filterInputInvoiceOrderWithDetail.stream()
          .map(item -> new PurchaseOrderInvoiceRelation(item.getInputInvoiceOrder())).distinct()
          .collect(Collectors.toList());
      purchaseOrderWarehousingDTO.setPurchaseOrderInvoiceRelationList(inputInvoiceOrders);
      return purchaseOrderWarehousingDTO;
    }).collect(Collectors.toList());
    return new PageResult<>(result, pageResult.getTotalCount(), pageResult.getTotalPages(),
        pageResult.getPageNo(), pageResult.getPageSize());
  }

  @Override
  @DefaultSearchScheme(searchType = Constants.GO_DOWN_ENTRY_PAGE)
  public PurchaseOrderWarehousingStatistics warehouseStatistics(WarehouseEntryListParams param,
      User user) {
    String purchaseId = user.getId();
    String createMan = user.getId();
    List<String> userNameList;
    // 用户权限下采购名称集合
    if (user.getRoleList().contains(Constants.SUPPLIER_USER_ROLE_ADMIN) || user.getRoleList()
        .contains(Constants.ROLE_ADMINISTRATOR)) {
      purchaseId = StrUtil.EMPTY;
      createMan = StrUtil.EMPTY;
      userNameList = null;
    } else {
      // 用户权限下采购名称集合
      userNameList = permissionTypeService.getConcatNumUserNameList(user.getId(),
          Constants.USER_PERMISSION_SUPPLIER_ORDER,
          ListUtil.toList(Constants.SUPPLIER_USER_ROLE_ORDINARY));
    }
//    List<WarehousingDTO> warehousingDTOList = supplierOrderToFormDao.warehousingStatistics(
//        param.toQueryMap(userNameList, purchaseId, createMan));
//    List<PurchaseOrderWarehousingDTO> purchaseOrderWarehousingDTO =
//        warehousingDTOList.stream().map(PurchaseOrderWarehousingDTO::new).collect(
//            Collectors.toList());
//    BigDecimal stockInputQty = purchaseOrderWarehousingDTO.stream()
//        .map(item -> Convert.toBigDecimal(item.getStockInputQty())).filter(Objects::nonNull)
//        .reduce(BigDecimal.ZERO, BigDecimal::add);
//    BigDecimal stockOutputQty = purchaseOrderWarehousingDTO.stream()
//        .map(item -> Convert.toBigDecimal(item.getStockOutputQty())).filter(Objects::nonNull)
//        .reduce(BigDecimal.ZERO, BigDecimal::add);
//    BigDecimal invoiceNum = purchaseOrderWarehousingDTO.stream()
//        .map(item -> Convert.toBigDecimal(item.getInvoiceNum())).filter(Objects::nonNull)
//        .reduce(BigDecimal.ZERO, BigDecimal::add);
//    BigDecimal invoicedAmount = purchaseOrderWarehousingDTO.stream()
//        .map(item -> Convert.toBigDecimal(item.getInvoicedAmount())).filter(Objects::nonNull)
//        .reduce(BigDecimal.ZERO, BigDecimal::add);
//    BigDecimal unInvoiceNum = purchaseOrderWarehousingDTO.stream()
//        .map(item -> Convert.toBigDecimal(item.getUnInvoiceNum())).filter(Objects::nonNull)
//        .reduce(BigDecimal.ZERO, BigDecimal::add);
//    BigDecimal unInvoicedAmount = purchaseOrderWarehousingDTO.stream()
//        .map(item -> Convert.toBigDecimal(item.getUnInvoicedAmount())).filter(Objects::nonNull)
//        .reduce(BigDecimal.ZERO, BigDecimal::add);
//    BigDecimal totalPriceAndTax = purchaseOrderWarehousingDTO.stream()
//        .map(item -> Convert.toBigDecimal(item.getTotalPriceAndTax())).filter(Objects::nonNull)
//        .reduce(BigDecimal.ZERO, BigDecimal::add);
//    BigDecimal nakedTotalPrice = purchaseOrderWarehousingDTO.stream()
//        .map(item -> Convert.toBigDecimal(item.getNakedTotalPrice())).filter(Objects::nonNull)
//        .reduce(BigDecimal.ZERO, BigDecimal::add);
//    PurchaseOrderWarehousingStatistics statistics = new PurchaseOrderWarehousingStatistics();
//    statistics.setStockInputQty(stockInputQty);
//    statistics.setStockOutputQty(stockOutputQty);
//    statistics.setInvoiceNum(invoiceNum);
//    statistics.setInvoicedAmount(invoicedAmount);
//    statistics.setUnInvoiceNum(unInvoiceNum);
//    statistics.setUnInvoicedAmount(unInvoicedAmount);
//    statistics.setTotalPriceAndTax(totalPriceAndTax);
//    statistics.setNakedTotalPrice(nakedTotalPrice);
    return supplierOrderToFormDao.warehousingStatistics2(param.toQueryMap(userNameList, purchaseId, createMan));
  }

  @Override
  @DefaultSearchScheme(searchType = Constants.CANCELLATION_FORM_PAGE)
  public PageResult<RetreatWarehouseDTO> outBoundDeliveryPageRef(OutBoundDeliveryPrams param,
      User user) {
    String purchaseId = user.getId();
    String createMan = user.getId();
    List<String> userNameList;
    // 用户权限下采购名称集合
    if (user.getRoleList().contains(Constants.SUPPLIER_USER_ROLE_ADMIN) || user.getRoleList().contains(Constants.ROLE_ADMINISTRATOR)) {
      purchaseId = StrUtil.EMPTY;
      createMan = StrUtil.EMPTY;
      userNameList = null;
    } else {
      // 用户权限下采购名称集合
      userNameList =
          permissionTypeService.getConcatNumUserNameList(
              user.getId(),
              Constants.USER_PERMISSION_SUPPLIER_ORDER,
              ListUtil.toList(Constants.SUPPLIER_USER_ROLE_ORDINARY));
    }
    PageResult<RetreatWarehousePageDTO> pageResult =
        supplierOrderToFormDao.outBoundDeliveryPageRef(param.toQueryMap(userNameList, purchaseId, createMan));
    List<RetreatWarehousePageDTO> content = pageResult.getContent();
    List<String> detailIds =
        content.stream().map(RetreatWarehousePageDTO::getDetailId).distinct().collect(Collectors.toList());
    List<InputInvoiceOrderWithDetail> inputInvoiceOrderWithDetailList =
        shareInputInvoiceService.getOrderInvoiceRelationListByDetailIdsRef(detailIds);
    List<RetreatWarehouseDTO> result = content.stream().map(returnDto -> {
      RetreatWarehouseDTO retreatWarehouseDTO = new RetreatWarehouseDTO(returnDto);
      // 根据detailId过滤出相应的inputInvoice
      List<InputInvoiceOrderWithDetail> filterInputInvoiceOrderWithDetail = inputInvoiceOrderWithDetailList.stream()
          .filter(item -> item.getDistinctDetailIds().contains(returnDto.getDetailId()))
          .collect(Collectors.toList());
      List<PurchaseOrderInvoiceRelation> inputInvoiceOrders = filterInputInvoiceOrderWithDetail.stream()
          .map(item -> new PurchaseOrderInvoiceRelation(item.getInputInvoiceOrder())).distinct()
          .collect(Collectors.toList());
      retreatWarehouseDTO.setPurchaseOrderInvoiceRelationList(inputInvoiceOrders);
      return retreatWarehouseDTO;
    }).collect(Collectors.toList());
    return new PageResult<>(result, pageResult.getTotalCount(), pageResult.getTotalPages(),
        pageResult.getPageNo(), pageResult.getPageSize());
  }

  @Override
  @DefaultSearchScheme(searchType = Constants.GO_DOWN_ENTRY_PAGE)
  public PurchaseOrderOutBoundDeliveryStatistics outBoundDeliveryStatistics(
      OutBoundDeliveryPrams param, User user) {
    String purchaseId = user.getId();
    String createMan = user.getId();
    List<String> userNameList;
    // 用户权限下采购名称集合
    if (user.getRoleList().contains(Constants.SUPPLIER_USER_ROLE_ADMIN) || user.getRoleList().contains(Constants.ROLE_ADMINISTRATOR)) {
      purchaseId = StrUtil.EMPTY;
      createMan = StrUtil.EMPTY;
      userNameList = null;
    } else {
      // 用户权限下采购名称集合
      userNameList =
          permissionTypeService.getConcatNumUserNameList(
              user.getId(),
              Constants.USER_PERMISSION_SUPPLIER_ORDER,
              ListUtil.toList(Constants.SUPPLIER_USER_ROLE_ORDINARY));
    }
//    List<RetreatWarehousePageDTO> outBoundDeliveryList =
//        supplierOrderToFormDao.outBoundDeliveryStatistics(param.toQueryMap(userNameList, purchaseId, createMan));
//    List<RetreatWarehouseDTO> dtoList = outBoundDeliveryList.stream().map(item -> {
//      RetreatWarehouseDTO retreatWarehouseDTO = new RetreatWarehouseDTO(item);
//      return retreatWarehouseDTO;
//    }).collect(Collectors.toList());
//    BigDecimal stockOutputQty = dtoList.stream()
//        .map(RetreatWarehouseDTO::getStockOutputQty).filter(Objects::nonNull)
//        .reduce(BigDecimal.ZERO, BigDecimal::add);
//    BigDecimal stockInputQty = dtoList.stream()
//        .map(item -> Convert.toBigDecimal(item.getStockInputQty()))
//            .filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
//    BigDecimal redInvoiceQty = dtoList.stream()
//        .map(RetreatWarehouseDTO::getRedInvoiceQty).filter(Objects::nonNull)
//        .reduce(BigDecimal.ZERO, BigDecimal::add);
//    BigDecimal returnPrice = dtoList.stream()
//        .map(RetreatWarehouseDTO::getReturnPrice).filter(Objects::nonNull)
//        .reduce(BigDecimal.ZERO, BigDecimal::add);
//    BigDecimal returnAmount =
//        dtoList.stream().map(RetreatWarehouseDTO::getReturnAmount).filter(Objects::nonNull)
//            .reduce(BigDecimal.ZERO, BigDecimal::add);
//    PurchaseOrderOutBoundDeliveryStatistics result = new PurchaseOrderOutBoundDeliveryStatistics();
//    result.setStockOutputQty(stockOutputQty);
//    result.setStockInputQty(stockInputQty);
//    result.setRedInvoiceQty(redInvoiceQty);
//    result.setReturnPrice(returnPrice);
//    result.setReturnAmount(returnAmount);
//    return result;
    return supplierOrderToFormDao.outBoundDeliveryStatistics2(param.toQueryMap(userNameList, purchaseId, createMan));
  }


  @Override
  @DefaultSearchScheme(searchType = Constants.CANCELLATION_FORM_PAGE)
  public void exportOutBoundDelivery(User user, OutBoundDeliveryPrams outBoundDeliveryPrams) {
    checkOrderExportPermission(user.getId());
    outBoundDeliveryPrams.setPageNo(1);
    outBoundDeliveryPrams.setPageSize(Integer.MAX_VALUE);
    String purchaseId = user.getId();
    String createMan = user.getId();
    List<String> userNameList;
    // 用户权限下采购名称集合
    if (user.getRoleList().contains(Constants.SUPPLIER_USER_ROLE_ADMIN) || user.getRoleList().contains(Constants.ROLE_ADMINISTRATOR)) {
      purchaseId = StrUtil.EMPTY;
      createMan = StrUtil.EMPTY;
      userNameList = null;
    } else {
      // 用户权限下采购名称集合
      userNameList =
          permissionTypeService.getConcatNumUserNameList(
              user.getId(),
              Constants.USER_PERMISSION_SUPPLIER_ORDER,
              ListUtil.toList(Constants.SUPPLIER_USER_ROLE_ORDINARY));
    }
    Map<String, Object> queryMap =
        outBoundDeliveryPrams.toQueryMap(userNameList, purchaseId, createMan);
    queryMap.put("version", ShardingContext.getVersion());
    Mission mission =
        missionService.createMission(user, "导出-退库单信息", Constants.PLATFORM_TYPE_AFTER, null, null);
    missionDispatcher.doDispatch(
        mission.getId(),
        JSON.toJSONString(queryMap),
        MissionTypeEnum.BATCH_TASK_EXPORT_RETURN_STORAGE_ORDER);
  }

  @Override
  @DefaultSearchScheme(searchType = Constants.GO_DOWN_ENTRY_PAGE)
  public void exportWarehouseWarrant(User user, WarehouseEntryListParams warehouseEntryListParams) {
    checkOrderExportPermission(user.getId());
    warehouseEntryListParams.setPageNo(1);
    warehouseEntryListParams.setPageSize(Integer.MAX_VALUE);
    String purchaseId = user.getId();
    String createMan = user.getId();
    List<String> userNameList;
    // 用户权限下采购名称集合
    if (user.getRoleList().contains(Constants.SUPPLIER_USER_ROLE_ADMIN) || user.getRoleList()
        .contains(Constants.ROLE_ADMINISTRATOR)) {
      purchaseId = StrUtil.EMPTY;
      createMan = StrUtil.EMPTY;
      userNameList = null;
    } else {
      // 用户权限下采购名称集合
      userNameList = permissionTypeService.getConcatNumUserNameList(user.getId(),
          Constants.USER_PERMISSION_SUPPLIER_ORDER,
          ListUtil.toList(Constants.SUPPLIER_USER_ROLE_ORDINARY));
    }
    Map<String, Object> queryMap =
        warehouseEntryListParams.toQueryMap(userNameList, purchaseId, createMan);
    queryMap.put("version", ShardingContext.getVersion());
    Mission mission =
        missionService.createMission(user, "导出-入库单信息", Constants.PLATFORM_TYPE_AFTER, null, null);
    //    batchTaskMqSender.toHandleBatchTask(mission.getId(),
    // JSON.toJSONString(exportWarehouseWarrantParams),
    //        Constants_Batch.BATCH_TASK_EXPORT_STORAGE_ORDER);
    missionDispatcher.doDispatch(
        mission.getId(),
        JSON.toJSONString(queryMap),
        MissionTypeEnum.BATCH_TASK_EXPORT_STORAGE_ORDER);
  }

  private void checkOrderExportPermission(String id) {
    String permissionCode = userService.getPermissionByType(id,
        Constants.USER_PERMISSION_EXPORT_WAREHOUSE_RETURN);
    permissionCode = StrUtil.blankToDefault(permissionCode,Constants.NOT_EXPORT_IMPORT_KEY);
    if (StrUtil.equals(permissionCode,Constants.NOT_EXPORT_IMPORT_KEY)) {
      throw new CheckException("您没有导出入库单/退库单的权限！");
    }

  }

  public BigDecimal getMaxAdvancePrice(String purchaseOrderCode, FinancialVoucher originFinancialVoucher) {
    Objects.requireNonNull(purchaseOrderCode);
    BigDecimal actualPrepaidAmount = getActualPrepaidAmount(purchaseOrderCode);
    BigDecimal amountPayable = getAmountPayable(purchaseOrderCode);
    BigDecimal finalPrice = getFinalPrice(purchaseOrderCode, originFinancialVoucher);
    return NumberUtil.sub(finalPrice, actualPrepaidAmount, amountPayable);
  }

  @Override
  @Transactional(rollbackFor = Exception.class)
  public void updateSupplierOrderBaseInfo(UpdateSupplierOrderBaseInfoDTO params) {
    String id = params.getId();
    SupplierOrder supplierOrder =
        get(id, () -> CheckException.noFindException(SupplierOrder.class, id));
    List<String> updateState = ListUtil.toList(SupplierOrderState.WAIT.getOrderState(),
        SupplierOrderState.IN_PROGRESS.getOrderState(),SupplierOrderState.COMPLETE.getOrderState());
    if (!updateState.contains(supplierOrder.getOrderState())) {
      throw new CheckException("该状态下订单无法修改基础信息");
    }
    // 限定标准采购和寄售采购订单才能更新基础信息
    if (!supplierOrder.getOrderType().equals(PurchaseOrderTypeEnum.SAP.getKey())
        && !supplierOrder.getOrderType().equals(PurchaseOrderTypeEnum.CONSIGNMENT.getKey())
        && !supplierOrder.getOrderType().equals(PurchaseOrderTypeEnum.RETURN_EXCHANGE.getKey())
        && !supplierOrder.getOrderType().equals(PurchaseOrderTypeEnum.INITIAL_PURCHASE.getKey())
    ) {
      throw new CheckException("只有标准采购和寄售采购和退换货订单和期初订单才能更新基础信息");
    }
    // 是否更新直发
    boolean updateDirectShipment = ObjectUtil.notEqual(supplierOrder.getDirectShipment(), params.getDirectShipment());
    // 是否更新备注，备注特殊处理，为null时置空
    boolean updateMark =
        ObjectUtil.notEqual(StrUtil.emptyIfNull(supplierOrder.getMark()), params.getMark());
    // 如果是退换货订单，updateMark默认为false
    if (supplierOrder.getOrderType().equals(PurchaseOrderTypeEnum.RETURN_EXCHANGE.getKey())) {
      updateMark = false;
    }
    // 是否更新采购部门
    boolean updatePurchaseDept = ObjectUtil.notEqual(supplierOrder.getPurchaseDept(), params.getPurchaseDept());
    // 是否更新采购部门编码
    boolean updatePurchaseDeptCode = ObjectUtil.notEqual(supplierOrder.getPurchaseDeptCode(), params.getPurchaseDeptCode());
    // 是否更新采购人
    boolean updatePurchaseMan = ObjectUtil.notEqual(supplierOrder.getPurchaseMan(), params.getPurchaseMan());
    // 是否更新采购人编码
    boolean updatePurchaseCode = ObjectUtil.notEqual(supplierOrder.getPurchaseCode(), params.getPurchaseCode());
    // 是否调用sap
    boolean callSap = updateDirectShipment || updateMark || updatePurchaseDept || updatePurchaseDeptCode || updatePurchaseMan || updatePurchaseCode;
    // 只要更新了直发、备注、采购部门、采购部门编码、采购人、采购人编码，就需要调用sap
    supplierOrder.setMark(params.getMark());
    supplierOrder.setDirectShipment(params.getDirectShipment());
    supplierOrder.setInvoiceType(params.getInvoiceType());
    supplierOrder.setPurchaseDept(params.getPurchaseDept());
    supplierOrder.setPurchaseDeptCode(params.getPurchaseDeptCode());
    supplierOrder.setPurchaseMan(params.getPurchaseMan());
    supplierOrder.setPurchaseCode(params.getPurchaseCode());
    save(supplierOrder);
    if (updateDirectShipment) {
      Optional.ofNullable(supplierOrderToFormService.getDetailedBySupplierOrderId(supplierOrder.getId()))
          .ifPresent(supplierOrderToForm -> {
            String formId = supplierOrderToForm.getId();
            supplierOrderDetailService.getByOrderToFormId(formId).forEach(
                detail->{
                  if (BooleanUtil.isTrue(supplierOrder.getDirectShipment())) {
                    detail.setWarehouse(WarehouseEnum.HAI_NING_DIRECT_SALES.getCode());
                    detail.setWarehouseName(WarehouseEnum.HAI_NING_DIRECT_SALES.getName());
                  }else{
                    detail.setWarehouse(WarehouseEnum.HAI_NING_FINISHED_PRODUCTS.getCode());
                    detail.setWarehouseName(WarehouseEnum.HAI_NING_FINISHED_PRODUCTS.getName());
                  }
                  supplierOrderDetailService.save(detail);
                }
            );
          });
    }
    if (callSap) {
      // 项目编号
      List<ITEMDTO> itemdtos = new ArrayList<>();
      Optional.ofNullable(supplierOrderToFormService.getDetailedBySupplierOrderId(supplierOrder.getId()))
          .ifPresent(supplierOrderToForm -> {
            String formId = supplierOrderToForm.getId();
            supplierOrderDetailService.getByOrderToFormId(formId).forEach(
                detail->{
                  ITEMDTO itemdto = new ITEMDTO();
                  SupplierOrderProduct supplierOrderProduct = detail.getSupplierOrderProduct();
                  // 采购订单行项目
                  itemdto.setEbelp(detail.getSortNum().toString());
                  // 物料编码
                  itemdto.setMatnr(supplierOrderProduct.getCode());
                  itemdto.setTxz01(supplierOrderProduct.getName());
                  // 仓库编码
                  itemdto.setLgort(detail.getWarehouse());
                  itemdto.setYfgys(Constants_Sap.DEFAULT_ATTRIBUTE_VALUES);
                  itemdto.setGsgys(Constants_Sap.DEFAULT_ATTRIBUTE_VALUES);
                  itemdtos.add(itemdto);
                  List<WWDTO> wwdtos = new ArrayList<>();
                  List<SupplierOrderDetail> entrustDetailList =
                      supplierOrderDetailRepository.findAllByEntrustDetailIdAndState(id,
                          Constants.STATE_OK);
                  if (CollUtil.isNotEmpty(entrustDetailList)) {
                    for (SupplierOrderDetail supplierOrderDetail : entrustDetailList) {
                      SupplierOrderProduct supplierOrderProduct1 =
                          supplierOrderDetail.getSupplierOrderProduct();
                      WWDTO wwdto = new WWDTO();
                      wwdto.setEbelp(supplierOrderDetail.getSortNum().toString());
                      wwdto.setWlzj(supplierOrderProduct1.getCode());
                      wwdtos.add(wwdto);
                    }
                  }
                  itemdto.setWw(wwdtos);
                }
            );
          });
      UpdatePurchaseOrderSapParam param = new UpdatePurchaseOrderSapParam();
      UpdatePurchaseOrderDATADTO datadto = new UpdatePurchaseOrderDATADTO();
      UpdatePurchaseOrderHEADDTO headdto = new UpdatePurchaseOrderHEADDTO();
      // 采购订单号
      headdto.setEbeln(supplierOrder.getCode());
      headdto.setEkgrp(supplierOrder.getPurchaseDeptCode());
   /*   // todo 给产品测试用，暂时拿掉
      headdto.setZttwb(srmConfig.getContractFileH5Url()+"?id="+supplierOrder.getId());*/
      headdto.setZttwb("【点击查看合同："+srmConfig.getContractFileH5Url()+"?id="+supplierOrder.getId()+"】"+supplierOrder.getMark());
      if (supplierOrder.getOrderType().equals(PurchaseOrderTypeEnum.RETURN_EXCHANGE.getKey())) {
        // 退换货订单不覆盖
        headdto.setZttwb(Constants_Sap.DEFAULT_ATTRIBUTE_VALUES);
      }
      headdto.setAfnam(supplierOrder.getPurchaseMan().substring(4,
          supplierOrder.getPurchaseMan().length()));
      headdto.setZsp("X");
      headdto.setZcdoa("N");
      headdto.setBednr(supplierOrder.getPurchaseCode());
      headdto.setItem(itemdtos);
      datadto.setHead(headdto);
      param.setData(datadto);
      sapService.sapPurchaseOrderWithAlarm(param, "");
    }
  }

  @Override
  public void updateOrderCode(String code, String supplierOpenInvoiceState) {
    if (StrUtil.isBlank(code)) {
      throw new CheckException("采购订单号不能为空！");
    }
    if (code.length() > 1000) {
      throw new CheckException("输入采购订单号超过最大长度限制！");
    }
    if (StrUtil.isBlank(supplierOpenInvoiceState)) {
      throw new CheckException("供应商开票状态不能为空！");
    }
    if (!Constants.ORDER_SUPPLIER_INVOICE_STATE_TYPE_NEW.containsKey(supplierOpenInvoiceState)) {
      throw new CheckException("供应商开票状态不合法！");
    }
    supplierOrderDetailDao.updateOrderCode(code, supplierOpenInvoiceState);
  }

  @SneakyThrows
  @Override
  public void importWarehouseInvoiceNum(MultipartFile file, String userId) {
    Assert.notNull(file);
    Assert.notEmpty(userId);
    User user = userService.getUserById(userId);
    Map<String, Object> params = new HashMap<>();
    params.put("version", ShardingContext.getVersion());
    params.put("userId", userId);
    String savePath = importExcelUtil.saveExcel(file);
    Mission mission =
        missionService.createMission(
            user, "导入-入库单已开票数量", Constants.PLATFORM_TYPE_AFTER, savePath, file.getOriginalFilename());
    batchTaskMqSender.toHandleBatchTask(
        mission.getId(), JSON.toJSONString(params), Constants_Batch.BATCH_TASK_IMPORT_WAREHOUSE_INVOICE_NUM);
  }
  @SneakyThrows
  @Override
  public void importReturnRedInvoiceNum(MultipartFile file, String userId) {
    Assert.notNull(file);
    Assert.notEmpty(userId);
    User user = userService.getUserById(userId);
    String savePath = importExcelUtil.saveExcel(file);
    Map<String, Object> params = new HashMap<>();
    params.put("version", ShardingContext.getVersion());
    params.put("userId", userId);
    Mission mission =
        missionService.createMission(
            user, "导入-导入退库单已开红票数量", Constants.PLATFORM_TYPE_AFTER, savePath, file.getOriginalFilename());
    batchTaskMqSender.toHandleBatchTask(
        mission.getId(), JSON.toJSONString(params), Constants_Batch.BATCH_TASK_IMPORT_RETURN_RED_INVOICE_NUM);
  }

  @Override
  @Transactional(rollbackFor = Exception.class)
  public void deleteOutBound(String id, String sapRwoId) {
    //退库单对应的订单明细
    SupplierOrderDetail supplierOrderDetail = supplierOrderDetailRepository.findById(sapRwoId)
        .orElseThrow(() -> CheckException.noFindException(SupplierOrderDetail.class, sapRwoId));
    //退库单对应的订单
    SupplierOrder supplierOrder = supplierOrderRepository.findById(id)
        .orElseThrow(() -> CheckException.noFindException(SupplierOrder.class, id));
    String inWareHouseId = supplierOrderDetail.getInWareHouseId();
    SupplierOrderDetail detailed = supplierOrderDetail.getDetailed();
    if (detailed != null) {
      //对应的入库单明细
      SupplierOrderDetail wareHouseDetailed =
          supplierOrderDetailService.getFormIdAndDetailedId(inWareHouseId, detailed.getId());
      if (wareHouseDetailed != null) {
        //退库数量减去本次删除的退库数量
        wareHouseDetailed.setReturnQty(NumberUtil.sub(wareHouseDetailed.getReturnQty(),
            supplierOrderDetail.getStockOutputQty()));
        wareHouseDetailed.setStockOutputQty(NumberUtil.sub(wareHouseDetailed.getStockOutputQty(),
            supplierOrderDetail.getStockOutputQty()));
        //加回可开票数量=删除的退库数量
        wareHouseDetailed.setInvoicableNum(
            NumberUtil.add(NumberUtil.toBigDecimal(wareHouseDetailed.getInvoicableNum()),
                supplierOrderDetail.getStockOutputQty()));
        supplierOrderDetailRepository.save(wareHouseDetailed);
      }
      // 实际交货数量（入库数量-退库数量）
      detailed.setSettleQty(
          NumberUtil.sub(detailed.getStockInputQty(), detailed.getStockOutputQty()));
      detailed.setStockOutputQty(NumberUtil.sub(detailed.getStockOutputQty(),supplierOrderDetail.getStockOutputQty()));
      // 总入库数量增加
      supplierOrder.setTotalStockInputQty(NumberUtil.add(supplierOrder.getTotalStockInputQty(),
          supplierOrderDetail.getStockOutputQty()));
      supplierOrder.setCancelReturnPrice(NumberUtil.sub(supplierOrder.getCancelReturnPrice(),
          NumberUtil.mul(supplierOrderDetail.getStockOutputQty(),
              supplierOrderDetail.getReturnPrice())));
      supplierOrder.setFinalPrice(NumberUtil.add(NumberUtil.toBigDecimal(supplierOrder.getFinalPrice()),
          NumberUtil.mul(supplierOrderDetail.getStockOutputQty(),
              supplierOrderDetail.getReturnPrice())));
      //退货状态
      supplierOrder.setOrderReturnState(false);
      supplierOrderDetail.setReturnQty(BigDecimal.ZERO);
      supplierOrderDetailRepository.save(supplierOrderDetail);
      // 更新入库进度
      setStockProgress(supplierOrder);
      // 待入库数量（已经发货的数量 - 采购入库数量 - 退库数量）
      detailed.setWaitStockInputQty(NumberUtil.sub(detailed.getShipQty(), detailed.getStockInputQty(),
          detailed.getStockOutputQty()));
      supplierOrderDetailRepository.save(detailed);
    }
    List<SupplierOrderDetail> orderDetailList =
        supplierOrderDetailRepository.findByOrderToFormIdAndState(
            supplierOrderDetail.getOrderToFormId(), Constants.STATE_OK);
    supplierOrderDetail.setState(Constants.STATE_NO);
    supplierOrderDetailRepository.save(supplierOrderDetail);
    if (orderDetailList.size() <= 1) {
      supplierOrderToFormRepository.findById(supplierOrderDetail.getOrderToFormId())
          .ifPresent(supplierOrderToForm -> {
            supplierOrderToForm.setState(Constants.STATE_NO);
            supplierOrderToFormRepository.save(supplierOrderToForm);
          });
    }
  }

  @Override
  @Transactional(rollbackFor = Exception.class)
  public void submitInternalRemark(SupplierOrderInternalRemarkParam param, User user) {
    String supplierOrderId = param.getSupplierOrderId();
    SupplierOrder supplierOrder = supplierOrderRepository.findById(supplierOrderId)
        .orElseThrow(() -> new CheckException("订单" + supplierOrderId + "不存在"));
    supplierOrder.setInternalRemark(StrUtil.trimToEmpty(param.getInternalRemark()));
    supplierOrder.setInternalRemarkCreator(user.getRealName());
    supplierOrder.setInternalRemarkUpdateTime(new Date().getTime());
    supplierOrderDao.save(supplierOrder);
  }

  @Override
  @Transactional(rollbackFor = Exception.class)
  public void handleInboundErrorData(String code, String orderState, String stockProgress,
      BigDecimal finalPrice) {
    SupplierOrder supplierOrder = Optional.ofNullable(
            supplierOrderRepository.findFirstByCodeAndState(code, Constants.STATE_OK))
        .orElseThrow(() -> new CheckException("订单号" + code + "不存在"));
    // 物料明细里的所有物料行都变为交货完成，入库数量和实际交货数量都变为订货数量，剩余入库数量都变成0
    List<String> detailedFormIds =
        supplierOrderToFormRepository.findBySupplierOrderIdAndTypeAndState(supplierOrder.getId(),
                SupplierOrderFormType.DETAILED.getKey(), Constants.STATE_OK).stream()
            .map(SupplierOrderToForm::getId).collect(Collectors.toList());
    List<SupplierOrderDetail> allDetailsByOrderToFormId =
        supplierOrderDetailRepository.findAllByOrderToFormIdInAndState(detailedFormIds,
            Constants.STATE_OK);
    allDetailsByOrderToFormId.forEach(item -> {
      item.setStockInputQty(item.getNum());
      item.setSettleQty(item.getNum());
      item.setRemainQty(BigDecimal.ZERO);
      supplierOrderDetailRepository.save(item);
    });
    List<String> warehousingFormIds =
        supplierOrderToFormRepository.findBySupplierOrderIdAndTypeAndState(supplierOrder.getId(),
                SupplierOrderFormType.WAREHOUSING.getKey(), Constants.STATE_OK).stream()
            .map(SupplierOrderToForm::getId).collect(Collectors.toList());
    List<SupplierOrderDetail> allWarehousingDetailsByOrderToFormId =
        supplierOrderDetailRepository.findAllByOrderToFormIdInAndState(warehousingFormIds,
            Constants.STATE_OK);
    allWarehousingDetailsByOrderToFormId.forEach(item -> {
      item.setInvoicedNum(item.getStockInputQty());
      supplierOrderDetailRepository.save(item);
    });
    SupplierOrderState orderStateEnum = SupplierOrderState.findValueByOrderState(
        StrUtil.blankToDefault(orderState, SupplierOrderState.COMPLETE.getOrderState()));
    //订单上的入库进度更新为11/11，状态变为已完成，实际订货金额为480.74，已开票进度为0/11
    supplierOrder.setOrderState(orderStateEnum.getOrderState());
    supplierOrder.makeAndSetStockProgress(supplierOrder.getTotalStockInputQty(), supplierOrder.getTotalStockInputQty(), stockProgress);
    supplierOrder.setFinalPrice(finalPrice == null ? supplierOrder.getPrice() : finalPrice);
    supplierOrderRepository.save(supplierOrder);
  }

  @Override
  @DefaultSearchScheme(searchType = Constants.GO_DOWN_ENTRY_PAGE)
  public Long getExportWarehouseWarrantCount(WarehouseEntryListParams param, User user) {
    List<String> ids = param.getIds();
    if (CollUtil.isNotEmpty(ids)) {
      return (long) ids.size();
    }
    PageResult<PurchaseOrderWarehousingDTO> pageResult =
        this.warehouseWarrantPageRef(param, user);
    return pageResult.getTotalCount();
  }

  @Override
  @DefaultSearchScheme(searchType = Constants.CANCELLATION_FORM_PAGE)
  public Long getExportOutBoundCount(OutBoundDeliveryPrams param, User user) {
    List<String> ids = param.getIds();
    if (CollUtil.isNotEmpty(ids)) {
      return (long) ids.size();
    }
    PageResult<RetreatWarehouseDTO> pageResult =
        this.outBoundDeliveryPageRef(param, user);
    return pageResult.getTotalCount();
  }

  @Override
  @Transactional(rollbackFor = Exception.class)
  public void addScreeningCondition(UserScreeningConditionParam param) {
    User user = userService.get(param.getUserId(),
        () -> CheckException.noFindException(User.class, param.getUserId()));
    user.setScreening(param.getScreening());
    userService.save(user);
  }

  @Deprecated
  @Override
  public UserScreeningConditionParam getScreeningConditionByUser(String userId) {
    User user = userService.get(userId,
        () -> CheckException.noFindException(User.class, userId));
    UserScreeningConditionParam conditionParam = new UserScreeningConditionParam();
    conditionParam.setUserId(user.getId());
    conditionParam.setScreening(user.getScreening());
    return conditionParam;
  }

  @Override
  @Transactional(rollbackFor = Exception.class)
  public void updateWarehouseLogistics(UpdateWarehouseLogisticsParam param) {
    SupplierOrder supplierOrder = supplierOrderRepository.findById(param.getOrderId())
        .orElseThrow(() -> CheckException.noFindException(SupplierOrder.class, param.getOrderId()));
    SupplierOrderToForm supplierOrderToForm =
        supplierOrderToFormRepository.findById(param.getOrderToFormId()).orElseThrow(
            () -> CheckException.noFindException(SupplierOrderToForm.class,
                param.getOrderToFormId()));
    supplierOrderToForm.setLogisticsCompany(param.getLogisticsCompany());
    supplierOrderToForm.setLogisticsCode(param.getLogisticsCode());
    supplierOrderToForm.setTrackNum(param.getTrackNum());
    supplierOrderToFormRepository.save(supplierOrderToForm);
    List<SupplierOrderDetail> supplierOrderDetailList =
        supplierOrderDetailRepository.findByOrderToFormIdAndState(param.getOrderToFormId(),
            Constants.STATE_OK);
    //更新入库单信息，并且调用SAPMM031接口
    updateReceiptVoucherSync(supplierOrder,supplierOrderToForm,supplierOrderDetailList);
  }

  private void updateReceiptVoucherSync(SupplierOrder supplierOrder,
      SupplierOrderToForm supplierOrderToForm, List<SupplierOrderDetail> supplierOrderDetails) {
    supplierOrderDetails.forEach(supplierOrderDetail -> {
      Optional<SupplierOrderDetail> supplierOrderDetail1 =
          supplierOrderDetailRepository.findById(supplierOrderDetail.getDetailedId());
      supplierOrderDetail1.ifPresent(supplierOrderDetail::setDetailed);
    });
    ReceiptVoucherSynchronizationParam param =
        ReceiptVoucherSynchronizationParam.updateReceiptParam(supplierOrder, supplierOrderToForm,
            supplierOrderDetails);
    ReceiptVoucherSynchronizationResult result;
    try {
      result = sapService.sapMaterialVoucherWithLockGroup(param);
    } catch (CheckException e){
      throw e;
    }catch (Exception e) {
      log.error(ExceptionUtil.stacktraceToString(e));
      throw new CheckException("SAP系统响应异常，请联系管理员！");
    }
    List<ReturnMessage> returnMessages = result.getReturnMessages();
    for (ReturnMessage returnMessage : returnMessages) {
      String lineItem = returnMessage.getLineItem();
      String documentNumber = returnMessage.getDocumentNumber();
      String purchaseOrderLineItems = returnMessage.getPurchaseOrderLineItems();
      supplierOrderToForm.setProductVoucher(documentNumber);
      if (StrUtil.isNotBlank(purchaseOrderLineItems)) {
        Integer purchaseOrderLineItems1 = Integer.valueOf(purchaseOrderLineItems);
        Optional<SupplierOrderDetail> first =
            supplierOrderDetails.stream().filter(supplierOrderDetail -> {
              return Objects.equals(supplierOrderDetail.getSortNum(), purchaseOrderLineItems1);
            }).findFirst();
        first.ifPresent(supplierOrderDetail -> {
          supplierOrderDetail.setBatchNo(returnMessage.getCharge());
          supplierOrderDetail.setSapRowId(lineItem);
          supplierOrderDetailRepository.save(supplierOrderDetail);
        });
      }
    }
    supplierOrderToFormRepository.save(supplierOrderToForm);
  }
  @Override
  public String getContractAttachment(String id) {
    File file = fileDao.getFirstFileByRIdAndRType(id, Constants_FileRelationType.ORDER_CONTRACT);
    if (file == null || StrUtil.isBlank(file.getUrl())) {
      throw new CheckException("该订单没有上传合同附件");
    }
    return srmConfig.getUploadUrl() + file.getUrl();
  }

  @Override
  public void importOutBoundDelivery(MultipartFile file, User user) {
    String savePath = null;
    try {
      savePath = importExcelUtil.saveExcel(file);
    } catch (IOException e) {
      throw new RuntimeException(e);
    }
    Mission mission = missionService.createMission(user,
        MissionTypeEnum.BATCH_TASK_IMPORT_OUTBOUND_DELIVERY.getTypeName(), Constants.PLATFORM_TYPE_AFTER,
        null, null);
    Map<String, Object> mapParam = new HashMap<>(4);
    mapParam.put("userId", user.getId());
    mapParam.put("filePath", savePath);
    mapParam.put("fileName", file.getOriginalFilename());
    mapParam.put("version", ShardingContext.getVersion());
    missionDispatcher.doDispatch(
        mission.getId(),
        JSON.toJSONString(mapParam),
        MissionTypeEnum.BATCH_TASK_IMPORT_OUTBOUND_DELIVERY);
  }

  @Override
  public PurchaseOrderLargeTicketInfoDTO getLargeTicketInfoByProjectNoList(String  projectNos) {
    // 组装参数
    PlatformLargeTicketParam param = new PlatformLargeTicketParam();
    param.setProjectNoList(Arrays.asList(projectNos.split(",")));
    List<PlatformLargeTicketProjectDTO> list = omsService.getLargeTicketInfoByProjectNoList(param);
    // 开票状态,如果全部开票那么状态为已开票，存在部分开票状态为部分开票，未开票状态为未开票
    // 开票状态,如果全部开票那么状态为已开票，存在部分开票状态为部分开票，未开票状态为未开票
    String  invoiceStatus = Constants_OmsLargeTicket.LARGE_TICKET_INVOICE_STATUS_WKP;
    List<String> invoiceStatusList = list.stream().map(PlatformLargeTicketProjectDTO::getInvoiceStatus).collect(Collectors.toList());
    // 如果已开票的数量等于集合长度那么为已开票
    if(invoiceStatusList.stream().filter(x -> {
      return Objects.equals(Constants_OmsLargeTicket.LARGE_TICKET_INVOICE_STATUS_YKP, x);
    }).count() == invoiceStatusList.size()){
      invoiceStatus = Constants_OmsLargeTicket.LARGE_TICKET_INVOICE_STATUS_YKP;
    }
    // 如果不分开票的长度大于0那么为部分开票
    if(invoiceStatusList.stream().filter(x -> {
      return Objects.equals(Constants_OmsLargeTicket.LARGE_TICKET_INVOICE_STATUS_PART, x);
    }).findAny().isPresent()){
      invoiceStatus = Constants_OmsLargeTicket.LARGE_TICKET_INVOICE_STATUS_PART;
    }
    // 如果存在已开票和未开票那么为部分开票
    if(invoiceStatusList.contains(Constants_OmsLargeTicket.LARGE_TICKET_INVOICE_STATUS_YKP)&&invoiceStatusList.contains(Constants_OmsLargeTicket.LARGE_TICKET_INVOICE_STATUS_WKP)){
      invoiceStatus = Constants_OmsLargeTicket.LARGE_TICKET_INVOICE_STATUS_PART;
    }
    // 回款状态
    String paymentStatus = Constants_OmsLargeTicket.LARGE_TICKET_PAYMENT_STATUS_WHK;
    List<String> paymentStatusList =
        list.stream().map(PlatformLargeTicketProjectDTO::getPaymentState).collect(Collectors.toList());
    // 如果已回款的数量等于集合长度那么为已回款
    if(paymentStatusList.stream().filter(x -> {
      return Objects.equals(Constants_OmsLargeTicket.LARGE_TICKET_PAYMENT_STATUS_YHK, x);
    }).count() == paymentStatusList.size()){
      paymentStatus = Constants_OmsLargeTicket.LARGE_TICKET_PAYMENT_STATUS_YHK;
    }
    // 如果部分回款的长度大于0那么为部分回款
    if(paymentStatusList.stream().filter(x -> {
      return Objects.equals(Constants_OmsLargeTicket.LARGE_TICKET_PAYMENT_STATUS_PART, x);
    }).findAny().isPresent()){
      paymentStatus = Constants_OmsLargeTicket.LARGE_TICKET_PAYMENT_STATUS_PART;
    }
    // 如果存在已回款和未回款那么为部分回款
    if(paymentStatusList.contains(Constants_OmsLargeTicket.LARGE_TICKET_PAYMENT_STATUS_YHK)&&paymentStatusList.contains(Constants_OmsLargeTicket.LARGE_TICKET_PAYMENT_STATUS_WHK)){
      paymentStatus = Constants_OmsLargeTicket.LARGE_TICKET_PAYMENT_STATUS_PART;
    }
    return new PurchaseOrderLargeTicketInfoDTO(invoiceStatus,paymentStatus);
  }
}
