package com.xhgj.srm.api.dto.purchase.order;

import java.util.List;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
public class ShippingAndWarehousingInformationVO {

  /**
   * 入库单信息
   */
  private List<PurchaseOrderWarehousingEntryInfoVO> warehousingEntryInfo;

  /**
   * 发货单信息
   */
  private List<PurchaseOrderInvoiceVO> invoice;

  public ShippingAndWarehousingInformationVO(List<PurchaseOrderInvoiceVO> purchaseOrderInvoiceInfo,
      List<PurchaseOrderWarehousingEntryInfoVO> purchaseOrderWarehousingEntryInfo) {
    this.invoice = purchaseOrderInvoiceInfo;
    this.warehousingEntryInfo = purchaseOrderWarehousingEntryInfo;
  }
}
