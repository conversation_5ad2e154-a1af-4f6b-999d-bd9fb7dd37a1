package com.xhgj.srm.api.dto.supplierorder;

import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2022/12/15 10:05
 */
@Data
public class UpdateShipFormParams {

  @ApiModelProperty(value = "发货单 id ", required = true)
  @NotBlank(message = "发货单 id 必传")
  private String shipFormId;

  @ApiModelProperty("退货/取消物料数量")
  @NotNull(message = "退货/取消数量 必传")
  private List<@Valid UpdateShipDetailDTO> updateShipDetailDTOList;

  @ApiModelProperty("物流公司")
  @NotBlank(message = "物流公司必传")
  private String logisticsCompany;

  @ApiModelProperty("物流单号")
  @NotBlank(message = "物流公司编码必传")
  private String trackNum;

  @ApiModelProperty("物流编码")
  @NotBlank(message = "物流编码必传")
  private String logisticsCode;

}
