package com.xhgj.srm.api.dto.supplier.search;

import com.xhgj.srm.api.enums.SupplierRelativeKeyType;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 新增查询结果基类
 *
 * <AUTHOR>
 * @since 2022/7/14 9:25
 */
@Data
@NoArgsConstructor
public abstract class BaseSupplierSearchResult {
  @ApiModelProperty("企业名称")
  private String name;

  @ApiModelProperty("关联 key（用于后续新增接口）")
  private String relativeKey;

  @ApiModelProperty("关联 key 类型（用于后续新增接口）")
  private SupplierRelativeKeyType relativeKeyType;

  @ApiModelProperty("组织内供应商 id：有值则代表该企业在当前组织中")
  private String supplierInGroupId;
  @ApiModelProperty("MDMCode")
  private String mdmCode;
  @ApiModelProperty("法人代表")
  private String corporate;
  @ApiModelProperty("注册地址")
  private String regAddress;
}
