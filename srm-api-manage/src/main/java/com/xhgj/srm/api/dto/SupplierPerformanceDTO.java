package com.xhgj.srm.api.dto;

import com.xhgj.srm.jpa.dto.supplierRate.SupplierRateDetailInfoDTO;
import com.xhgj.srm.registration.dto.entryregistration.EntryRegistrationDiscountDetailDTO;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import java.math.BigDecimal;
import java.util.List;

@Data
@ApiModel("落地商履约信息出参")
public class SupplierPerformanceDTO {

  /**
   * id
   */
  private String id;

  /**
   * 平台code
   */
  private String platformCode;


  /**
   * 平台名称
   */
  private String platformName;

  /**
   * 状态 1:生效; 2:关闭;
   */
  private String status;

  /**
   * 联系人
   */
  private String contacts;

  /**
   * 电话
   */
  private String mobile;

  /**
   * 合作区域/项目
   */
  private String area;

  /**
   * 关联合同
   */
  private String landingContractId;

  /**
   * 合同号
   */
  private String landingContractNo;

  /**
   * 业务负责人
   */
  private String businessLeader;

  /**
   * 业务负责人Id
   */
  private String businessLeaderId;

  /**
   * 对接采购 ERP名称
   */
  private String dockingPurchaseErpName;

  /**
   * 对接采购 ERP 编码
   */
  private String dockingPurchaseErpCode;


  /**
   * 对接助理
   */
  private String dockingAssistant;

  /**
   * 对接助理Id
   */
  private String dockingAssistantId;

  /**
   * 合作类型
   */
  private String typeOfCooperation;

  /**
   * 合作品牌
   */
  private String cooperationBrand;

  /**
   * 是否有仓储。值为1表示有，0表示无。
   */
  private String storage;

  /**
   * 仓库地址
   */
  private String storageAddress;

  /**
   * 仓库面积
   */
  private BigDecimal storageArea;

  /**
   * 保证金
   */
  private BigDecimal deposit;

  /**
   * 保证金状态
   */
  private Boolean depositState;

  /**
   * 账期
   */
  private Integer accountingPeriod;
  /**
   * 账期（背靠背）
   */
  private Boolean backToBack;

  /**
   * 付款形式
   */
  private String paymentType;

  /**
   * 保底金额
   */
  private BigDecimal guaranteedAmount;

  /**
   * 违约金: 两个值用，分开
   */
  private String penalty;

  /**
   * 票种
   */
  private String invoiceType;

  /**
   * 税率。
   */
  private BigDecimal taxRate;

  /**
   * 付款方式后输入框 (月份、其他)
   */
  private String paymentTypeInput;

  /**
   * 准入说明
   */
  private String notes;

  /**
   * 身份证
   */
  private List<FileDTO> idCardPhoto;

  /**
   * 产品资质书
   */
  private List<FileDTO> productQualification;

  /**
   * 合同生效开始时间
   */
  private Long effectiveStart;

  /**
   * 合同失效时间
   */
  private Long effectiveEnd;

  /**
   * 合作比例/折扣信息
   */
  private List<EntryRegistrationDiscountDetailDTO> entryRegistrationDiscountInfo;

  /**
   * 履约金额
   */
  private String performanceAmount;

  /**
   * 当前折扣比例
   */
  private String rate;

  /**
   * 阶梯折扣比例集合
   */
  private List<SupplierRateDetailInfoDTO> supplierRateDetailInfo;

  /**
   * 品牌折扣比例集合
   */
  private List<SupplierRateDetailInfoDTO> orderRateBrandDetailInfo;

  /**
   * 补充附件
   */
  private List<FileDTO> supplyFile;
}
