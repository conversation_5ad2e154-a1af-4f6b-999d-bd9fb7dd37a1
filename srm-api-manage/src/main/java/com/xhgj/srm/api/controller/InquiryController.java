package com.xhgj.srm.api.controller;

import com.xhgj.srm.api.dto.inquiry.InquiryPage;
import com.xhgj.srm.api.dto.inquiry.InquiryQueryForm;
import com.xhgj.srm.api.dto.inquiry.InquiryRankDataDTO;
import com.xhgj.srm.api.service.InquiryService;
import com.xhgj.srm.jpa.dto.inquiry.InquiryStatistics;
import com.xhiot.boot.mvc.base.ResultBean;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import java.net.URLEncoder;
import java.text.ParseException;
import java.util.List;
import lombok.SneakyThrows;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

@Api(tags = {"询价管理接口"})
@RestController
@RequestMapping("/inquiry")
public class InquiryController {

  @Autowired private InquiryService inquiryService;

  @ApiOperation(value = "分页获取询价列表", notes = "分页获取询价列表")
  @RequestMapping(value = "/getInquiryPage", method = RequestMethod.GET)
  public ResultBean<InquiryPage> getInquiryPage(InquiryQueryForm form) {
    return new ResultBean<>(inquiryService.getInquiryPageRef(form));
  }

  @ApiOperation(value = "获取询价统计", notes = "获取询价统计")
  @GetMapping(value = "/statistics")
  public ResultBean<InquiryStatistics> statistics(InquiryQueryForm form) {
    return new ResultBean<>(inquiryService.statistics(form));
  }


  @ApiOperation(value = "新增询价记录", notes = "新增询价记录")
  @ApiImplicitParam(name = "addJson", value = "询价信息", required = true)
  @RequestMapping(value = "/addInquiry", method = RequestMethod.POST)
  public ResultBean<Boolean> addInquiry(String addJson, String userId, String userGroup)
      throws ParseException {
    inquiryService.addInquiry(addJson, userId, userGroup);
    return new ResultBean<>(true, "操作成功!");
  }

  @ApiOperation(value = "修改询价记录", notes = "修改询价记录")
  @ApiImplicitParams({
    @ApiImplicitParam(name = "id", value = "询价信息", required = true),
    @ApiImplicitParam(name = "enterpriseName", value = "供应商名称"),
    @ApiImplicitParam(name = "brands", value = "品牌"),
    @ApiImplicitParam(name = "name", value = "商品名称"),
    @ApiImplicitParam(name = "model", value = "型号"),
    @ApiImplicitParam(name = "marketPrice", value = "含税市场价"),
    @ApiImplicitParam(name = "salesPrice", value = "经销含税成本"),
    @ApiImplicitParam(name = "offererName", value = "报价人姓名"),
    @ApiImplicitParam(name = "offererPhone", value = "报价人手机"),
    @ApiImplicitParam(name = "remark", value = "备注"),
    @ApiImplicitParam(name = "des", value = "描述"),
    @ApiImplicitParam(name = "transferPrice", value = "含税调拨价"),
    @ApiImplicitParam(name = "salesMan", value = "业务员"),
    @ApiImplicitParam(name = "num", value = "数量"),
    @ApiImplicitParam(name = "unit", value = "单位"),
  })
  @RequestMapping(value = "/updateInquiry", method = RequestMethod.POST)
  public ResultBean<Boolean> updateInquiry(
      String id,
      String enterpriseName,
      String brands,
      String name,
      String model,
      String marketPrice,
      String salesPrice,
      String offererName,
      String offererPhone,
      String remark,
      String des,
      String transferPrice,
      String salesMan,
      String num,
      String unit,
      String userId) {
    inquiryService.updateInquiry(
        id,
        enterpriseName,
        brands,
        name,
        model,
        marketPrice,
        salesPrice,
        offererName,
        offererPhone,
        remark,
        des,
        transferPrice,
        salesMan,
        num,
        unit,
        userId);
    return new ResultBean<>(true, "操作成功!");
  }

  @ApiOperation(value = "删除询价记录", notes = "删除询价记录")
  @ApiImplicitParams({
    @ApiImplicitParam(name = "userGroup", value = "组织编号"),
    @ApiImplicitParam(name = "inquiryids", value = "询价记录id", required = true)
  })
  @RequestMapping(value = "/delInquiry", method = RequestMethod.POST)
  public ResultBean<Boolean> delInquiry(
      @RequestParam(value = "inquiryids", required = false) String inquiryids,
      String userGroup,
      String userId) {
    inquiryService.delInquiry(inquiryids, userGroup, userId);
    return new ResultBean<>(true, "操作成功!");
  }

  @ApiOperation(value = "询价排行榜", notes = "询价排行榜")
  @ApiImplicitParams({@ApiImplicitParam(name = "userGroup", value = "组织编码", required = true)})
  @RequestMapping(value = "/InquiryRank", method = RequestMethod.GET)
  public ResultBean<List<InquiryRankDataDTO>> InquiryRank(String userGroup) {
    return new ResultBean<>(inquiryService.getInquiryRank(userGroup));
  }

  @SneakyThrows
  @ApiOperation(value = "导入询价", notes = "导入询价")
  @ApiImplicitParams({
    @ApiImplicitParam(name = "userId", value = "用户id"),
  })
  @PostMapping("/importInquiry")
  public ResultBean<Boolean> importInquiry(MultipartFile file, String userId) {
    inquiryService.saveImportExcelInquiry(file, userId);
    return new ResultBean<>(true, "操作成功！");
  }

  @SneakyThrows
  @ApiOperation(value = "导出询价", notes = "导出询价")
  @RequestMapping(value = "/exportInquiry", method = RequestMethod.GET)
  public ResponseEntity<byte[]> exportInquiry() {
    HttpHeaders headers = new HttpHeaders();
    headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);
    headers.setContentDispositionFormData("attachment", URLEncoder.encode("inquiry.xlsx", "UTF-8"));
    byte[] bytes = inquiryService.exportInquiry();
    return new ResponseEntity<>(bytes, headers, HttpStatus.CREATED);
  }

  @ApiOperation(value = "处理已有询价记录", notes = "处理已有询价记录")
  @PostMapping("/updateInquiryCreateMan")
  public ResultBean<Boolean> updateInquiryCreateMan() {
    inquiryService.updateInquiryCreateMan();
    return new ResultBean<>(true, "操作成功！");
  }
}
