package com.xhgj.srm.api.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.exceptions.ExceptionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.xhgj.srm.api.factory.MapStructFactory;
import com.xhgj.srm.api.service.MissionService;
import com.xhgj.srm.common.Constants_Batch;
import com.xhgj.srm.common.enums.landingContract.ContractStatus;
import com.xhgj.srm.jpa.annotations.DefaultSearchScheme;
import com.xhgj.srm.api.domain.SrmUserDetails;
import com.xhgj.srm.api.dto.FileDTO;
import com.xhgj.srm.jpa.entity.LandingMerchantContract;
import com.xhgj.srm.jpa.entity.Mission;
import com.xhgj.srm.jpa.repository.GroupRepository;
import com.xhgj.srm.jpa.repository.PlatformRepository;
import com.xhgj.srm.jpa.repository.SupplierPerformanceRepository;
import com.xhgj.srm.registration.domain.OperatingUser;
import com.xhgj.srm.registration.dto.entryregistration.EntryRegistrationDTO;
import com.xhgj.srm.registration.dto.entryregistration.EntryRegistrationDTO.PlatformForEntry;
import com.xhgj.srm.registration.dto.entryregistration.EntryRegistrationDetailDTO;
import com.xhgj.srm.registration.dto.entryregistration.EntryRegistrationDiscountDTO;
import com.xhgj.srm.registration.dto.entryregistration.EntryRegistrationLandingMerchantAddParam;
import com.xhgj.srm.registration.dto.entryregistration.EntryRegistrationLandingMerchantDTO;
import com.xhgj.srm.registration.dto.entryregistration.EntryRegistrationLandingMerchantSendSMSDTO;
import com.xhgj.srm.registration.dto.entryregistration.EntryRegistrationOrderAddParam;
import com.xhgj.srm.registration.dto.entryregistration.EntryRegistrationOrderDTO;
import com.xhgj.srm.registration.dto.entryregistration.EntryRegistrationOrderSubmitAuditParam;
import com.xhgj.srm.registration.dto.entryregistration.EntryRegistrationPageExport;
import com.xhgj.srm.registration.dto.entryregistration.EntryRegistrationPageParams;
import com.xhgj.srm.registration.entity.EntryRegistrationEntity;
import com.xhgj.srm.registration.factory.EntryRegistrationFactory;
import com.xhgj.srm.registration.repository.EntryRegistrationRepository;
import com.xhgj.srm.api.service.EntryRegistrationService;
import com.xhgj.srm.api.service.FileService;
import com.xhgj.srm.api.service.SupplierPerformanceService;
import com.xhgj.srm.api.service.XhgjService;
import com.xhgj.srm.api.utils.ManageSecurityUtil;
import com.xhgj.srm.common.Constants;
import com.xhgj.srm.common.Constants_DingCarTemplate;
import com.xhgj.srm.common.config.SrmConfig;
import com.xhgj.srm.common.enums.AssessStateEnum;
import com.xhgj.srm.common.enums.FileReviewStateEnum;
import com.xhgj.srm.common.enums.PayTypeSAPEnums;
import com.xhgj.srm.common.enums.ShortMessageEnum;
import com.xhgj.srm.common.enums.entryregistration.EntryRegistrationCompanyAttributeEnum;
import com.xhgj.srm.common.enums.entryregistration.EntryRegistrationCooperationTypeEnum;
import com.xhgj.srm.common.enums.entryregistration.EntryRegistrationDiscountTypeEnum;
import com.xhgj.srm.common.enums.entryregistration.EntryRegistrationPaymentConditionEnum;
import com.xhgj.srm.common.enums.entryregistration.EntryRegistrationStatusEnum;
import com.xhgj.srm.common.utils.dingding.DingUtils;
import com.xhgj.srm.jpa.dao.EntryRegistrationDao;
import com.xhgj.srm.jpa.dao.EntryRegistrationLandingMerchantDao;
import com.xhgj.srm.jpa.dao.LandingMerchantContractDao;
import com.xhgj.srm.jpa.entity.EntryRegistrationDiscount;
import com.xhgj.srm.jpa.entity.EntryRegistrationLandingMerchant;
import com.xhgj.srm.jpa.entity.EntryRegistrationOrder;
import com.xhgj.srm.jpa.entity.File;
import com.xhgj.srm.jpa.entity.Group;
import com.xhgj.srm.jpa.entity.Platform;
import com.xhgj.srm.jpa.entity.Supplier;
import com.xhgj.srm.jpa.entity.SupplierPerformance;
import com.xhgj.srm.jpa.entity.User;
import com.xhgj.srm.jpa.repository.EntryRegistrationDiscountRepository;
import com.xhgj.srm.jpa.repository.EntryRegistrationLandingMerchantRepository;
import com.xhgj.srm.jpa.repository.EntryRegistrationOrderRepository;
import com.xhgj.srm.jpa.repository.FileRepository;
import com.xhgj.srm.jpa.repository.LandingMerchantContractRepository;
import com.xhgj.srm.jpa.repository.SupplierRepository;
import com.xhgj.srm.jpa.repository.UserRepository;
import com.xhgj.srm.request.dto.hZero.process.StartProcessVo;
import com.xhgj.srm.request.service.third.hZero.HZeroService;
import com.xhgj.srm.request.service.third.xhgj.XhgjSMSRequest;
import com.xhgj.srm.sender.mq.sender.BatchTaskMqSender;
import com.xhgj.srm.service.ShareEntryRegistrationLandingMerchantService;
import com.xhgj.srm.service.ShareEntryRegistrationService;
import com.xhgj.srm.service.ShareLandingMerchantContractService;
import com.xhgj.srm.service.ShareSupplierPerformanceService;
import com.xhiot.boot.core.common.exception.CheckException;
import com.xhiot.boot.framework.jpa.repository.BootBaseRepository;
import com.xhiot.boot.mvc.base.PageResult;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicReference;
import java.util.concurrent.locks.ReentrantLock;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
@Slf4j
public class EntryRegistrationServiceImpl implements EntryRegistrationService {

  @Resource
  private EntryRegistrationOrderRepository entryRegistrationOrderRepository;

  @Resource
  private EntryRegistrationLandingMerchantRepository entryRegistrationLandingMerchantRepository;

  @Resource
  private EntryRegistrationLandingMerchantDao entryRegistrationLandingMerchantDao;
  @Resource
  private EntryRegistrationDiscountRepository entryRegistrationDiscountRepository;
  @Resource
  private SupplierRepository supplierRepository;
  @Resource
  private SupplierPerformanceService supplierPerformanceService;
  @Resource
  private XhgjService xhgjService;
  @Autowired
  private EntryRegistrationDao dao;
  @Resource
  private FileService fileService;
  @Resource
  private  DingUtils dingUtils;
  @Resource
  private UserRepository userRepository;
  @Resource
  private XhgjSMSRequest xhgjSMSRequest;
  @Autowired
  private LandingMerchantContractRepository landingMerchantContractRepository;
  @Resource private LandingMerchantContractDao landingMerchantContractDao;
  @Resource
  private EntryRegistrationRepository registrationRepository;
  @Resource
  ManageSecurityUtil manageSecurityUtil;
  @Resource
  EntryRegistrationFactory entryRegistrationFactory;
  @Autowired
  private FileRepository fileRepository;
  @Resource
  private ShareEntryRegistrationLandingMerchantService shareEntryRegistrationLandingMerchantService;
  @Resource
  private ShareEntryRegistrationService shareEntryRegistrationService;
  /**
   * 防止同一时间提交同一个报备单，防止同一个报备单短时间内多次调用OA的接口
   */
  private final ConcurrentHashMap<String, ReentrantLock> lockMap = new ConcurrentHashMap<>();

  @Override
  public BootBaseRepository<EntryRegistrationOrder, String> getRepository() {
    return entryRegistrationOrderRepository;
  }
  private final String baseUrl;
  @Resource
  private ApplicationContext applicationContext;

  @Resource
  private ShareSupplierPerformanceService shareSupplierPerformanceService;
  @Resource
  private MissionService missionService;
  @Resource
  private BatchTaskMqSender batchTaskMqSender;
  @Resource
  private ShareLandingMerchantContractService shareLandingMerchantContractService;
  @Resource
  private SupplierPerformanceRepository supplierPerformanceRepository;
  @Resource
  private PlatformRepository platformRepository;
  @Resource
  private GroupRepository groupRepository;
  @Resource
  private HZeroService hZeroService;
  public EntryRegistrationServiceImpl(SrmConfig config) {
    this.baseUrl = config.getUploadUrl();
  }


  @Override
  @DefaultSearchScheme(searchType = Constants.CHECK_IN_REPORT_FORM)
  public PageResult<EntryRegistrationDTO> entryRegistrationPageListRef(EntryRegistrationPageParams params) {
    User user = manageSecurityUtil.getSrmUserDetails().getUser();
    boolean showAll = user.getRoleList().stream().anyMatch(role ->
        StrUtil.equalsAny(role, Constants.SUPPLIER_USER_ROLE_ADMIN,
            Constants.SUPPLIER_USER_ROLE_COMMERCE_ADMIN,
            Constants.ROLE_ADMINISTRATOR));
    if (!showAll) {
      params.setSalesmanName(user.getRealName());
    }
    Page<EntryRegistrationOrder> page = dao.getPageRef(params.toQueryMap());
    List<EntryRegistrationOrder> list = page.getContent();
    if (CollUtil.isEmpty(list)) {
      return new PageResult<>(new ArrayList<>(), page.getTotalElements(), page.getTotalPages(),
          params.getPageNo(), params.getPageSize());
    }
    AtomicReference<List<EntryRegistrationDTO>> res = new AtomicReference<>(new ArrayList<>());
    shareEntryRegistrationService.batchGetEntryRegistrationInfo(list, (entryId2Merchant, id2Platforms) -> {
      res.set(list.stream().map(item -> {
        EntryRegistrationDTO dto = MapStructFactory.INSTANCE.toEntryRegistrationDTO(item);
        // 处理SalesmanGroup
        dto.setSalesmanGroup(item.getBusinessCompany());
        Optional.ofNullable(entryId2Merchant.get(dto.getId())).ifPresent(merchant -> {
          dto.setSupplierAuditStatus(merchant.getSupplierAuditStatus());
          dto.setIsComplete(Constants.STATE_OK.equals(merchant.getCanBeSubmitted()));
          dto.setSupplierAuditStatus(merchant.getSupplierAuditStatus());
        });
        EntryRegistrationEntity entity = new EntryRegistrationEntity(registrationRepository, item,
            entryId2Merchant.get(dto.getId()));
        dto.setRegistrationStatusKey(entity.getRealEntryRegistrationStatus().getKey());
        dto.setRegistrationStatus(entity.getRealEntryRegistrationStatus().getDescription());
        List<String> platformList = item.getOriginPlatformList();
        // 查找相应的platform
        List<Platform> platforms =
            platformList.stream().map(id2Platforms::get).collect(Collectors.toList());
        dto.setPlatformList(
            platforms.stream().map(PlatformForEntry::new).collect(Collectors.toList()));
        return dto;
      }).collect(Collectors.toList()));
    });
    return new PageResult<>(res.get(), page.getTotalElements(), page.getTotalPages(), params.getPageNo(),
        params.getPageSize());
  }

  @Override
  @DefaultSearchScheme(searchType = Constants.CHECK_IN_REPORT_FORM)
  public void exportEntryRegistration(EntryRegistrationPageExport exportForm) {
    User user = manageSecurityUtil.getSrmUserDetails().getUser();
    boolean showAll = user.getRoleList().stream().anyMatch(role ->
        StrUtil.equalsAny(role, Constants.SUPPLIER_USER_ROLE_ADMIN,
            Constants.SUPPLIER_USER_ROLE_COMMERCE_ADMIN,
            Constants.ROLE_ADMINISTRATOR));
    if (!showAll) {
      exportForm.setSalesmanName(user.getRealName());
    }
    exportForm.setPageNo(1);
    exportForm.setPageSize(Integer.MAX_VALUE);
    Map<String, Object> queryMap = exportForm.toQueryMap();
    OperatingUser operatingUser = manageSecurityUtil.getOperatingUser();
    Mission mission =
        missionService.createMission(user, "导出-入驻报备单", Constants.PLATFORM_TYPE_AFTER, null,
            null);
    Map<String, Object> mapParam = new HashMap<>(5);
    mapParam.put("userId", operatingUser.getUserId());
    mapParam.put("query", queryMap);
    batchTaskMqSender.toHandleBatchTask(
        mission.getId(), JSON.toJSONString(mapParam), Constants_Batch.BATCH_TASK_EXPORT_ENTRY_REGISTRATION);
  }

  @Override
  public EntryRegistrationDetailDTO getEntryRegistrationDetailRef(String registrationNumber) {
    EntryRegistrationEntity entity =
        registrationRepository.byId(registrationNumber);
    return entity.toEntryRegistrationDetailDTO(baseUrl);
  }

  @Override
  public void checkInReportLinkRef(String id) {
    EntryRegistrationEntity entity =
        registrationRepository.byId(id, manageSecurityUtil.getOperatingUser());
    entity.checkInReportLink();
    entity.save();
  }

  @Override
  public Boolean addRef(EntryRegistrationOrderAddParam param) {
    String id = param.getEntryRegistrationOrder().getId();
    if (id == null) {
      throw new CheckException("报备单ID为空");
    }
    ReentrantLock lock = lockMap.computeIfAbsent(id + "_add", key -> new ReentrantLock());
    try {
      lock.lock();
      EntryRegistrationServiceImpl proxy =
          applicationContext.getBean(EntryRegistrationServiceImpl.class);
      return proxy.addRefTransactional(param);
    } catch (Exception e) {
      throw new CheckException(e);
    } finally {
      lock.unlock();
    }
  }

  @Transactional(rollbackFor = Exception.class)
  public Boolean addRefTransactional(EntryRegistrationOrderAddParam param) {
    SrmUserDetails srmUserDetails = manageSecurityUtil.getSrmUserDetails();
    User user = srmUserDetails.getUser();
    Group group =
        groupRepository.findFirstByErpCodeAndState(param.getUserGroup(), Constants.STATE_OK);
    if (group == null) {
      throw new CheckException("未找到对应的组织");
    }
    // 工厂方法创建entity
    EntryRegistrationEntity entity = entryRegistrationFactory.createEntity(param, user, group);
    EntryRegistrationLandingMerchant originMerchant = entity.getMerchant();
    entity.setRegistrationStatus(EntryRegistrationStatusEnum.PENDING_CONFIRMATION.getKey());
    // 对应的合同附件上传处于验证中状态时，不允许修改报备单
    Optional.ofNullable(entity.getContract()).ifPresent(landingMerchantContract -> {
      if (StrUtil.equals(landingMerchantContract.getFileReviewState(),
          FileReviewStateEnum.VERIFICATION.getKey())) {
        throw new CheckException("对应合同附件处于验证中不可修改！");
      }
    });
    // 判断入驻报备原状态是否为已成功，是则清空oaId
    if (entity.getId() != null) {
      EntryRegistrationEntity origin = registrationRepository.byId(entity.getId());
      if (EntryRegistrationStatusEnum.APPROVED.getKey().equals(origin.getRegistrationStatus())) {
        log.info("入驻报备:{}原状态为已成功，清空OA审核ID:{}", origin.getId(), origin.getOaAuditId());
        entity.setOaAuditId(StrUtil.EMPTY);
      }
    }
    entity.save();
    if (Objects.equals(param.getEntryRegistrationOrder().getRegistrationStatus(),
        EntryRegistrationStatusEnum.UNDER_REVIEW.getKey())) {
      submitAuditRefWithLock(entity.getId(), false);
    }
    if (StrUtil.isNotBlank(param.getEntryRegistrationLandingMerchant().getId())) {
      // 首次
      //判断是否修改落地商信息(并发送短信)
      EntryRegistrationLandingMerchantSendSMSDTO smsDTO =
          new EntryRegistrationLandingMerchantSendSMSDTO(
              originMerchant,
              entity.getMerchant(),
              param.getEntryRegistrationLandingMerchant().getLicense(),
              param.getEntryRegistrationLandingMerchant().getIdCardPhoto(),
              param.getEntryRegistrationLandingMerchant().getProductQualification());
      modifyTheInformationOfTheLandingMerchant(smsDTO.getCurrentEntryRegistrationLandingMerchant(),
          smsDTO.getLandingMerchantEntity(),
          smsDTO.getLicense(),
          smsDTO.getIdCardPhoto(),
          smsDTO.getProductQualification(),
          entity.getRegistrationNumber(),
          entity.getSalesmanId(),
          entity.getMerchant().getEnterpriseName()
      );
    }
    return true;
  }

  /**
   * 新增成功  钉钉通知 业务员
   * @param registrationNumber
   * @param salesmanId
   * @param enterpriseName
   */
  private void sendNotifications(String registrationNumber,String salesmanId ,String enterpriseName) {
    //新增成功  钉钉通知 业务员
    if (StrUtil.isNotBlank(salesmanId)) {
      User u = userRepository.getOne(salesmanId);
      if (u != null) {
        String mobile = u.getMobile();
        if (StrUtil.isNotBlank(mobile)) {
          try {
            //发送钉钉通知
            doSendDingDingInform(registrationNumber, enterpriseName,mobile);
          } catch (Exception e) {
            log.error(ExceptionUtil.stacktraceToString(e, -1));
          }
        }
      }
    }
  }

  /**
   * 判断是否修改落地商信息 并 发送短信
   */
  private void modifyTheInformationOfTheLandingMerchant(EntryRegistrationLandingMerchant merchant,
      EntryRegistrationLandingMerchant dto, String license, List<String> idCardPhoto,
      List<String> productQualification, String registrationNumber, String salesmanId,
      String enterpriseName) {
    if (merchant != null && dto != null){
      //判断 字段值
      AtomicBoolean upd =
          new AtomicBoolean(isUpd(merchant, dto, license, idCardPhoto, productQualification));
      if (upd.get()) {
        //发送短信
        if (StrUtil.isNotBlank(merchant.getAccountUserPhone())){
          xhgjSMSRequest.sendSms(ShortMessageEnum.MODIFY_THE_FILING_FORM_NOTIFY_THE_LANDER,
              merchant.getAccountUserPhone(), new HashMap<>());
        }
        //钉钉通知
        sendNotifications(registrationNumber, salesmanId, enterpriseName);
      }
    }
  }

  public boolean isUpd(EntryRegistrationLandingMerchant merchant,
      EntryRegistrationLandingMerchant dto, String license, List<String> idCardPhoto,
      List<String> productQualification) {
    AtomicReference<Boolean> result = new AtomicReference<>(false);
    if (!ObjectUtil.equals(merchant, dto)){
      result.set(true);
    }
    // 查询附件
    // 营业执照
    fileService.findFirstByRelationIdAndRelationType(dto.getId(),
        Constants.FILE_TYPE_LANDING_MERCHANT_LICENSE).ifPresent(file -> {
      if (!ObjectUtil.equals(file.getId(), license)) {
        result.set(true);
      }
    });

    // 身份证 文件
    List<File> idCardFiles = fileService.getFileList(dto.getId(),
        Constants.FILE_TYPE_LANDING_MERCHANT_ID_CARD_PHOTO);
    Set<String> originIdCardPhoto = new HashSet<>();
    if (CollUtil.isNotEmpty(idCardFiles)) {
      idCardFiles.forEach(file -> originIdCardPhoto.add(file.getId()));
    }
    if (!originIdCardPhoto.containsAll(idCardPhoto)
        && originIdCardPhoto.size() != idCardPhoto.size()) {
      result.set(true);
    }

    // 产品资质书
    List<File> fileListByIdAndType = fileService.getFileList(dto.getId(),
        Constants.FILE_TYPE_LANDING_MERCHANT_PRODUCT_QUALIFICATION);
    List<String> fileDTOS = new ArrayList<>();
    if (fileListByIdAndType != null) {
      fileListByIdAndType.forEach(file -> {
        fileDTOS.add(new FileDTO(file).getId());
      });
    }
    if (!fileDTOS.containsAll(productQualification)
        && fileDTOS.size() != productQualification.size()) {
      result.set(true);
    }
    return result.get();
  }

  private void checkStatus(EntryRegistrationOrderAddParam param) {
    String id = param.getEntryRegistrationOrder().getId();
    if (StrUtil.isNotBlank(id)) {
      EntryRegistrationOrder entryRegistrationOrder = entryRegistrationOrderRepository.findById(id)
          .orElseThrow(() -> CheckException.noFindException(EntryRegistrationOrder.class, id));
      String registrationStatus = entryRegistrationOrder.getRegistrationStatus();
      String contractArchivingStatus = entryRegistrationOrder.getContractArchivingStatus();
      if (Objects.equals(registrationStatus, EntryRegistrationStatusEnum.APPROVED.getKey())
          && Objects.equals(contractArchivingStatus, Constants.STATE_OK)) {
        throw new CheckException("该报备单已审核通过并且合同已经归档，无法再次修改");
      }
    }
  }

  /**
   * 准入报备单提交审核
   */
  @Transactional
  @Override
  public Boolean batchSubmitAudit(EntryRegistrationOrderSubmitAuditParam param) {
    // 用于收集所有未通过校验的报备单ID
    StringBuilder errorMessages = new StringBuilder();
    List<String> entryRegistrationOrderIds = param.getEntryRegistrationOrderIds();
    for (String entryRegistrationOrderId : entryRegistrationOrderIds) {
      Map<String, Object> map = submitAuditRefWithLock(entryRegistrationOrderId, true);
      if (Boolean.FALSE.equals(map.get("success")) && "submitted".equals(map.get("type"))) {
        // 拼接id
        errorMessages.append(map.get("registrationNumber")).append("、");
      }
    }
    // 如果errorMessages不是空的，说明有未通过校验的报备单ID
    if (StrUtil.isNotBlank(errorMessages)) {
      // errorMessages去除最后一个、
      errorMessages.deleteCharAt(errorMessages.length() - 1);
      throw new CheckException(errorMessages + "入驻报备中，供应商信息未填写，可以直接在页面补充，或通过右上角邀请按钮邀请供应商进行填写");
    }
    return true;
  }


  public Map<String, Object> submitAuditRefWithLock(String entryRegistrationOrderId, Boolean batch) {
    ReentrantLock lock = lockMap.computeIfAbsent(entryRegistrationOrderId + "_submit", key -> new ReentrantLock());
    try {
      lock.lock();
      EntryRegistrationServiceImpl proxy =
          applicationContext.getBean(EntryRegistrationServiceImpl.class);
      return proxy.submitAuditRefTransactional(entryRegistrationOrderId, batch);
    } catch (Exception e) {
      throw new CheckException(e);
    } finally {
      lock.unlock();
    }
  }



  @Transactional(rollbackFor = Exception.class)
  public Map<String, Object> submitAuditRefTransactional(String entryRegistrationOrderId, Boolean batch) {
    Map<String, Object> map = new HashMap<>();
    EntryRegistrationEntity entity = registrationRepository.byId(entryRegistrationOrderId,
        manageSecurityUtil.getOperatingUser());
    EntryRegistrationLandingMerchant landingMerchant = entity.getMerchant();
    checkAuditStatus(entity);
    if (!checkDataIntegrity(landingMerchant)) {
      if (Boolean.TRUE.equals(batch)) {
        // 此错误特殊处理，batchSubmitAudit方法会收集所有错误信息
        map.put("success", false);
        map.put("type", "submitted");
        map.put("registrationNumber", entity.getRegistrationNumber());
        return map;
      }
      throw new CheckException(entity.getRegistrationNumber()
          + "入驻报备中，供应商信息未填写，可以直接在页面补充，或通过右上角邀请按钮邀请供应商进行填写");
    }
    // merchant的input没有此字段
    checkLandingMerchantRef(landingMerchant, entity.getPlatformCodes());
    String originStatus = entity.getRegistrationStatus();
    entity.setRegistrationStatus(EntryRegistrationStatusEnum.UNDER_REVIEW.getKey());
    landingMerchant.setSupplierAuditStatus(null);
    entity.save();
    // 修改为jpa保存
    entryRegistrationLandingMerchantRepository.save(landingMerchant);
    // 提交人为报备单创建人
    User user = userRepository.findById(entity.getCreateMan())
        .orElseThrow(() -> CheckException.noFindException(User.class, entity.getCreateMan()));
    StartProcessVo startProcessVo = hZeroService.startEntryOrderProcess(user, entity);
    entity.setOaAuditId(startProcessVo.getInstanceId());
    entity.save();
    /*boolean updateFlag = StrUtil.isNotBlank(entity.getOaAuditId()) && !EntryRegistrationStatusEnum.APPROVED.getKey().equals(originStatus);
    if (updateFlag) {
      oaRequest.updateEntryRegistrationOrderAudit(user, entity);
    } else {
      oaRequest.addEntryRegistrationOrderAudit(user, entity);
    }*/
    return map;
  }

  private boolean checkDataIntegrity(
      EntryRegistrationLandingMerchant entryRegistrationLandingMerchant) {
    if (entryRegistrationLandingMerchant == null) {
      return false;
    }
    return Objects.equals(entryRegistrationLandingMerchant.getCanBeSubmitted(), Constants.STATE_OK);
  }

  private void checkAuditStatus(EntryRegistrationOrder entryRegistrationOrder) {
    String registrationStatus = entryRegistrationOrder.getRegistrationStatus();
    if (Objects.equals(registrationStatus, EntryRegistrationStatusEnum.UNDER_REVIEW.getKey())) {
      throw new CheckException("报备单所处状态不可提交审核");
    }
    if (Objects.equals(registrationStatus, EntryRegistrationStatusEnum.APPROVED.getKey())) {
      throw new CheckException("报备单所处状态不可提交审核");
    }
  }

  /**
   * 校验落地商信息 - 适配多平台项目
   */
  private void checkLandingMerchantRef(
      EntryRegistrationLandingMerchant entryRegistrationLandingMerchant,
      String platformCodes) {

    String enterpriseName = entryRegistrationLandingMerchant.getEnterpriseName();
    String uscc = entryRegistrationLandingMerchant.getUscc();
    Supplier supplier = getSupplierByNameOrUscc(enterpriseName, uscc).orElse(null);
    if (supplier != null && StrUtil.isBlank(entryRegistrationLandingMerchant.getMdmCode())) {
      throw new CheckException("供应商数据异常，请联系人工处理");
    }
    /* 不校验下单平台重复性
    if (supplier != null) {
      // 校验项目是否重复
      String[] platformCodeArray = StrUtil.split(platformCodes, StrUtil.COMMA);
      for (String platformCode : platformCodeArray) {
        checkPlatformCodeExist(supplier.getId(), platformCode);
      }
    }*/
    // srm没有供应商，也米有mdm编码 (MDM中也没有)
    if (supplier == null && StrUtil.isBlank(entryRegistrationLandingMerchant.getMdmCode())) {
      // 调用mdm接口对比名称与信用代码是否重复，重复则提示：有存在相同名称或信用代码企业，请重新填写！
      xhgjService.getPartnerByName(entryRegistrationLandingMerchant.getEnterpriseName(),
          Constants.SUPPLIERTYPE_CHINA).ifPresent(partnerDTO -> {
        // 不与天眼查中的数据比较
        if (Objects.equals(partnerDTO.getCreditCode(), uscc) && StrUtil.isNotBlank(
            partnerDTO.getMdmCode())) {
          throw new CheckException("有存在相同名称或信用代码企业，请重新填写！");
        }
      });
    }
    // srm没有供应商，有mdm编码 (MDM中有)
    if (supplier == null && StrUtil.isNotBlank(entryRegistrationLandingMerchant.getMdmCode())) {
      // 调用mdm标记接口，将企业标记成供应商
      String mdmCode = entryRegistrationLandingMerchant.getMdmCode();
      // 无论之前是否是供应商身份 都进行标记
      xhgjService.markPartnerSupplier(mdmCode);
      // 存入本系统。当OA审核通过后则跳过MDM审核
      Supplier supplierEntity = shareEntryRegistrationLandingMerchantService.convertToSupplier(
          entryRegistrationLandingMerchant);
      supplierEntity.setMdmCode(mdmCode);
      supplierEntity.setState(Constants.STATE_OK);
      supplierEntity.addPlatform(platformCodes);
      supplierRepository.save(supplierEntity);
      entryRegistrationLandingMerchant.setSupplierId(supplierEntity.getId());
      entryRegistrationLandingMerchant.setMdmCode(mdmCode);
      entryRegistrationLandingMerchantRepository.save(entryRegistrationLandingMerchant);
    }
  }

  private Optional<Supplier> getSupplierByNameOrUscc(String supplierName, String uscc) {
    AtomicReference<Supplier> supplier = new AtomicReference<>();
    if (StrUtil.isNotBlank(supplierName)) {
      supplierRepository.findFirstByEnterpriseNameAndState(supplierName, Constants.STATE_OK)
          .ifPresent(supplier::set);
    }
    if (StrUtil.isNotBlank(uscc)) {
      supplierRepository.findFirstByUsccAndState(uscc, Constants.STATE_OK).ifPresent(supplier::set);
    }
    if (supplier.get() == null) {
      return Optional.empty();
    }
    return Optional.of(supplier.get());
  }

  private void checkPlatformCodeExist(String supplierId, String platformCode) {
    SupplierPerformance supplierPerformance =
        supplierPerformanceService.getFirstBySupplierIdAndPlatformCode(supplierId, platformCode);
    if (supplierPerformance != null) {
      throw new CheckException("已存在相同项目，若新增请去合同管理手动新增！");
    }
  }

  private void checkAddParam(EntryRegistrationOrderAddParam param) {
    EntryRegistrationOrderDTO entryRegistrationOrder = param.getEntryRegistrationOrder();
    if (!Objects.equals(entryRegistrationOrder.getStorage(), Constants.STATE_OK) && !Objects.equals(
        entryRegistrationOrder.getStorage(), Constants.STATE_NO)) {
      throw new CheckException("未知的仓储信息");
    }
    EntryRegistrationCooperationTypeEnum entryRegistrationCooperationTypeEnum =
        EntryRegistrationCooperationTypeEnum.valueOfByKey(
            entryRegistrationOrder.getTypeOfCooperation());
    if (entryRegistrationCooperationTypeEnum == null) {
      throw new CheckException("合作类型错误");
    }
    PayTypeSAPEnums entryRegistrationPaymentTypeEnum =
        PayTypeSAPEnums.fromKey(entryRegistrationOrder.getPaymentType());
    if (entryRegistrationPaymentTypeEnum == null) {
      throw new CheckException("付款方式错误");
    }
    for (String paymentTerms : entryRegistrationOrder.getPaymentTerms().split(StrUtil.COMMA)) {
      EntryRegistrationPaymentConditionEnum entryRegistrationPaymentConditionEnum =
          EntryRegistrationPaymentConditionEnum.valueOfByKey(paymentTerms);
      if (entryRegistrationPaymentConditionEnum == null) {
        throw new CheckException("付款条件错误");
      }
    }
    Optional<EntryRegistrationStatusEnum> entryRegistrationStatusEnum =
        EntryRegistrationStatusEnum.fromKey(entryRegistrationOrder.getRegistrationStatus());
    if (!entryRegistrationStatusEnum.isPresent()) {
      throw new CheckException("报备状态错误");
    }
    List<EntryRegistrationDiscountDTO> entryRegistrationDiscountInfo =
        entryRegistrationOrder.getEntryRegistrationDiscountInfo();
    if (CollUtil.isNotEmpty(entryRegistrationDiscountInfo)) {
      entryRegistrationDiscountInfo.forEach(entryRegistrationDiscountDTO -> {
        Optional<EntryRegistrationDiscountTypeEnum> entryRegistrationDiscountTypeEnum =
            EntryRegistrationDiscountTypeEnum.fromKey(entryRegistrationDiscountDTO.getType());
        if (!entryRegistrationDiscountTypeEnum.isPresent()) {
          throw new CheckException("折扣类型错误");
        }
      });
    }
    EntryRegistrationLandingMerchantDTO entryRegistrationLandingMerchant =
        param.getEntryRegistrationLandingMerchant();
    if (StrUtil.isNotBlank(entryRegistrationLandingMerchant.getCompanyAttributeType())) {
      for (String companyAttributeType : StrUtil.split(
          entryRegistrationLandingMerchant.getCompanyAttributeType(), ",")) {
        Optional<EntryRegistrationCompanyAttributeEnum> entryRegistrationCompanyAttributeEnum =
            EntryRegistrationCompanyAttributeEnum.fromKey(companyAttributeType);
        if (!entryRegistrationCompanyAttributeEnum.isPresent()) {
          throw new CheckException("公司属性错误");
        }
      }
    }
  }

  private String generateRegistrationNumber() {
    String prefix = "RZBB";
    String toDay = DateUtil.format(new Date(), "yyyyMMdd");
    String firstNumber = "000001";
    EntryRegistrationOrder lastByCreateTimeAndState =
        entryRegistrationOrderRepository.findFirstByOrderByCreateTimeDesc();
    if (lastByCreateTimeAndState == null) {
      return prefix + toDay + firstNumber;
    }
    Long createTime = lastByCreateTimeAndState.getCreateTime();
    long now = System.currentTimeMillis();
    // 将时间戳转换为LocalDateTime对象
    LocalDateTime dateTime1 = DateUtil.date(createTime).toTimestamp().toLocalDateTime();
    LocalDateTime dateTime2 = DateUtil.date(now).toTimestamp().toLocalDateTime();
    // 比较年、月、日是否相同
    if (dateTime1.toLocalDate().equals(dateTime2.toLocalDate())) {
      String registrationNumber = lastByCreateTimeAndState.getRegistrationNumber();
      int newRegistrationNumber = Integer.parseInt(
          StrUtil.sub(registrationNumber, registrationNumber.length() - 6,
              registrationNumber.length()));
      return prefix + toDay + StrUtil.fillBefore(Integer.toString(++newRegistrationNumber), '0', 6);
    }
    return prefix + toDay + firstNumber;
  }


  @Override
  @Transactional
  public void contractArchivingHandleRef(String entryRegistrationOrderId) {
    EntryRegistrationEntity entity = registrationRepository.byId(entryRegistrationOrderId,
        manageSecurityUtil.getOperatingUser());
    // 获取落地商
    EntryRegistrationLandingMerchant merchant = entity.getMerchant();
    List<Platform> platformList = entity.getPlatformList();
    // 修改为多平台适配
    // 下单平台与对方签约主体(供应商)确定唯一一条合同、若重复则上一条失效
    for (Platform platform : platformList) {
      List<LandingMerchantContract> landingMerchantContracts =
          landingMerchantContractDao.findBySecSupplierAndPlatformCode(merchant.getSupplierId(),
              platform.getCode());
      landingMerchantContracts.forEach(item -> {
        item.setContractStatus(ContractStatus.INVALID.getCode());
        item.setUpdateTime(System.currentTimeMillis());
        item.setUpdateMan(manageSecurityUtil.getOperatingUser().getUserId());
        registrationRepository.saveContract(item);
      });
    }
    entity.contractArchivingHandle();
    registrationRepository.saveEntity(entity);
    LandingMerchantContract contract = entity.getContract();
    if (contract == null) {
      throw new CheckException("合同信息不存在");
    }
    String contractStatus =
        ContractStatus.judgeStatus(contract.getEffectiveStart(), contract.getEffectiveEnd(), FileReviewStateEnum.THROUGH_THE.getKey()).getCode();
    entity.getContract().setContractStatus(contractStatus);
    entity.getContract().setFileReviewState(FileReviewStateEnum.THROUGH_THE.getKey());
    // 保存合同
    registrationRepository.saveContract(entity.getContract());
    List<SupplierPerformance> supplierPerformanceList = entity.getSupplierPerformanceList();
    for (SupplierPerformance supplierPerformance : supplierPerformanceList) {
      shareSupplierPerformanceService.relatedContract(supplierPerformance.getSupplierId(),
          entity.getContract().getId(), supplierPerformance.getPlatformCode(),
          manageSecurityUtil.getOperatingUser().getUserId(), true);
    }
    //报备单附件审核通过更新折扣信息到OMS
    shareEntryRegistrationService.batchUpdateDiscountToOms(entity);
    //归档推送报备单折扣比例到合同
    pushDiscountOrderToContract(entity);
    //合同归档短信通知 落地商
    String phone = merchant.getAccountUserPhone();
    if (StrUtil.isNotBlank(phone)){
      xhgjSMSRequest.sendSms(ShortMessageEnum.CONTRACT_ARCHIVING_NOTIFIES_THE_SUPPLIER,
          phone,null);
    }
  }

  private void pushDiscountOrderToContract(EntryRegistrationEntity entity) {
    //归档推送报备单折扣比例到合同
    deleteEntryRegistrationDiscount(entity.getContract().getId());
    List<EntryRegistrationDiscountDTO> discountList =
        Optional.ofNullable(entity.getDiscounts()).orElse(new ArrayList<>()).stream().map(item -> {
          EntryRegistrationDiscountDTO dto = new EntryRegistrationDiscountDTO();
          dto.setId(item.getId());
          dto.setType(item.getType());
          dto.setPerformanceAmount(item.getPerformanceAmount());
          dto.setDiscountRatio(item.getDiscountRatio());
          dto.setBrandId(item.getBrandId());
          dto.setBrandName(item.getBrandName());
          return dto;
        }).collect(Collectors.toList());
    discountList.forEach(entryRegistrationDiscountDTO -> entryRegistrationDiscountRepository.save(
          entryRegistrationDiscountDTO.toEntity(System.currentTimeMillis(), null,
              manageSecurityUtil.getOperatingUser().getUserId(), entity.getContract().getId())));
  }

  private void deleteEntryRegistrationDiscount(String contractId) {
    List<EntryRegistrationDiscount> entryRegistrationDiscounts =
        entryRegistrationDiscountRepository.findByLandingContractIdAndState(
            contractId, Constants.STATE_OK);
    if (CollUtil.isEmpty(entryRegistrationDiscounts)) {
      return;
    }
    for (EntryRegistrationDiscount entryRegistrationDiscount : entryRegistrationDiscounts) {
      entryRegistrationDiscount.setState(Constants.STATE_DELETE);
      entryRegistrationDiscountRepository.save(entryRegistrationDiscount);
    }
  }


  @Override
  @Transactional(rollbackFor = Exception.class)
  public boolean landingMerchantAdd(EntryRegistrationLandingMerchantAddParam param) {
    EntryRegistrationOrder entryRegistrationOrder = get(param.getEntryRegistrationOrderId());
    String registrationStatus = entryRegistrationOrder.getRegistrationStatus();
    if (Objects.equals(registrationStatus, EntryRegistrationStatusEnum.UNDER_REVIEW.getKey())) {
      throw new CheckException("该报备单正在审核中，不能再次修改");
    }
    Boolean first = false;
    String license = param.getLicense();
    List<String> idCardPhoto = param.getIdCardPhoto();
    List<String> productQualification = param.getProductQualification();
    if (StrUtil.isNotBlank(param.getId())) {
      first = true;
      fileService.delete(param.getId(), Constants.FILE_TYPE_LANDING_MERCHANT_LICENSE);
      fileService.delete(param.getId(), Constants.FILE_TYPE_LANDING_MERCHANT_ID_CARD_PHOTO);
      fileService.delete(param.getId(), Constants.FILE_TYPE_LANDING_MERCHANT_PRODUCT_QUALIFICATION);
    }
    fileService.association(license, param.getId(), Constants.FILE_TYPE_LANDING_MERCHANT_LICENSE);
    if (CollUtil.isNotEmpty(idCardPhoto)) {
      idCardPhoto.forEach(
          idCard -> {
            fileService.association(
                idCard, param.getId(), Constants.FILE_TYPE_LANDING_MERCHANT_ID_CARD_PHOTO);
          });
    }
    if (CollUtil.isNotEmpty(productQualification)) {
      productQualification.forEach(
          product -> {
            fileService.association(
                product, param.getId(), Constants.FILE_TYPE_LANDING_MERCHANT_PRODUCT_QUALIFICATION);
          });
    }
    long createTime = System.currentTimeMillis();
    if (StrUtil.isNotBlank(param.getId())) {
      EntryRegistrationLandingMerchant entryRegistrationLandingMerchant =
          entryRegistrationLandingMerchantRepository.findById(param.getId()).orElseThrow(
              () -> CheckException.noFindException(EntryRegistrationLandingMerchant.class,
                  param.getId()));
      createTime = entryRegistrationLandingMerchant.getCreateTime();
    }
    entryRegistrationLandingMerchantRepository.save(param.toEntity(createTime));

    if (first){
      //新增成功  钉钉通知 业务员
      sendNotifications(entryRegistrationOrder.getRegistrationNumber(),
          entryRegistrationOrder.getSalesmanId(),param.getEnterpriseName());
    }
    return true;
  }

  @Override
  @Transactional(rollbackFor = Exception.class)
  public boolean landingMerchantAddRef(EntryRegistrationLandingMerchantAddParam param) {
    EntryRegistrationEntity entity =
        registrationRepository.byId(param.getEntryRegistrationOrderId(), manageSecurityUtil.getOperatingUser());
    // 判断报备单是否正在审核
    if (entity.isRegistrationUnderReviewReal()) {
      throw new CheckException(EntryRegistrationEntity.ENTRY_REGISTRATION_UNDER_REVIEW);
    }
    entity.setMerchantInput(entryRegistrationFactory.toMerchantInput(param));
    entity.save();
    // 首次
    if (StrUtil.isNotBlank(param.getId())) {
      //新增成功  钉钉通知 业务员
      sendNotifications(entity.getRegistrationNumber(),
          entity.getSalesmanId(),param.getEnterpriseName());
    }
    return true;
  }

  private  void doSendDingDingInform(String registrationNumber,String enterpriseName,
      String mobile) {
    String outTrackId = String.valueOf(System.currentTimeMillis());
    List<String> mobileList = new ArrayList<>();
    if (StrUtil.isNotBlank(registrationNumber) && StrUtil.isNotBlank(enterpriseName) && StrUtil.isNotBlank(mobile)) {
      HashMap<String, Object> map = new HashMap<>();
      map.put("registrationNumber", registrationNumber);
      map.put("enterpriseName", enterpriseName);
      mobileList.add(mobile);
      dingUtils.sendDingTaskRobotCar(Constants_DingCarTemplate.DING_CARD_ID_LANDING_BUSINESS_REGISTRATION_FORM,dingUtils.getDingUserIdList(mobileList),
          outTrackId,map);
    }
  }

  /**
   * 入驻报备单供 H5 详情
   *
   * @param id
   * @return
   */
  @Override
  public EntryRegistrationDetailDTO settlementRegistrationFormForH5Details(String id) {
    EntryRegistrationEntity entity = registrationRepository.byId(id);
    long aDay = 7 * 24 * 60 * 60 * 1000;
    if (entity.getLinkGenerationTime() == null
        || DateUtil.betweenMs(
        new Date(entity.getLinkGenerationTime()),
        new Date()) > aDay) {
      throw  new CheckException("链接已失效");
    }
    return entity.toEntryRegistrationDetailDTO(baseUrl);
  }

  @Override
  public List<EntryRegistrationOrder> getByIds(List<String> entryRegistrationOrderIds) {
    if (CollUtil.isEmpty(entryRegistrationOrderIds)) {
      return new ArrayList<>();
    }
    return entryRegistrationOrderRepository.findByStateAndIdIn(Constants.STATE_OK,
        entryRegistrationOrderIds);
  }

  @Override
  @Transactional
  public Boolean addAndSubmitRef(EntryRegistrationOrderAddParam param) {
    // 修改时携带报备状态、新增时报备状态设置审核中(将要设置的状态)
    param.getEntryRegistrationOrder().setRegistrationStatus(
        StrUtil.blankToDefault(param.getEntryRegistrationOrder().getRegistrationStatus(),
            EntryRegistrationStatusEnum.UNDER_REVIEW.getKey()));
    return addRef(param);
  }

  @Override
  @Transactional
  public void deleteWithLandingMerchantInfo(List<String> ids, User user) {
    // 若状态为审核中或审核通过 则不可删除
    ids.forEach(id -> {
      EntryRegistrationOrder entryRegistrationOrder =
          entryRegistrationOrderRepository.findByIdAndState(id, Constants.STATE_OK)
              .orElseThrow(() -> CheckException.noFindException(EntryRegistrationOrder.class, id));
      EntryRegistrationLandingMerchant landingMerchant =
          entryRegistrationLandingMerchantRepository.findFirstByEntryRegistrationOrderIdAndState(
              entryRegistrationOrder.getId(), Constants.STATE_OK);
      String registrationStatus = entryRegistrationOrder.getRegistrationStatus();
      String supplierAuditStatus = "";
      String finalStatus = registrationStatus;
      if (landingMerchant != null && StrUtil.isNotBlank(landingMerchant.getSupplierAuditStatus())) {
        supplierAuditStatus = landingMerchant.getSupplierAuditStatus();
      }
      //OA驳回
      if (ObjectUtil.equals(registrationStatus,
          EntryRegistrationStatusEnum.REJECTED.getKey())) {
        finalStatus = EntryRegistrationStatusEnum.REJECTED.getKey();
      } else {
        //MDM审核通过
        if (Objects.equals(supplierAuditStatus, AssessStateEnum.PASS.getKey())) {
          finalStatus = EntryRegistrationStatusEnum.APPROVED.getKey();
        }
        //MDM审核驳回
        if (Objects.equals(supplierAuditStatus, AssessStateEnum.REJECT.getKey())) {
          finalStatus = EntryRegistrationStatusEnum.REJECTED.getKey();
        }
        //MDM审核中
        if (ObjectUtil.equals(supplierAuditStatus, AssessStateEnum.UN_ASSESS.getKey())) {
          finalStatus = EntryRegistrationStatusEnum.UNDER_REVIEW.getKey();
        }
      }
      if (StrUtil.equalsAny(finalStatus, EntryRegistrationStatusEnum.UNDER_REVIEW.getKey(),
          EntryRegistrationStatusEnum.APPROVED.getKey())) {
        throw new CheckException("报备单状态为待确认和审核驳回才可删除，请重新勾选！");
      }
      // 若报备单已经存在对应的合同，则无法进行删除
      Optional.ofNullable(landingMerchantContractRepository.findFirstByEntryRegistrationOrderIdAndState(id,
          Constants.STATE_OK)).ifPresent(contract -> {
        throw new CheckException("已生成合同数据，不可删除！");
      });
    });
    dao.logicDeleteInBatch(ids,user);
    // 删除关联的准入落地商信息
    entryRegistrationLandingMerchantDao.logicDeleteByEntryRegistrationOrderIds(ids,user);
  }

  @Transactional
  @Override
  public void copyEntryRegistrationOrder(String id, User user, String userGroup) {
    EntryRegistrationOrder entryRegistrationOrder = Optional.ofNullable(get(id))
        .orElseThrow(()->CheckException.noFindException(EntryRegistrationOrder.class,id));
    EntryRegistrationOrder newOrder =
        MapStructFactory.INSTANCE.toEntryRegistrationOrder(entryRegistrationOrder);
    newOrder.setId(null);
    // 新单号
    newOrder.setRegistrationNumber(generateRegistrationNumber());
    newOrder.setRegistrationStatus(EntryRegistrationStatusEnum.PENDING_CONFIRMATION.getKey());
    // 未归档
    newOrder.setContractArchivingStatus(Constants.STATE_NO);
    newOrder.setReasonForRejection(null);
    newOrder.setRegistrationTime(System.currentTimeMillis());
    newOrder.setOaAuditId(null);
    newOrder.setSalesmanName(user.getRealName());
    newOrder.setCreateTime(System.currentTimeMillis());
    newOrder.setCreateMan(user.getId());
    newOrder.setUpdateMan(user.getId());
    newOrder.setUpdateTime(System.currentTimeMillis());
    if (entryRegistrationOrder.getMerchantCompletionTime() != null) {
      newOrder.setMerchantCompletionTime(System.currentTimeMillis());
    }
    newOrder.setMdmCompletionTime(null);
    newOrder.setOaCompletionTime(null);
    Group group = groupRepository.findFirstByErpCodeAndState(userGroup, Constants.STATE_OK);
    if (group == null) {
      throw new CheckException("未找到对应的组织");
    }
    newOrder.setBusinessCompany(group.getName());
    // 更新业务员公司
    save(newOrder);
    AtomicReference<String> newLandingContractId = new AtomicReference<>("");
    Optional.ofNullable(entryRegistrationLandingMerchantDao.getEntryRegistrationOrderId(entryRegistrationOrder.getId()))
        .ifPresent(entryRegistrationLandingMerchant -> {
          // 同时copy落地商信息
          EntryRegistrationLandingMerchant newMerchant =
              MapStructFactory.INSTANCE.toEntryRegistrationLandingMerchant(entryRegistrationLandingMerchant);
          newMerchant.setId(null);
          newMerchant.setEntryRegistrationOrderId(newOrder.getId());
          newMerchant.setCreateTime(System.currentTimeMillis());
          newMerchant.setCreateMan(user.getId());
          newMerchant.setUpdateMan(user.getId());
          newMerchant.setUpdateTime(System.currentTimeMillis());
          newMerchant.setAssessId(null);
          // 报备单报备状态为待确认，此字段值为null
          newMerchant.setSupplierAuditStatus(null);
          newMerchant.setRejectReason(null);
          entryRegistrationLandingMerchantRepository.save(newMerchant);
          String oldId = entryRegistrationLandingMerchant.getId();
          String newId = newMerchant.getId();
          newLandingContractId.set(newMerchant.getId());
          // 关联文件 营业执照、身份证照片、产品资质书 多个
          associationOneFile(oldId, newId, Constants.FILE_TYPE_LANDING_MERCHANT_LICENSE);
          associationManyFile(oldId, newId, Constants.FILE_TYPE_LANDING_MERCHANT_ID_CARD_PHOTO);
          associationManyFile(oldId, newId,
              Constants.FILE_TYPE_LANDING_MERCHANT_PRODUCT_QUALIFICATION);
        });
    // copy折扣信息(可能有多个)
    List<EntryRegistrationDiscount> newDiscountList = CollUtil.emptyIfNull(
        entryRegistrationDiscountRepository.findByEntryRegistrationOrderIdAndState(
            entryRegistrationOrder.getId(), Constants.STATE_OK)).stream().map(discount -> {
      EntryRegistrationDiscount newDiscount = MapStructFactory.INSTANCE.toEntryRegistrationDiscount(discount);
      newDiscount.setId(null);
      newDiscount.setEntryRegistrationOrderId(newOrder.getId());
      newDiscount.setLandingContractId(
          StrUtil.isNotBlank(newLandingContractId.get()) ? newLandingContractId.get() : null);
      newDiscount.setCreateTime(System.currentTimeMillis());
      newDiscount.setCreateMan(user.getId());
      newDiscount.setUpdateTime(System.currentTimeMillis());
      newDiscount.setUpdateMan(user.getId());

      return newDiscount;
    }).collect(Collectors.toList());
    if (CollUtil.isNotEmpty(newDiscountList)) {
      entryRegistrationDiscountRepository.saveAll(newDiscountList);
    }
  }

  /**
   * relationType存在多个关联
   * 复制relationType类型的文件并关联
   * @param oldId 被copy的id
   * @param newId 需被关联的id
   * @param relationType 文件关联类型
   */
  private void associationManyFile(String oldId,
      String newId,String relationType) {
    fileRepository.findAllByRelationIdAndRelationTypeAndState(
            oldId, relationType, Constants.STATE_OK)
        .ifPresent(files -> {
          files.forEach(file -> {
            copyFileAndRelation(newId, file);
          });
        });
  }

  /**
   * relationType仅存在一个关联
   * 复制relationType类型的文件并关联
   * @param oldId 被copy的id
   * @param newId 需被关联的id
   * @param relationType 文件关联类型
   */
  private void associationOneFile(String oldId,
      String newId,String relationType) {
    fileRepository.findFirstByRelationIdAndRelationTypeAndState(
        oldId,
        relationType, Constants.STATE_OK).ifPresent(file -> {
      copyFileAndRelation(newId, file);
    });
  }

  /**
   * 复制文件并关联
   * @param relationId 关联id
   * @param file 被copy落地商的文件
   */
  private void copyFileAndRelation(String relationId, File file) {
    // copy文件并关联
    File newFile = MapStructFactory.INSTANCE.toFile(file);
    newFile.setId(null);
    newFile.setRelationId(relationId);
    fileRepository.save(newFile);
  }

  @Override
  public void batchUpdateSupplierPerformanceContactsData() {
    List<SupplierPerformance> supplierPerformanceList =
        supplierPerformanceService.findAllByContactsIsNull();
    for (SupplierPerformance supplierPerformance : supplierPerformanceList) {
      try {
        landingMerchantContractRepository.findById(supplierPerformance.getLandingContractId()).ifPresent(landingMerchantContract -> {
          EntryRegistrationEntity entryRegistrationEntity =
              registrationRepository.byId(landingMerchantContract.getEntryRegistrationOrderId());
          if (entryRegistrationEntity != null) {
            EntryRegistrationLandingMerchant merchant = entryRegistrationEntity.getMerchant();
            supplierPerformance.setContacts(merchant.getAccountUser());
            supplierPerformance.setMobile(merchant.getAccountUserPhone());
            supplierPerformance.setArea(entryRegistrationEntity.getCooperationRegion());
            supplierPerformanceService.update(supplierPerformance);
          }
        });
      } catch (Exception e) {
        log.error(ExceptionUtil.stacktraceToString(e, -1));
      }
    }
  }

  @Override
  public void batchUpdateSupplierPerformanceBusinessLeaderData() {
    List<SupplierPerformance> supplierPerformanceList =
        supplierPerformanceService.findAllByBusinessLeaderIsNull();
    for (SupplierPerformance supplierPerformance : supplierPerformanceList) {
      try {
        landingMerchantContractRepository.findById(supplierPerformance.getLandingContractId()).ifPresent(landingMerchantContract -> {
          EntryRegistrationEntity entryRegistrationEntity =
              registrationRepository.byId(landingMerchantContract.getEntryRegistrationOrderId());
          if (entryRegistrationEntity != null) {
            String businessLeaderId = shareLandingMerchantContractService.getBusinessLeaderId(
                entryRegistrationEntity.getSalesmanId());
            if (StrUtil.isNotBlank(businessLeaderId)) {
              supplierPerformance.setBusinessLeader(businessLeaderId);
              supplierPerformanceService.update(supplierPerformance);
            }
          }
        });
      } catch (Exception e) {
        log.error(ExceptionUtil.stacktraceToString(e, -1));
      }
    }
  }

  @Override
  public void batchUpdateSupplierPerformanceDockingPurchaseErpCode() {
    List<SupplierPerformance> supplierPerformanceList =
        supplierPerformanceRepository.findOldData();
    for (SupplierPerformance supplierPerformance : supplierPerformanceList) {
      try {
        AtomicReference<User> defaultPurchase = new AtomicReference<>(
            userRepository.findFirstByRealNameAndState(Constants.USER_NAME_GUO_JIA_LEI,
                Constants.STATE_OK));
        Platform platform = platformRepository.findFirstByCodeAndState(supplierPerformance.getPlatformCode(),
                Constants.STATE_OK)
            .orElseThrow(() -> new CheckException("平台信息不存在"));
        String defaultPurchaseId = Optional.ofNullable(platform.getDefaultPurchase()).orElse("-1");
        userRepository.findById(defaultPurchaseId).ifPresent(defaultPurchase::set);
        supplierPerformance.setDockingPurchaseErpCode(defaultPurchase.get().getCode());
      } catch (Exception e) {
        log.error("批量更新供应商绩效采购员数据异常:{}", e.getMessage(), e);
      }
    }
  }
}
