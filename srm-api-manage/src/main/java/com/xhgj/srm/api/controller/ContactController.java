package com.xhgj.srm.api.controller;

import com.xhgj.srm.api.dto.ContactAddParamDTO;
import com.xhgj.srm.api.dto.ContactsData;
import com.xhgj.srm.api.service.ContactService;
import com.xhiot.boot.mvc.base.ResultBean;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

@RestController
@RequestMapping("/contact")
@Api(tags = {"联系人"})
@Slf4j
public class ContactController {

    @Autowired
    ContactService contactService;

    @ApiOperation(value = "获取联系人列表", notes = "分页获取联系人")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "supplierId", value = "供应商id", required=true, dataType = "String"),
            @ApiImplicitParam(name = "userId", value = "用户id", required=true, dataType = "String")
    })
    @RequestMapping(value="/getCurContactList",method= RequestMethod.GET)
    @ResponseBody
    public ResultBean<List<ContactsData>> getCurContactList(
            String supplierId,String userId
    ) {
        return new ResultBean<>(contactService.getCurContactList(supplierId,userId));
    }

    @ApiOperation(value = "添加修改联系人", notes = "添加修改联系人")
    @RequestMapping(value="/addContact",method= RequestMethod.POST)
    @ResponseBody
    public ResultBean<Boolean> addContact(
            @Valid @RequestBody ContactAddParamDTO contactAddParamDTO
            ) {
        contactService.addContact(contactAddParamDTO.getContacts(),contactAddParamDTO.getSupplierId(),contactAddParamDTO.getMobile(),
                contactAddParamDTO.getUserId());
        return new ResultBean<>(true, "操作成功!");
    }

  @ApiOperation(value = "获取供应商联系人列表", notes = "获取供应商联系人列表")
  @ApiImplicitParams({
      @ApiImplicitParam(name = "supplierId", value = "供应商id", required=true, dataType = "String"),
      @ApiImplicitParam(name = "groupCode", value = "组织code", required=true, dataType = "String")
  })
  @RequestMapping(value="/getSupContactList",method= RequestMethod.GET)
  @ResponseBody
  public ResultBean<List<ContactsData>> getSupContactList(
      String supplierId,String groupCode
  ) {
    return new ResultBean<>(contactService.getSupContactList(supplierId,groupCode));
  }

}
