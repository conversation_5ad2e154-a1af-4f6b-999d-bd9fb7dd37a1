package com.xhgj.srm.api.controller;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.xhgj.srm.api.dto.ChangeRecordTableDTO;
import com.xhgj.srm.api.dto.supplier.AbroadSupplierDTO;
import com.xhgj.srm.api.dto.supplier.ChinaSupplierDTO;
import com.xhgj.srm.api.dto.supplier.PersonSupplierDTO;
import com.xhgj.srm.api.dto.supplier.SupplierBrandDTO;
import com.xhgj.srm.api.service.ChangeRecordService;
import com.xhgj.srm.api.service.SupplierInGroupService;
import com.xhgj.srm.api.task.supplier.SupplierInfoRefreshTask;
import com.xhgj.srm.api.utils.ManageSecurityUtil;
import com.xhgj.srm.api.vo.supplier.ChinaSupplierVO;
import com.xhgj.srm.jpa.entity.User;
import com.xhgj.srm.request.service.third.mpm.MPMService;
import com.xhgj.srm.request.vo.supplierCategory.CategoryTreeResultVO;
import com.xhiot.boot.core.common.exception.CheckException;
import com.xhiot.boot.core.common.util.StringUtils;
import com.xhiot.boot.framework.web.dto.param.PageParam;
import com.xhiot.boot.mvc.base.PageResult;
import com.xhiot.boot.mvc.base.ResultBean;
import com.xhiot.boot.repeat.annotation.RepeatSubmit;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import java.util.List;
import java.util.Objects;
import javax.annotation.Resource;
import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

/**
 * <AUTHOR>
 * @since 2022/7/12 14:13
 */
@Api(tags = {"组织内供应商管理接口"})
@RestController
@RequestMapping("supplierInGroup")
@Validated
public class SupplierInGroupController extends AbstractRestController {

  @Autowired private SupplierInGroupService service;
  @Autowired private ChangeRecordService changeRecordService;
  @Resource private MPMService mpmService;
  @Resource
  private ManageSecurityUtil manageSecurityUtil;
  @Resource
  private SupplierInfoRefreshTask supplierInfoRefreshTask;

  @ApiOperation(value = "保存国内供应商")
  @ApiImplicitParams({@ApiImplicitParam(name = "groupCode", value = "组织编码", required = true)})
  @PostMapping("saveChinaSupplier")
  @RepeatSubmit
  public ResultBean<Boolean> saveChinaSupplier(
      @RequestBody @Valid ChinaSupplierDTO dto, @RequestParam String groupCode) {
    User user = getUser();
    // 增加品牌校验 后续了解方法后优化此校验
    if (Objects.nonNull(dto) && CollUtil.isNotEmpty(dto.getBrandList())) {
      List<SupplierBrandDTO> brandList = dto.getBrandList();
      for (SupplierBrandDTO brandDTO : brandList) {
        if (Objects.nonNull(brandDTO) && StrUtil.isBlank(brandDTO.getBusinessType())) {
          throw new CheckException("品牌经营类型不能为空");
        }
      }
    }
    service.saveSupplierInGroup(dto, user, validateAndGetGroup(user, groupCode));
    return successSaveBean();
  }

  @ApiOperation(value = "供应商等级调整", notes = "供应商等级调整")
  @ApiImplicitParams({
      @ApiImplicitParam(name = "id", value = "id", required = true),
      @ApiImplicitParam(name = "enterpriseLevel", value = "等级", required = true),
      @ApiImplicitParam(name = "groupCode", value = "组织code", required = true)
  })
  @PostMapping(value = "/updateSupplierLeave")
  public ResultBean<Boolean> updateSupplierLeave(
      @RequestParam String id,
      @RequestParam @NotBlank(message = "等级不能为空") String enterpriseLevel,
      @RequestParam String groupCode) {
    User user = getUser();
    service.updateSupplierLeave(id, enterpriseLevel,user,validateAndGetGroup(user, groupCode));
    return new ResultBean<>(true);
  }

  @ApiOperation(value = "保存个人供应商")
  @ApiImplicitParams({@ApiImplicitParam(name = "groupCode", value = "组织编码", required = true)})
  @PostMapping("savePersonSupplier")
  @RepeatSubmit
  public ResultBean<Boolean> savePersonSupplier(
      @RequestBody @Valid PersonSupplierDTO dto, @RequestParam String groupCode) {
    User user = getUser();
    service.savePersonSupplier(dto, user, validateAndGetGroup(user, groupCode));
    return successSaveBean();
  }

  @ApiOperation(value = "导入个人供应商")
  @ApiImplicitParams({
      @ApiImplicitParam(name = "file", value = "文件", required = true)
  })
  @PostMapping("/importPersonSupplier")
  public ResultBean<Boolean> saveSupplierOrderExcel(
      @RequestParam MultipartFile file,
      @RequestParam String groupCode) {
    User user = getUser();
    service.importPersonSupplier(file,validateAndGetGroup(user, groupCode),user);
    return new ResultBean<>();
  }

  @ApiOperation(value = "获取国内供应商")
  @ApiImplicitParams({@ApiImplicitParam(name = "id", value = "组织内供应商 id", required = true)})
  @GetMapping("getChinaSupplier")
  @RepeatSubmit
  public ResultBean<ChinaSupplierVO> getChinaSupplier(@RequestParam String id) {
    if (StringUtils.isNullOrEmpty(id)) {
      throw new CheckException("id 缺失，请联系管理员！");
    }
    return new ResultBean<>(service.getChinaSupplier(id, getUser()));
  }

  @ApiOperation(value = "保存海外供应商")
  @ApiImplicitParams({@ApiImplicitParam(name = "groupCode", value = "组织编码", required = true)})
  @PostMapping("saveAbroadSupplier")
  public ResultBean<Boolean> saveAbroadSupplier(
      @RequestBody @Valid AbroadSupplierDTO dto, @RequestParam String groupCode) {
    User user = getUser();
    service.saveSupplierInGroup(dto, user, validateAndGetGroup(user, groupCode));
    return successSaveBean();
  }

  @ApiOperation(value = "获取海外供应商")
  @ApiImplicitParams({@ApiImplicitParam(name = "id", value = "组织内供应商 id", required = true)})
  @GetMapping("getAbroadSupplier")
  public ResultBean<AbroadSupplierDTO> getAbroadSupplier(@RequestParam String id) {
    if (StringUtils.isNullOrEmpty(id)) {
      throw new CheckException("id 缺失，请联系管理员！");
    }
    return new ResultBean<>(service.getAbroadSupplier(id, getUser()));
  }

  @ApiOperation("分页获取组织内供应商变更记录")
  @ApiImplicitParams({@ApiImplicitParam(name = "id", value = "组织内供应商 id", required = true)})
  @GetMapping("/getChangeRecordPage")
  public ResultBean<PageResult<ChangeRecordTableDTO>> getChangeRecordPage(
      @RequestParam String id, @Valid PageParam param) {
    if (StringUtils.isNullOrEmpty(id)) {
      throw new CheckException("组织内供应商 id 必传！");
    }
    return new ResultBean<>(changeRecordService.getPage(id, param.toPageable()));
  }

  @ApiOperation(value = "导入批改联系人", notes = "导入批改联系人")
  @ApiImplicitParams({
    @ApiImplicitParam(name = "userId", value = "用户 id", required = true),
    @ApiImplicitParam(name = "file", value = "文件", required = true)
  })
  @PostMapping(value = "/importGroup", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
  public ResultBean<Boolean> importCorrectionContact(
      @RequestParam MultipartFile file,
      @RequestParam @NotBlank(message = "用户 id 不能为空") String userId) {
    service.importCorrectionContact(file, userId);
    return new ResultBean<>(true);
  }

  @ApiOperation(value = "删除组织下供应商", notes = "删除组织下供应商")
  @ApiImplicitParams({
    @ApiImplicitParam(name = "ids", value = "组织下供应商 id 逗号分隔", required = true),
  })
  @PostMapping(value = "/deleteSupplierInGroup", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
  public ResultBean<Boolean> deleteSupplierInGroup(
      @RequestParam @NotBlank(message = "组织下供应商 id 不能为空") String ids) {
    service.deleteSupplierInGroup(ids);
    return new ResultBean<>(true);
  }

  @ApiOperation(value = "处理供应商无组织编码的数据")
  @PostMapping("handleSupplierNoCreateCodeData")
  public ResultBean<Boolean> handleSupplierNoCreateCodeData(){
    service.handleSupplierNoCreateCodeData();
    return new ResultBean<>(true);
  }

  @ApiOperation(value = "获取供应商的所在组织erpId")
  @GetMapping("erpCode")
  public ResultBean<List<String>> getGroupErpId(@NotBlank(message = "参数非法") String supplierId){
    return new ResultBean<>(service.getGroupErpId(supplierId));
  }

  @ApiOperation("批处理程序不同组织供应商的营业执照")
  @GetMapping("/batchUpdateSupplierInGroupLicenses")
  public ResultBean<Boolean> batchUpdateSupplierInGroupLicenses() {
    service.batchUpdateSupplierInGroupLicenses();
    return new ResultBean<>();
  }

  /**
   * 供应商经营类目树
   * @return
   */
  @ApiOperation(value = "类目树", notes = "类目树")
  @GetMapping(value = "/getCategoryTree")
  public ResultBean<List<CategoryTreeResultVO>> getCategoryTree() {
    return new ResultBean<>(mpmService.getCategoryListTree("", ""));
  }

  /**
   * 供应商经营品牌导入
   * @param file
   * @param userId
   * @return
   */
  @PostMapping(value = "/import/brand", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
  public ResultBean<Boolean> importBrand(
      @RequestParam MultipartFile file) {
    User user = manageSecurityUtil.getSrmUserDetails().getUser();
    service.importBrand(file, user.getId());
    return new ResultBean<>(true);
  }

  /**
   * 供应商经营类目导入
   */
  @PostMapping(value = "/import/category", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
  public ResultBean<Boolean> importCategory(
      @RequestParam MultipartFile file){
    User user = manageSecurityUtil.getSrmUserDetails().getUser();
    service.importCategory(file, user.getId());
    return new ResultBean<>(true);
  }

  /**
   * 供应商等级导入
   */
  @PostMapping(value = "/import/level", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
  public ResultBean<Boolean> importLevel(@RequestParam MultipartFile file) {
    User user = manageSecurityUtil.getSrmUserDetails().getUser();
    service.importLevel(file, user.getId());
    return new ResultBean<>(true);
  }

  @PostMapping(value = "/supplierInfoRefreshTask")
  public ResultBean<Boolean> supplierInfoRefreshTask(@RequestParam Boolean batch,
      @RequestParam(required = false) List<String> groupCodes) {

    if (batch == null) {
      batch = true;
    }
    supplierInfoRefreshTask.taskHandler(batch, groupCodes);
    return new ResultBean<>(true);
  }
}
