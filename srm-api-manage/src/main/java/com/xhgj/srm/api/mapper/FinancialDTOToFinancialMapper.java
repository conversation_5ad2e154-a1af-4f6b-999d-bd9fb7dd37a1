package com.xhgj.srm.api.mapper;

import com.xhgj.srm.common.Constants;
import com.xhgj.srm.common.config.BaseMappingConfig;
import com.xhgj.srm.dto.financial.FinancialDTO;
import com.xhgj.srm.jpa.entity.Financial;
import org.mapstruct.BeforeMapping;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;

/** <AUTHOR> @ClassName FinancialDTOToFinancialMapper */
@Mapper(componentModel = "spring")
public interface FinancialDTOToFinancialMapper extends BaseMappingConfig<FinancialDTO, Financial> {

  /**
   * 映射同名属性
   *
   * @param financialDTO 源对象
   * @return 目标对象
   */
  @Override
  Financial sourceToTarget(FinancialDTO financialDTO);

  /**
   * 在映射最后一步对属性的自定义映射处理
   *
   * @param financial 目标对象
   */
  @BeforeMapping
  static void afterUpdateDto(@MappingTarget Financial financial) {
    financial.setState(Constants.STATE_OK);
    financial.setCreateTime(System.currentTimeMillis());
  }

}
