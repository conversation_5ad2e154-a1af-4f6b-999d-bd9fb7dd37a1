package com.xhgj.srm.api.dto.financial.voucher;

import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Size;
import lombok.Data;

@Data
public class ExtensionApplicationParam {
  @ApiModelProperty("付款申请id")
  private String id;

  @ApiModelProperty(value = "财务凭证id集合")
  @NotEmpty(message = "财务凭证id集合不能为空")
  private List<DataMapping> data;

  @ApiModelProperty(value = "登录人id")
  @NotBlank(message = "登录人id不能为空")
  private String userId;


  @ApiModelProperty("开户行")
  private String bank;

  @ApiModelProperty("银行账号")
  private String bankAccount;

  @ApiModelProperty("联行号")
  private String bankCode;

  @ApiModelProperty("账户名称")
  private String accountName;

  /**
   * 新增备注字段 限制500字符
   */
  @ApiModelProperty(value = "备注")
  @Size(max = 500, message = "备注不能超过500个字符")
  String remark;

  @Data
  public class DataMapping{
    private String id;
    private String value;
    private String paymentType;
  }
}
