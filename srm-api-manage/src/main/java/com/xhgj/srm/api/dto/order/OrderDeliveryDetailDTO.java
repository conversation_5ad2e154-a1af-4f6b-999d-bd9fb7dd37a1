package com.xhgj.srm.api.dto.order;

import com.xhgj.srm.common.utils.supplierorder.BigDecimalUtil;
import com.xhgj.srm.jpa.entity.OrderDeliveryDetail;
import com.xhiot.boot.core.common.util.StringUtils;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import lombok.Data;


@Data
public class OrderDeliveryDetailDTO {

    @ApiModelProperty("发货明细id")
    private String id;
    @ApiModelProperty("物料编码")
    private String code;
    @ApiModelProperty("品牌")
    private String brand;
    @ApiModelProperty("商品名称")
    private String name;
    @ApiModelProperty("型号")
    private String model;
    @ApiModelProperty("数量")
    private BigDecimal num;
    @ApiModelProperty("单位")
    private String unit;
    @ApiModelProperty("单价")
    private BigDecimal price;
    @ApiModelProperty("发货数量")
    private BigDecimal delCount;

    public OrderDeliveryDetailDTO(OrderDeliveryDetail orderDeliveryDetail){
        this.id = orderDeliveryDetail.getId();
        this.code = StringUtils.emptyIfNull(orderDeliveryDetail.getCode());
        this.brand = StringUtils.emptyIfNull(orderDeliveryDetail.getBrand());
        this.name = StringUtils.emptyIfNull(orderDeliveryDetail.getName());
        this.model = StringUtils.emptyIfNull(orderDeliveryDetail.getModel());
        this.num = BigDecimalUtil.formatForStandard(orderDeliveryDetail.getNum());
        this.price = orderDeliveryDetail.getPrice();
        this.unit = StringUtils.emptyIfNull(orderDeliveryDetail.getUnit());
        this.delCount = BigDecimalUtil.formatForStandard(orderDeliveryDetail.getShipNum());
    }

}
