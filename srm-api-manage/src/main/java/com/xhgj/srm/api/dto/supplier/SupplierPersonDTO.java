package com.xhgj.srm.api.dto.supplier;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 分页展示个人供应商的信息
 *
 * <AUTHOR>
 * @since 2022/7/11 13:31
 */
@Data
public class SupplierPersonDTO {

  @ApiModelProperty("组织内供应商 id")
  private String id;

  @ApiModelProperty("mdm 编码")
  private String mdmCode;

  @ApiModelProperty("姓名")
  private String personName;

  @ApiModelProperty("联系方式")
  private String mobile;

  @ApiModelProperty("开户银行")
  private String bankName;

  @ApiModelProperty("银行账号")
  private String bankNum;

  @ApiModelProperty("联行号")
  private String bankCode;

  @ApiModelProperty("负责人")
  private String purchaserName;

  @ApiModelProperty("创建时间")
  private Long createTime;

  @ApiModelProperty("账户名称")
  private String bankAccount;

}
