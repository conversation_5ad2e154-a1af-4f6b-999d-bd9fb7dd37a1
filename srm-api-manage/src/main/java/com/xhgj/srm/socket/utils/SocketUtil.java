package com.xhgj.srm.socket.utils;

import com.alibaba.fastjson.JSONObject;
import com.xhgj.srm.common.Constants;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.messaging.simp.SimpMessagingTemplate;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @since 2020/6/7 15:39
 */
@Component
public class SocketUtil {
  @Autowired SimpMessagingTemplate template;

  /**
   * 通知用户任务完成
   *
   * @param userId userId
   */
  public void noticeUserTaskDone(
      String userId, String missionName, String missionId,String state,String executeResult) {
    JSONObject obj = new JSONObject();
    obj.put("taskId", missionId);
    obj.put("taskType", missionName);
    obj.put("executeStateCode", state);
    obj.put("executeStateName", missionName+(Constants.YES.equals(state)?"成功":"失败"));
    obj.put("executeResult", executeResult);
    noticeUser(userId, obj.toString());
  }

  /**
   * 向用户发送通知消息
   *
   * @param userId userId
   * @param payload 消息内容
   */
  public void noticeUser(String userId, String payload) {
    template.convertAndSendToUser(userId, "/notice", payload);
  }
}
