spring:
  application:
    name: srm-api-manage
  profiles:
    active: env-local
    include: datasource,boot
  http:
    encoding:
      charset: utf-8
      enabled: true
      force: true
  servlet:
    multipart:
      max-file-size: 2048MB
      max-request-size: 2058MB
  mail:
    host: smtp.qiye.aliyun.com
    username: <EMAIL>
    password: ga6UVI0WMjIZZXCw
    properties:
      mail:
        smtp:
          auth: true
          starttls:
            enable: true
            required: true
          ssl:
            enable: false
    port: 465
  rabbitmq:
    listener:
      simple:
        prefetch: 1
        acknowledge-mode: manual
      direct:
        acknowledge-mode: manual
    connection-timeout: 0s
    publisher-confirms: true
    publisher-returns: true
  cloud:
    nacos:
      config:
        file-extension: "yaml"
        shared-configs:
          - data-id: "srm-shared-configs.yaml"
            refresh: true
          - data-id: "srm-ledger-subject.yaml"
            refresh: true
xhiot:
  boot:
    security:
      jwt:
        secret: srm-api-manage
        on-cache: true
        sys-short-name: SRM
      custom:
        ignore-urls:
          - /inventorySafety/check/confirm
          - /inventorySafety/check/detail
          - /login/userLogin
          - /contract/addContract
          - /socket/**
          - /user/batchUpdateUserDepart
          - /supplier/dealNoXESupplier
          - /assess/mdmPassAssess
          - /assess/mdmRejectAssess
          - /supplier/mdmUpdateSupplierMainData
          - /supplierOrder/saveSupplierOrder
          - /supplierOrder/updateStockQty
          - /supplierOrder/noticeAssessResult
          - /supplier/getSupplierDomesticByPage
          - /supplier/getByMdmCodeAndNameAndGroupId
          - /supplier/getCodeBySupplierId
          - /supplier/dockingPurchase
          - /supplier/getIdByMDMCode
          - /supplier/getSupplierInfo
          - /account/getAccountPageInfo
          - /account/webhook/openInvoice
          - /account/getAccountDetail
          - /account/confirmAccountInvoice
          - /account/confirmAccountInvoiceByH5
          - /supplierBrand/setBrandPass
          - /supplierBrand/setBrandUnpassReason
          - /supplierBrand/updateBrand
          - /manage/orderInvoiceRelation/audit-h5
          - /manage/orderInvoiceRelation/webhook/openInvoice
          - /manage/orderInvoiceRelation/oldOrderAccount
          - /manage/orderInvoiceRelation/processOldOrder
          - /manage/orderInvoiceRelation/getDetail
          - /manage/orderInvoiceRelation/getDetailCheck
          - /manage/orderInvoiceRelation/verification-result
          - /manage/orderInvoiceRelation/audit-h5
          - /order/exportOrderNew
          - /upload/base-url
          - /manage/orderInvoiceRelation/verification
          - /order/updatePaymentState
          - /supplierOrder/refuseOrderCallback
          - /account/getAccountPage
          - /product/sendProductPriceReject
          - /product/sendProductPricePass
          - /supplierInGroup/importPersonSupplier
          - /entry-registration/landing-merchant/add
          - /entry-registration/landing-merchant/detail
          - /supplierSearch/searchChinaSupplier
          - /order/getOrderPlatformList
          - /xhgj/getAllProvinceCity
          - /brand/getSearchByName
          - /boot/ding/auth
          - /purchaseOrder/updateOrderCode
          - /purchaseOrder/getContractFiles
          - /financialVoucher/exportExcel
          - /financialVoucher/exportFinancialExcel
          - /upload/uploadFile
          - /assess/doPassHandle
          - /assess/doRejectHandle
          - /data-warehouse/bank/branch/page
          - /data-warehouse/bank/branch/pageNew
          - /login/getCodeImg
          - /order/orderImportUpdatePlatformYsOrder
    app-name: ${spring.application.name}
    sys:
      error:
        ding:
          config:
            access-token: "3c0c25fd94f37f3c7b19ab884d1a39148b4d5c5ed6dfe2a54e57b27baf2e651a"
            at-mobiles-or-user-ids: ["***********"]
    ding:
      client-id: "ding65euvxming81gc3c"
      client-secret: "3UDwpVNWYLYYqLjoeGL_g7DK-2W-MjQ_8DDEtTbHrbwjq0gMzEe_9m0lzn5vgGjw"
server:
  port: 8081
  servlet:
    context-path: /${spring.application.name}
  tomcat:
    uri-encoding: utf-8
logging:
  level:
    root: info
third:
  xhgj:
    partner:
      enable-log: true