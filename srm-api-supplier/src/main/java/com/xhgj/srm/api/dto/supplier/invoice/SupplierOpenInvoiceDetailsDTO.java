package com.xhgj.srm.api.dto.supplier.invoice;

import cn.hutool.core.collection.CollUtil;
import com.xhgj.srm.api.dto.FileDTO;
import com.xhgj.srm.common.utils.supplierorder.BigDecimalUtil;
import com.xhgj.srm.jpa.entity.OrderSupplierInvoice;
import com.xhgj.srm.jpa.entity.SupplierOrder;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import lombok.Data;

/**
 * Created by Geng Shy on 2023/11/30
 */

@Data
public class SupplierOpenInvoiceDetailsDTO {

  @ApiModelProperty("发票列表")
  private List<SupplierOpenInvoiceDetailsInvoiceDTO> invoices;
  @ApiModelProperty("订单列表")
  private List<SupplierOpenInvoiceDetailsOrderDTO> orders;
  @ApiModelProperty("审核状态")
  private String dataState;
  @ApiModelProperty("发票价税合计")
  private String invoiceTotalAmountIncludingTax;
  @ApiModelProperty("订单价税合计")
  private String orderTotalAmountIncludingTax;
  @ApiModelProperty("驳回理由")
  private String rejection;
  @ApiModelProperty("物流公司")
  private String logisticsCompany;
  @ApiModelProperty("物流单号")
  private String logisticsNum;


  @Data
  public static class SupplierOpenInvoiceDetailsInvoiceDTO {

    @ApiModelProperty("id")
    private String id;
    @ApiModelProperty("发票类型")
    private String invoiceType;
    @ApiModelProperty("发票号")
    private String invoiceNum;
    @ApiModelProperty("发票代码")
    private String invoiceCode;
    @ApiModelProperty("开票时间")
    private Long invoiceTime;
    @ApiModelProperty("合计金额")
    private BigDecimal totalAmount;
    @ApiModelProperty("合计税额")
    private BigDecimal totalTaxAmount;
    @ApiModelProperty("价税合计")
    private BigDecimal totalAmountIncludingTax;
    @ApiModelProperty("发票附件")
    private FileDTO file;
    @ApiModelProperty("验真类型")
    private String verificationType;
    @ApiModelProperty("校验码")
    private String checkCode;

    public static List<SupplierOpenInvoiceDetailsInvoiceDTO> buildList(List<OrderSupplierInvoice> orderSupplierInvoices) {
      ArrayList<SupplierOpenInvoiceDetailsInvoiceDTO> result = new ArrayList<>();
      if (CollUtil.isEmpty(orderSupplierInvoices)) {
        return result;
      }
      for (OrderSupplierInvoice orderSupplierInvoice : orderSupplierInvoices) {
        SupplierOpenInvoiceDetailsInvoiceDTO
            invoice = new SupplierOpenInvoiceDetailsInvoiceDTO();
        invoice.setId(orderSupplierInvoice.getId());
        invoice.setInvoiceType(orderSupplierInvoice.getInvoiceType());
        invoice.setInvoiceNum(orderSupplierInvoice.getInvoiceNum());
        invoice.setInvoiceCode(orderSupplierInvoice.getInvoiceCode());
        invoice.setInvoiceTime(orderSupplierInvoice.getInvoiceTime());
        invoice.setTotalAmount(orderSupplierInvoice.getTotalAmount());
        invoice.setTotalTaxAmount(orderSupplierInvoice.getTotalTaxAmount());
        invoice.setTotalAmountIncludingTax(orderSupplierInvoice.getTotalAmountIncludingTax());
        invoice.setVerificationType(orderSupplierInvoice.getVerificationType());
        invoice.setCheckCode(orderSupplierInvoice.getCheckCode());
        result.add(invoice);
      }
      return result;
    }
  }

  @Data
  public static class SupplierOpenInvoiceDetailsOrderDTO {

    @ApiModelProperty("订单id")
    private String id;
    @ApiModelProperty("采购单号")
    private String code;
    @ApiModelProperty("创建时间")
    private Long createTime;
    @ApiModelProperty("收件人")
    private String receiveMan;
    @ApiModelProperty("结算件数")
    private BigDecimal number;
    @ApiModelProperty("最终结算金额")
    private BigDecimal finalPrice;

    public static List<SupplierOpenInvoiceDetailsOrderDTO> buildList(List<SupplierOrder> orders) {
      ArrayList<SupplierOpenInvoiceDetailsOrderDTO> result = new ArrayList<>();
      if (CollUtil.isEmpty(orders)) {
        return result;
      }
      for (SupplierOrder order : orders) {
        SupplierOpenInvoiceDetailsOrderDTO
            supplierInvoiceOrder = new SupplierOpenInvoiceDetailsOrderDTO();
        supplierInvoiceOrder.id = order.getId();
        supplierInvoiceOrder.code = order.getCode();
        supplierInvoiceOrder.createTime = order.getCreateTime();
        supplierInvoiceOrder.receiveMan = order.getReceiveMan();
        supplierInvoiceOrder.finalPrice = BigDecimalUtil.formatForStandard(order.getFinalPrice());
        result.add(supplierInvoiceOrder);
      }
      return result;
    }
  }
}
