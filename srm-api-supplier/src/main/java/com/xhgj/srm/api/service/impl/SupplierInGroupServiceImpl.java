package com.xhgj.srm.api.service.impl;

import com.xhgj.srm.api.service.SupplierInGroupService;
import com.xhgj.srm.common.Constants;
import com.xhgj.srm.jpa.dao.SupplierInGroupDao;
import com.xhgj.srm.jpa.entity.SupplierInGroup;
import com.xhgj.srm.jpa.repository.SupplierInGroupRepository;
import com.xhiot.boot.framework.jpa.repository.BootBaseRepository;
import java.util.List;
import java.util.Optional;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @since 2022/10/12 9:09
 */
@Service
@Slf4j
public class SupplierInGroupServiceImpl implements SupplierInGroupService {
  @Autowired
  private SupplierInGroupRepository repository;

  @Autowired private SupplierInGroupDao dao;

  @Override
  public BootBaseRepository<SupplierInGroup, String> getRepository() {
    return repository;
  }

  @Override
  public List<SupplierInGroup> getBySupplierId(String supplierId) {
    return dao.getAllBySupplier(supplierId);
  }

  @Override
  public Optional<SupplierInGroup> getFirstBySupplierIdAndGroupId(String supplierId,
      String groupId) {
    return repository.findFirstBySupplierIdAndGroupIdAndStateNot(supplierId, groupId,
        Constants.STATE_DELETE);

  }
}
