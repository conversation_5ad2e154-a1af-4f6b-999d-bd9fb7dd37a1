package com.xhgj.srm.api.service;

import com.xhgj.srm.api.dto.FinancialLicenseAddParam;
import com.xhgj.srm.api.dto.FinancialPageData;
import com.xhgj.srm.api.dto.financial.FinancialInfoDTO;
import com.xhgj.srm.jpa.entity.File;
import com.xhgj.srm.jpa.entity.Financial;
import com.xhgj.srm.jpa.entity.SupplierFb;
import com.xhiot.boot.framework.jpa.service.BootBaseService;
import com.xhiot.boot.mvc.base.PageResult;
import java.util.List;

/**
 * @ClassName FinancialService
 * Create by Liuyq on 2021/6/7 16:19
 **/
public interface FinancialService extends BootBaseService<Financial, String> {

    /**
     * 分页获取财务信息
     * @Author: liuyq
     * @Date: 2021/6/7 16:22
     * @param supplierId
     * @param pageNo
     * @param pageSize
     * @return com.xhiot.boot.mvc.base.PageResult<com.xhgj.srm.api.dto.FinancialPageData>
     **/
    PageResult<FinancialPageData> getFinancialList(String supplierId, String pageNo, String pageSize);

    /**
     * 上传开户许可证书
     * @Author: liuyq
     * @Date: 2021/6/7 16:49
     * @param addParam
     * @return com.xhgj.srm.jpa.entity.File
     **/
    File addFinancialLicence(FinancialLicenseAddParam addParam);


    /**
     * 将供应商的财务复制到目标供应商副本
     *
     * @param supplierId 供应商 id
     * @param supplierFb 目标供应商副本实体
     */
    void copySupplierFinancialToSupplierFb(String supplierId, SupplierFb supplierFb);

    /**
     * 根据供应商 id 获得收款信息
     * @param supplierId 供应商 id 必传
     */
    List<FinancialInfoDTO> getFinancialDTOList(String supplierId);
}
