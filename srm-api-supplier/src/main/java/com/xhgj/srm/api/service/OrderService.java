package com.xhgj.srm.api.service;

import com.xhgj.srm.api.dto.FileDTO;
import com.xhgj.srm.api.dto.order.BaseOrderInvoiceDTO;
import com.xhgj.srm.api.dto.order.BatchOrderInvoiceParams;
import com.xhgj.srm.api.dto.order.CustomerPaybackDTO;
import com.xhgj.srm.api.dto.order.CustomerPaybackParams;
import com.xhgj.srm.api.dto.order.DeliveryOrderDetailDTO;
import com.xhgj.srm.api.dto.order.DeliveryParamDTO;
import com.xhgj.srm.api.dto.order.OrderInvoiceInfoDTO;
import com.xhgj.srm.api.dto.order.OrderInvoicePageQuery;
import com.xhgj.srm.api.dto.order.OrderInvoiceParams;
import com.xhgj.srm.api.dto.order.OrderSomeStatusDTO;
import com.xhgj.srm.api.dto.order.SubmitOrderAcceptDTO;
import com.xhgj.srm.api.dto.order.param.OrderPageQueryParam;
import com.xhgj.srm.api.dto.order.vo.OrderCountVO;
import com.xhgj.srm.api.dto.order.vo.OrderDetailVO;
import com.xhgj.srm.api.dto.order.vo.OrderPageVO;
import com.xhgj.srm.common.dto.ExpressCompanyDTO;
import com.xhgj.srm.dto.order.AllowPaymentOrderDTO;
import com.xhgj.srm.dto.order.OrderDescriptionDetailDTO;
import com.xhgj.srm.dto.order.OrderDetailInvoiceDTO;
import com.xhgj.srm.dto.order.param.AllowPaymentOrderQueryParam;
import com.xhgj.srm.dto.order.vo.OrderDescriptionDetailVO;
import com.xhgj.srm.jpa.entity.Order;
import com.xhgj.srm.request.vo.dock.FileByteAndType;
import com.xhiot.boot.framework.jpa.service.BootBaseService;
import com.xhiot.boot.framework.web.dto.param.PageParam;
import com.xhiot.boot.mvc.base.PageResult;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * author wangdw
 */
public interface OrderService extends BootBaseService<Order, String> {

  /**
   * 获取订单状态及其个数
   *
   * @return
   */
  OrderCountVO getOrderStateAndCount(String supplierId);

  /**
   * 分页获取订单信息
   *
   * @return
   */
  PageResult<OrderPageVO> getOrderPage(OrderPageQueryParam orderPageQuery);

  /**
   * 分页获取订单开票信息（排除掉开票状态为 {@link com.xhgj.srm.common.Constants_order#INVOICE_STATE_UN} 的订单）
   */
  <T extends BaseOrderInvoiceDTO> PageResult<T> getOrderInvoicePage(Class<T> cls,
      OrderInvoicePageQuery orderInvoicePageQuery, boolean excludeNoInvoiceOrder,
      String supplierId);

  /**
   * 新建发货单
   *
   * @param deliveryParamDTO
   */
  void orderDelivery(DeliveryParamDTO deliveryParamDTO);

  /**
   * 变更发货单
   *
   * @param deliveryParamDTO
   */
  void orderDeliveryUpdate(DeliveryParamDTO deliveryParamDTO);

  /**
   * 获取发货单详情
   *
   * @param orderDeliveryId
   * @return
   */
  DeliveryOrderDetailDTO getOrderDeliveryDetail(String orderDeliveryId);

  /**
   * 获取物流公司
   *
   * @param type
   * @return
   */
  List<ExpressCompanyDTO> getExpressCompanyList(String type);

  /**
   * 导出验收单模板
   *
   * @param id
   * @return
   */
  FileByteAndType downloadAcceptTemp(String id);

  /**
   * 更新订单物流状态
   */
  void updateOrderLogistic();

  /**
   * 导出订单明细
   *
   * @return
   */
  byte[] exportOrderDetail(List<String> ids, String supplierId, String state);

  /**
   * 提交开票申请
   *
   * @param orderInvoiceParams 参数必传
   */
  void submitOrderInvoice(OrderInvoiceParams orderInvoiceParams);

  /**
   * 提交签收信息
   *
   * @param submitOrderAcceptDTO 参数 必传
   */
  void submitOrderAccept(SubmitOrderAcceptDTO submitOrderAcceptDTO);

  /**
   * 获取发票申请详情
   *
   * @param orderInvoiceId 开票申请单 id 必传
   */
  OrderInvoiceInfoDTO getOrderInvoiceApplyInfo(String orderInvoiceId);

  /**
   * 分页获取允许付款订单列表
   *
   * @param query 查询条件
   * @param param 分页条件
   */
  PageResult<AllowPaymentOrderDTO> getAllowPaymentOrderPage(AllowPaymentOrderQueryParam query,
      PageParam param);

  /**
   * 设置订单的付款状态为待申请
   *
   * @param order 订单
   */
  void setAccountStatusAllow(Order order);

  /**
   * 订单合并开票
   *
   * @param batchOrderInvoiceParams 订单开票信息
   * @param userId 当前用户id
   */
  void batchSubmitOrderInvoice(BatchOrderInvoiceParams batchOrderInvoiceParams, String userId);

  /**
   * 根据开票申请单获取相关订单详情
   *
   * @param applicationNumber
   * @return
   */
  List<OrderDetailVO> getOrderDetailsByApplicationNumber(String applicationNumber);

  /**
   * 获取可开票订单
   *
   * @param <T>
   * @return
   */
  <T extends BaseOrderInvoiceDTO> PageResult<T> getPermitOrderInvoicePage(
      Class<T> cls, OrderInvoicePageQuery
      orderInvoicePageQuery,
      boolean excludeNoInvoiceOrder,
      String supplierId);

  /**
   * 根据订单 id 获取明细发票详情
   * @param orderId 订单 id
   * @return
   */
  OrderDetailInvoiceDTO getOrderDetailInvoiceByOrderId(String orderId);

  /**
   * 获取OMS承运商物流公司列表
   *
   * @return
   */
  List<ExpressCompanyDTO> getLogisticsCompanies();

  /**
   * 发货单自动入库定时任务使用
   */
  void autoWarehousing();

  /**
   * 获取订单总计
   * @param supplierId 供应商id
   * @param orderStatus 订单状态
   */
  long getOrderTotal(String supplierId, String orderStatus, String paymentStatus);

  /**
   * 通过订单编号获取订单的大票 列表
   *
   * @param orderNo   订单编号，必传
   * @param orderType 订单类型，必传
   */
  List<String> getOrderLargeTicketProjectNo(Order order,String orderNo, String orderType);

  /**
   * 根据订单id验证签收凭证、客户回款、供应商开票
   * @param orderIdList
   * @return
   */
  List<OrderSomeStatusDTO> validateByOrderIds(String orderIdList);

  /**
   * 获取客户回款详情
   * @param params
   * @return
   */
  List<CustomerPaybackDTO> getCustomerPaybackList(CustomerPaybackParams params);

  /**
   * 根据开票状态和供应商 id 获取对应数量
   *
   * @param type       开票状态
   * @param supplierId 供应商 id
   */
  Long getInvoiceCountByType(String type, String supplierId);

  OrderDescriptionDetailVO getOrderDescriptionDetail(String orderId);

  void addOrUpdateOrderDescription(OrderDescriptionDetailDTO params);

}
