package com.xhgj.srm.api.dto.filing;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import javax.validation.constraints.DecimalMin;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

@Data
@ApiModel("客户订单报备单参数，用于新增及修改")
public class FilingOrderNoParamDTO {

  @ApiModelProperty("报备单id(修改传入)")
  private String id;

  @ApiModelProperty(value = "下单平台", required = true)
  @NotBlank(message = "下单平台不能为空！")
  private String platform;

  @ApiModelProperty(value = "客户订单号", required = true)
  @Length(max = 100, message = "客户订单号超长")
  @NotBlank(message = "客户订单号不能为空！")
  private String orderNo;

  @ApiModelProperty("报备金额")
  @DecimalMin(value = "0.00", message = "报备金额格式不正确")
  @NotNull(message = "报备金额不能为空")
  private BigDecimal price;

  @ApiModelProperty(value = "客户单位", required = true)
  @NotBlank(message = "客户单位不能为空！")
  @Length(max = 100, message = "客户单位超长")
  private String customer;

  @ApiModelProperty("备注")
  @Length(max = 500, message = "备注超长")
  private String remark;

  @ApiModelProperty(value = "供应商id", required = true)
  @NotBlank(message = "供应商id不能为空！")
  private String supplierId;

  @ApiModelProperty(value = "对接销售id", required = true)
  @NotBlank(message = "参数异常（dockingSalesId")
  private String dockingSalesId;

  @ApiModelProperty(value = "对接销售工号", required = true)
  @NotBlank(message = "参数异常（dockingSalesJobNumber")
  private String dockingSalesJobNumber;

  @ApiModelProperty(value = "对接销售名称", required = true)
  @NotBlank(message = "参数异常（dockingSalesName")
  private String dockingSalesName;
  @ApiModelProperty(value = "用户id", required = true)
  @NotBlank(message = "用户id不能为空！")
  private String userId;
  @ApiModelProperty("客户收货地址")
  private String receiveAddress;
}
