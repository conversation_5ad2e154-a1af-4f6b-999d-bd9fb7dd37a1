package com.xhgj.srm.api.dto.supplierOrder;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.xhgj.srm.common.enums.PurchaseOrderTypeEnum;
import com.xhgj.srm.common.enums.supplierorder.SupplierOrderState;
import com.xhgj.srm.jpa.entity.SupplierOrder;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.util.Date;
import java.util.Optional;
import lombok.Data;
import lombok.NoArgsConstructor;

/** <AUTHOR> @ClassName BaseSupplierOrderDTO */
@Data
@NoArgsConstructor
public abstract class BaseSupplierOrderDTO {
  @ApiModelProperty("id")
  private String id;

  @ApiModelProperty("采购订单号")
  private String code;

  @ApiModelProperty("创建时间")
  private String orderCreateTime;

  @ApiModelProperty("订单状态")
  private String orderState;

  @ApiModelProperty("订单状态名称")
  private String orderStateToName;

  @ApiModelProperty("是否厂直发")
  private Boolean directShipment;

  @ApiModelProperty("采购组织")
  private String groupName;

  @ApiModelProperty("采购员")
  private String purchaseMan;

  @ApiModelProperty("收件人")
  private String receiveMan;

  @ApiModelProperty("订单金额")
  private String price;

  @ApiModelProperty("入库进度")
  private String warehousingProgress;

  @ApiModelProperty("备注")
  private String remark;

  @ApiModelProperty("客户订单号")
  private String customerOrderCode;

  @ApiModelProperty("供应商名称")
  private String supplierName;

  @ApiModelProperty("订单类型 1 金蝶采购订单 NB 标准采购（SAP） Z040 寄售 Z010 委外加工")
  private String orderType;

  public BaseSupplierOrderDTO(SupplierOrder supplierOrder) {
    this.id = supplierOrder.getId();
    this.code = StrUtil.emptyIfNull(supplierOrder.getCode());
    this.orderState = StrUtil.emptyIfNull(supplierOrder.getOrderState());
    this.orderStateToName =
        SupplierOrderState.findValueByOrderState(supplierOrder.getOrderState()).getValue();
    this.directShipment = supplierOrder.getDirectShipment();
    this.groupName = StrUtil.emptyIfNull(supplierOrder.getGroupName());
    this.purchaseMan = StrUtil.emptyIfNull(supplierOrder.getPurchaseMan());
    this.receiveMan = StrUtil.emptyIfNull(supplierOrder.getReceiveMan());
    this.orderCreateTime =
        ObjectUtil.isNotEmpty(supplierOrder.getOrderCreateTime())
                && supplierOrder.getOrderCreateTime() > 0
            ? DateUtil.format(
                new Date(supplierOrder.getOrderCreateTime()), DatePattern.NORM_DATETIME_PATTERN)
            : "";
    this.price =
        Optional.ofNullable(supplierOrder.getPrice()).orElse(BigDecimal.ZERO).stripTrailingZeros().toPlainString();
    this.warehousingProgress = StrUtil.emptyIfNull(supplierOrder.getStockProgress());
    this.remark = StrUtil.emptyIfNull(supplierOrder.getMark());
    this.customerOrderCode = StrUtil.emptyIfNull(supplierOrder.getCustomerOrderCode());
    this.supplierName = StrUtil.emptyIfNull(supplierOrder.getSupplierName());
    this.orderType = StrUtil.emptyToDefault(supplierOrder.getOrderType(),
        PurchaseOrderTypeEnum.JIN_DIE.getKey());
  }
}
