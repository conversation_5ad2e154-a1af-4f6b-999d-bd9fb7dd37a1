package com.xhgj.srm.api.provider.impl;

import cn.hutool.core.collection.CollUtil;
import com.xhgj.srm.api.dto.order.DeliveryParamDTO;
import com.xhgj.srm.api.dto.order.DeliveryProductParamDTO;
import com.xhgj.srm.api.dto.order.SubmitOrderAcceptDTO;
import com.xhgj.srm.api.dto.order.SubmitOrderAcceptDTO.DataInfo;
import com.xhgj.srm.api.front.factory.MapStructFactory;
import com.xhgj.srm.api.service.OrderService;
import com.xhgj.srm.common.Constants_order;
import com.xhgj.srm.jpa.dao.OrderDeliveryDao;
import com.xhgj.srm.jpa.dao.OrderDetailDao;
import com.xhgj.srm.jpa.entity.Order;
import com.xhgj.srm.jpa.entity.OrderAccept;
import com.xhgj.srm.jpa.entity.OrderDetail;
import com.xhgj.srm.jpa.repository.OrderAcceptRepository;
import com.xhgj.srm.open.entity.OrderEntity;
import com.xhgj.srm.open.form.order.OpenOrderShipForm.OpenOrderShipFormDetail;
import com.xhgj.srm.open.repository.OrderEntityRepository;
import com.xhgj.srm.open.utils.OpenSecurityUtil;
import com.xhgj.srm.request.service.third.oms.OMSService;
import com.xhgj.srm.request.utils.DownloadThenUpUtil;
import com.xhgj.srm.common.Constants;
import com.xhgj.srm.jpa.entity.File;
import com.xhgj.srm.jpa.repository.FileRepository;
import com.xhgj.srm.open.form.order.OpenOrderShipForm;
import com.xhgj.srm.open.form.order.OpenOrderSignForm;
import com.xhgj.srm.open.form.order.OpenOrderSignForm.SignForm;
import com.xhgj.srm.open.provider.OpenOrderProvider;
import com.xhiot.boot.core.common.exception.CheckException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class OpenOrderProviderImpl implements OpenOrderProvider {

  @Resource
  OrderService orderService;
  @Resource
  DownloadThenUpUtil downloadThenUpUtil;
  @Resource
  OrderDetailDao orderDetailDao;
  @Resource
  OrderEntityRepository orderEntityRepository;
  @Resource
  OrderAcceptRepository orderAcceptRepository;
  @Resource
  OMSService omsService;

  private static final String UPLOAD_PATH = "srm/accept";

  @Override
  @Transactional(rollbackFor = Exception.class)
  public void orderSign(OpenOrderSignForm openOrderSignForm, String supplierUserId) {
    // 判断订单签收状态，如果是审核中则抛出异常
    OrderEntity orderEntity = orderEntityRepository.byId(openOrderSignForm.getOrderId());
    if (
        Constants_order.ORDER_ACCEPT_PENDING_AUDITING.equals(orderEntity.getConfirmVoucherAuditStatus())
        || Constants_order.ORDER_ACCEPT_CONSENT.equals(orderEntity.getConfirmVoucherAuditStatus())
    ) {
      throw new CheckException("订单签收凭证正在审核中或已确认，无法提交");
    }
    // 删除原有的验收单内容
    List<OrderAccept> originAccepts = orderAcceptRepository.findAllByOrderIdIn(
        Collections.singletonList(openOrderSignForm.getOrderId()));
    if (CollUtil.isNotEmpty(originAccepts)) {
      orderAcceptRepository.deleteAll(originAccepts);
    }
    // 保存验收单内容
    SubmitOrderAcceptDTO submitOrderAcceptDTO = new SubmitOrderAcceptDTO();
    List<SignForm> signForms = openOrderSignForm.getSignForms();
    List<DataInfo> dataInfos = new ArrayList<>();
    List<String> urls = signForms.stream().map(SignForm::getUrl).collect(Collectors.toList());
    List<String> ossUrls = downloadThenUpUtil.downloadAndUploadConcurrent(urls, UPLOAD_PATH);
    List<File> fileList = downloadThenUpUtil.saveFile(ossUrls, null, supplierUserId);
    int i = 0;
    for (SignForm signForm : signForms) {
      DataInfo dataInfo = new DataInfo();
      dataInfo.setType(signForm.getType());
      // 检查 fileList 是否为空或索引是否超出范围
      if (fileList != null && i < fileList.size()) {
        File file = fileList.get(i++);
        // 通过url下载文件至oss，并保存至fileIdList
        dataInfo.setFileIdList(Collections.singletonList(file.getId()));
      } else {
        // 处理 fileList 为空或索引超出范围的情况
        dataInfo.setFileIdList(Collections.emptyList()); // 或者其他默认值
        // 记录错误、抛出异常等
      }
      dataInfos.add(dataInfo);
    }
    submitOrderAcceptDTO.setDataInfo(dataInfos);
    submitOrderAcceptDTO.setOrderId(openOrderSignForm.getOrderId());
    submitOrderAcceptDTO.setUploadMan(supplierUserId);
    orderService.submitOrderAccept(submitOrderAcceptDTO);
  }

  @Override
  public void orderShip(OpenOrderShipForm openOrderShipForm, String supplierId) {
    DeliveryParamDTO deliveryParamDTO = new DeliveryParamDTO();
    deliveryParamDTO.setSupplierId(supplierId);
    deliveryParamDTO.setOrderId(openOrderShipForm.getOrderId());
    deliveryParamDTO.setDeliveryId(null);
    deliveryParamDTO.setExpressCompany(openOrderShipForm.getExpressCompany());
    deliveryParamDTO.setExpressCode(openOrderShipForm.getExpressCode());
    deliveryParamDTO.setExpressNo(openOrderShipForm.getExpressNo());
    List<OpenOrderShipFormDetail> shipFormDetails = openOrderShipForm.getShipFormDetails();
    List<DeliveryProductParamDTO> shipFormParam = shipFormDetails.stream().map(shipFormDetail -> {
      DeliveryProductParamDTO deliveryProductParamDTO = new DeliveryProductParamDTO();
      OrderDetail orderDetail =
          orderDetailDao.getOrderDetailByOrderIdAndCode(
              deliveryParamDTO.getOrderId(), shipFormDetail.getCode());
      deliveryProductParamDTO.setCode(orderDetail.getCode());
      deliveryProductParamDTO.setBrand(orderDetail.getBrand());
      deliveryProductParamDTO.setName(orderDetail.getName());
      deliveryProductParamDTO.setModel(orderDetail.getModel());
      deliveryProductParamDTO.setNum(orderDetail.getNum());
      deliveryProductParamDTO.setUnit(orderDetail.getUnit());
      deliveryProductParamDTO.setPrice(orderDetail.getPrice());
      deliveryProductParamDTO.setDelCount(shipFormDetail.getDelCount());
      deliveryProductParamDTO.setRowId(orderDetail.getRowNo());
      deliveryProductParamDTO.setCostPriceTaxRate(orderDetail.getCostPriceTaxRate());
      return deliveryProductParamDTO;
    }).collect(Collectors.toList());
    deliveryParamDTO.setProductDetailList(shipFormParam);
    orderService.orderDelivery(deliveryParamDTO);
  }

  @Override
  public void updateOmsOrderCustomerInfo(Order order) {
    Order newOne = MapStructFactory.INSTANCE.toOrder(order);
    omsService.updateOmsOrderCustomerInfo(newOne);
  }
}

