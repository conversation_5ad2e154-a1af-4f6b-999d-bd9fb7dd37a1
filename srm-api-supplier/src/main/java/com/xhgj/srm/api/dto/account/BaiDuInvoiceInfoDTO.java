package com.xhgj.srm.api.dto.account;

import com.xhgj.srm.common.enums.InvoiceTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class BaiDuInvoiceInfoDTO {

  @ApiModelProperty(value = "发票号")
  private String invoiceNum;

  @ApiModelProperty(value = "订单号")
  private String dockingOrderNo;

  @ApiModelProperty(value = "发票代码")
  private String invoiceCode;

  @ApiModelProperty("合计金额")
  private String totalAmount;

  @ApiModelProperty("合计税额")
  private String totalTaxAmount;

  @ApiModelProperty("价税合计")
  private String totalAmountIncludingTax;

  @ApiModelProperty(value = "开票时间")
  private String invoiceTime;

  @ApiModelProperty(value = "前缀路径")
  private String baseUrl;

  @ApiModelProperty(value = "附件路径")
  private String url;

  @ApiModelProperty(value = "发票类型")
  private InvoiceTypeEnum invoiceType;

  @ApiModelProperty(value = "发票备注")
  private String remarks;
  public BaiDuInvoiceInfoDTO(String num, String code, String totalAmount, String totalTaxAmount,
      String totalAmountIncludingTax, String invoiceDate, String baseUrl, String url,
      String invoiceType, String remarks) {
    this.invoiceCode = code;
    this.invoiceNum = num;
    this.totalAmount = totalAmount;
    this.totalTaxAmount = totalTaxAmount;
    this.totalAmountIncludingTax = totalAmountIncludingTax;
    this.invoiceTime = invoiceDate;
    this.baseUrl = baseUrl;
    this.url = url;
    this.invoiceType =  InvoiceTypeEnum.fromDescription(invoiceType);
    this.remarks = remarks;
  }

  public BaiDuInvoiceInfoDTO(String baseUrl, String url) {
    this.baseUrl = baseUrl;
    this.url = url;
  }
}
