package com.xhgj.srm.api.dto.supplierinvoice;

import com.xhgj.srm.common.utils.supplierorder.BigDecimalUtil;
import com.xhgj.srm.jpa.entity.SupplierOrder;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.util.Optional;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Created by Geng Shy on 2023/11/28
 */
@Data
@NoArgsConstructor
public class SupplierInvoiceOrderPageVO {
  @ApiModelProperty("id")
  private String id;
  @ApiModelProperty("最终结算金额")
  private BigDecimal finalPrice;
  @ApiModelProperty("收件人")
  private String receiveMan;
  @ApiModelProperty("下单时间")
  private Long createTime;
  @ApiModelProperty("采购单号")
  private String code;
  @ApiModelProperty("采购件数")
  private BigDecimal number;
  @ApiModelProperty("是否为不可勾选")
  private Boolean isDisabled;

  public SupplierInvoiceOrderPageVO(SupplierOrder supplierOrder, boolean isDisabled) {
    this.id = supplierOrder.getId();
    this.createTime = supplierOrder.getCreateTime();
    this.receiveMan = supplierOrder.getReceiveMan();
    this.finalPrice =
        supplierOrder.getFinalPrice() == null ? BigDecimal.ZERO :
            BigDecimalUtil.formatForStandard(supplierOrder.getFinalPrice());
    this.code = supplierOrder.getCode();
    // 采购件数修改为 实际入库数
    this.number = supplierOrder.getTotalStockInputQty();
    this.isDisabled = isDisabled;
  }

}
