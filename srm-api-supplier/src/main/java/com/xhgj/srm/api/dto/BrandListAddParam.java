package com.xhgj.srm.api.dto;

import com.xhgj.srm.common.Constants;
import com.xhgj.srm.jpa.entity.Brand;
import com.xhgj.srm.jpa.entity.Contact;
import com.xhgj.srm.jpa.entity.Supplier;
import com.xhgj.srm.jpa.entity.SupplierUser;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * @ClassName ContactListAddParam
 * Create by Liuyq on 2021/6/9 16:53
 **/
@Data
public class BrandListAddParam {
    @NotBlank(message = "品牌mdmid不能为空")
    @ApiModelProperty("品牌mdmid")
    private String brandMdmId;
    @ApiModelProperty("品牌logo")
    private String logoUrl;
    @NotBlank(message = "品牌英文名不能为空")
    @ApiModelProperty("品牌英文名")
    private String brandNameEn;
    @NotBlank(message = "品牌中文名不能为空")
    @ApiModelProperty("品牌中文名")
    private String brandNameCn;
    @NotBlank(message = "经营形式不能为空")
    @ApiModelProperty("经营形式 1-品牌商/2-集货商")
    private String manageType;

    public Brand bulidBrand(Supplier supplier) {
        Brand brand = new Brand();
        brand.setSupplier(supplier);
        brand.setSupplierId(supplier.getId());
        brand.setLogoUrl(logoUrl);
        brand.setBrandMdmId(brandMdmId);
        brand.setBrandnameCn(brandNameCn);
        brand.setBrandnameEn(brandNameEn);
        brand.setManageType(manageType);
        brand.setIsPermission(Constants.UPLOAD_STATUS_NOT_UPLOADED);
        brand.setState(Constants.STATE_OK);
        return brand;
    }
}
