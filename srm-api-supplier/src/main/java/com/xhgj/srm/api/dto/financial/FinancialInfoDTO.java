package com.xhgj.srm.api.dto.financial;

import com.xhgj.srm.jpa.entity.Financial;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @since 2022/10/12 9:01
 */
@Data
@EqualsAndHashCode
public class FinancialInfoDTO {

  @ApiModelProperty("银行账号")
  private String bankNum;
  @ApiModelProperty("开户行")
  private String bankName;
  @ApiModelProperty("账户名称")
  private String bankAccount;

  public FinancialInfoDTO(Financial financial) {
    this.bankNum = financial.getBankNum();
    this.bankName = financial.getBankName();
    /**
     * 供应商的收款信息中账户名称字段是跟供应商名称同步一致的。供应商修改名称审核通过之后并不会同步修改账户名称，
     * 所以这里直接取供应商名称即可。
     */
//    this.bankAccount = financial.getBankAccount();
  }
}
