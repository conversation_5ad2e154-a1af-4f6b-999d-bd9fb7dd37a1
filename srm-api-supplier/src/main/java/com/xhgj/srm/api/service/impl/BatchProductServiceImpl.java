package com.xhgj.srm.api.service.impl;

import com.xhgj.srm.api.service.BatchProductService;
import com.xhgj.srm.common.Constants;
import com.xhgj.srm.common.enums.product.ProductStateEnum;
import com.xhgj.srm.jpa.entity.Check;
import com.xhgj.srm.jpa.entity.Product;
import com.xhgj.srm.jpa.entity.Supplier;
import com.xhgj.srm.jpa.repository.CheckRepository;
import com.xhgj.srm.jpa.repository.ProductRepository;
import com.xhgj.srm.open.enums.OpenMessageResultEnum;
import com.xhgj.srm.open.provider.OpenMessageCreateProvider;
import com.xhiot.boot.core.common.exception.CheckException;
import com.xhiot.boot.framework.jpa.repository.BootBaseRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;

@Slf4j
@Service
public class BatchProductServiceImpl implements BatchProductService {

    @Autowired
    ProductRepository repository;

    @Autowired
    CheckRepository checkRepository;

  @Resource
  private OpenMessageCreateProvider openMessageCreateProvider;

    @Override
    public BootBaseRepository<Product, String> getRepository() {
        return repository;
    }



    @Override
    public void setProductPass(String id,String checkMan) {
      Product product = repository.findById(id).orElseThrow(() -> CheckException.noFindException(Product.class, id));
      if(product!=null){
          product.setState(ProductStateEnum.NORMAL.getKey());
          repository.save(product);
          Check check = new Check();
          check.setRelationId(product.getId());
          check.setResource(product.getResource());
          check.setCreateTime(System.currentTimeMillis());
          check.setState(Constants.STATE_OK);
          check.setOperaType(ProductStateEnum.NORMAL.getKey());
          check.setDescription("审核通过");
          check.setCheckMan(checkMan);
          check.setReadTime(System.currentTimeMillis());
          checkRepository.save(check);
      }
      Supplier supplier = product.getSupplier();
      if (supplier != null) {
        // 新建流转消息 product审核通过
        openMessageCreateProvider.auditProductMessage(id, OpenMessageResultEnum.PASS.getCode(), supplier.getId());
      }
    }

    @Override
    public void setProductReject(String id,String des,String checkMan) {
        Product product = repository.findById(id).orElseThrow(() -> CheckException.noFindException(Product.class, id));
        if(product!=null){
          // todo 状态修改验证正确性
            product.setState(ProductStateEnum.REJECT.getKey());
            repository.save(product);
            Check check = new Check();
            check.setRelationId(product.getId());
            check.setResource(product.getResource());
            check.setCreateTime(System.currentTimeMillis());
            check.setState(Constants.STATE_OK);
            check.setDescription(des);
            check.setCheckMan(checkMan);
            check.setOperaType(ProductStateEnum.REJECT.getKey());
            check.setReadTime(System.currentTimeMillis());
            checkRepository.save(check);
        }
        Supplier supplier = product.getSupplier();
        if (supplier != null) {
          // 新建流转消息 product审核驳回
          openMessageCreateProvider.auditProductMessage(id, OpenMessageResultEnum.REJECT.getCode(), supplier.getId());
        }
    }
}
