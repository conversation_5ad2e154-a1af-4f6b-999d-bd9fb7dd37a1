package com.xhgj.srm.api.service.impl;

import static com.xhgj.srm.common.enums.PayTypeSAPEnums.getContractDownloadPayType;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.ReflectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.xhgj.srm.api.dto.FileDTO;
import com.xhgj.srm.api.dto.FileDetails;
import com.xhgj.srm.api.dto.LandingContractPageDTO;
import com.xhgj.srm.api.dto.LandingContractPageSchemeDTO;
import com.xhgj.srm.api.dto.landingContract.form.FrontLandingMerchantContractQueryForm;
import com.xhgj.srm.api.service.EntryRegistrationLandingMerchantService;
import com.xhgj.srm.api.service.FileService;
import com.xhgj.srm.api.service.LandingMerchantContractService;
import com.xhgj.srm.api.service.SearchSchemeService;
import com.xhgj.srm.api.utils.SupplierSecurityUtil;
import com.xhgj.srm.common.Constants;
import com.xhgj.srm.common.dto.LandingContract.LandingMerchantContractPut2Pdf;
import com.xhgj.srm.common.enums.AssessStateEnum;
import com.xhgj.srm.common.enums.FileReviewStateEnum;
import com.xhgj.srm.common.enums.PayTypeSAPEnums;
import com.xhgj.srm.common.enums.PurchaseOrderInvoiceType;
import com.xhgj.srm.common.enums.entryregistration.EntryRegistrationCooperationTypeEnum;
import com.xhgj.srm.common.enums.entryregistration.EntryRegistrationDiscountTypeEnum;
import com.xhgj.srm.common.enums.entryregistration.EntryRegistrationStatusEnum;
import com.xhgj.srm.common.enums.landingContract.ContractStatus;
import com.xhgj.srm.common.utils.FileUtil;
import com.xhgj.srm.common.utils.MoneyUtil;
import com.xhgj.srm.common.utils.WordPoiUtils;
import com.xhgj.srm.common.utils.ZIPUtils;
import com.xhgj.srm.common.utils.supplierorder.BigDecimalUtil;
import com.xhgj.srm.jpa.dao.GroupDao;
import com.xhgj.srm.jpa.dao.LandingMerchantContractDao;
import com.xhgj.srm.jpa.dao.OrderDao;
import com.xhgj.srm.jpa.dao.SupplierDao;
import com.xhgj.srm.jpa.dao.UserDao;
import com.xhgj.srm.jpa.entity.Assess;
import com.xhgj.srm.jpa.entity.EntryRegistrationDiscount;
import com.xhgj.srm.jpa.entity.EntryRegistrationLandingMerchant;
import com.xhgj.srm.jpa.entity.EntryRegistrationOrder;
import com.xhgj.srm.jpa.entity.Group;
import com.xhgj.srm.jpa.entity.LandingMerchantContract;
import com.xhgj.srm.jpa.entity.SearchScheme;
import com.xhgj.srm.jpa.entity.Supplier;
import com.xhgj.srm.jpa.entity.SupplierPerformance;
import com.xhgj.srm.jpa.entity.SupplierUser;
import com.xhgj.srm.jpa.entity.User;
import com.xhgj.srm.jpa.repository.AssessRepository;
import com.xhgj.srm.jpa.repository.EntryRegistrationDiscountRepository;
import com.xhgj.srm.jpa.repository.EntryRegistrationOrderRepository;
import com.xhgj.srm.jpa.repository.SupplierPerformanceRepository;
import com.xhgj.srm.registration.entity.EntryRegistrationEntity;
import com.xhgj.srm.registration.repository.EntryRegistrationRepository;
import com.xhgj.srm.request.utils.DownloadThenUpUtil;
import com.xhiot.boot.core.common.exception.CheckException;
import com.xhiot.boot.mvc.base.PageResult;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.InputStream;
import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.UUID;
import java.util.stream.Collectors;
import java.util.stream.IntStream;
import javax.annotation.Resource;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.xwpf.usermodel.XWPFDocument;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

/**
 * 落地商合同服务层
 *
 * <AUTHOR> Shangyi
 */
@Service
@Slf4j
public class LandingMerchantContractServiceImpl implements LandingMerchantContractService {

  @Resource
  private LandingMerchantContractDao contractDao;
  @Resource
  private OrderDao orderDao;
  @Resource
  private UserDao userDao;
  @Resource
  private GroupDao groupDao;
  @Resource
  private SearchSchemeService searchSchemeService;
  @Autowired
  private AssessRepository assessRepository;
  @Resource
  private EntryRegistrationOrderRepository entryRegistrationOrderRepository;
  @Resource
  private EntryRegistrationLandingMerchantService entryRegistrationLandingMerchantService;

  @Resource
  private FileService fileService;
  private static final String MANUAL_REVIEW = "5";
  @Resource
  SupplierSecurityUtil supplierSecurityUtil;
  @Resource private SupplierPerformanceRepository supplierPerformanceRepository;


  /**
   * 根据合同id获取附件信息
   */
  @Override
  public FileDetails detailsOfTheAnnexToTheContract(String id) {
    LandingMerchantContract contract = contractDao.get(id);
    FileDetails fileDetails = new FileDetails();
    // 合同附件
    fileService.findFirstByRelationIdAndRelationType(contract.getId(),
        Constants.FILE_TYPE_LANDING_CONTRACT).ifPresent(file -> {
      fileDetails.setFileDTO(new FileDTO(file));
    });
    fileDetails.setFileReviewState(contract.getFileReviewState());
    //合同附件 审核中
    if (ObjectUtil.equals(contract.getFileReviewState(),
        FileReviewStateEnum.VERIFICATION.getKey() )){
      Assess assess = assessRepository.findBytargetId(contract.getId());
      if (assess != null) {
        //人工识别
        fileDetails.setFileReviewState(MANUAL_REVIEW);
      }
    }
    //合同附件 驳回
    if (ObjectUtil.equals(contract.getFileReviewState(),
        FileReviewStateEnum.FAILED_TO_PASS.getKey() )){
      Assess assess =assessRepository.findBytargetId(contract.getId());
      if (assess != null) {
        if (StrUtil.isNotBlank(assess.getAssessResult())){
          fileDetails.setReasonForRejection(assess.getAssessResult());
        }
      }else {
        EntryRegistrationOrder entryRegistrationOrder =
            entryRegistrationOrderRepository.getOne(contract.getEntryRegistrationOrderId());
        if (entryRegistrationOrder != null) {
          fileDetails.setReasonForRejection(entryRegistrationOrder.getFileOcrInfo());
        }
      }
    }
    return fileDetails;
  }

  @Override
  public PageResult<LandingContractPageDTO> getFrontContractPage(FrontLandingMerchantContractQueryForm form) {
    // 获取当前供应商
    SupplierUser supplierUserDetails = supplierSecurityUtil.getSupplierUserDetails();
    // 处理方案
    if (StrUtil.isBlank(form.getSchemeId())) {
      SearchScheme search =
          searchSchemeService.getDefaultSearchScheme(form.getUserId(), Constants.SEARCH_TYPE_CONTRACT_PAGE);
      if (search != null) {
        form.setSchemeId(search.getId());
      }
    }
    if (StrUtil.isNotEmpty(form.getSchemeId())) {
      SearchScheme search = searchSchemeService.get(form.getSchemeId());
      if (search != null && StrUtil.isNotEmpty(search.getContent())) {
        LandingContractPageSchemeDTO schemeDTO = JSON.parseObject(search.getContent(),
            new TypeReference<LandingContractPageSchemeDTO>() {});
        if (schemeDTO != null) {
          form.setContractNum(StrUtil.blankToDefault(schemeDTO.getContractNum(), form.getContractNum()));
          form.setContractType(StrUtil.blankToDefault(schemeDTO.getContractType(), form.getContractType()));
          form.setEnterpriseName(StrUtil.blankToDefault(schemeDTO.getEnterpriseName(), form.getEnterpriseName()));
          form.setSigningType(StrUtil.blankToDefault(schemeDTO.getSigningType(), form.getSigningType()));
          form.setLandingContractStatus(
              StrUtil.blankToDefault(schemeDTO.getLandingContractStatus(), form.getLandingContractStatus()));
        }
      }
    }
    Page<LandingMerchantContract> page =
        contractDao.getFronContractPage(form.getContractNum(), form.getContractType(),
            form.getSigningType(), form.getEnterpriseName(), form.getLandingContractStatus(),
            form.getPageNo(), form.getPageSize(),
            Optional.ofNullable(supplierUserDetails).map(SupplierUser::getSupplierId).orElse(null));
    List<LandingMerchantContract> pageDataList = page.getContent();
    List<LandingContractPageDTO> pageDTOS = new ArrayList<>();
    pageDataList.forEach(data -> {
      LandingContractPageDTO pageDTO = new LandingContractPageDTO();
      pageDTO.setContractNo(data.getContractNo());
      pageDTO.setId(data.getId());
      pageDTO.setType(Constants.TYPE_OF_CONTRACT.get(data.getType()));
      User user = userDao.get(data.getCreateMan());
      if (!ObjectUtils.isEmpty(user)) {
        pageDTO.setCreateMan(user.getRealName());
      }
      pageDTO.setSigningType(Constants.CONTRACT_SIGNING_MODE.get(data.getSigningType()));
      if (!ObjectUtils.isEmpty(data.getCreateTime())) {
        pageDTO.setCreateTime(String.valueOf(DateUtil.date(data.getCreateTime())));
      }
      Group group = groupDao.get(data.getFirstSigningGroupId());
      if (!ObjectUtils.isEmpty(group)) {
        pageDTO.setSecondSigningSupplierId(group.getName());
      }
      pageDTO.setPlatformCode(getPlatformCode(data));
      pageDTO.setSignatureStatus(Constants.SIGNATURE_STATUS_TYPE.get(data.getSignatureStatus()));
      pageDTO.setContractStatus(ContractStatus.getDescByCode(data.getContractStatus()));
      BigDecimal amount = getContractOrderAmount(data);
      pageDTO.setAccruingAmounts(BigDecimalUtil.setScaleBigDecimalHalfUp(amount, 2));
      pageDTO.setDeposit(data.getDeposit());
      pageDTO.setDepositState(data.getDepositState());
      if (StrUtil.isNotBlank(data.getFileReviewState())) {
        pageDTO.setFileReviewState(
            FileReviewStateEnum.fromKey(data.getFileReviewState()).get().getDescription());
      }
      if (!StringUtils.isEmpty(data.getEntryRegistrationOrderId())) {
        EntryRegistrationOrder entryRegistrationOrder =
            Optional.ofNullable(entryRegistrationOrderRepository.getOne(data.getEntryRegistrationOrderId()))
                .get();

        if (!ObjectUtils.isEmpty(entryRegistrationOrder)) {
          EntryRegistrationLandingMerchant merchant =
              entryRegistrationLandingMerchantService.getEntryRegistrationOrderId(entryRegistrationOrder.getId());
          if (merchant != null) {
            pageDTO.setReportReviewState(EntryRegistrationStatusEnum.fromKey(entryRegistrationOrder.getRegistrationStatus()).get().getDescription());
            //OA驳回
            if (ObjectUtil.equals(entryRegistrationOrder.getRegistrationStatus(),
                EntryRegistrationStatusEnum.REJECTED.getDescription())){
              pageDTO.setReportReviewState(EntryRegistrationStatusEnum.REJECTED.getDescription());
            }else {
              //OA驳回
              if (ObjectUtil.equals(entryRegistrationOrder.getRegistrationStatus(),
                  EntryRegistrationStatusEnum.REJECTED.getDescription())){
                pageDTO.setReportReviewState(EntryRegistrationStatusEnum.REJECTED.getDescription());
              }else {
                //审核通过
                if (Objects.equals(merchant.getSupplierAuditStatus(),AssessStateEnum.PASS.getKey())){
                  pageDTO.setReportReviewState(EntryRegistrationStatusEnum.APPROVED.getDescription());
                }
                //审核驳回
                if (Objects.equals(merchant.getSupplierAuditStatus(),AssessStateEnum.REJECT.getKey())){
                  pageDTO.setReportReviewState(EntryRegistrationStatusEnum.REJECTED.getDescription());
                }
                //审核中
                if (ObjectUtil.equals(merchant.getSupplierAuditStatus(),AssessStateEnum.UN_ASSESS.getKey())){
                  pageDTO.setReportReviewState(EntryRegistrationStatusEnum.UNDER_REVIEW.getDescription());
                }
              }
            }
          }
        }
        pageDTO.setRegistrationNumber(entryRegistrationOrder.getRegistrationNumber());
      }
      if (ObjectUtil.equals(data.getFileReviewState(), FileReviewStateEnum.THROUGH_THE.getKey())){
        fileService.findFirstByRelationIdAndRelationType(data.getId(),
            Constants.FILE_TYPE_LANDING_CONTRACT).ifPresent(file -> {
          pageDTO.setFileDTO(new FileDTO(file));
        });
      }
      pageDTO.setTypeOfCooperation(data.getTypeOfCooperation());
      pageDTO.setCooperationRRegion(data.getCooperationRRegion());
      SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
      if (data.getEffectiveStart() != null && data.getEffectiveEnd() != null) {
        pageDTO.setCooperationValidity(
            sdf.format(new Date(data.getEffectiveStart())) + "~" + sdf.format(
                new Date(data.getEffectiveEnd())));
      }
      pageDTOS.add(pageDTO);
    });
    return new PageResult<>(pageDTOS, page.getTotalElements(), page.getTotalPages(), form.getPageNo(),
        form.getPageSize());
  }

  private String getPlatformCode(LandingMerchantContract contract) {
    if (StrUtil.isNotBlank(contract.getEntryRegistrationOrderId())) {
      EntryRegistrationOrder entryRegistrationOrder =
          entryRegistrationOrderRepository.getOne(contract.getEntryRegistrationOrderId());
      return entryRegistrationOrder == null ? "" : entryRegistrationOrder.getPlatform();
    }
    return null;
  }


  // 处理合同关联的订单
  private BigDecimal getContractOrderAmount(LandingMerchantContract contract) {
    List<SupplierPerformance> supplierPerformances =
        supplierPerformanceRepository.findAllByLandingContractIdAndState(contract.getId(),
            Constants.STATE_OK);
    BigDecimal totalAmount = BigDecimal.ZERO;
    if (CollUtil.isNotEmpty(supplierPerformances)) {
      BigDecimal totalAmountTemp =
          Convert.toBigDecimal(orderDao.getContractOrderAmountBatch(supplierPerformances));
      // 下单平台
      totalAmount = totalAmountTemp == null ? BigDecimal.ZERO : totalAmountTemp;
    }
    return totalAmount;
  }
}
