package com.xhgj.srm.api.dto.account;

import cn.hutool.core.util.StrUtil;
import com.xhgj.srm.jpa.entity.OrderSupplierInvoice;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2023/1/9 15:02
 */
@Data
@NoArgsConstructor
public class BaseOrderSupplierInvoice {

  @ApiModelProperty("发票信息 id")
  private String accountInvoiceId;

  @ApiModelProperty("发票号")
  @NotBlank(message = "发票号 必传")
  private String invoiceNum;

  @ApiModelProperty("发票代码")
  private String invoiceCode;

  @ApiModelProperty("开票时间")
  @NotNull(message = "开票时间 必传")
  private Long invoiceTime;

  @ApiModelProperty("含税金额")
  @NotNull(message = "含税金额 必传")
  private BigDecimal price;

  @ApiModelProperty("物流公司")
  @NotBlank(message = "物流公司 必传")
  private String logisticsCompany;

  @ApiModelProperty("物流单号")
  @NotBlank(message = "物流单号 必传")
  private String logisticsNum;

  public BaseOrderSupplierInvoice(OrderSupplierInvoice orderSupplierInvoice) {
    this.accountInvoiceId = StrUtil.emptyIfNull(orderSupplierInvoice.getId());
    this.invoiceNum = StrUtil.emptyIfNull(orderSupplierInvoice.getInvoiceNum());
    this.invoiceCode = StrUtil.emptyIfNull(orderSupplierInvoice.getInvoiceCode());
    this.invoiceTime = orderSupplierInvoice.getInvoiceTime();
    this.price = orderSupplierInvoice.getTotalAmountIncludingTax();
    this.logisticsCompany = StrUtil.emptyIfNull(orderSupplierInvoice.getLogisticsCompany());
    this.logisticsNum = StrUtil.emptyIfNull(orderSupplierInvoice.getLogisticsNum());
  }
}
