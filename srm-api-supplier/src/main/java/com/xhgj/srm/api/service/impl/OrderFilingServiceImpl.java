package com.xhgj.srm.api.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.PageUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.xhgj.srm.api.dto.SingleBaseParam;
import com.xhgj.srm.api.dto.filing.FilingDetailDTO;
import com.xhgj.srm.api.dto.filing.FilingExportQuery;
import com.xhgj.srm.api.dto.filing.FilingImportProductVo;
import com.xhgj.srm.api.dto.filing.FilingOrderNoParamDTO;
import com.xhgj.srm.api.dto.filing.FilingPageDTO;
import com.xhgj.srm.api.dto.filing.FilingParamDTO;
import com.xhgj.srm.api.dto.filing.FilingParamProductDTO;
import com.xhgj.srm.api.dto.filing.FilingProductDetailDTO;
import com.xhgj.srm.api.dto.filing.OrderNoFilingDetailDTO;
import com.xhgj.srm.api.front.factory.MapStructFactory;
import com.xhgj.srm.api.service.OrderFilingService;
import com.xhgj.srm.api.service.SupplierPerformanceService;
import com.xhgj.srm.api.service.SupplierService;
import com.xhgj.srm.common.Constants;
import com.xhgj.srm.common.Constants_Batch;
import com.xhgj.srm.common.Constants_Excel;
import com.xhgj.srm.common.Constants_order;
import com.xhgj.srm.common.config.SrmConfig;
import com.xhgj.srm.common.dto.Approval;
import com.xhgj.srm.common.dto.Approval.FormComponentValue;
import com.xhgj.srm.common.dto.ApprovalInstanceResult;
import com.xhgj.srm.common.dto.ApprovalResult;
import com.xhgj.srm.common.dto.OAUserInfoDTO;
import com.xhgj.srm.common.dto.OrderPlatformDTO;
import com.xhgj.srm.common.enums.ShortMessageEnum;
import com.xhgj.srm.common.utils.ExportUtil;
import com.xhgj.srm.common.utils.FileUtils;
import com.xhgj.srm.common.utils.HttpUtil;
import com.xhgj.srm.common.utils.MissionUtil;
import com.xhgj.srm.common.utils.dingding.DingUtils;
import com.xhgj.srm.jpa.dao.OrderFilingDao;
import com.xhgj.srm.jpa.dao.OrderFilingDetailDao;
import com.xhgj.srm.jpa.dao.SearchSchemeDao;
import com.xhgj.srm.jpa.entity.Mission;
import com.xhgj.srm.jpa.entity.Order;
import com.xhgj.srm.jpa.entity.OrderFiling;
import com.xhgj.srm.jpa.entity.OrderFilingDetail;
import com.xhgj.srm.jpa.entity.SearchScheme;
import com.xhgj.srm.jpa.entity.Supplier;
import com.xhgj.srm.jpa.entity.SupplierUser;
import com.xhgj.srm.jpa.repository.MissionRepository;
import com.xhgj.srm.jpa.repository.OrderFilingDetailRepository;
import com.xhgj.srm.jpa.repository.OrderFilingRepository;
import com.xhgj.srm.jpa.repository.SupplierUserRepository;
import com.xhgj.srm.request.dto.oms.OMSFilingSheetAddParam.OMSProduct;
import com.xhgj.srm.request.service.third.mpm.MPMService;
import com.xhgj.srm.request.service.third.xhgj.XhgjMPMRequest;
import com.xhgj.srm.request.service.third.xhgj.XhgjPersonRequest;
import com.xhgj.srm.request.service.third.xhgj.XhgjSMSRequest;
import com.xhgj.srm.sender.mq.sender.BatchTaskMqSender;
import com.xhgj.srm.service.OAUserService;
import com.xhgj.srm.service.ShareOrderFilingService;
import com.xhgj.srm.service.SharePlatformService;
import com.xhiot.boot.core.common.exception.CheckException;
import com.xhiot.boot.core.common.util.DateUtils;
import com.xhiot.boot.core.common.util.ExcelUtil;
import com.xhiot.boot.core.common.util.StringUtils;
import com.xhiot.boot.framework.jpa.repository.BootBaseRepository;
import com.xhiot.boot.mvc.base.PageResult;
import com.xhiot.boot.upload.util.OssUtil;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

@Slf4j
@Service
public class OrderFilingServiceImpl implements OrderFilingService {

    @Autowired
    private OrderFilingRepository repository;
    @Autowired
    private OrderFilingDetailRepository orderFilingDetailRepository;
    @Autowired
    private SearchSchemeDao searchSchemeDao;
    @Autowired
    private OrderFilingDao orderFilingDao;
    @Autowired
    private OrderFilingDetailDao orderFilingDetailDao;
    @Autowired
    private HttpUtil httpUtil;
    @Autowired
    private MissionUtil missionUtil;
    @Autowired
    private MissionRepository missionRepository;
    @Autowired
    private BatchTaskMqSender batchTaskMqSender;
    @Autowired
    private ExportUtil ex;
    @Resource private DingUtils dingUtils;
    @Resource private SrmConfig srmConfig;
    @Resource
    private SupplierUserRepository supplierUserRepository;
    @Resource
    private XhgjSMSRequest xhgjSMSRequest;
    @Resource
    private XhgjMPMRequest xhgjMPMRequest;
    @Resource
    private SupplierPerformanceService supplierPerformanceService;
    @Resource
    private SupplierService supplierService;
    @Resource
    private SharePlatformService platformService;
    @Resource
    private OAUserService oaUserService;
    @Resource
    MPMService mpmService;
    @Resource
    ShareOrderFilingService shareOrderFilingService;
    @Autowired private OssUtil ossUtil;
    @Autowired private XhgjPersonRequest xhgjPersonRequest;
    @Override
    public BootBaseRepository<OrderFiling, String> getRepository() {
        return repository;
    }

    @Override
    public PageResult<FilingPageDTO> getFilingPage(String supplierId, String supplierUserId,
        String filingNo, String platform, String price, String customer,
        String state, String startTime, String endTime, String arriveStartTime,
        String arriveEndTime, String orderNo, String filingType, String fillingState, String dockingSalesName,
        String schemeId, int pageNo,
        int pageSize) {
        //查询方案
        if (StringUtils.isNullOrEmpty(schemeId)) {
            SearchScheme search = searchSchemeDao.getDefaultSearchScheme(supplierUserId, Constants.SEARCH_TYPE_SUPPLIER_ORDER);
            if (search != null) {
                schemeId = search.getId();
            }
        }
      if (!StringUtils.isNullOrEmpty(schemeId)) {
        SearchScheme search = searchSchemeDao.get(schemeId);
        if (search != null && !StringUtils.isNullOrEmpty(search.getContent())) {
          JSONObject searchJo = JSONObject.parseObject(search.getContent());
          if (searchJo != null) {
            filingNo = StrUtil.blankToDefault(filingNo,searchJo.containsKey("filingNo") ?
                searchJo.getString("filingNo") :
                "");
            price = StrUtil.blankToDefault(price,searchJo.containsKey("price") ?
                searchJo.getString("price") : "");
            platform = StrUtil.blankToDefault(platform,searchJo.containsKey("platform") ?
                searchJo.getString("platform") : "");
            customer = StrUtil.blankToDefault(customer,searchJo.containsKey("customer") ?
                searchJo.getString("customer") : "");
            state = StrUtil.blankToDefault(state,searchJo.containsKey("state") ?
                searchJo.getString("state") : "");
            startTime =StrUtil.blankToDefault(startTime,searchJo.containsKey("filingStartTime") ?
                searchJo.getString("filingStartTime") : "");
            endTime =StrUtil.blankToDefault( endTime,searchJo.containsKey("filingEndTime") ?
                searchJo.getString("filingEndTime") : "");
            arriveStartTime =
                StrUtil.blankToDefault(arriveStartTime,searchJo.containsKey(
                    "filingArriveStartTime") ? searchJo.getString("filingArriveStartTime")
                    : "");
            arriveEndTime =
                StrUtil.blankToDefault( arriveEndTime,
                    searchJo.containsKey("filingArriveEndTime") ? searchJo.getString("filingArriveEndTime") : "");
            orderNo =StrUtil.blankToDefault( orderNo,searchJo.containsKey("orderNo") ?
                searchJo.getString("orderNo") : "");
            filingType = StrUtil.blankToDefault(filingType,searchJo.containsKey("filingType") ?
                searchJo.getString("filingType") : "");
            fillingState = StrUtil.blankToDefault(fillingState,searchJo.containsKey("fillingState") ? searchJo.getString("fillingState") : "");
            dockingSalesName = StrUtil.blankToDefault(dockingSalesName,searchJo.containsKey(
                "dockingSalesName") ? searchJo.getString("dockingSalesName") : "");
          }
        }
      }
      Page<OrderFiling> page =
          orderFilingDao.getOrderFilingPageBySupplier(supplierId, filingNo, platform,
              price, customer, state, startTime, endTime, arriveStartTime,
              arriveEndTime, orderNo, filingType, fillingState, dockingSalesName, pageNo, pageSize);
        List<FilingPageDTO> pageDataList = new ArrayList<>();
        int totalPages = page.getTotalPages();
        if (pageNo <= totalPages) {
            List<OrderFiling> orderFilings = page.getContent();
            PageUtil.setOneAsFirstPageNo();
            for (OrderFiling filing : orderFilings) {
              String typeName = platformService.findNameByCode(filing.getType());
              FilingPageDTO data = new FilingPageDTO(filing, typeName);
                pageDataList.add(data);
            }
        }
        return new PageResult<>(pageDataList, page.getTotalElements(), totalPages, pageNo, pageSize);
    }

    @Override
    public FilingDetailDTO getFilingDetail(String filingId) {
        if (StringUtils.isNullOrEmpty(filingId)) {
            throw new CheckException("接口请求有误");
        }
        OrderFiling orderFiling = repository.findById(filingId).orElseThrow(() -> CheckException.noFindException(OrderFiling.class, filingId));
        if (orderFiling != null) {
            FilingDetailDTO filingDetailDTO = new FilingDetailDTO(orderFiling);
            List<OrderFilingDetail> orderFilingDetails = orderFilingDetailDao.getOrderFilingDetailListByFiling(filingId);
            List<FilingProductDetailDTO> filingProductDetailDTOS = new ArrayList<>();
            if (orderFilingDetails != null && orderFilingDetails.size() > 0) {
                for (OrderFilingDetail orderFilingDetail : orderFilingDetails) {
                    FilingProductDetailDTO filingProductDetailDTO = new FilingProductDetailDTO(orderFilingDetail);
                    filingProductDetailDTOS.add(filingProductDetailDTO);
                }
            }
            filingDetailDTO.setProductList(filingProductDetailDTOS);
            return filingDetailDTO;
        } else {
            throw new CheckException("没有此报备单");
        }
    }

  @Override
  public OrderNoFilingDetailDTO getOrderNoFilingDetail(String filingId) {
    if (StringUtils.isNullOrEmpty(filingId)) {
      throw new CheckException("接口请求有误");
    }
    OrderFiling orderFiling = repository.findById(filingId)
        .orElseThrow(() -> CheckException.noFindException(OrderFiling.class, filingId));
    if (orderFiling != null) {
      return new OrderNoFilingDetailDTO(orderFiling);
    } else {
      throw new CheckException("没有此报备单");
    }
  }

    @Transactional
    @Override
    public void saveOrUpdateFiling(FilingParamDTO filingParamDTO) {
        OrderFiling orderFiling;
        if (StringUtils.isNullOrEmpty(filingParamDTO.getId())) {
            orderFiling = new OrderFiling();
            orderFiling.setSupplierId(filingParamDTO.getSupplierId());
            orderFiling.setFilingNo(generateFilingNo());
            orderFiling.setCustomer(StringUtils.emptyIfNull(filingParamDTO.getCustomer()));
            orderFiling.setFilingTime(System.currentTimeMillis());
            orderFiling.setOrderTime(!StringUtils.isNullOrEmpty(filingParamDTO.getOrderTime()) ? DateUtils.parseNormalDateToTimeStamp(filingParamDTO.getOrderTime()) : 0);
            orderFiling.setNum(filingParamDTO.getNum());
            orderFiling.setPrice(filingParamDTO.getPrice());
            orderFiling.setRemark(StringUtils.emptyIfNull(filingParamDTO.getRemark()));
            orderFiling.setType(filingParamDTO.getPlatform());
            String platformName = platformService.findNameByCode(filingParamDTO.getPlatform());
            orderFiling.setTypeName(platformName);
            orderFiling.setFilingState(Constants_order.FILING_STATE_IN_REVIEW);
            orderFiling.setState(Constants.STATE_OK);
            orderFiling.setCreateTime(System.currentTimeMillis());
            orderFiling.setFilingType(Constants.FILING_TYPE_PRODUCT);
            orderFiling.setDockingSalesId(filingParamDTO.getDockingSalesId());
            orderFiling.setDockingSalesName(filingParamDTO.getDockingSalesName());
            orderFiling.setCreateMan(filingParamDTO.getUserId());
            orderFiling.setReceiveAddress(filingParamDTO.getReceiveAddress());
            orderFiling.setReceiveAddressName(filingParamDTO.getReceiveAddressName());
            orderFiling.setDockingSalesJobNumber(filingParamDTO.getDockingSalesJobNumber());
            repository.saveAndFlush(orderFiling);
        } else {
          orderFiling = repository.findById(filingParamDTO.getId()).orElseThrow(
              () -> CheckException.noFindException(Order.class, filingParamDTO.getId()));
          if (!Objects.equals(orderFiling.getFilingState(), Constants_order.FILING_STATE_REJECT)) {
            throw new CheckException("审核驳回状态的报备单才能修改");
          }
          orderFiling.setRemark(StringUtils.emptyIfNull(filingParamDTO.getRemark()));
          orderFiling.setNum(filingParamDTO.getNum());
          orderFiling.setPrice(filingParamDTO.getPrice());
          orderFiling.setFilingType(Constants.FILING_TYPE_PRODUCT);
          orderFiling.setDockingSalesId(filingParamDTO.getDockingSalesId());
          orderFiling.setDockingSalesName(filingParamDTO.getDockingSalesName());
          orderFiling.setFilingState(Constants_order.FILING_STATE_IN_REVIEW);
          orderFiling.setCreateMan(filingParamDTO.getUserId());
          orderFiling.setCustomer(filingParamDTO.getCustomer());
          orderFiling.setOrderTime(StrUtil.isNotBlank(filingParamDTO.getOrderTime()) ?
              DateUtils.parseNormalDateToTimeStamp(filingParamDTO.getOrderTime()) : 0);
          orderFiling.setType(filingParamDTO.getPlatform());
          String platformName = platformService.findNameByCode(filingParamDTO.getPlatform());
          orderFiling.setTypeName(platformName);
          //客户收货地址
          orderFiling.setReceiveAddress(filingParamDTO.getReceiveAddress());
          orderFiling.setReceiveAddressName(filingParamDTO.getReceiveAddressName());
          orderFiling.setDockingSalesJobNumber(filingParamDTO.getDockingSalesJobNumber());
          repository.saveAndFlush(orderFiling);
          orderFilingDetailDao.deleteFilingDetailByFilingId(orderFiling.getId());
        }
        //保存报备单详情信息
      List<FilingParamProductDTO> filingProductDetailDTOS = filingParamDTO.getProductList();
      for (FilingParamProductDTO filingParamProductDTO : CollUtil.emptyIfNull(
          filingProductDetailDTOS)) {
        OrderFilingDetail orderFilingDetail = new OrderFilingDetail();
        orderFilingDetail.setFilingId(orderFiling.getId());
        orderFilingDetail.setBrand(StringUtils.emptyIfNull(filingParamProductDTO.getBrand()));
        orderFilingDetail.setCode(StringUtils.emptyIfNull(filingParamProductDTO.getCode()));
        orderFilingDetail.setPrice(
            filingParamProductDTO.getPrice() != null ? filingParamProductDTO.getPrice()
                : BigDecimal.ZERO);
        orderFilingDetail.setNum(
            filingParamProductDTO.getNum() != null ? filingParamProductDTO.getNum() :
                new BigDecimal(BigInteger.ZERO));
        orderFilingDetail.setModel(StringUtils.emptyIfNull(filingParamProductDTO.getModel()));
        orderFilingDetail.setName(StringUtils.emptyIfNull(filingParamProductDTO.getName()));
        orderFilingDetail.setState(Constants.STATE_OK);
        orderFilingDetail.setCreateTime(System.currentTimeMillis());
        orderFilingDetailRepository.saveAndFlush(orderFilingDetail);
      }
      createProductFilingApproval(orderFiling);
      shareOrderFilingService.syncFilingToOms(orderFiling);
    }

  private String getMobile(String oaUserMdmId) {
    String mobile = "";
    if (StrUtil.isNotBlank(oaUserMdmId)) {
      JSONObject userJson = httpUtil.getOAUserInfoById(oaUserMdmId);
      mobile = userJson.containsKey("mobile") ? String.valueOf(userJson.get("mobile")) : "";
    }
    return mobile;
  }

  @Transactional
  @Override
  public void saveOrUpdateOrderNoFiling(FilingOrderNoParamDTO param) {
    OrderPlatformDTO orderPlatform = platformService.findByCode(param.getPlatform());
    OrderFiling orderFiling = null;
    if (StrUtil.isNotBlank(param.getId())) {
      orderFiling = updateOrderNoFiling(param, orderPlatform);
    }else {
      orderFiling = saveOrderNoFiling(param, orderPlatform);
    }
    shareOrderFilingService.syncFilingToOms(orderFiling);
  }
  private void createOrderFilingApproval(final OrderFiling orderFiling) {
    if (!Objects.equals(orderFiling.getFilingType(), Constants.FILING_TYPE_ORDER_NO)
        || StrUtil.isBlank(orderFiling.getSupplierId())) {
      return;
    }
    Supplier supplier = supplierService.get(orderFiling.getSupplierId(),
        ()-> CheckException.noFindException(Supplier.class, orderFiling.getSupplierId()));
    Long filingTime = orderFiling.getFilingTime();
    String filingTimeFormat = "";
    if (filingTime != null) {
      filingTimeFormat = DateUtil.format(new DateTime(filingTime), "yyyy年MM月dd日");
    }
    Long arriveTime = orderFiling.getArriveTime();
    String arriveTimeFormat = "";
    if (arriveTime != null) {
      arriveTimeFormat = DateUtil.format(new DateTime(arriveTime), "yyyy年MM月dd日");
    }
    OrderPlatformDTO platformDTO = platformService.findByCode(orderFiling.getType());
    List<FormComponentValue> formComponentValues = ListUtil.of(
        new FormComponentValue("供应商名称", supplier.getEnterpriseName()),
        new FormComponentValue("下单平台",
            platformDTO == null ? "" : platformDTO.getPlatformName()),
        new FormComponentValue("客户订单号", orderFiling.getOrderNo()), new FormComponentValue("报备金额",
            orderFiling.getPrice() == null ? "" : orderFiling.getPrice().toString()),
        new FormComponentValue("客户单位", orderFiling.getCustomer()),
        new FormComponentValue("备注", StrUtil.isBlank(orderFiling.getRemark()) ? "" : orderFiling.getRemark()),
        new FormComponentValue("报备单号", orderFiling.getFilingNo()),
        new FormComponentValue("报备时间", filingTimeFormat),
        new FormComponentValue("报备到期日", arriveTimeFormat));
    Optional<String> dockingAssistantOptional =
        supplierPerformanceService.getDockingAssistant(orderFiling.getSupplierId(),
            orderFiling.getType());
    String mobile = "";
    if (dockingAssistantOptional.isPresent()) {
      mobile = getMobile(dockingAssistantOptional.get());
    }
    Approval approval =
        Approval.createInstance(srmConfig.getDingTalkApprovalOriginatorUserId(),
            ListUtil.of(orderFiling.getDockingSalesId()),
        srmConfig.getDingTalkApprovalTemplateOrder(), formComponentValues,
        ListUtil.of(mobile));
    ApprovalInstanceResult approvalInstanceResult = dingUtils.createApproval(approval);
    if (approvalInstanceResult == null || StrUtil.isBlank(approvalInstanceResult.getInstanceId())) {
      log.error("报备单创建钉钉审批实例异常，数据已经提交，报备单号：{}", orderFiling.getFilingNo());
      return;
    }
    orderFiling.setDingApprovalId(approvalInstanceResult.getInstanceId());
    repository.save(orderFiling);
  }

  private List<List<FormComponentValue>> filingProductDetailsConvert(String filingId) {
    List<OrderFilingDetail> orderFilingDetails =
        orderFilingDetailDao.getOrderFilingDetailListByFiling(filingId);
    if (CollUtil.isEmpty(orderFilingDetails)) {
      return null;
    }
    List<List<FormComponentValue>> formComponentValues = new ArrayList<>();
    for (OrderFilingDetail orderFilingDetail : orderFilingDetails) {
        formComponentValues.add(ListUtil.of(new FormComponentValue("商品编码",
                orderFilingDetail.getCode()),
            new FormComponentValue("品牌", orderFilingDetail.getBrand()),
            new FormComponentValue("名称", orderFilingDetail.getName()),
            new FormComponentValue("型号", orderFilingDetail.getModel()),
            new FormComponentValue("数量", orderFilingDetail.getNum() == null ? "" :
                orderFilingDetail.getNum().toString()),
            new FormComponentValue("点单价", orderFilingDetail.getPrice() == null ? "" :
                orderFilingDetail.getPrice().toString())
            ));
    }
    return formComponentValues;
  }
  private void createProductFilingApproval(final OrderFiling orderFiling) {
    if (!Objects.equals(orderFiling.getFilingType(), Constants.FILING_TYPE_PRODUCT)
        || StrUtil.isBlank(orderFiling.getSupplierId())) {
      return;
    }
    Supplier supplier = supplierService.get(orderFiling.getSupplierId(),
        () -> CheckException.noFindException(Supplier.class, orderFiling.getSupplierId()));
    Long orderTime = orderFiling.getOrderTime();
    String orderTimeFormat = "";
    if (orderTime != null) {
      orderTimeFormat = DateUtil.format(new DateTime(orderTime), "yyyy年MM月dd日");
    }
    Long arriveTime = orderFiling.getArriveTime();
    String arriveTimeFormat = "";
    if (arriveTime != null) {
      arriveTimeFormat = DateUtil.format(new DateTime(arriveTime), "yyyy年MM月dd日");
    }
    OrderPlatformDTO platformDTO = platformService.findByCode(orderFiling.getType());
    List<FormComponentValue> formComponentValues = ListUtil.of(
        new FormComponentValue("供应商名称", supplier.getEnterpriseName()),
        new FormComponentValue("报备金额",
            orderFiling.getPrice() == null ? "" : orderFiling.getPrice().toString()),
        new FormComponentValue("下单平台",
                platformDTO == null ? "" : platformDTO.getPlatformName()),
        new FormComponentValue("客户单位", orderFiling.getCustomer()),
        new FormComponentValue("报备到期日", arriveTimeFormat),
        new FormComponentValue("预计点单日", orderTimeFormat),
        new FormComponentValue("商品明细", filingProductDetailsConvert(orderFiling.getId()) == null ?
            "" : JSON.toJSONString(filingProductDetailsConvert(orderFiling.getId()))),
        new FormComponentValue("备注", StrUtil.isBlank(orderFiling.getRemark()) ? "" :
            orderFiling.getRemark()),
        new FormComponentValue("报备单号", orderFiling.getFilingNo()),
        new FormComponentValue("对接销售", JSON.toJSONString(new String[]{orderFiling.getDockingSalesId()}))
    );
    Optional<String> dockingAssistantOptional =
        supplierPerformanceService.getDockingAssistant(orderFiling.getSupplierId(),
            orderFiling.getType());
    String mobile = "";
    if (dockingAssistantOptional.isPresent()) {
      mobile = getMobile(dockingAssistantOptional.get());
    }
    Approval approval =
        Approval.createInstance(srmConfig.getDingTalkApprovalOriginatorUserId(),
        null,
        srmConfig.getDingTalkApprovalTemplateProduct(), formComponentValues
        , ListUtil.of(mobile));
    approval.setApprovers(null);
    approval.setDeptId(-1L);
    ApprovalInstanceResult approvalInstanceResult = dingUtils.createApproval(approval);
    if (approvalInstanceResult == null || StrUtil.isBlank(approvalInstanceResult.getInstanceId())) {
      log.error("报备单创建钉钉审批实例异常，数据已经提交，报备单号：{}", orderFiling.getFilingNo());
      return;
    }
    orderFiling.setDingApprovalId(approvalInstanceResult.getInstanceId());
    repository.save(orderFiling);
  }

  private OrderFiling updateOrderNoFiling(FilingOrderNoParamDTO param, OrderPlatformDTO orderPlatform) {
    OrderFiling orderFiling = repository.findById(param.getId())
        .orElseThrow(() -> CheckException.noFindException(OrderFiling.class, param.getId()));
    if (!Objects.equals(orderFiling.getFilingState(), Constants_order.FILING_STATE_REJECT)) {
      throw new CheckException("审核驳回状态的报备单才能修改");
    }
    orderFiling.setType(param.getPlatform());
    orderFiling.setTypeName(orderPlatform == null ?
        "" : orderPlatform.getPlatformName());
    orderFiling.setOrderNo(param.getOrderNo());
    orderFiling.setPrice(param.getPrice());
    orderFiling.setCustomer(param.getCustomer());
    orderFiling.setRemark(param.getRemark());
    orderFiling.setDockingSalesId(param.getDockingSalesId());
    orderFiling.setDockingSalesName(param.getDockingSalesName());
    orderFiling.setDockingSalesJobNumber(param.getDockingSalesJobNumber());
    orderFiling.setCreateMan(param.getUserId());
    orderFiling.setFilingState(Constants_order.FILING_STATE_IN_REVIEW);
    orderFiling.setReceiveAddress(param.getReceiveAddress());
    repository.saveAndFlush(orderFiling);
    createOrderFilingApproval(orderFiling);
    return orderFiling;
  }

  private OrderFiling saveOrderNoFiling(FilingOrderNoParamDTO param, OrderPlatformDTO orderPlatform) {
    long sevenDays = 60 * 60 * 24 * 7 * 1000;
    long filingTime = System.currentTimeMillis();
    long arriveTime = filingTime + sevenDays;
    OrderFiling orderFiling = new OrderFiling();
    orderFiling.setFilingNo(generateFilingNo());
    orderFiling.setFilingType(Constants.FILING_TYPE_ORDER_NO);
    orderFiling.setOrderNo(param.getOrderNo());
    orderFiling.setFilingState(Constants_order.FILING_STATE_IN_REVIEW);
    orderFiling.setFilingTime(filingTime);
    orderFiling.setCustomer(param.getCustomer());
    orderFiling.setRemark(param.getRemark());
    orderFiling.setArriveTime(arriveTime);
    orderFiling.setCreateTime(filingTime);
    orderFiling.setPrice(param.getPrice());
    orderFiling.setSupplierId(param.getSupplierId());
    orderFiling.setType(param.getPlatform());
    orderFiling.setTypeName(orderPlatform == null ?
        "" : orderPlatform.getPlatformName());
    orderFiling.setState(Constants.STATE_OK);
    orderFiling.setDockingSalesId(param.getDockingSalesId());
    orderFiling.setDockingSalesName(param.getDockingSalesName());
    orderFiling.setDockingSalesJobNumber(param.getDockingSalesJobNumber());
    orderFiling.setCreateMan(param.getUserId());
    orderFiling.setReceiveAddress(param.getReceiveAddress());
    repository.saveAndFlush(orderFiling);
    createOrderFilingApproval(orderFiling);
    return orderFiling;
  }


  private String generateFilingNo() {
    String startDate = DateUtils.formatTimeStampToNormalDate(System.currentTimeMillis());
    String dateStr = DateUtils.formatTimeStampToNormalDate(System.currentTimeMillis()).replace("-", "");
    long count = orderFilingDao.getOrderFilingCountByOrderId(startDate,
        DateUtils.formatTimeStampToNormalDate(DateUtils.parseNormalDateToTimeStamp(startDate) + 24 * 60 * 60 * 1000l)) + 1;
    String number = StrUtil.padPre(String.valueOf(count), 8, '0');
    return "BBD" + dateStr + number;
  }


    @Override
    public void endFiling(SingleBaseParam singleBaseParam) {
        if (StringUtils.isNullOrEmpty(singleBaseParam.getId())) {
            throw new CheckException("操作失败,报备单id为空!");
        }
        OrderFiling orderFiling = repository.findById(singleBaseParam.getId()).orElseThrow(() -> CheckException.noFindException(Order.class, singleBaseParam.getId()));
        if (orderFiling != null) {
            orderFiling.setFilingState(Constants_order.FILING_STATE_DONE);
            repository.save(orderFiling);
        }
    }

    @SneakyThrows
    @Override
    public void saveImportExcelOrderFiling(MultipartFile file, String supplierId,String userId,String erpCode) {
        if (file != null) {
            String fileName = file.getOriginalFilename();
            if (fileName == null || fileName.length() == 0) {
                throw new CheckException("文件异常,请查看文件");
            }
            String fileNewName = FileUtils.getFileNameWithInsertStr(file.getOriginalFilename(), String.valueOf(System.currentTimeMillis()));
          // 导入文件上传至 OSS
          String fileUrl = ossUtil.putOneFile(file.getInputStream(), fileNewName,
              Constants_Batch.UPLOAD_FILE_DIR_TASK_IMPORT + DateUtils.formatTimeStampToPureDate(
                  System.currentTimeMillis()));
          if (StringUtils.isNullOrEmpty(fileUrl)) {
            throw new CheckException("文件上传异常！");
          }
            //设置任务编号
          //新增任务
          Mission mission = Mission.createStartingMission(
              missionUtil.getMissionCode(erpCode),
              "导入-导入报备单",
              userId,
              Constants.PLATFORM_TYPE_BEFORE,
              fileNewName,
              fileUrl
          );
            missionRepository.save(mission);
            JSONObject jo = new JSONObject();
            jo.put("supplierId", supplierId);
            batchTaskMqSender.toHandleBatchTask(mission.getId(), jo.toString(), Constants_Batch.BATCH_TASK_FILLING_IN);
        } else {
            throw new CheckException("无上传文件");
        }
    }

    @Override
    public void exportFiling(FilingExportQuery filingExportQuery) {
        //设置任务编号
      Mission mission = Mission.createStartingMission(
          missionUtil.getMissionCode(filingExportQuery.getErpCode()),
          "导出-报备单导出",
          filingExportQuery.getUserId(),
          Constants.PLATFORM_TYPE_BEFORE
      );
        missionRepository.save(mission);
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("supplierId",filingExportQuery.getSupplierId());
        if(CollUtil.isNotEmpty(filingExportQuery.getFilingIds())){
            jsonObject.put("filingIds",filingExportQuery.getFilingIds());
        } else {
            jsonObject.put("supplierId",StringUtils.emptyIfNull(filingExportQuery.getSupplierId()));
            jsonObject.put("filingNo",StringUtils.emptyIfNull(filingExportQuery.getFilingNo()));
            jsonObject.put("platform",StringUtils.emptyIfNull(filingExportQuery.getPlatform()));
            jsonObject.put("price",StringUtils.emptyIfNull(filingExportQuery.getPrice()));
            jsonObject.put("num",StringUtils.emptyIfNull(filingExportQuery.getNum()));
            jsonObject.put("customer",StringUtils.emptyIfNull(filingExportQuery.getCustomer()));
            jsonObject.put("fillingState",StringUtils.emptyIfNull(filingExportQuery.getFillingState()));
            jsonObject.put("filingStartTime",StringUtils.emptyIfNull(filingExportQuery.getFilingStartTime()));
            jsonObject.put("filingEndTime",StringUtils.emptyIfNull(filingExportQuery.getFilingEndTime()));
            jsonObject.put("filingArriveStartTime",StringUtils.emptyIfNull(filingExportQuery.getFilingArriveStartTime()));
            jsonObject.put("filingArriveEndTime",StringUtils.emptyIfNull(filingExportQuery.getFilingArriveEndTime()));
            jsonObject.put("filingOrderStartTime",StringUtils.emptyIfNull(filingExportQuery.getFilingOrderStartTime()));
            jsonObject.put("filingOrderEndTime",StringUtils.emptyIfNull(filingExportQuery.getFilingOrderEndTime()));
            jsonObject.put("schemeId",StringUtils.emptyIfNull(filingExportQuery.getSchemeId()));
        }
        batchTaskMqSender.toHandleBatchTask(mission.getId(),jsonObject.toString(), Constants_Batch.BATCH_TASK_FILLING_OUT);
    }


  @Override
  public void doRejectHandle(ApprovalResult approvalResult) {
    String processInstanceId =  approvalResult.getProcessInstanceId();
    String dingApprovalOpinion = approvalResult.getRemark();
    String approvalMan = approvalResult.getStaffId();
    OrderFiling orderFiling =
        repository.findByDingApprovalIdAndState(processInstanceId, Constants.STATE_OK).orElse(null);
    if (orderFiling == null) {
      log.error("数据异常，未通过审批实例id查询出报备单：{}", processInstanceId);
    }
    if (!Objects.equals(orderFiling.getFilingState(), Constants_order.FILING_STATE_IN_REVIEW)) {
      throw new CheckException("报备单不在审核中状态");
    }
    //查询oa用户信息
    if (StrUtil.isNotBlank(approvalMan)) {
      OAUserInfoDTO oaUserInfo = oaUserService.getOAUserInfoByDingTalkId(approvalMan);
      if (oaUserInfo == null || oaUserInfo.getData() == null) {
        log.error("根据钉钉id查询oa用户未查询到数据。");
      }else {
        orderFiling.setDingApprovalMan(oaUserInfo.getData().getName());
      }
    }
    orderFiling.setFilingState(Constants_order.FILING_STATE_REJECT);
    orderFiling.setDingApprovalOpinion(dingApprovalOpinion);
    repository.save(orderFiling);
    // 同步至OMS
    shareOrderFilingService.syncFilingToOms(orderFiling);
    //发送短信通知
    if (StrUtil.isNotBlank(orderFiling.getCreateMan())) {
      Optional<SupplierUser> supplierUserOptional =
          supplierUserRepository.findById(orderFiling.getCreateMan());
      supplierUserOptional.ifPresent(
          supplierUser -> xhgjSMSRequest.sendSms(ShortMessageEnum.FILING_VERIFY_REJECT,
              supplierUser.getMobile(), new HashMap<String, String>(){{
                put("cocustomerName", orderFiling.getCustomer());
                put("filingNo", orderFiling.getFilingNo());
              }}));
    }
  }



  @Override
  @Transactional
  public void doPassHandle(ApprovalResult approvalResult) {
    String processInstanceId =  approvalResult.getProcessInstanceId();
    String dingApprovalOpinion = approvalResult.getRemark();
    String approvalMan = approvalResult.getStaffId();
    OrderFiling orderFiling =
        repository.findByDingApprovalIdAndState(processInstanceId, Constants.STATE_OK).orElse(null);
    if (orderFiling == null) {
      log.error("数据异常，未通过审批实例id查询出报备单：{}", processInstanceId);
    }
    if (!Objects.equals(orderFiling.getFilingState(), Constants_order.FILING_STATE_IN_REVIEW)) {
      throw new CheckException("报备单不在审核中状态");
    }
    //查询oa用户信息
    if (StrUtil.isNotBlank(approvalMan)) {
      OAUserInfoDTO oaUserInfo = oaUserService.getOAUserInfoByDingTalkId(approvalMan);
      if (oaUserInfo == null || oaUserInfo.getData() == null) {
        log.error("根据钉钉id查询oa用户未查询到数据。");
      }else {
        orderFiling.setDingApprovalMan(oaUserInfo.getData().getName());
      }
    }
    //修改状态
    orderFiling.setFilingState(Constants_order.FILING_STATE_ING);
    orderFiling.setDingApprovalOpinion(dingApprovalOpinion);
    repository.save(orderFiling);
    //商品报备同步至OMS
    if (Objects.equals(orderFiling.getFilingType(), Constants.FILING_TYPE_PRODUCT)) {
      if (StrUtil.isBlank(orderFiling.getReceiveAddressName())) {
        throw new CheckException(orderFiling.getOrderNo() + "商品报备单客户收货地址数据错误");
      }
      List<OrderFilingDetail> orderFilingDetails =
          orderFilingDetailDao.getOrderFilingDetailListByFiling(orderFiling.getId());
      if (CollUtil.isEmpty(orderFilingDetails)) {
        throw new CheckException("脏数据！商品报备单未找到相关联的商品");
      }
      List<FilingParamProductDTO> filingParamProductDTOS =
          orderFilingDetails.stream().filter(Objects::nonNull).map(FilingParamProductDTO::new)
              .collect(Collectors.toList());
      updateNoPushPlatform(orderFilingDetails, orderFiling.getType());
      // 统一同步至OMS
      List<OMSProduct> productList = filingParamProductDTOS.stream().map(
          MapStructFactory.INSTANCE::toOMSProduct).collect(Collectors.toList());
      shareOrderFilingService.syncFilingToOms(orderFiling);
    } else {
      //订单报备同步至OMS
      shareOrderFilingService.syncFilingToOms(orderFiling);
    }
    //发送短信通知
    if (StrUtil.isNotBlank(orderFiling.getCreateMan())) {
      Optional<SupplierUser> supplierUserOptional =
          supplierUserRepository.findById(orderFiling.getCreateMan());
      supplierUserOptional.ifPresent(
          supplierUser -> xhgjSMSRequest.sendSms(ShortMessageEnum.FILING_VERIFY_PASS,
              supplierUser.getMobile(), new HashMap<String, String>(){{
                put("cocustomerName", orderFiling.getCustomer());
                put("filingNo", orderFiling.getFilingNo());
              }}));
    }
  }

  public void dataHandle(MultipartFile file) {
    InputStream inputStream = null;
    try {
      inputStream = file.getInputStream();
    } catch (IOException e) {
      log.error("文件读取异常", e);
      throw new CheckException("文件读取异常", e);
    }
    Workbook book = null;
    try {
      book = ExcelUtil.buildByFile("fileName.xlsx", inputStream);
    } catch (IOException e) {
      log.error("构建workbook异常", e);
      throw new CheckException("构建workbook异常", e);
    }
    Sheet sheet = book.getSheetAt(0);
    int rowNums = sheet.getPhysicalNumberOfRows();
    for (int i = 1; i < rowNums; i++) {
      try {
        Row row = sheet.getRow(i);
        String orderFilingNo = ex.getCellStringValue(row.getCell(0)).trim();
        OrderFiling orderFiling = orderFilingDao.getOrderFilingByFilingNo(orderFilingNo);
        dataHandle(orderFiling.getId());
      } catch (Exception e) {
        log.error("数据处理异常", e);
      }
    }
  }

  public void dataHandle(String id) {
    OrderFiling orderFiling = orderFilingDao.get(id);
    //商品报备同步至OMS
    if (Objects.equals(orderFiling.getFilingType(), Constants.FILING_TYPE_PRODUCT)) {
      List<OrderFilingDetail> orderFilingDetails =
          orderFilingDetailDao.getOrderFilingDetailListByFiling(orderFiling.getId());
      if (CollUtil.isEmpty(orderFilingDetails)) {
        throw new CheckException("脏数据！商品报备单未找到相关联的商品");
      }
      List<FilingParamProductDTO> filingParamProductDTOS =
          orderFilingDetails.stream().filter(Objects::nonNull).map(FilingParamProductDTO::new)
              .collect(Collectors.toList());
      try {
        updateNoPushPlatform(orderFilingDetails, orderFiling.getType());
      } catch (Exception e) {

      }
      // 统一同步至OMS
      List<OMSProduct> productList = filingParamProductDTOS.stream().map(
          MapStructFactory.INSTANCE::toOMSProduct).collect(Collectors.toList());
      shareOrderFilingService.syncFilingToOms(orderFiling);
    }else{
      // 统一同步至OMS
      shareOrderFilingService.syncFilingToOms(orderFiling);
    }
  }

  /**
   * 商品报备单删除物料禁止推送标记
   * @param orderFilingDetails 报备单详情
   * @param platformCode 下单平台code
   */
  private void updateNoPushPlatform(List<OrderFilingDetail> orderFilingDetails,
      String platformCode) {
    if (CollUtil.isEmpty(orderFilingDetails) || StrUtil.isBlank(platformCode)) {
      throw new CheckException("参数异常！");
    }
    orderFilingDetails =
        orderFilingDetails.stream().filter(Objects::nonNull).collect(Collectors.toList());
    for (OrderFilingDetail orderFilingDetail : orderFilingDetails) {
      xhgjMPMRequest.updateNoPushPlatform(orderFilingDetail.getCode(), ListUtil.of(platformCode));
    }
  }

  private BigDecimal checkProductImportNumberAndPrice(BigDecimal min, BigDecimal max, String value) {
    if (!NumberUtil.isNumber(value)) {
      return null;
    }
    BigDecimal bigDecimal = new BigDecimal(value);
    if (NumberUtil.isGreaterOrEqual(min, bigDecimal)) {
      return null;
    }
    if (NumberUtil.isGreater(bigDecimal, max)) {
      return null;
    }
    int maxDigit = 2;
    if (bigDecimal.scale() > maxDigit) {
      return null;
    }
    return bigDecimal;
  }

  @Override
  @SneakyThrows
  public FilingImportProductVo importProduct(MultipartFile file) {
    FilingImportProductVo vo = new FilingImportProductVo();
    ArrayList<String> errorMessages = new ArrayList<>();
    ArrayList<JSONObject> jsonObjects = new ArrayList<>();
    Workbook book = ExcelUtil.buildByFile(file.getOriginalFilename(), file.getInputStream());
    if (book == null) {
      throw new CheckException("文件异常，请导入指定模板。");
    }
    Sheet sheet = book.getSheetAt(0);
    if (!ex.validateExcel(sheet, 1, Constants_Excel.IMPORT_PRODUCT_FILING_PRODUCT_LIST)) {
      throw new CheckException("文件异常，请导入指定模板。");
    }
    int COLUMN_HEADER_LENGTH = 2;
    // 总行数
    int rowNums = sheet.getPhysicalNumberOfRows();
    int successRow = 0;
    int failRow = 0;
    for (int i = COLUMN_HEADER_LENGTH; i < rowNums; i++) {
      boolean successSign = true;
      Row row = sheet.getRow(i);
      // 获得当前行
      if (row == null) {
        throw new CheckException("文件异常，请联系管理员！");
      }
      Cell cell = row.getCell(0);
      Cell cell1 = row.getCell(1);
      Cell cell2 = row.getCell(2);
      String productCode = ex.getCellStringValue(cell).trim();
      String number = ex.getCellStringValue(cell1).trim();
      String price = ex.getCellStringValue(cell2).trim();
      //空行校验
      if (StrUtil.isBlank(productCode)) {
        errorMessages.add(StrUtil.format("第" + i + "行", "【", "】") + " 物料编码未填写，请重新导入");
        failRow++;
        successSign = false;
        continue;
      }
      productCode = StrUtil.removeAll(productCode, " ").toUpperCase();
      JSONObject productDetail = null;
      try {
        // 已检验正确性
        TypeReference<JSONObject> typeReference = new TypeReference<JSONObject>() {};
        productDetail = mpmService.getProductDetail(productCode, typeReference);
      } catch (Exception e) {
        errorMessages.add(StrUtil.format(productCode, "【", "】") + " 物料编码不存在，请重新导入");
        log.error("获取物料详情异常", e);
        failRow++;
        successSign = false;
        continue;
      }
      BigDecimal min_number_and_price = new BigDecimal(BigInteger.ZERO);
      BigDecimal max_number = new BigDecimal("999999");
      BigDecimal max_price = new BigDecimal("999999999");
      BigDecimal resultNumber =
          checkProductImportNumberAndPrice(min_number_and_price, max_number, number);
      number = resultNumber == null ? null : resultNumber.toPlainString();
      BigDecimal resultPrice =
          checkProductImportNumberAndPrice(min_number_and_price, max_price, price);
      price = resultPrice == null ? null : resultPrice.toPlainString();
      boolean numberSign = true;
      if (number == null) {
        errorMessages.add(
            StrUtil.format(productCode, "【", "】") + " 数量不符合规则，可直接在页面中补充");
        numberSign = false;
        failRow++;
        successSign = false;
      }
      if (price == null && numberSign) {
        errorMessages.add(
            StrUtil.format(productCode, "【", "】") + " 点单价不符合规则，可直接在页面中补充");
        failRow++;
        successSign = false;
      }
      productDetail.put("price", price);
      productDetail.put("number", number);
      jsonObjects.add(productDetail);
      if (successSign) {
        successRow++;
      }
    }
    vo.setProductInfo(jsonObjects);
    vo.setErrorInfo(errorMessages);
    vo.setSuccessNumber(successRow);
    vo.setFailNumber(failRow);
    return vo;
  }
}
