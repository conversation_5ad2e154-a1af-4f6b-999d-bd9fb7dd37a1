package com.xhgj.srm.api.dto.supplierOrder;

import cn.hutool.core.util.ObjectUtil;
import com.xhgj.srm.jpa.entity.SupplierOrderDetail;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import lombok.Data;

/** <AUTHOR> @ClassName ShipProductDTO */
@Data
public class CancelProductDTO extends BaseOrderDetailProductDTO {

  @ApiModelProperty("取消数量/")
  private BigDecimal cancelQty;

  public CancelProductDTO(SupplierOrderDetail supplierOrderDetail) {
    super(supplierOrderDetail.getSupplierOrderProduct(),supplierOrderDetail);
    this.cancelQty =
        ObjectUtil.defaultIfNull(supplierOrderDetail.getCancelQty(), new BigDecimal(0));
  }
}
