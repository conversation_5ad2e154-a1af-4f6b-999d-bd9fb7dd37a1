package com.xhgj.srm.api.controller;


import com.xhgj.srm.api.dto.*;
import com.xhgj.srm.api.service.SchemeService;
import com.xhiot.boot.mvc.base.ResultBean;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

@RestController
@RequestMapping("/scheme")
@Api(tags = {"查询方案接口"})
@Slf4j
public class SchemeController {

    @Autowired
    SchemeService schemeService;


    @ApiOperation(value = "查询检索方案", notes = "查询检索方案")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "supplierUserId", value = "登陆供应商用户id"),
            @ApiImplicitParam(name = "type", value = "类型"),
    })
    @GetMapping(value = "/getMySchemeList")
    public ResultBean<List<SchemeDataDTO>> getMySchemeList(
            String supplierUserId,
            String type
    ) {
        return new ResultBean<>(schemeService.getMySchemeList(supplierUserId,type));
    }

    @ApiOperation(value = "新增检索方案", notes = "新增检索方案")
    @PostMapping("/addScheme")
    public ResultBean<Boolean> addScheme(
            @RequestBody @Valid SchemeAddParamDTO schemeAddParamDTO
    ) {
        schemeService.addScheme(schemeAddParamDTO);
        return new ResultBean<>(true, "操作成功!");
    }

    @ApiOperation(value = "修改检索方案", notes = "修改检索方案")
    @PostMapping("/updateScheme")
    public ResultBean<Boolean> updateScheme(
            @RequestBody @Valid SchemeUpdateParamDTO schemeUpdateParamDTO
    ) {
        schemeService.updateScheme(schemeUpdateParamDTO);
        return new ResultBean<>(true, "操作成功!");
    }

    @ApiOperation(value = "删除检索方案", notes = "删除检索方案")
    @PostMapping("/deleteScheme")
    public ResultBean<Boolean> deleteScheme(
            @RequestBody @Valid  SingleBaseParam param
    ) {
        schemeService.deleteScheme(param);
        return new ResultBean<>(true, "操作成功!");
    }


    @ApiOperation(value = "检索方案设为默认", notes = "检索方案设为默认")
    @PostMapping("/setSchemeDefault")
    public ResultBean<Boolean> setSchemeDefault(
            @RequestBody @Valid  SingleBaseParam param
            ) {
        schemeService.setSchemeDefault(param);
        return new ResultBean<>(true, "操作成功!");
    }


    @ApiOperation(value = "检索方案详情", notes = "删除检索方案")
    @GetMapping("/getSchemeDetail")
    public ResultBean<SchemeDataDTO> getSchemeDetail(
            String schemeId
    ) {
        return new ResultBean<>(schemeService.getSchemeDetail(schemeId));
    }

}
