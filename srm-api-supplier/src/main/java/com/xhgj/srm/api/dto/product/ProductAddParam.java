package com.xhgj.srm.api.dto.product;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import com.xhgj.srm.api.dto.FileDTO;
import com.xhgj.srm.common.Constants;
import com.xhgj.srm.dto.product.externalLink.ExternalLinkSaveForm;
import com.xhgj.srm.jpa.annotations.NumberLengthAndScale;
import com.xhgj.srm.jpa.entity.Product;
import com.xhiot.boot.core.common.util.StringUtils;
import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import java.util.Objects;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Size;
import lombok.Data;
import org.hibernate.validator.constraints.Length;
import org.springframework.util.ObjectUtils;

@Data
public class ProductAddParam {

  @NotBlank(message = "物料类型不能为空")
  @ApiModelProperty("物料类型")
  private String productType;

  @NotBlank(message = "临时编码不能为空")
  @ApiModelProperty("临时编码")
  private String tempCode;

  @NotBlank(message = "品牌不能为空")
  @ApiModelProperty("品牌mdmId")
  private String brandMdmId;

  @NotBlank(message = "品牌不能为空")
  @ApiModelProperty("品牌")
  private String brand;

  @ApiModelProperty("品牌英文名")
  private String brandNameEn;

  @ApiModelProperty("品牌中文名")
  private String brandNameCn;

  @NotBlank(message = "物料名称不能为空")
  @Length(min = 1, max = 40, message = "物料名称超出40字符不展示")
  @ApiModelProperty("物料名称")
  private String name;

  @NotBlank(message = "规格型号不能为空")
  @Length(min = 1, max = 40, message = "规格型号超出40字符不展示")
  @ApiModelProperty("规格型号")
  private String model;

  @NotBlank(message = "基本单位不能为空")
  @ApiModelProperty("基本单位")
  private String basicUnit;

  @NotBlank(message = "基本单位不能为空")
  @ApiModelProperty("基本单位名称")
  private String basicUnitName;

  @NotBlank(message = "类目不能为空")
  @ApiModelProperty("四级类目id")
  private String fourthCateMdmId;

  @NotBlank(message = "类目不能为空")
  @ApiModelProperty("类目名称")
  private String cateName;

  @NumberLengthAndScale(integer = 13, fraction = 2,min = 0.01, message = "毛重值请输入1-13位且大于0，2位小数的数字")
  @ApiModelProperty("毛重")
  private String grossWeight;

  @NotBlank(message = "市场价不能为空")
  @NumberLengthAndScale(min = 0.01, message = "市场价请输入大于0，2位小数的数字")
  @ApiModelProperty("市场价")
  private String marketPrice;

  @NumberLengthAndScale(min = 0.01, message = "参考供货价请输入大于0，2位小数的数字")
  @ApiModelProperty("参考供货价")
  private String purchasePrice;

  @NotBlank(message = "起订量不能为空")
  @NumberLengthAndScale(integer = 7, fraction = 3,min = 0.001, message = "起订量请输入1-7位且大于0，3位小数的数字")
  @ApiModelProperty("起订量")
  private String orderQuantity;

  @ApiModelProperty("是否含安装费")
  private String packingExpense;

  @ApiModelProperty("是否含检测费")
  private String testingFee;

  @NotBlank(message = "是否含运费不能为空")
  @ApiModelProperty("是否含运费")
  private String freight;

  @ApiModelProperty("描述")
  @Length(max = 200, message = "描述超出200字符不展示")
  private String des;

  @ApiModelProperty("条形码")
  @Length(max = 50, message = "条形码超出20字符不展示")
  private String barCode;

  @ApiModelProperty("净重")
  @NumberLengthAndScale(integer = 13, fraction = 2, message = "净重值请输入1-13位且大于0，2位小数的数字")
  private String netWeight;

  @NotBlank(message = "是否已停产不能为空")
  @ApiModelProperty("是否已停产")
  private String haltProduction;

  @ApiModelProperty("长")
  private String length;

  @ApiModelProperty("宽")
  private String width;

  @ApiModelProperty("高")
  private String height;

  @ApiModelProperty("发货日")
  private String deliveryDate;
  /**
   * 缺少关联项目信息
   */
  @ApiModelProperty("产品资料")
  private List<FileDTO> productLiteratures;
  @ApiModelProperty("检测报告")

  private List<FileDTO> reports;
  @ApiModelProperty("质量证明")

  private List<FileDTO> certificateOfQualitys;

  @ApiModelProperty("外部链接")
  private List<ExternalLinkSaveForm> externalLinks;

  @NotBlank(message = "图片关系不能为空")
  @ApiModelProperty("图片关系")
  private String pictureRelationship;

  @NotEmpty(message = "主图不能为空")
  @Size(max = 20, message = "最多上传20张主图")
  @ApiModelProperty("主图")
  private List<FileDTO> mainPictures;

  @NotEmpty(message = "详情图不能为空")
  @ApiModelProperty("详情图")
  private List<FileDTO> detailedPictures;

  @ApiModelProperty("供应商id")
  private String supplierId;

  @ApiModelProperty("商品mdmId")
  private String productMdmId;

  @ApiModelProperty("存储类型")
  private String saveType;

  @ApiModelProperty("扩展属性")
  private String expands;

  @ApiModelProperty("用户id")
  private String userId;

  @ApiModelProperty("上架项目名称/下单平台名称")
  private List<PlatformAndFieldDTO> platformDetails;
  @ApiModelProperty("体积")
  @Length(max = 30, message = "体积超出30字符不展示")
  private String volume;

  /**
   * 税收分类编码
   */
  @ApiModelProperty("税收分类编码")
  @NotBlank(message = "税收分类编码不能为空")
  private String taxCategoryCode;

  /**
   * 税收分类名称
   */
  @ApiModelProperty("税收分类名称")
  @Length(max = 100, message = "税收分类请输入1-100位中文")
  @NotBlank(message = "税收分类名称必填")
  private String taxCategoryName;

  /**
   * 税率字段
   */
  @ApiModelProperty("税率")
  @NumberLengthAndScale(integer = 1, fraction = 2,max = 0.99, message = "税率请输入小于1的两位小数数字")
  @NotBlank(message = "税收分类税率必填")
  private String taxCategoryRate;

  /**
   * 税收分类简称
   */
  @ApiModelProperty("税收分类简称")
  @NotBlank(message = "税收分类简称必填")
  private String taxCategoryAbbr;


  public Product buildProduct() {
    Product product = new Product();
    //上架项目
    StringBuilder platformCodes = new StringBuilder();
    if (!ObjectUtils.isEmpty(platformDetails)) {
      platformDetails.forEach(platformAndFieldDTO -> {
        platformCodes.append(platformAndFieldDTO.getPlatformCode()).append(",");
      });
      platformCodes.deleteCharAt(platformCodes.lastIndexOf(","));
    }
    product.setPlatform(platformCodes.toString());
    product.setProductType(productType);
    product.setTempCode(tempCode);
    //可能缺少品牌编码
    product.setBrandnameEn(brandNameEn);
    product.setBrandnameCn(brandNameCn);
    product.setName(name);
    product.setModel(model);
    product.setBasicUnit(basicUnit);
    product.setUnitName(basicUnitName);
    //可能缺少类目名称
    product.setFourthCateMdmId(fourthCateMdmId);
    product.setGrossWeight(grossWeight);
    product.setMarketPrice(Double.valueOf(marketPrice));
    product.setVolume(volume);
    if (!ObjectUtils.isEmpty(purchasePrice)) {
      product.setPurchasePrice(Double.valueOf(purchasePrice));
    }
    product.setOrderQuantity(orderQuantity);
    product.setIsPack(packingExpense);
    product.setIsDetected(testingFee);
    product.setIsFreeShip(freight);
    product.setDes(des);
    product.setBarCode(barCode);
    product.setNetWeight(netWeight);
    product.setSaleState(haltProduction);
    product.setLength(length);
    product.setWidth(width);
    product.setHeight(height);
    product.setDeliveryDate(deliveryDate);
    product.setPictureType(pictureRelationship.charAt(0));
    product.setTaxCategoryCode(taxCategoryCode);
    product.setTaxCategoryName(taxCategoryName);
    product.setTaxCategoryRate(Convert.toBigDecimal(taxCategoryRate));
    product.setTaxCategoryAbbr(taxCategoryAbbr);
    if (Constants.PRODUCT_TYPE_LANDING_MERCHANT.equals(productType)) {
      product.setOwner(Constants.HEADQUARTERS_CODE);
    }
    if (Constants.PRODUCT_TYPE_SUPPLY_MERCHANT.equals(productType)) {
      product.setOwner(Constants.GROUP_WANJU_CODE);
    }
    product.setBrandMdmId(brandMdmId);
    //品牌
    if (!StringUtils.isNullOrEmpty(brand)) {
      if (!StringUtils.isNullOrEmpty(brandNameCn)) {
        product.setBrandnameCn(brandNameCn);
      }
      if (!StringUtils.isNullOrEmpty(brandNameEn)) {
        product.setBrandnameEn(brandNameEn);
      }
    }
    product.setFourthCateName(cateName);
    if (!StringUtils.isNullOrEmpty(productMdmId)) {
      product.setProductMdmId(productMdmId);
    }
    product.setCreateTime(System.currentTimeMillis());
    //是否关联主图
    if (!ObjectUtils.isEmpty(mainPictures)) {
      product.setRelevanceMainPicture(Constants.PRODUCT_RELEVANCY_MAIN_PICTURE_YES);
    } else {
      product.setRelevanceMainPicture(Constants.PRODUCT_RELEVANCY_MAIN_PICTURE_NO);
    }
    //是否关联详情图
    if (!ObjectUtils.isEmpty(detailedPictures)) {
      product.setRelevanceDetailsPicture(Constants.PRODUCT_RELEVANCY_MAIN_PICTURE_YES);
    } else {
      product.setRelevanceDetailsPicture(Constants.PRODUCT_RELEVANCY_MAIN_PICTURE_NO);
    }
    //设置扩展属性状态
    if (Objects.equals(productType, Constants.PRODUCT_TYPE_LANDING_MERCHANT)) {
      product.setExtendedAttributeState(Constants.PRODUCT_EXPAND_NEED_NOT);
    }
    return product;
  }
}
