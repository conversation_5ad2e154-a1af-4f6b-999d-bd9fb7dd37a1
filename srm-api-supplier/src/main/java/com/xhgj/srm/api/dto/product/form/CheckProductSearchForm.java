package com.xhgj.srm.api.dto.product.form;

import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.util.StrUtil;
import com.xhgj.srm.jpa.dto.BaseDefaultSearchSchemeForm;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * 我的申请查询表单
 */
@ApiModel(description = "我的申请查询表单")
@Data
public class CheckProductSearchForm implements BaseDefaultSearchSchemeForm {
  /**
   * 供应商Id
   */
  @ApiModelProperty("供应商Id")
  private String supplierId;

  /**
   * 品牌
   */
  @ApiModelProperty("品牌")
  private String brand;

  /**
   * 物料名称
   */
  @ApiModelProperty("物料名称")
  private String name;

  /**
   * 物料规格
   */
  @ApiModelProperty("物料规格")
  private String model;

  /**
   * 物料编码
   */
  @ApiModelProperty("物料编码")
  private String tempCode;

  /**
   * 方案Id
   */
  @ApiModelProperty("方案Id")
  private String schemeId;

  /**
   * 用户Id
   */
  @ApiModelProperty("用户Id")
  private String userId;

  /**
   * 单位编码
   */
  @ApiModelProperty("单位编码")
  private String unitCode;

  /**
   * 审核人
   */
  @ApiModelProperty("审核人")
  private String checkMan;

  /**
   * 审核状态
   */
  @ApiModelProperty("审核状态")
  private String checkState;

  /**
   * 审核开始时间
   */
  @ApiModelProperty("审核开始时间")
  private String auditStartDate;

  /**
   * 审核结束时间
   */
  @ApiModelProperty("审核结束时间")
  private String auditEndDate;

  /**
   * 分页 pageSize
   */
  @ApiModelProperty("分页 pageSize")
  private Integer pageSize;

  /**
   * 分页 pageNo
   */
  @ApiModelProperty("分页 pageNo")
  private Integer pageNo;

  public Integer getpageNo() {
    if (pageNo == null || pageNo < 1) {
      return 1;
    }
    return pageNo;
  }

  public Integer getpageSize() {
    if (pageSize == null || pageSize < 1) {
      return 25;
    }
    return pageSize;
  }

  public Map<String,Object> toQueryMap() {
    Map<String, Object> map = new HashMap<>();
    map.put("supplierId",supplierId);
    map.put("brand",brand);
    map.put("name",name);
    map.put("model",model);
    map.put("tempCode",tempCode);
    map.put("schemeId",schemeId);
    map.put("userId",userId);
    map.put("unitCode",unitCode);
    map.put("checkMan",checkMan);
    map.put("checkState",checkState);
    map.put("pageNo",getpageNo());
    map.put("pageSize",getpageSize());
    // 转换时间
    if (StrUtil.isNotBlank(auditStartDate)) {
      // 判断auditStartDate是否包含T
      if (auditStartDate.contains("T")) {
        auditStartDate = auditStartDate.substring(0, auditStartDate.indexOf("T"));
      }
      // 转换为时间戳
      LocalDateTime start = LocalDateTimeUtil.parse(auditStartDate, "yyyy-MM-dd");
      // 设置为一天的开始
      start = start.withHour(0).withMinute(0).withSecond(0).withNano(0);
      map.put("auditStartDate", start.toInstant(ZoneOffset.of("+8")).toEpochMilli());
    }
    if (StrUtil.isNotBlank(auditEndDate)) {
      // 判断auditStartDate是否包含T
      if (auditEndDate.contains("T")) {
        auditEndDate = auditEndDate.substring(0, auditEndDate.indexOf("T"));
      }
      // 转换为时间戳
      LocalDateTime end = LocalDateTimeUtil.parse(auditEndDate, "yyyy-MM-dd");
      // 设置为一天的结束
      end = end.withHour(23).withMinute(59).withSecond(59).withNano(999);
      map.put("auditEndDate", end.toInstant(ZoneOffset.of("+8")).toEpochMilli());
    }
    return map;
  }
}
