package com.xhgj.srm.api.dto.order;

import com.xhgj.srm.dto.order.OrderDetailInvoiceDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;

@Data
public class OrderReceiveParamDTO {


    @ApiModelProperty("客户订单号")
    @NotBlank(message = "客户订单号不能为空！")
    private String orderNo;

    @ApiModelProperty("下单时间")
    private String createTime;

    @ApiModelProperty("下单平台")
    @NotBlank(message = "下单平台不能为空！")
    private String platform;

    @ApiModelProperty("下单金额")
    @NotNull(message = "下单金额不能为空！")
    private BigDecimal price;

    @ApiModelProperty("退货金额")
    private BigDecimal returnPrice;

    @ApiModelProperty("客户名称")
    private String customer;

    @ApiModelProperty("收件人")
    private String consignee;

    @ApiModelProperty("联系方式")
    private String mobile;

    @ApiModelProperty("收件地址")
    private String address;

    @ApiModelProperty("开票状态")
    private String invoicingState;

    @ApiModelProperty("供应商id")
    @NotBlank(message = "供应商id不能为空！")
    private String supplierId;

    @ApiModelProperty("报备单号")
    private String filingNo;

    @ApiModelProperty("派单公司id")
    private String dispatchManCompanyId;

    @ApiModelProperty("供应商订单id")
    private String supplierOrderId;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("商品明细")
    @NotNull(message = "商品明细不能为空！")
    private List<OrderReceiveProductDetailParam> productList;

    @ApiModelProperty("发票信息")
    private OrderDetailInvoiceDTO orderDetailInvoice;

    @ApiModelProperty("销售助理名称")
    private String saleAssistantName;

    @ApiModelProperty("销售助理编码")
    private String saleAssistantCode;

    @ApiModelProperty("订单折扣比例")
    private String orderRate;

    @ApiModelProperty("关联大票项目")
    private List<String> largeTicketProjectNameList;

    @ApiModelProperty("销售订单号")
    private String saleOrderNo;

    @ApiModelProperty("订单备注")
    private String orderRemark;

}
