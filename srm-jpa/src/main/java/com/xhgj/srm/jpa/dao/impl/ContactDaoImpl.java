package com.xhgj.srm.jpa.dao.impl;

import com.xhgj.srm.common.Constants;
import com.xhgj.srm.jpa.dao.ContactDao;
import com.xhgj.srm.jpa.entity.Contact;
import com.xhiot.boot.framework.jpa.dao.AbstractBaseDao;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Service
public class ContactDaoImpl extends AbstractExtDao<Contact> implements ContactDao {

  /**
   * @return com.xhiot.project.pojo.Contact
   * @Title getContactBySid
   * @description 获取联系人列表
   * <AUTHOR>
   * @date 2019/8/6 14:40
   */
  @Override
  public Contact getCurContactBySid(String sid, String mobile) {
    String hql = "from Contact co where co.state = ? and co.supplier.id = ? and co.phone = ? ";
    Object[] params = new Object[]{Constants.STATE_OK, sid, mobile};
    hql += "order by co.createTime desc";
    return getFirstHqlEntity(hql, params);
  }


  /**
   * @return com.xhiot.project.pojo.Contact
   * @Title getContactBySid
   * @description 获取联系人列表
   * <AUTHOR>
   * @date 2019/8/6 14:40
   */
  @Override
  public List<Contact> getContactListBySid(String sid) {
    String hql = "from Contact co where co.state = ? and co.supplier.id = ? ";
    Object[] params = new Object[]{Constants.STATE_OK, sid};
    hql += "order by co.createTime desc";
    return getHqlList(hql, params);
  }

  /**
   * @return com.xhiot.project.pojo.Contact
   * @Title getContactByFbid
   * @description 获取副本关联联系人列表
   * <AUTHOR>
   * @date 2019/8/6 14:40
   */
  @Override
  public List<Contact> getContactListByFbid(String sid) {
    String hql = "from Contact co where co.state = ? and co.supplierFb.id = ? ";
    Object[] params = new Object[]{Constants.STATE_OK, sid};
    hql += "order by co.createTime desc";
    return getHqlList(hql, params);
  }

  /**
   * @return com.xhiot.project.pojo.Contact
   * @Title getCurContactByFbid
   * @description 获取副本关联联系人列表
   * <AUTHOR>
   * @date 2019/8/6 14:40
   */
  @Override
  public Contact getCurContactByFbid(String sid, String mobile) {
    String hql = "from Contact co where co.state = ? and co.supplierFb.id = ? and co.phone = ? ";
    Object[] params = new Object[]{Constants.STATE_OK, sid, mobile};
    hql += "order by co.createTime desc";
    return getFirstHqlEntity(hql, params);
  }

  /**
   * @Title:
   * @Description:获取该供应商创建人建的所有联系人
   * <AUTHOR>
   * @date 2019/12/11 15:10
   */
  @Override
  public List<Contact> getContactListBySidAndUserid(String sid, String userid) {
    String hql = "from Contact co where co.state = ? and co.supplier.id = ? and co.createMan = ? ";
    Object[] params = new Object[]{Constants.STATE_OK, sid, userid};
    hql += "order by co.createTime desc";
    return getHqlList(hql, params);
  }

  /**
   * @Title:
   * @Description:根据联系人手机和姓名获取除创建人之外的其他人输入的供应商联系人
   * <AUTHOR>
   * @date 2019/12/11 15:08
   */
  @Override
  public Contact getCurContactBySidAndUser(String sid, String mobile, String name, String userid) {
    String hql = "from Contact co where co.state = ? and co.supplier.id = ? and co.phone = ? and co.name = ? and co.createMan != ? ";
    Object[] params = new Object[]{Constants.STATE_OK, sid, mobile, name, userid};
    hql += "order by co.createTime desc";
    return (Contact) getFirstHqlEntity(hql, params);
  }


  /**
   * @Title:
   * @Description:根据联系人手机和姓名获取除创建人之外的其他人输入的供应商联系人
   * <AUTHOR>
   * @date 2019/12/11 15:08
   */
  @Override
  public Contact getCurContactBySidAndName(String sid, String name) {
    String hql = "from Contact co where co.state = ? and co.supplier.id = ? and co.name = ? ";
    Object[] params = new Object[]{Constants.STATE_OK, sid, name};
    hql += "order by co.createTime desc";
    return getFirstHqlEntity(hql, params);
  }

  @Transactional
  @Override
  public void delContactBySidAndUid(String sid, String supplierUserId) {
    String hql = "delete from t_contact  where c_state = ? and supplierId = ? and c_createMan = ? ";
    Object[] params = new Object[]{Constants.STATE_OK, sid, supplierUserId};
    executeSqlUpdate(hql, params);
  }

    @Override
    public Page<Contact> getContactListBySidAndUserId(String sid, String supplierUserId, String pageNo, String pageSize) {
        String hql = "from Contact co where co.state = ? and co.supplier.id = ? and co.createMan = ? ";
        Object[] params = new Object[]{Constants.STATE_OK, sid, supplierUserId};
        hql += "order by co.createTime desc";
        return findPage(hql, params, Integer.parseInt(pageNo), Integer.parseInt(pageSize));
    }

  @Override
  public List<String> getContactIdListBySid(String sid) {
    String hql = "select co.id from Contact co where co.state = ? and co.supplier.id = ? ";
    Object[] params = new Object[]{Constants.STATE_OK, sid};
    hql += "order by co.createTime desc";
    return getHqlObjList(hql, params);
  }

  @Override
  public List<Contact> getContactListBySupplierInGroupId(String id) {
    String hql = "from Contact where state = ? and supplierInGroupId = ? ";
    Object[] params = new Object[]{Constants.STATE_OK, id};
    hql += "ORDER BY createTime ASC ";
    return getHqlList(hql,params);
  }

  @Transactional
  @Override
  public void updateContactSupplierInGroup(String supplierInGroupId, String supplierId) {
    String hql = "update Contact co set co.supplierInGroupId = ? where co.state = ? and co.supplier.id = ? ";
    Object[] params = new Object[]{supplierInGroupId,Constants.STATE_OK,supplierId};
    executeUpdate(hql, params);
  }
}
