package com.xhgj.srm.jpa.entity;

import lombok.Data;
import org.hibernate.annotations.GenericGenerator;
import javax.persistence.*;
import java.io.Serializable;

/**
 * t_order_invoice_template表实体
 *
 * <AUTHOR>
 * @since 2023-06-04 15:57:34
 */
@Data
@Entity
@Table(name = "t_order_invoice_template")
public class OrderInvoiceTemplate implements Serializable {
    private static final long serialVersionUID = -46547390844578014L;
     /** 主键*/
     @Id
     @Column(name ="id", nullable = false)
     @GeneratedValue(generator = "system-uuid")
     @GenericGenerator(name = "system-uuid", strategy = "uuid")
     private String id;
     /**订单 id*/
     @Column(name = "order_id")
     private String orderId;
     /**发票类型(中文)*/
     @Column(name = "c_invoice_type_str")
     private String invoiceTypeStr;
     /**发票类型*/
     @Column(name = "c_invoice_type")
     private String invoiceType;
     /**发票抬头*/
     @Column(name = "c_invoice_title")
     private String invoiceTitle;
     /**纳税识别号*/
     @Column(name = "c_tax_code")
     private String taxCode;
     /**开户行*/
     @Column(name = "c_bank_name")
     private String bankName;
     /**银行账户*/
     @Column(name = "c_bank_account")
     private String bankAccount;
     /**开票电话*/
     @Column(name = "c_open_invoice_mobile")
     private String openInvoiceMobile;
     /**开票地址*/
     @Column(name = "c_invoice_address")
     private String invoiceAddress;
     /**票面信息*/
     @Column(name = "c_invoice_face_info")
     private String invoiceFaceInfo;
     /**发票接收人*/
     @Column(name = "c_receive_man")
     private String receiveMan;
     /**联系人电话*/
     @Column(name = "c_mobile")
     private String mobile;
     /**收票地址*/
     @Column(name = "c_receive_address")
     private String receiveAddress;
}

