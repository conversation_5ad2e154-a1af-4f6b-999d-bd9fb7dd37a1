package com.xhgj.srm.jpa.entity;/**
 * @since 2025/2/19 11:04
 */

import lombok.Data;
import org.hibernate.annotations.GenericGenerator;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import java.math.BigDecimal;

/**
 *<AUTHOR>
 *@date 2025/2/19 11:04:47
 *@description
 */
@Data
@Entity
@Table(name = "t_inventory_safety")
public class InventorySafety {

  @Id
  @Column(name = "id", nullable = false)
  @GeneratedValue(generator = "system-uuid")
  @GenericGenerator(name = "system-uuid", strategy = "uuid")
  private String id;

  /**
   * 组织编码
   */
  @Column(name = "c_group_code")
  private String groupCode;

  /**
   * 物料编码
   */
  @Column(name = "c_product_code")
  private String productCode;

  /**
   * 仓库编码
   */
  @Column(name = "c_warehouse")
  private String warehouse;

  /**
   * 仓库id
   */
  @Column(name = "c_warehouse_id")
  private String warehouseId;

  /**
   * 品牌名称中文
   */
  @Column(name = "c_brand_name_cn")
  private String brandNameCn;

  /**
   * 品牌名称英文
   */
  @Column(name = "c_brand_name_en")
  private String brandNameEn;

  /**
   * 品牌名称
   */
  @Column(name = "c_brand_name")
  private String brandName;

  /**
   * 物料名称
   */
  @Column(name = "c_name")
  private String name;

  /**
   * 物料型号
   */
  @Column(name = "c_model")
  private String model;

  /**
   * 物料单位
   */
  @Column(name = "c_unit")
  private String unit;

  /**
   * 库存安全最小数量
   */
  @Column(name = "c_min_safety_stock")
  private BigDecimal minSafetyStock;

  /**
   * 通知人
   */
  @Column(name = "c_notified_person")
  private String notifiedPerson;

  /**
   * 通知人id
   */
  @Column(name = "c_notified_person_id")
  private String notifiedPersonId;

  /**
   * 通知人工号
   */
  @Column(name = "c_notified_person_code")
  private String notifiedPersonCode;

  /**
   * 通知人手机号
   */
  @Column(name = "c_notified_person_phone")
  private String notifiedPersonPhone;

  /**
   * 上次提醒时间
   */
  @Column(name = "c_last_reminder_time")
  private Long lastReminderTime;

  /**
   * 下次提醒时间
   */
  @Column(name = "c_next_reminder_time")
  private Long nextReminderTime;

  /**
   * 通知状态
   */
  @Column(name = "c_status")
  private Byte status;

  /**
   * 创建时间
   */
  @Column(name = "c_create_time")
  private Long createTime;

  /**
   * 更新时间
   */
  @Column(name = "c_update_time")
  private Long updateTime;

  /**
   * 数据状态
   */
  @Column(name = "c_state")
  private String state;
}
