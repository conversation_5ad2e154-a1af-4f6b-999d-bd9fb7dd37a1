package com.xhgj.srm.jpa.dao.impl;

import cn.hutool.core.lang.Assert;
import com.xhgj.srm.common.Constants;
import com.xhgj.srm.jpa.dao.OrderSupplierInvoiceDao;
import com.xhgj.srm.jpa.entity.OrderSupplierInvoice;
import java.util.List;
import java.util.Optional;
import com.xhiot.boot.framework.jpa.util.HqlUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class OrderSupplierInvoiceDaoImpl extends AbstractExtDao<OrderSupplierInvoice>
    implements OrderSupplierInvoiceDao {

  @Override
  public List<OrderSupplierInvoice> getOrderAccountInvoiceByAccount(String accountId) {
    String hql = "from OrderSupplierInvoice o where o.orderAccountId = ?  ";
    Object[] params = new Object[] {accountId};
    return getHqlList(hql, params);
  }

  @Override
  public Optional<OrderSupplierInvoice> getValidInvoiceByNum(String invoiceNum) {
    Assert.notBlank(invoiceNum);
    String hql =
        "FROM OrderSupplierInvoice "
            + "WHERE state = ? AND (offset = ? or offset is null) AND invoiceNum = ? ";
    return Optional.ofNullable(
        getFirstHqlEntity(hql, Constants.STATE_OK, Constants.NO, invoiceNum));
  }

  @Override
  public Optional<OrderSupplierInvoice> getValidInvoiceByInvoiceNumList(List<String> invoiceNumList) {
    StringBuilder hql = new StringBuilder(
        "FROM OrderSupplierInvoice "
            + "WHERE state = ? AND (offset = ? or offset is null)  ");
    Object[] params = new Object[] {Constants.STATE_OK, Constants.NO};
    params = HqlUtil.appendFieldIn(hql,params,"invoiceNum", invoiceNumList);
    hql.append(" order by invoiceTime asc");
    return Optional.ofNullable(
        getFirstHqlEntity(hql.toString(), params));
  }
}
