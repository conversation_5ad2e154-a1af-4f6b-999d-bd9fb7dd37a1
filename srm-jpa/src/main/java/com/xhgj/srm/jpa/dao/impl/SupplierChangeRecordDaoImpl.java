package com.xhgj.srm.jpa.dao.impl;

import cn.hutool.core.collection.CollUtil;
import com.xhgj.srm.jpa.dao.SupplierChangeRecordDao;
import com.xhgj.srm.jpa.entity.SupplierChangeRecord;
import com.xhiot.boot.core.common.util.ObjectUtils;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;
import java.util.List;

/**
  *@ClassName SupplierRateRecordDaoImpl
  *<AUTHOR>
  *@Date 2023/11/22 14:40
*/
@Service
public class SupplierChangeRecordDaoImpl extends AbstractExtDao<SupplierChangeRecord> implements
    SupplierChangeRecordDao {

  @Override
  public Page<SupplierChangeRecord> getSupplierRateRecodeList(String supplierId, Integer pageNo,
                                                           Integer pageSize, List<Byte> types) {
    StringBuilder sql = new StringBuilder("select * from t_supplier_change_record srr where 1=1 ");
    Object[] params = new Object[]{};
    if (supplierId != null){
      sql.append("and srr.supplier_id = ? ");
      params = ObjectUtils.objectAdd(params,supplierId);
    }
    if (CollUtil.isNotEmpty(types)){
      sql.append("and srr.c_type in ( ");
      for (int i = 0; i < types.size(); i++) {
        if (i != 0){
          sql.append(" , ");
        }
        sql.append(" ? ");
        params = ObjectUtils.objectAdd(params,types.get(i));
      }
      sql.append(") ");
    }
    sql.append(" order by srr.c_create_time desc");
    return findPageSql(sql.toString(), params, pageNo, pageSize);
  }
}
