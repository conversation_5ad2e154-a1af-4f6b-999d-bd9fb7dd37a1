package com.xhgj.srm.jpa.repository;

import com.xhgj.srm.jpa.entity.FieldConfig;
import com.xhiot.boot.framework.jpa.repository.BootBaseRepository;
import org.springframework.transaction.annotation.Transactional;
import java.util.List;

public interface FieldConfigRepository extends BootBaseRepository<FieldConfig, String> {

  /**
   * 根据大类查询字段配置
   *
   * @param bigType 大类
   * @return 字段配置列表
   */
  List<FieldConfig> findAllByBigType(String bigType);

  /**
   * 根据大类和分组类型查询字段配置
   * @param bigType
   * @param groupType
   * @return
   */
  List<FieldConfig> findAllByBigTypeAndGroupTypeOrderByGroupTypeAscSortAsc(String bigType,
      String groupType);

  /**
   * 根据大类和分组类型查询字段配置
   * @param bigType
   * @param groupType
   */
  @Transactional
  void deleteAllByBigTypeAndGroupTypeIn(String bigType, List<String> groupType);
}
