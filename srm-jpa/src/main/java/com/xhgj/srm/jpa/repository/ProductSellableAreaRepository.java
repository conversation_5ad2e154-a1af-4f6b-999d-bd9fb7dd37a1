package com.xhgj.srm.jpa.repository;

import com.xhgj.srm.jpa.entity.ProductSellableArea;
import com.xhiot.boot.framework.jpa.repository.BootBaseRepository;
import java.util.List;

public interface ProductSellableAreaRepository extends BootBaseRepository<ProductSellableArea, String> {
  List<ProductSellableArea> getAllBySupplierIdAndProductCode(String supplierId,
      String productCode);

  int deleteAllBySupplierIdAndProductCodeAndSellArea(String supplierId,
      String productCode,String sellArea);

  int deleteAllBySupplierIdAndProductCode(String supplierId,
      String productCode);

  ProductSellableArea getFirstBySupplierIdAndProductCodeAndSellArea(String supplierId,
      String productCode,String sellArea);

  List<ProductSellableArea> getAllBySupplierIdAndProductCodeAndSellAreaIsLike(String supplierId,
      String productCode,String sellArea);

  ProductSellableArea getFirstByProductCodeAndSellArea(
      String productCode,String sellArea);
}