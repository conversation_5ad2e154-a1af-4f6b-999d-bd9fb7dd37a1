package com.xhgj.srm.jpa.repository;

import com.xhgj.srm.jpa.entity.ExportFiledBase;
import com.xhiot.boot.framework.jpa.repository.BootBaseRepository;
import java.util.List;

/**
 * 导出字段基础配置表(ExportFiledBase)表数据库访问层
 *
 * <AUTHOR>
 * @since 2024-05-24 09:15:17
 */
public interface ExportFiledBaseRepository extends BootBaseRepository<ExportFiledBase, String> {

  List<ExportFiledBase> getAllByTypeAndRegularFiledAndStateOrderBySortAsc(String type,
      Boolean regularFiled, String state);

  List<ExportFiledBase> getAllByTypeAndRegularFiledIsNullAndStateAndSuperIdIsNullOrderBySortAsc(
      String type, String state);

  List<ExportFiledBase> getAllByTypeAndRegularFiledIsNullAndStateAndSuperIdOrderBySortAsc(
      String type, String state, String superId);
}


