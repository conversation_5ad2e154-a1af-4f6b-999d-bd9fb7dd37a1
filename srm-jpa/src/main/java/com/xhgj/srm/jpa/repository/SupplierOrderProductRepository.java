package com.xhgj.srm.jpa.repository;

import com.xhgj.srm.jpa.entity.SupplierOrderProduct;
import com.xhiot.boot.framework.jpa.repository.BootBaseRepository;

/**
 * <AUTHOR>
 * @since 2022/11/28 15:03
 */
public interface SupplierOrderProductRepository extends BootBaseRepository<SupplierOrderProduct,String> {

  /**
   * 根据物料编码获得物料
   * @param code 物料编码必传
   */
  SupplierOrderProduct getFirstByCode(String code);
}
