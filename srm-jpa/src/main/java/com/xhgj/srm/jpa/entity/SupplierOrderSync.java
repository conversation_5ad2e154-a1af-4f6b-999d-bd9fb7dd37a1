package com.xhgj.srm.jpa.entity;/**
 * @since 2025/4/15 11:35
 */

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import lombok.Data;
import org.hibernate.annotations.GenericGenerator;

/**
 *<AUTHOR>
 *@date 2025/4/15 11:35:54
 *@description
 */
@Data
@Entity
@Table(name = "t_supplier_order_sync")
public class SupplierOrderSync {

  @Id
  @Column(name = "id", nullable = false)
  @GeneratedValue(generator = "snowflake-id")
  @GenericGenerator(name = "snowflake-id", strategy = "com.xhgj.srm.jpa.util.SnowflakeIdGenerator")
  private String id;

  /**
   * 采购订单id
   */
  @Column(name = "c_supplier_order_id", length = 32)
  private String supplierOrderId;

  /**
   * 采购订单号
   */
  @Column(name = "c_supplier_order_code", length = 100)
  private String supplierOrderCode;

  /**
   * 目标系统 飞搭、SAP
   * @see com.xhgj.srm.common.constants.SupplierOrderSyncConstants
   */
  @Column(name = "c_target", length = 255)
  private String target;

  /**
   * 创建时间
   */
  @Column(name = "c_create_time")
  private Long createTime;

  /**
   * 完成时间
   */
  @Column(name = "c_success_time")
  private Long successTime;

  /**
   * 同步方式 手动重推、自动同步
   * @see com.xhgj.srm.common.constants.SupplierOrderSyncConstants
   */
  @Column(name = "c_sync_type", length = 20)
  private String syncType;

  /**
   * 操作人
   */
  @Column(name = "c_create_man" , length = 32)
  private String createMan;

  /**
   * 操作人名称
   */
  @Column(name = "c_create_man_name", length = 50)
  private String createManName;

  /**
   * 同步结果
   * @see com.xhgj.srm.common.enums.supplierorder.SupplierOrderSyncStatus
   */
  @Column(name = "c_status")
  private Byte status;

  /**
   * 审核id
   */
  @Column(name = "c_review_id", length = 32)
  private String reviewId;

  /**
   * 审核时间
   */
  @Column(name = "c_review_time")
  private Long reviewTime;

  /**
   * 审核原因
   */
  @Column(name = "c_review_reason", length = 200)
  private String reviewReason;

  /**
   * 审核状态
   * @see com.xhgj.srm.common.enums.supplierorder.SupplierOrderSyncStatus
   */
  @Column(name = "c_review_status")
  private Byte reviewStatus;

  /**
   * 数据状态标识
   */
  @Column(name = "c_state", length = 1)
  private String state;

  /**
   * 请求参数
   */
  @Column(name = "c_req")
  private String req;

  /**
   * 响应参数
   */
  @Column(name = "c_res")
  private String res;

  /**
   * 请求url
   */
  @Column(name = "c_url")
  private String url;

  /**
   * 请求头
   */
  @Column(name = "c_header")
  private String header;

  /**
   * 类型  1采购订单飞搭审核   2 sap 084
   */
  @Column(name = "c_type")
  private String type;
}
