package com.xhgj.srm.jpa.config;/**
 * @since 2025/4/15 13:36
 */

import com.xhgj.srm.jpa.aop.ShardingAnnotationProcessor;
import com.xhgj.srm.jpa.sharding.Interceptor.HintInterceptor;
import org.hibernate.jpa.boot.spi.EntityManagerFactoryBuilder;
import org.springframework.boot.autoconfigure.orm.jpa.HibernatePropertiesCustomizer;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.orm.jpa.JpaVendorAdapter;
import org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean;

import javax.sql.DataSource;
import java.sql.SQLException;
import java.util.HashMap;
import java.util.Map;
import java.util.Properties;

/**
 *<AUTHOR>
 *@date 2025/4/15 13:36:18
 *@description
 */
@Configuration
public class ShardingConfig {

  @Bean
  public DataSource dataSource(DataSourceConfig dataSourceConfig)
      throws SQLException {
    return new ShardingAnnotationProcessor(dataSourceConfig).createShardingDataSource();
  }

  @Bean
  public HibernatePropertiesCustomizer hibernatePropertiesCustomizer() {
    return hibernateProperties -> hibernateProperties.put("hibernate.session_factory.statement_inspector", new HintInterceptor());
  }
}
