package com.xhgj.srm.jpa.entity;

import com.xhgj.srm.common.enums.transferOrder.WarehouseBusinessType;
import lombok.Data;
import org.hibernate.annotations.GenericGenerator;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;

/**
 * 库位管理
 */
@Entity
@Table(name = "t_inventory_location")
@Data
public class InventoryLocation {

  /**
   * 主键
   */
  @Id
  @Column(name = "id", nullable = false)
  @GeneratedValue(generator = "system-uuid")
  @GenericGenerator(name = "system-uuid", strategy = "uuid")
  private String id;

  /**
   * 组织编码
   */
  @Column(name = "c_group_code")
  private String groupCode;

  /**
   * 组织名称
   */
  @Column(name = "c_group_name")
  private String groupName;

  /**
   * 库房名称
   */
  @Column(name = "c_warehouse_name")
  private String warehouseName;

  /**
   * 库房编码
   */
  @Column(name = "c_warehouse")
  private String warehouse;

  /**
   * 是否涉及WMS:1-是，0-否
   */
  @Column(name = "c_is_wms")
  private String isWms;

  /**
   * 涉及WMS的业务类型：1-采购订单，2-货物移动
   */
  @Column(name = "c_business_type")
  private String businessType;


  /**
   * 创建时间
   */
  @Column(name = "c_create_time")
  private Long createTime;

  /**
   * 更新时间
   */
  @Column(name = "c_update_time")
  private Long updateTime;
  /**
   * 消息可用
   */
  @Column(name = "c_state")
  private String state;

  /**
   * 允许SRM制单和收货的订单类型（取值为采购订单类型数据字典）
   */
  @Column(name = "c_prepare_receive_order_type")
  private String prepareReceiveOrderType;

  /**
   * 是否允许SRM退货 0 否 1是
   */
  @Column(name = "c_allow_return_type")
  private String allowReturnType;

  /**
   * 是否允许SRM入库单退库单冲销 0 否 1是
   */
  @Column(name = "c_inbound_return_reversal")
  private String inboundReturnReversal;

  /**
   * 是否允许SRM调拨 0 否 1是
   */
  @Column(name = "c_allow_transfer")
  private String allowTransfer;

  /**
   * 是否允许SRM组装拆卸 0 否 1是
   */
  @Column(name = "c_allow_assemble_disassemble")
  private String allowAssembleDisassemble;

  /**
   * 是否涉及wms-货物移动
   */
  public boolean hasMovement() {
    return WarehouseBusinessType.WMS_MOVEMENT.hasMovement(businessType);
  }

  /**
   * 是否涉及wms-货物移动
   */
  public boolean hasWmsOrder() {
    return WarehouseBusinessType.WMS_PURCHASE.hasPurchase(businessType);
  }

}
