package com.xhgj.srm.jpa.aop;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.ReflectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.xhgj.srm.jpa.annotations.DefaultSearchScheme;
import com.xhgj.srm.jpa.dao.SearchSchemeDao;
import com.xhgj.srm.jpa.dto.BaseDefaultSearchSchemeForm;
import com.xhgj.srm.jpa.entity.SearchScheme;
import com.xhgj.srm.jpa.repository.SearchSchemeRepository;
import com.xhgj.srm.unified.dto.BaseUnifiedForm;
import com.xhgj.srm.unified.dto.UnifiedChildForm;
import com.xhgj.srm.unified.dto.UnifiedChildForm.UnifiedFilter;
import com.xhgj.srm.unified.dto.UnifiedForm;
import com.xhgj.srm.unified.enmus.UnifiedCombinationLogicEnums;
import com.xhgj.srm.unified.enmus.UnifiedOperatorEnums;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.hssf.record.PageBreakRecord.Break;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.stereotype.Component;
import javax.annotation.Resource;
import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;

/**
 * <AUTHOR>
 * 默认搜索方案切面
 */
@Aspect
@Component
@Slf4j
public class DefaultSearchSchemeAspect {
  @Resource
  private SearchSchemeRepository searchSchemeRepository;
  @Resource
  private SearchSchemeDao searchSchemeDao;

  /**
   * 操作符后缀
   */
  private static final String LogicSuffix = "UnifiedLogic";

  /**
   * 逻辑符后缀
   */
  private static final String OperatorSuffix = "UnifiedOperator";

  /**
   * 默认搜索方案
   * @param joinPoint
   * @param defaultSearchScheme
   * @return
   * @throws Throwable
   */
  @Around("@annotation(defaultSearchScheme)")
  public Object defaultSearchScheme(ProceedingJoinPoint joinPoint,
      DefaultSearchScheme defaultSearchScheme) throws Throwable {
    MethodSignature signature = (MethodSignature) joinPoint.getSignature();
    Method method = signature.getMethod();
    DefaultSearchScheme annotation = method.getAnnotation(DefaultSearchScheme.class);
    String searchType = annotation.searchType();
    Object[] args = joinPoint.getArgs();
    Object form = null;
    // args 筛选出form对象
    for (Object arg : args) {
      if (arg instanceof BaseDefaultSearchSchemeForm) {
        form = arg;
      }
    }
    if (form == null) {
      return joinPoint.proceed();
    }
    String schemeId = Convert.toStr(ReflectUtil.getFieldValue(form, "schemeId"));
    String userId = Convert.toStr(ReflectUtil.getFieldValue(form, "userId"));
    SearchScheme search;
    if (StrUtil.isBlank(schemeId)) {
      search = searchSchemeDao.getDefaultSearchScheme(searchType, userId);
    }else{
      search = searchSchemeRepository.findById(schemeId).orElse(null);
    }

    if (search != null && StrUtil.isNotEmpty(search.getContent())) {
      JSONObject jsonObject = JSON.parseObject(search.getContent());
      // 获取origin中字段名称包含OperatorSuffix和LogicSuffix的字段
      Set<String> strings = jsonObject.keySet();
      Boolean isUnifiedForm = strings.stream().anyMatch(item -> item.endsWith(OperatorSuffix) ||
          item.endsWith(LogicSuffix));
      // 根据不同的表单类型处理
      if (form instanceof BaseUnifiedForm && isUnifiedForm) {
        // 统一表单处理
        setUnifiedForm(form, JSON.parseObject(search.getContent()));
      } else {
        Object obj = JSON.parseObject(search.getContent(), form.getClass());
        this.copyPropertiesIgnoreNullOrExists(obj, form);
      }
    }
    return joinPoint.proceed();
  }

  /**
   * 复制属性，忽略null或者已存在的字段
   * @param origin
   * @param form
   */
  public void copyPropertiesIgnoreNullOrExists(Object origin, Object form) {
    if (origin == null || form == null) {
      return;
    }
    // 遍历 origin的字段
    Map<String, Field> fieldMap = ReflectUtil.getFieldMap(origin.getClass());
    for (String fieldName : fieldMap.keySet()) {
      Object value = ReflectUtil.getFieldValue(origin, fieldName);
      if (value == null) {
        continue;
      }
      if (!ReflectUtil.hasField(form.getClass(), fieldName)) {
        // 跳过当前字段的处理
        continue;
      }
      // 获取目标对象的字段值
      Object targetValue = ReflectUtil.getFieldValue(form, fieldName);

      // 如果目标值是字符串类型，且为空或只包含空白字符
      if (targetValue instanceof String && StrUtil.isBlank((String) targetValue)) {
        // 设置目标对象的字段值
        ReflectUtil.setFieldValue(form, fieldName, value);
      } else if (targetValue == null) {
        // 如果目标值为 null，也设置字段值
        ReflectUtil.setFieldValue(form, fieldName, value);
      }
    }
  }

  public void setUnifiedForm(Object toForm, JSONObject origin) {
    // 获取origin中字段名称包含OperatorSuffix和LogicSuffix的字段
    Set<String> strings = origin.keySet();
    for (String string : strings) {
      if (string.endsWith(OperatorSuffix) ||
          string.endsWith(LogicSuffix)) {
        setUnifiedForm(toForm, origin, string);
      }
    }
  }

  /**
   * 设置统一表单
   */
  public void setUnifiedForm(Object toForm, JSONObject origin, String operatorFieldName) {
    if (toForm instanceof BaseUnifiedForm) {
      String fieldName = StrUtil.removeSuffix(operatorFieldName, OperatorSuffix);
      // 初始化默认值
      UnifiedCombinationLogicEnums logic = getEnumFromOrigin(origin, fieldName + LogicSuffix, UnifiedCombinationLogicEnums.class, UnifiedCombinationLogicEnums.AND);
      UnifiedOperatorEnums operator = getEnumFromOrigin(origin, operatorFieldName, UnifiedOperatorEnums.class);
      Object value = origin.get(fieldName);
      // 获取对应的toForm字段类型
      Field field = ReflectUtil.getField(toForm.getClass(), fieldName);
      // 如果field存在获取相应的类型
      Class<?> classz = String.class;
      if (field != null) {
        classz = field.getType();
      }
      // 获取统一表单并确保其存在
      UnifiedForm unifiedForm = getOrCreateUnifiedForm(toForm);
      // 获取指定逻辑的子表单，若不存在则创建
      UnifiedChildForm unifiedChildForm = getOrCreateChildForm(unifiedForm, logic);
      // 新建并添加过滤器
      unifiedChildForm.getFilters().add(createFilter(fieldName, value, operator, classz));
      // 更新子表单和主表单
      unifiedForm.setChildForms(unifiedForm.getChildForms());
    }
  }

  // 获取枚举值的通用方法
  private <T extends Enum<T>> T getEnumFromOrigin(Map<String, Object> origin, String key, Class<T> enumClass, T defaultValue) {
    return Optional.ofNullable(origin.get(key))
        .map(Object::toString)
        .map(value -> Enum.valueOf(enumClass, value))
        .orElse(defaultValue);
  }

  // 获取 operator 字段值的简化方法
  private <T extends Enum<T>> T getEnumFromOrigin(Map<String, Object> origin, String key, Class<T> enumClass) {
    return getEnumFromOrigin(origin, key, enumClass, null);
  }

  // 工具方法：获取或创建统一表单
  private UnifiedForm getOrCreateUnifiedForm(Object toForm) {
    UnifiedForm unifiedForm = ((BaseUnifiedForm) toForm).getUnifiedForm();
    if (unifiedForm == null) {
      unifiedForm = new UnifiedForm();
      ReflectUtil.setFieldValue(toForm, "unifiedForm", unifiedForm);
    }
    return unifiedForm;
  }

  // 工具方法：获取或创建子表单
  private UnifiedChildForm getOrCreateChildForm(UnifiedForm unifiedForm, UnifiedCombinationLogicEnums logic) {
    return unifiedForm.getChildForms().stream()
        .filter(item -> item.getLogicOperator() == logic)
        .findFirst()
        .orElseGet(() -> {
          UnifiedChildForm newChildForm = new UnifiedChildForm(logic);
          unifiedForm.getChildForms().add(newChildForm);
          return newChildForm;
        });
  }

  // 工具方法：创建过滤器
  private UnifiedFilter createFilter(String fieldName, Object value,
      UnifiedOperatorEnums operator, Class<?> classz) {
    UnifiedFilter filter = new UnifiedFilter();
    filter.setField(fieldName);
    filter.setValue(value);
    filter.setOperator(operator);
    filter.setClassTypeStr(classz.getName());
    return filter;
  }



}
