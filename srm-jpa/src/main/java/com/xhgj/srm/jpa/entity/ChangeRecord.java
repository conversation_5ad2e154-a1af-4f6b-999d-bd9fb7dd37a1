package com.xhgj.srm.jpa.entity;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.GenericGenerator;

/**
 * <AUTHOR>
 * @since 2022/8/16 1:00
 */
@Entity
@Table(name = "t_change_record")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ChangeRecord {

  @Id
  @Column(name = "id", nullable = false)
  @GeneratedValue(generator = "system-uuid")
  @GenericGenerator(name = "system-uuid", strategy = "uuid")
  private String id;

  @Column(name = "relation_id")
  private String relationId;

  @Column(name = "c_field_code")
  private String fieldCode;

  @Column(name = "c_old_value")
  private String oldValue;

  @Column(name = "c_new_value")
  private String newValue;

  @Column(name = "c_operate_man")
  private String operateMan;

  @Column(name = "c_operate_time")
  private Long operateTime;
}
