package com.xhgj.srm.jpa.entity;

import com.xhgj.srm.jpa.dto.supplierUser.SupplierUserPermissionAware;
import com.xhgj.srm.jpa.util.LazyLoadEntityListener;
import lombok.Data;
import org.hibernate.annotations.GenericGenerator;
import org.springframework.data.annotation.CreatedDate;

import javax.persistence.*;
import java.io.Serializable;

@Table(name = "t_supplier_user")
@Entity
@Data
@EntityListeners(LazyLoadEntityListener.class)
public class SupplierUser implements Serializable , SupplierUserPermissionAware {

    private static final long serialVersionUID = 1L;

    @Id
    @Column(name = "id", nullable = false)
    @GeneratedValue(generator = "system-uuid")
    @GenericGenerator(name = "system-uuid", strategy = "uuid")
    private String id;

    @JoinColumn(name = "supplierId",insertable = false,updatable = false)
    @ManyToOne(fetch = FetchType.LAZY)
    private Supplier supplier;

    @Column(name = "supplierId")
    private String supplierId;

    @Column(name = "c_name")
    private String name;

    @Column(name = "c_password")
    private String password;

    @Column(name = "c_realName")
    private String realName;

    @Column(name = "c_mobile")
    private String mobile;

    @Column(name = "c_mail")
    private String mail;

    @Column(name = "c_role")
    private String role;

    @Column(name = "c_state")
    private String state;

    @CreatedDate
    @Column(name = "c_createTime")
    private Long createTime;

  /**
   * 创建人用户id
   */
  @Column(name = "c_create_man")
    private String createMan;


  /**
   * 修改时间
   */
  @Column(name = "c_update_time")
  private Long updateTime;


  /**
   * 来源 0 后台，1前台
   */
  @Column(name = "c_source")
  private Byte source;

  /**
   * 用户权限 (1供应商权限 2自营供应商权限)
   */
  @Column(name = "c_permission")
  private String permission;
}
