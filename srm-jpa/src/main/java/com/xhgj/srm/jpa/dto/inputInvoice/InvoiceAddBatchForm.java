package com.xhgj.srm.jpa.dto.inputInvoice;

import com.xhgj.srm.common.dto.FileDTO;
import com.xhgj.srm.jpa.dto.inputInvoice.InputInvoiceAddBaseForm;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.util.List;
import lombok.Data;
import javax.validation.constraints.NotBlank;

/**
  *@ClassName InvoiceAddParams
  *<AUTHOR>
  *@Date 2023/12/26 10:38
*/
@Data
public class InvoiceAddBatchForm extends InputInvoiceAddBaseForm {

  @ApiModelProperty("发票信息")
  private List<InvoiceInfo> invoiceInfo;

  @ApiModelProperty(value = "被删除的发票id")
  private List<String> deleteInvoiceIds;

  @ApiModelProperty("是否是红票  0不是 1 是")
  private String  needRedTicket;

  @Data
  public static class InvoiceInfo extends InputInvoiceAddBaseForm{
    @ApiModelProperty("发票号")
    private String invoiceNumber;

    @ApiModelProperty("进项票id")
    private String orderInvoiceRelationId;

    @ApiModelProperty("关联类型 订单 入库单 明细")
    private String associationType;

    @ApiModelProperty("发票备注")
    private String remark;

    @ApiModelProperty("账期")
    private String accountPeriod;

    @ApiModelProperty("付款方式")
    private String payType;

    @ApiModelProperty("发票附件")
    private List<FileDTO> files;

    @ApiModelProperty("进项票信息")
    private List<InvoiceDTO> invoiceDTOList;

    @ApiModelProperty("尾差原因")
    private String tailDifference;

    @ApiModelProperty("销方")
    @NotBlank(message = "销方名称必传")
    private String seller;

    /**
     * 进项票来源(1前台录入、2后台批量录票、3后台手工票)
     * {@link com.xhgj.srm.common.enums.inputInvoice.InputInvoiceSourceEnums }
     */
    private String source;
  }

  @Data
  public static class InvoiceDTO {
//    @ApiModelProperty("所选订单id")
//    private String orderId;
//    @ApiModelProperty("所选入库单id")
//    private String orderToFormId;
//    @ApiModelProperty("所选明细id")
//    private String detailId;
//    @ApiModelProperty("开票数量")
//    private BigDecimal num;
    @ApiModelProperty("关联明细id")
    private String id;
    @ApiModelProperty("本次开票数量")
    private BigDecimal num;
    @ApiModelProperty("本次开票金额")
    private BigDecimal openAmount;
  }

}
