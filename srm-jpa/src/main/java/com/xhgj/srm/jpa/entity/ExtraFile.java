package com.xhgj.srm.jpa.entity;

import lombok.Data;
import org.hibernate.annotations.GenericGenerator;

import javax.persistence.*;
import java.io.Serializable;

@Entity
@Table(name = "t_extrafile")
@Data
public class ExtraFile implements Serializable {

    private static final long serialVersionUID = 1L;

    @Id
    @Column(name = "id", nullable = false)
    @GeneratedValue(generator = "system-uuid")
    @GenericGenerator(name = "system-uuid", strategy = "uuid")
    private String id;

    @Column(name = "c_relationName")
    private String relationName;

    @Column(name = "c_relationId")
    private String relationId;

    @Column(name = "c_name")
    private String name;

    @Column(name = "c_url")
    private String url;

    @Column(name = "c_type")
    private String type;

    @Column(name = "c_createTime")
    private Long createTime;

    @Column(name = "c_state")
    private String state;

    @Column(name = "c_description")
    private String description;

}
