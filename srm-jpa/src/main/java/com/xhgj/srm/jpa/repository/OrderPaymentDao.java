package com.xhgj.srm.jpa.repository;

import com.xhgj.srm.jpa.dto.order.OrderPaymentStatistics;
import com.xhgj.srm.jpa.dto.order.OrderPaymentWithOrderId;
import com.xhgj.srm.jpa.entity.OrderPayment;
import com.xhiot.boot.framework.jpa.dao.BootBaseDao;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2023-03-16 17:26
 */
public interface OrderPaymentDao extends BootBaseDao<OrderPayment> {

  /**
   * 分页获取付款单信息
   *
   * @param supplierId 供应商 id
   * @param paymentNo 付款单号
   * @param paymentStatus 付款状态
   * @param submitMan 提交人
   * @param orderCount 订单数
   * @param applyPrice 付款申请金额
   * @param paymentPrice 已付金额
   * @param createTimeStart 提交时间范围 开始
   * @param createTimeEnd 提交时间范围 结束
   * @param pageable 分页参数
   */
  Page<OrderPayment> findPage(
      String supplierId,
      String paymentNo,
      String paymentStatus,
      String submitMan,
      String orderCount,
      String applyPrice,
      String paymentPrice,
      Long createTimeStart,
      Long createTimeEnd,
      Boolean autoDraw,
      Pageable pageable);

  /**
   * 分页获取付款单信息
   * @param queryMap
   * @return
   */
  Page<OrderPayment> findPageRef(Map<String, Object> queryMap);

  /**
   * * 通过付款状态查询付款单状态
   *
   * @param paymentStatus 支付状态 必填
   * <AUTHOR>
   * @date 2023/3/22 17:31
   * @return long
   */
  long getOrderPaymentNumByPaymentStatus(String paymentStatus);

  /**
   * 获取落地商合同关联的付款单 - 分页
   * @param contractId
   * @return
   */
  Page<Object[]> getByContract(String contractId, Integer pageNo, Integer pageSize);

  /**
  *获取付款申请单数量 <AUTHOR> @Date: 2024年6月16日 16:09:44
  * @param supplierId
   * @param paymentStatus
  * @return long
  */
  long getOrderPaymentNumByPaymentStatus(String supplierId,String paymentStatus);

  /**
   * 通过订单 ids 查询已完成的付款单
   * @param orderIds
   * @return
   */
  List<OrderPaymentWithOrderId> findAllCompleteByOrderIdIn(List<String> orderIds);
  /**
   * 通过订单id查询是否存在已完成或付款中的付款单
   */
  long getExistsCountByOrderId(String orderId);

  /**
   * 付款单统计
   * @param queryMap
   * @return
   */
  List<OrderPayment> findStatistics(Map<String, Object> queryMap);

  /**
   * 付款单统计
   * @param queryMap
   * @return
   */
  OrderPaymentStatistics findStatistics2(Map<String, Object> queryMap);

  List<String> getAllOrderPaymentListHandle();

  /**
   * 通过订单id查询付款单
   * @param orderId
   * @return
   */
  List<OrderPayment> findAllByOrderId(String orderId);
}
