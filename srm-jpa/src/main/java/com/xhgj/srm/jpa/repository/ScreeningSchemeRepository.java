package com.xhgj.srm.jpa.repository;

import com.xhgj.srm.jpa.entity.ScreeningScheme;
import com.xhiot.boot.framework.jpa.repository.BootBaseRepository;

public interface ScreeningSchemeRepository extends BootBaseRepository<ScreeningScheme, String> {

  /**
   * 根据用户id和类型和数据状态查询
   * @param userId
   * @param type
   * @param state
   * @return
   */
  ScreeningScheme findByUserIdAndTypeAndState(String userId, String type, String state);
}
