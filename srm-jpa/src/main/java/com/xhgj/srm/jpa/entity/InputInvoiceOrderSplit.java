package com.xhgj.srm.jpa.entity;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import lombok.Data;
import org.hibernate.annotations.GenericGenerator;

/**
 * InputInvoiceOrder 进项票单拆分表
 */
@Entity
@Table(name = "t_input_invoice_order_split")
@Data
public class InputInvoiceOrderSplit {
  @Id
  @Column(name = "id")
  @GeneratedValue(generator = "system-uuid")
  @GenericGenerator(name = "system-uuid", strategy = "uuid")
  private String id;

  /**
   *  进项票单id
   */
  @Column(name = "c_input_invoice_id")
  private String inputInvoiceId;


  /**
   *  订单id
   */
  @Column(name = "c_supplier_order_id")
  private String supplierOrderId;


  /**
   *  订单号
   */
  @Column(name = "c_supplier_order_code")
  private String supplierOrderCode;

  /**
   *  创建时间
   */
  @Column(name = "c_create_time")
  private Long createTime;
}
