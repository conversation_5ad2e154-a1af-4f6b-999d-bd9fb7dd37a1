package com.xhgj.srm.jpa.repository;

import com.xhgj.srm.jpa.entity.BidProjectToPlatform;
import com.xhiot.boot.framework.jpa.repository.BootBaseRepository;
import java.util.Optional;

/**
 * BidProjectToPlatformRepository
 */
public interface BidProjectToPlatformRepository extends
    BootBaseRepository<BidProjectToPlatform, String> {

  Optional<BidProjectToPlatform> findByPlatformIdAndBidProjectId(String platformId, String projectId);

  boolean existsByPlatformIdAndBidProjectId(String platformId, String bidProjectId);
}
