package com.xhgj.srm.jpa.entity;

import lombok.Data;
import org.hibernate.annotations.GenericGenerator;

import javax.persistence.*;
import java.io.Serializable;

@Entity
@Table(name = "t_logininfo")
@Data
public class LoginInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    @Id
    @Column(name = "id", nullable = false)
    @GeneratedValue(generator = "system-uuid")
    @GenericGenerator(name = "system-uuid", strategy = "uuid")
    private String id;

    @Column(name = "c_loginUserId")
    private String loginUserId;

    @Column(name = "c_logintype")
    private String logintype;

    @Column(name = "c_loginPlatform")
    private String loginPlatform;

    @Column(name = "c_ip")
    private String ip;

    @Column(name = "c_description")
    private String description;

    @Column(name = "c_createTime")
    private Long createTime;

    @Column(name = "c_state")
    private String state;

}
