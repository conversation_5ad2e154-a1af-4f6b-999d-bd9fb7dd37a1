package com.xhgj.srm.jpa.dto.inquiry;

import lombok.Data;
import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 * <AUTHOR>
 */
@Data
public class InquiryStatistics {
  /**
   * 经销含税成本(元)
   */
  private BigDecimal salesPrice;

  /**
   * 含税市场价(元)
   */
  private BigDecimal marketPrice;

  /**
   * 含税调拨价(元)
   */
  private BigDecimal transferPrice;

  /**
   * 小数位2位
   */
  public BigDecimal getSalesPrice() {
    if (salesPrice == null) {
      return null;
    }
    return salesPrice.setScale(2, RoundingMode.HALF_UP);
  }

  /**
   * 小数位2位
   */
  public BigDecimal getMarketPrice() {
    if (marketPrice == null) {
      return null;
    }
    return marketPrice.setScale(2, RoundingMode.HALF_UP);
  }

  /**
   * 小数位2位
   */
  public BigDecimal getTransferPrice() {
    if (transferPrice == null) {
      return null;
    }
    return transferPrice.setScale(2, RoundingMode.HALF_UP);
  }
}
