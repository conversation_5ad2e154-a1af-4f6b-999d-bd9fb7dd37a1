package com.xhgj.srm.jpa.dao.impl;

import cn.hutool.core.util.StrUtil;
import com.xhgj.srm.common.Constants;
import com.xhgj.srm.jpa.dao.PurchaseApplyRecordDao;
import com.xhgj.srm.jpa.entity.PurchaseApplyRecord;
import com.xhiot.boot.core.common.util.ObjectUtils;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Repository;

/**
 * PurchaseApplyRecordDaoImpl
 */
@Repository
public class PurchaseApplyRecordDaoImpl extends AbstractExtDao<PurchaseApplyRecord>
    implements PurchaseApplyRecordDao {

  @Override
  public Page<PurchaseApplyRecord> getPurchaseApplyRecordDetailList(String purchaseApplyId,
      Integer pageNo, Integer pageSize) {
    StringBuilder sql = new StringBuilder("select * from t_purchase_apply_record par where par.c_state = ? ");
    Object[] params = new Object[]{Constants.STATE_OK};
    if (StrUtil.isNotBlank(purchaseApplyId)){
      sql.append("and par.c_purchase_apply_id = ? ");
      params = ObjectUtils.objectAdd(params,purchaseApplyId);
    }
    return findPageSql(sql.toString(), params, pageNo, pageSize);
  }
}
