package com.xhgj.srm.jpa.dao.impl;

import com.xhgj.srm.common.Constants;
import com.xhgj.srm.jpa.dao.ExpandDao;
import com.xhgj.srm.jpa.entity.Expand;
import com.xhgj.srm.jpa.entity.Product;
import com.xhiot.boot.framework.jpa.dao.AbstractBaseDao;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Service
public class ExpandDaoImpl extends AbstractExtDao<Expand> implements ExpandDao {

    @Override
    public List<Expand> getExpandListById(String id) {
        String hql = "from Expand e where e.state = ? and e.product.id = ? order by e.createTime desc";
        Object[] params = new Object[]{Constants.STATE_OK,id};
        return getHqlList(hql, params);
    }

    @Transactional
    @Override
    public void deleteExpand(String productId) {
        String hql = "delete from Expand e where e.state = ? and e.product.id = ? ";
        Object[] params = new Object[]{Constants.STATE_OK, productId};
        executeUpdate(hql, params);
    }

    @Override
    public Expand getExpandByIdAndKey(String productId, String name) {
        String hql = "from Expand e where e.state = ? and e.product.id = ? and e.attrkey = ? ";
        Object[] params = new Object[]{Constants.STATE_OK,productId,name};
        return getFirstHqlEntity(hql, params);
    }

}
