package com.xhgj.srm.jpa.dto.landingContract;/**
 * @since 2024/11/28 11:38
 */

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import javax.validation.constraints.NotBlank;

/**
 *<AUTHOR>
 *@date 2024/11/28 11:38:15
 *@description 合同绑品信息保存表单
 */
@Data
public class LandingContractBundleSaveForm {
  /**
   * 更新时传递
   */
  private String id;

  /**
   * 绑品类型 (0品牌绑定  1区域绑定  2客户单位绑定)
   * 详见 {@link com.xhgj.srm.common.enums.landingContract.BundleType}
   */
  @NotBlank(message = "绑品类型不能为空")
  private Byte bundleType;

  /**
   * 绑品区域(品牌区域 绑品区域)
   */
  private String bundleArea;

  /**
   * 绑品区域名称
   */
  private String bundleAreaName;

  /**
   * 绑品区域路径
   */
  @ApiModelProperty(hidden = true)
  private String bundleAreaPath;

  /**
   * 绑品品牌
   */
  private String bundleBrand;

  /**
   * 绑品品牌名称
   */
  private String bundleBrandName;

  /**
   * 绑品客户单位
   */
  private String bundleCustomer;

  /**
   * 绑品客户单位名称
   */
  private String bundleCustomerName;
}
