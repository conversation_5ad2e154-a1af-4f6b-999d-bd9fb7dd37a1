package com.xhgj.srm.jpa.sharding.merger;/**
 * @since 2025/5/25 14:07
 */

import cn.hutool.core.convert.Convert;
import org.apache.shardingsphere.infra.binder.statement.dml.SelectStatementContext;
import org.apache.shardingsphere.infra.executor.sql.execute.result.query.QueryResult;
import org.apache.shardingsphere.infra.merge.result.MergedResult;
import java.io.InputStream;
import java.math.BigDecimal;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Comparator;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.TreeMap;

/**
 * <AUTHOR>
 */
public class SumMergedResult implements MergedResult {

  // 存储累加后的值，Object 类型保留原始类型
  private final Map<String, Object> sums = new LinkedHashMap<>();
  private final List<String> columnLabels;
  private boolean returned = false;

  /**
   * 构造器直接从 QueryResult 列表聚合：
   * - 对 columnLabelIndexMap 中的每个列名做累加，
   * - 其它列将保留第一次出现的值
   */
  public SumMergedResult(final List<QueryResult> queryResults,
      final SelectStatementContext sqlCtx,
      final Map<String, Integer> columnLabelIndexMap) throws SQLException {

    // 按照索引值排序列标签
    List<Map.Entry<String, Integer>> sortedEntries = new ArrayList<>(columnLabelIndexMap.entrySet());
    sortedEntries.sort(Map.Entry.comparingByValue());

    // 使用排序后的条目初始化 columnLabels
    columnLabels = new ArrayList<>();
    for (Map.Entry<String, Integer> entry : sortedEntries) {
      columnLabels.add(entry.getKey());
    }

    // 初始化 sums
    for (String label : columnLabels) {
      sums.put(label, BigDecimal.ZERO);
    }

    // 遍历所有分片结果
    for (QueryResult qr : queryResults) {
      while (qr.next()) {
        // 按照排序后的列标签处理每一列
        for (Map.Entry<String, Integer> entry : sortedEntries) {
          String label = entry.getKey();
          int idx = entry.getValue();
          Object val = qr.getValue(idx, Object.class);
          // 如果为 null，跳过
          if (val == null) {
            continue;
          }
          Object current = sums.get(label);
          if (val instanceof Number) {
            // 数值类型按原始类型累加
            if (current == null) {
              // 第一次出现，直接保存
              sums.put(label, val);
            } else {
              sums.put(label, addNumbers(current, (Number) val));
            }
          } else {
            // 非数值类型，仅保留第一次
            if (current == null) {
              sums.put(label, val);
            }
          }
        }
      }
    }
  }

  private static Number addNumbers(final Object current, final Number next) {
    if (current instanceof Integer) {
      return ((Integer) current) + next.intValue();
    }
    if (current instanceof Long) {
      return ((Long) current) + next.longValue();
    }
    if (current instanceof Double) {
      return ((Double) current) + next.doubleValue();
    }
    if (current instanceof Float) {
      return ((Float) current) + next.floatValue();
    }
    if (current instanceof BigDecimal) {
      return ((BigDecimal) current).add(new BigDecimal(next.toString()));
    }
    // fallback
    BigDecimal bd = new BigDecimal(current.toString());
    return bd.add(new BigDecimal(next.toString()));
  }

  @Override
  public boolean next() {
    if (returned) {
      return false;
    }
    returned = true;
    return true;
  }

  @Override
  public Object getValue(final int columnIndex, final Class<?> type) throws SQLException {
    // 根据 columnIndex 找到对应列名
    String label = columnLabels.get(columnIndex - 1);
    Object sum = sums.get(label);
    if (sum == null) {
      return null;
    }
    if (type == BigDecimal.class) {
      return Convert.toBigDecimal(sum);
    }
    if (type == Long.class || type == long.class) {
      return Convert.toLong(sum);
    }
    if (type == Integer.class || type == int.class) {
      return Convert.toInt(sum);
    }
    if (type == Double.class || type == double.class) {
      return Convert.toDouble(sum);
    }
    if (type == Float.class || type == float.class) {
      return Convert.toFloat(sum);
    }
    if (Number.class.isAssignableFrom(type)) {
      try {
        return type.getConstructor(String.class).newInstance(sum.toString());
      } catch (Exception e) {
        throw new SQLException("无法转换累加结果为类型: " + type, e);
      }
    }
    // 对于非数值列，返回原值（已累加为0）
    return Convert.toStr(sum);
  }

  @Override
  public Object getCalendarValue(int columnIndex, Class<?> type, Calendar calendar)
      throws SQLException {
    return getValue(columnIndex, type);
  }

  @Override
  public InputStream getInputStream(final int columnIndex, final String type) throws SQLException {
    throw new UnsupportedOperationException("SumMergedResult 不支持 getInputStream");
  }

  @Override
  public boolean wasNull() {
    return false;
  }
}
