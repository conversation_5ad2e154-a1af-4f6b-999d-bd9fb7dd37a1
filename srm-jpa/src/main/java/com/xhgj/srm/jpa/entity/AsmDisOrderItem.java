package com.xhgj.srm.jpa.entity;

import java.math.BigDecimal;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.validation.constraints.Size;
import lombok.Data;
import org.hibernate.annotations.GenericGenerator;

/**
 * 组装拆卸单明细
 */
@Data
@Entity
@Table(name = "t_asm_dis_order_item")
public class AsmDisOrderItem {

  @Id
  @Column(name = "id", nullable = false)
  @GeneratedValue(generator = "system-uuid")
  @GenericGenerator(name = "system-uuid", strategy = "uuid")
  private String id;

  /**
   * 组装拆卸单id
   */
  @Size(max = 32)
  @Column(name = "c_asm_dis_id", length = 32)
  private String asmDisId;

  /**
   * 组装拆卸单号
   */
  @Size(max = 30)
  @Column(name = "c_asm_dis_code", length = 30)
  private String asmDisCode;

  /**
   * 单据类型 1.组装单 2.拆卸单
   * @see com.xhgj.srm.common.enums.asmDisOrder.AsmDisOrderType
   */
  @Column(name = "c_asm_dis_type")
  private Byte asmDisType;

  /**
   * 行id
   */
  @Size(max = 20)
  @Column(name = "c_row_id", length = 20)
  private String rowId;

  /**
   * 组装拆卸单明细类型 1.成品 2.子件
   */
  @Column(name = "c_type")
  private Byte type;

  /**
   * 物料code
   */
  @Size(max = 60)
  @Column(name = "c_product_code", length = 60)
  private String productCode;

  /**
   * 物料品牌
   */
  @Size(max = 80)
  @Column(name = "c_brand", length = 80)
  private String brand;

  /**
   * 物料名称
   */
  @Size(max = 150)
  @Column(name = "c_product_name", length = 150)
  private String productName;

  /**
   * 物料描述
   */
  @Size(max = 150)
  @Column(name = "c_desc", length = 150)
  private String desc;

  /**
   * 物料型号
   */
  @Size(max = 200)
  @Column(name = "c_model", length = 200)
  private String model;

  /**
   * 物料单位
   */
  @Size(max = 80)
  @Column(name = "c_unit", length = 80)
  private String unit;

  /**
   * 单位编码
   */
  @Column(name = "c_unit_code")
  private String unitCode;

  /**
   * 物料数量
   */
  @Column(name = "c_num", precision = 18, scale = 3)
  private BigDecimal num;

  /**
   * 未税单价
   */
  @Column(name = "c_origin_net_price", precision = 18, scale = 2)
  private BigDecimal originNetPrice;

  /**
   * 含税单价
   */
  @Column(name = "c_origin_price", precision = 18, scale = 2)
  private BigDecimal originPrice;

  /**
   * 含税总价
   */
  @Column(name = "c_origin_price_total", precision = 18, scale = 2)
  private BigDecimal originPriceTotal;

  /**
   * 结算单价
   */
  @Column(name = "c_price", precision = 18, scale = 2)
  private BigDecimal price;

  /**
   * 结算总价
   */
  @Column(name = "c_price_total", precision = 18, scale = 2)
  private BigDecimal priceTotal;

  /**
   * 税率(物料或订单税率)
   */
  @Column(name = "c_tax", precision = 18, scale = 2)
  private BigDecimal tax;

  /**
   * 仓库id
   * @see com.xhgj.srm.jpa.entity.InventoryLocation
   */
  @Size(max = 32)
  @Column(name = "c_warehouse_id", length = 32)
  private String warehouseId;

  /**
   * 备注
   */
  @Size(max = 200)
  @Column(name = "c_remark", length = 200)
  private String remark;

  /**
   * 关联销售订单号
   */
  @Size(max = 100)
  @Column(name = "c_sale_order_no", length = 100)
  private String saleOrderNo;

  /**
   * 关联销售订单行id
   */
  @Size(max = 10)
  @Column(name = "c_sale_order_product_row_id", length = 10)
  private String saleOrderProductRowId;

  /**
   * 关联采购订单id
   * @see com.xhgj.srm.jpa.entity.PurchaseApplyForOrder
   */
  @Size(max = 32)
  @Column(name = "c_purchase_apply_for_order_id", length = 32)
  private String purchaseApplyForOrderId;

  /**
   * 关联采购订单号
   */
  @Size(max = 40)
  @Column(name = "c_purchase_apply_for_order_code", length = 40)
  private String purchaseApplyForOrderCode;

  /**
   * 批次
   */
  @Size(max = 40)
  @Column(name = "c_batch_no", length = 40)
  private String batchNo;

  /**
   * 创建时间
   */
  @Column(name = "c_create_time")
  private Long createTime;

  /**
   * 更新时间
   */
  @Column(name = "c_update_time")
  private Long updateTime;

  /**
   * 数据状态
   */
  @Size(max = 1)
  @Column(name = "c_state", length = 1)
  private String state;
}