package com.xhgj.srm.jpa.dto.purchase.order;

import lombok.Data;
import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 * 采购订单统计 物料视图
 * <AUTHOR>
 */
@Data
public class PurchaseOrderProductStatistics {
  /**
   * 物料取消数量 3位小数
   */
  private BigDecimal cancelQty;

  /**
   * 退货数量 3位小数
   */
  private BigDecimal returnQty;

  /**
   * 含税单价 2位小数
   */
  private BigDecimal price;

  /**
   * 金额合计
   */
  private BigDecimal totalPrice;

  /**
   * 订货数量
   */
  private BigDecimal num;

  /**
   * 3位小数
   * @return
   */
  public BigDecimal getCancelQty() {
    if (cancelQty == null) {
      return BigDecimal.ZERO;
    }
    return cancelQty.setScale(3, RoundingMode.HALF_UP);
  }

  /**
   * 3位小数
   * @return
   */
  public BigDecimal getReturnQty() {
    if (returnQty == null) {
      return BigDecimal.ZERO;
    }
    return returnQty.setScale(3, RoundingMode.HALF_UP);
  }

  /**
   * 2位小数
   * @return
   */
  public BigDecimal getPrice() {
    if (price == null) {
      return BigDecimal.ZERO;
    }
    return price.setScale(2, RoundingMode.HALF_UP);
  }

  /**
   * 金额合计
   * 2位小数
   * @return
   */
  public BigDecimal getTotalPrice() {
    if (totalPrice == null) {
      return BigDecimal.ZERO;
    }
    return totalPrice.setScale(2, RoundingMode.HALF_UP);
  }

  /**
   * 订货数量 3位小数
   * @return
   */
  public BigDecimal getNum() {
    if (num == null) {
      return BigDecimal.ZERO;
    }
    return num.setScale(3, RoundingMode.HALF_UP);
  }
}
