package com.xhgj.srm.jpa.dao.impl;

import com.xhgj.srm.common.Constants;
import com.xhgj.srm.common.enums.product.ProductStateEnum;
import com.xhgj.srm.jpa.dao.CheckDao;
import com.xhgj.srm.jpa.entity.Check;
import com.xhiot.boot.core.common.util.DateUtils;
import com.xhiot.boot.core.common.util.ObjectUtils;
import com.xhiot.boot.core.common.util.StringUtils;
import com.xhiot.boot.framework.jpa.dao.AbstractBaseDao;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class CheckDaoImpl extends AbstractExtDao<Check> implements CheckDao {


    /**
     * @Title: getCheckedPage
     * @Description:获取已审核分页信息
     * <AUTHOR>
     * @date 2019年8月12日下午4:00:04
     */
    @Override
    public Page<String> getCheckedPage(String userId, String enterpriseName, String enterpriseNature,
                                       String enterpriseLevel, String industry, String useGroup, String brands,
                                       String purchaserName, String checkMan, String startTime, String endTime,
                                       String applyStartDate, String applyEndDate,
                                       String applyType, int pageNo, int pageSize
    ) {
        String sql = "select a.id from ( select ch.id,ch.c_createTime from t_check ch " +
                "left join t_supplier_fb fb on ch.c_relationId = fb.id  " +
                "left join t_brand b on fb.id = b.supplierFbId " +
                "left join t_user u on u.id = ch.c_userId " +
                "left join t_supplier su on fb.supplierId = su.id " +
                "where ch.c_state = ? and ch.c_resource != ? and ch.c_resource != ? and ch.c_resource != ? ";
        Object[] params = new Object[]{Constants.STATE_OK, Constants.RESOURCE_TYPE_AGREEMENT,
                Constants.RESOURCE_TYPE_SPADD, Constants.RESOURCE_TYPE_SPERR};
        if (!StringUtils.isNullOrEmpty(userId)) {
            sql += "  and ch.c_userId = ? ";
            params = ObjectUtils.objectAdd(params, userId);
        }
        if (!StringUtils.isNullOrEmpty(enterpriseName)) {
            sql += "  and fb.c_enterpriseName like ? ";
            params = ObjectUtils.objectAdd(params, "%" + enterpriseName + "%");
        }
        if (!StringUtils.isNullOrEmpty(enterpriseNature)) {
            sql += "  and fb.c_enterpriseNature in (" + enterpriseNature + ") ";
        }
        if (!StringUtils.isNullOrEmpty(industry)) {
            sql += "  and fb.c_industry = ? ";
            params = ObjectUtils.objectAdd(params, industry);
        }
        if (!StringUtils.isNullOrEmpty(enterpriseLevel)) {
            sql += "  and fb.c_enterpriseLevel = ? ";
            params = ObjectUtils.objectAdd(params, enterpriseLevel);
        }
        if (!StringUtils.isNullOrEmpty(useGroup)) {
            sql += "and fb.c_useGroup like ? ";
            params = ObjectUtils.objectAdd(params, "%" + useGroup + "%");
        }
        if (!StringUtils.isNullOrEmpty(brands)) {
            sql += "and ( b.c_brandname_en like ? or b.c_brandname_cn like ? ) ";
            params = ObjectUtils.objectAdd(params, "%" + brands + "%");
            params = ObjectUtils.objectAdd(params, "%" + brands + "%");
        }
        if (!StringUtils.isNullOrEmpty(purchaserName)) {
            sql += "and fb.c_purchaserName like ? ";
            params = ObjectUtils.objectAdd(params, "%" + purchaserName + "%");
        }
        if (!StringUtils.isNullOrEmpty(startTime)) {
            sql += "and ch.c_createTime >= ? ";
            params = ObjectUtils.objectAdd(params, DateUtils.parseNormalDateToTimeStamp(startTime));
        }
        if (!StringUtils.isNullOrEmpty(endTime)) {
            sql += "and ch.c_createTime < ? ";
            params = ObjectUtils.objectAdd(params, DateUtils.parseNormalDateToTimeStamp(endTime) + 24 * 60 * 60 * 1000l);
        }
        if (!StringUtils.isNullOrEmpty(applyStartDate)) {
            sql += "and fb.c_createTime >= ? ";
            params = ObjectUtils.objectAdd(params, DateUtils.parseNormalDateToTimeStamp(applyStartDate));
        }
        if (!StringUtils.isNullOrEmpty(applyEndDate)) {
            sql += "and fb.c_createTime < ? ";
            params = ObjectUtils.objectAdd(params, DateUtils.parseNormalDateToTimeStamp(applyEndDate) + 24 * 60 * 60 * 1000l);
        }
        if (!StringUtils.isNullOrEmpty(checkMan)) {
            sql += "and u.c_realname like ? ";
            params = ObjectUtils.objectAdd(params, "%" + checkMan + "%");
        }
        if (!StringUtils.isNullOrEmpty(applyType)) {
            if (Constants.SUPPLIERCHECKTYPE_MAP_UPDATE.equals(applyType)) {
                sql += "and su.c_editManId is not null and su.c_editManId <> ? ";
                params = ObjectUtils.objectAdd(params, "");
            } else if (Constants.SUPPLIERCHECKTYPE_MAP_ADD.equals(applyType)) {
                sql += "and ( su.c_editManId is null or su.c_editManId = ? ) ";
                params = ObjectUtils.objectAdd(params, "");
            }
        }
        sql += "group by ch.id ) a order by a.c_createTime desc";
        return findPageSqlObject(sql, params, pageNo, pageSize);
    }


    /**
     * @Title: getCheckByRid
     * @Description:根据relationid获取最近的审核
     * <AUTHOR>
     * @date 2019年8月13日下午7:34:01
     */
    @Override
    public Check getCheckByRid(String id) {
        String hql = "from Check ch where ch.state = ? and ch.relationId = ? order by ch.createTime desc";
        Object[] params = new Object[]{Constants.STATE_OK, id};
        return getFirstHqlEntity(hql, params);
    }


    /**
     * @Title: getFrontCheckedPage
     * @Description:获取已审核分页信息
     * <AUTHOR>
     * @date 2019年8月12日下午4:00:04
     */
    @Override
    public Page<String> getFrontCheckedPage(
            String useGroup, String applyStartTime, String applyEndTime, String enterpriseName,
            String uscc, String corporate, String brands, String type, String checkMan, String startTime, String endTime,
            String userId, int pageNo, int pageSize
    ) {
        String sql = "select a.id from ( select ch.id,ch.c_createTime from t_check ch " +
                "left join t_supplier_fb fb on ch.c_relationId = fb.id  " +
                "left join t_brand b on fb.id = b.supplierFbId " +
                "left join t_user u on u.id = ch.c_userId " +
                "left join t_supplier su on fb.supplierId = su.id " +
                "where ch.c_state = ? and su.c_purchaserId = ? and ch.c_resource != ? and ch.c_resource != ? and ch.c_resource != ? and su.c_isFromSupplier = ? ";
        Object[] params = new Object[]{Constants.STATE_OK, userId, Constants.RESOURCE_TYPE_AGREEMENT,
                Constants.RESOURCE_TYPE_SPADD, Constants.RESOURCE_TYPE_SPERR, Constants.YES};
        if (!StringUtils.isNullOrEmpty(enterpriseName)) {
            sql += "  and fb.c_enterpriseName like ? ";
            params = ObjectUtils.objectAdd(params, "%" + enterpriseName + "%");
        }
        if (!StringUtils.isNullOrEmpty(uscc)) {
            sql += "  and fb.c_uscc like ? ";
            params = ObjectUtils.objectAdd(params, "%" + uscc + "%");
        }
        if (!StringUtils.isNullOrEmpty(corporate)) {
            sql += "  and fb.c_corporate like ? ";
            params = ObjectUtils.objectAdd(params, "%" + corporate + "%");
        }
        if (!StringUtils.isNullOrEmpty(useGroup)) {
            sql += "and fb.c_useGroup like ? ";
            params = ObjectUtils.objectAdd(params, "%" + useGroup + "%");
        }
        if (!StringUtils.isNullOrEmpty(brands)) {
            sql += "and ( b.c_brandname_en like ? or b.c_brandname_cn like ? ) ";
            params = ObjectUtils.objectAdd(params, "%" + brands + "%");
            params = ObjectUtils.objectAdd(params, "%" + brands + "%");
        }
        if (!StringUtils.isNullOrEmpty(applyStartTime)) {
            sql += "and fb.c_createTime >= ? ";
            params = ObjectUtils.objectAdd(params, DateUtils.parseNormalDateToTimeStamp(applyStartTime));
        }
        if (!StringUtils.isNullOrEmpty(applyEndTime)) {
            //update zhuhd 2021年7月13日12:45:21 结束时间加一天
            sql += "and fb.c_createTime < ? ";
            long end = DateUtils.parseNormalDateToTimeStamp(applyEndTime)+86400000L;
            params = ObjectUtils.objectAdd(params, end);
        }
        if (!StringUtils.isNullOrEmpty(startTime)) {
            sql += "and ch.c_createTime >= ? ";
            params = ObjectUtils.objectAdd(params, DateUtils.parseNormalDateToTimeStamp(startTime));
        }
        if (!StringUtils.isNullOrEmpty(endTime)) {
            //update zhuhd 2021年7月13日12:45:21 结束时间加一天
            sql += "and ch.c_createTime < ? ";
            long end = DateUtils.parseNormalDateToTimeStamp(endTime)+86400000L;
            params = ObjectUtils.objectAdd(params, end);
        }
        if (!StringUtils.isNullOrEmpty(checkMan)) {
            //update zhuhd 2021年7月14日09:36:13 sql语句写错
            sql += "and u.c_realname like ? ";
            params = ObjectUtils.objectAdd(params, "%" + checkMan + "%");
        }
        if (!StringUtils.isNullOrEmpty(type)) {
            if (Constants.SUPPLIERCHECKTYPE_MAP_UPDATE.equals(type)) {
                sql += "and su.c_editTime > 0 ";
            } else if (Constants.SUPPLIERCHECKTYPE_MAP_ADD.equals(type)) {
                //update zhuhd 2021年7月13日12:45:21 sql语句少and
                sql += "and su.c_editTime = 0 ";
            }
        }
        sql += "group by ch.id ) a order by a.c_createTime desc";
        return findPageSqlObject(sql, params, pageNo, pageSize);
    }

    @Override
    public List<String> getUnReadSupplierCheckByRidAndType(String relationId) {
        String hql = "select id from t_check where c_state = ? and c_relationId = ? and c_operaType in(?,?)  and c_readTime > ? order by c_createTime desc";
        Object[] params = new Object[]{Constants.STATE_OK, relationId, Constants.AUDIT_TYPE_PURCHASEFAULT, Constants.AUDIT_TYPE_AGREESUCCESS, 0};
        return getSqlObjList(hql, params);
    }

    @Override
    public List<String> getUnReadProductCheckByRidAndType(String relationId) {
      // todo 状态修改是否正确
        String hql = "select id from t_check where c_state = ? and c_relationId = ? and c_operaType in(?)  and c_readTime > ? order by c_createTime desc";
        Object[] params = new Object[]{Constants.STATE_OK, relationId, ProductStateEnum.REJECT.getKey(), 0};
        return getSqlObjList(hql, params);
    }

    @Override
    public List<Check> getCheckByRidAndType(String supplierId, String type) {
        String hql = "from Check  where state = ? and relationId = ? and resource = ?  order by createTime desc";
        Object[] params = new Object[]{Constants.STATE_OK, supplierId, type};
        return getHqlList(hql, params);
    }

  @Override
  public void deleteCheckByRelationId(String relationId) {
    String sql = "update t_check set c_state = ? where c_relationId = ? ";
    Object[] params = new Object[] {Constants.STATE_DELETE, relationId};
    executeSqlUpdate(sql, params);
  }
}
