package com.xhgj.srm.jpa.repository;

import com.xhgj.srm.jpa.entity.EntryRegistrationDiscount;
import com.xhiot.boot.framework.jpa.repository.BootBaseRepository;
import java.util.Collection;
import java.util.List;

public interface EntryRegistrationDiscountRepository extends
    BootBaseRepository<EntryRegistrationDiscount, String> {

  List<EntryRegistrationDiscount> findByEntryRegistrationOrderIdAndState(String entryRegistrationOrderId, String state);

  List<EntryRegistrationDiscount> findByLandingContractIdAndState(String landingContractId, String state);


}
