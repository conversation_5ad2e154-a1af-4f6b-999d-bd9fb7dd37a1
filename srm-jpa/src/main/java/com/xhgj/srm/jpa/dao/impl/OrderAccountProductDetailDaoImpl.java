package com.xhgj.srm.jpa.dao.impl;

import com.xhgj.srm.common.Constants;
import com.xhgj.srm.jpa.dao.OrderAccountProductDetailDao;
import com.xhgj.srm.jpa.entity.OrderAccountProductDetail;
import com.xhiot.boot.core.common.util.ObjectUtils;
import com.xhiot.boot.core.common.util.StringUtils;
import com.xhiot.boot.framework.jpa.dao.AbstractBaseDao;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Slf4j
@Service
public class OrderAccountProductDetailDaoImpl extends AbstractExtDao<OrderAccountProductDetail> implements OrderAccountProductDetailDao {

    @Transactional
    @Override
    public void deleteOrderAccountProductByOrderAccount(String detailId) {
        String hql = "delete from OrderAccountProductDetail od where od.accountDetailId = ? ";
        Object[] params = new Object[]{detailId};
        executeUpdate(hql, params);
    }

    @Override
    public Page<OrderAccountProductDetail> getOrderAccountProductDetailPageByAccount(String accountId, int pageNo, int pageSize) {
        String hql = "from OrderAccountProductDetail o where o.account.id = ? and o.state <> ? ";
        Object[] params = new Object[]{accountId, Constants.COMMONSTATE_DELETE};
        return findPage(hql, params, pageNo, pageSize);
    }

    @Override
    public List<OrderAccountProductDetail> getOrderAccountProductDetailListByOrderNo(String accountId, String orderNo) {
        String hql = "from OrderAccountProductDetail o where o.account.id = ? and o.state <> ? ";
        Object[] params = new Object[]{accountId, Constants.COMMONSTATE_DELETE};
        if (!StringUtils.isNullOrEmpty(orderNo)) {
            hql += "and o.orderNo = ? ";
            params = ObjectUtils.objectAdd(params,orderNo);
        }
        return getHqlList(hql, params);
    }

}
