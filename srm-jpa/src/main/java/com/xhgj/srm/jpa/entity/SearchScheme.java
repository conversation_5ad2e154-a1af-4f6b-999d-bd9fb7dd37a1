package com.xhgj.srm.jpa.entity;

import lombok.Data;
import org.hibernate.annotations.GenericGenerator;

import javax.persistence.*;
import java.io.Serializable;

@Entity
@Data
@Table(name = "t_search_scheme")
public class SearchScheme implements Serializable {

    private static final long serialVersionUID = 1L;

    @Id
    @Column(name = "id", nullable = false)
    @GeneratedValue(generator = "system-uuid")
    @GenericGenerator(name = "system-uuid", strategy = "uuid")
    private String id;

    @Column(name = "c_type")
    private String type;

    @Column(name = "c_name")
    private String name;

    @Column(name = "c_content")
    private String content;

    @Column(name = "c_isDefault")
    private String isDefault;

    @Column(name = "c_createMan")
    private String createMan;

    @Column(name = "c_createTime")
    private Long createTime;

    @Column(name = "c_state")
    private String state;
}
