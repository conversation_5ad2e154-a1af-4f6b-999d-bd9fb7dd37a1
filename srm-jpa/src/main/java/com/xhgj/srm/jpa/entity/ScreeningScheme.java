package com.xhgj.srm.jpa.entity;

import com.xhgj.srm.common.enums.ScreeningSchemeEnum;
import lombok.Data;
import org.hibernate.annotations.GenericGenerator;
import javax.persistence.*;
import java.io.Serializable;

/**
 * 表头筛选方案表
 */
@Entity
@Data
@Table(name = "t_screening_scheme")
public class ScreeningScheme implements Serializable {

  private static final long serialVersionUID = 1L;

  @Id
  @Column(name = "id", nullable = false)
  @GeneratedValue(generator = "system-uuid")
  @GenericGenerator(name = "system-uuid", strategy = "uuid")
  private String id;

  /**
   * 用户ID
   */
  @Column(name = "c_user_id")
  private String userId;

  /**
   * 所属页面
   * #{@link ScreeningSchemeEnum}
   */
  @Column(name = "c_type")
  private String type;

  /**
   * 内容。json串
   */
  @Column(name = "c_content")
  private String content;

  /**
   * 数据状态。0-删除，1-正常
   */
  @Column(name = "c_state")
  private String state;

  /**
   * 更新时间
   */
  @Column(name = "c_create_time")
  private Long createTime;

  /**
   * 更新时间
   */
  @Column(name = "c_update_time")
  private Long updateTime;
}
