package com.xhgj.srm.jpa.dao.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.exceptions.ExceptionUtil;
import cn.hutool.core.util.StrUtil;
import com.xhgj.srm.common.Constants;
import com.xhgj.srm.common.Constants_order;
import com.xhgj.srm.jpa.dao.OrderAccountDao;
import com.xhgj.srm.jpa.dto.account.AccountStatistics;
import com.xhgj.srm.jpa.entity.OrderAccount;
import com.xhgj.srm.jpa.param.OrderAccountParam;
import com.xhiot.boot.core.common.util.DateUtils;
import com.xhiot.boot.core.common.util.ObjectUtils;
import com.xhiot.boot.core.common.util.StringUtils;
import com.xhiot.boot.framework.jpa.dao.AbstractBaseDao;
import com.xhiot.boot.framework.jpa.util.HqlUtil;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class OrderAccountDaoImpl extends AbstractExtDao<OrderAccount> implements OrderAccountDao {

    @Override
    public Page<String> getOrderAccountPageBySupplier(
        String supplierId, String accountNo, String accountPrice,
        String accountStartTime, String accountEndTime, String assessStartTime, String assessEndTime,
        List<String> accountState, List<String> accountOpenInvoiceStatus,
        String supplierName, String orderNo, Integer pageNo, Integer pageSize) {
        StringBuilder sql = new StringBuilder("select a.id from ( select distinct(o.id) from t_order_account o " +
                "left join t_order_account_to_order oa on oa.order_account_id = o.id " +
                "left join t_order oe on oe.id = oa.order_id where o.c_state != ? ");
        Object[] params = new Object[]{Constants.STATE_DELETE};
        if (!StringUtils.isNullOrEmpty(orderNo)) {
            sql.append("and od.c_order_no like ? ");
            params = ObjectUtils.objectAdd(params, "%" + orderNo + "%");
        }
        if (!StringUtils.isNullOrEmpty(orderNo)) {
            sql.append("and od.c_order_no like ? ");
            params = ObjectUtils.objectAdd(params, "%" + orderNo + "%");
        }
        if (!StringUtils.isNullOrEmpty(supplierName)) {
            sql.append("and o.c_create_supplier like ? ");
            params = ObjectUtils.objectAdd(params, "%" + supplierName + "%");
        }
        if (!StringUtils.isNullOrEmpty(supplierId)) {
            sql.append("and o.c_supplier_id = ? ");
            params = ObjectUtils.objectAdd(params, supplierId);
        }
        if (!StringUtils.isNullOrEmpty(accountNo)) {
            sql.append("and o.c_account_no like ? ");
            params = ObjectUtils.objectAdd(params, "%" + accountNo + "%");
        }
        if (!StringUtils.isNullOrEmpty(accountPrice)) {
            sql.append("and o.c_price = ? ");
            params = ObjectUtils.objectAdd(params,  new BigDecimal(accountPrice));
        }
        if (CollUtil.isNotEmpty(accountState)) {
          params = HqlUtil.appendFieldIn(sql, params, "o.c_account_state", accountState);
        }
        if (CollUtil.isNotEmpty(accountOpenInvoiceStatus)) {
          params = HqlUtil.appendFieldIn(sql, params, "o.c_account_open_invoice_status",
              accountOpenInvoiceStatus );
        }
        if (!StringUtils.isNullOrEmpty(accountStartTime)) {
            sql.append("and o.c_create_time >= ? ");
            params = ObjectUtils.objectAdd(params, DateUtils.parseNormalDateToTimeStamp(accountStartTime));
        }
        if (!StringUtils.isNullOrEmpty(accountEndTime)) {
            sql.append("and o.c_create_time < ? ");
            params = ObjectUtils.objectAdd(params, DateUtils.parseNormalDateToTimeStamp(accountEndTime) + 24 * 60 * 60 * 1000);
        }
        if (!StringUtils.isNullOrEmpty(assessStartTime)) {
            sql.append("and o.c_assess_time >= ? ");
            params = ObjectUtils.objectAdd(params, DateUtils.parseNormalDateToTimeStamp(assessStartTime));
        }
        if (!StringUtils.isNullOrEmpty(assessEndTime)) {
            sql.append("and o.c_assess_time < ? ");
            params = ObjectUtils.objectAdd(params, DateUtils.parseNormalDateToTimeStamp(assessEndTime) + 24 * 60 * 60 * 1000);
        }
        sql.append("order by o.c_create_time desc ) a ");
        return findPageSqlObject(sql.toString(), params, pageNo, pageSize);
    }

    @Override
    public long getOrderAccountCountByCreateTimeBetween(String startDate, String endDate) {
        String hql = " select count(od.id) from OrderAccount od where od.createTime >= ? and od.createTime < ?  ";
        Object[] params = new Object[]{DateUtils.parseNormalDateToTimeStamp(startDate), DateUtils.parseNormalDateToTimeStamp(endDate)};
        try {
            return count(hql, params);
        } catch (Exception e) {
            log.error(ExceptionUtil.stacktraceToString(e));
            return 0L;
        }
    }

    @Override
    public List<String> getAllOrderAccountList() {
        String hql = "select o.id from t_order_account o where o.c_state != ? and o.c_commit_time > ? ";
        Object[] params = new Object[]{Constants.STATE_DELETE,0L};
        hql += "order by o.c_commit_time desc";
        return getSqlObjList(hql, params);
    }

    @Override
    public Page<String> getOrderAccountPageByPurchase(String userIds, String supplierName, String accountNo, String returnAmount, String accountPrice, String accountStartTime,
                                                      String accountEndTime, String accountState, String invoicingState, Integer pageNo, Integer pageSize) {
        String sql = "select a.id from ( select distinct(o.id) from t_order_account o " +
                "left join t_order_account_detail od on od.account_id = o.id " +
                "left join t_supplier s on s.id = o.c_supplier_id where o.c_state != ? ";
        Object[] params = new Object[]{Constants.STATE_DELETE};
        if (!StringUtils.isNullOrEmpty(supplierName)) {
            sql += "and o.c_create_supplier like ? ";
            params = ObjectUtils.objectAdd(params, "%" + supplierName + "%");
        }
        if (!StringUtils.isNullOrEmpty(userIds)) {
            sql += "and s.c_purchaserId in (" + userIds + ") ";
        }
        if (!StringUtils.isNullOrEmpty(accountNo)) {
            sql += "and o.c_account_no like ? ";
            params = ObjectUtils.objectAdd(params, "%" + accountNo + "%");
        }
        if (!StringUtils.isNullOrEmpty(accountPrice)) {
            sql += "and o.c_price = ? ";
            params = ObjectUtils.objectAdd(params, accountPrice);
        }
        if (!StringUtils.isNullOrEmpty(returnAmount)) {
            sql += "and o.c_return_price = ? ";
            params = ObjectUtils.objectAdd(params, returnAmount);
        }
        if (!StringUtils.isNullOrEmpty(accountState)) {
            sql += "and o.c_account_state = ? ";
            params = ObjectUtils.objectAdd(params, accountState);
        } else {
            sql += "and o.c_account_state in ( ?, ?, ?, ?, ? ) ";
            params = ObjectUtils.objectAdd(params, Constants_order.ORDER_ACCOUNT_STATE_MAP_WAIT);
            params = ObjectUtils.objectAdd(params, Constants_order.ORDER_ACCOUNT_STATE_MAP_DONE);
            params = ObjectUtils.objectAdd(params, Constants_order.ORDER_ACCOUNT_STATE_MAP_PART_REFUND);
            params = ObjectUtils.objectAdd(params, Constants_order.ORDER_ACCOUNT_STATE_MAP_ALL_REFUND);
            params = ObjectUtils.objectAdd(params, Constants_order.ORDER_ACCOUNT_STATE_MAP_ALL_VOIDED);
        }
        if (!StringUtils.isNullOrEmpty(accountStartTime)) {
            sql += "and o.c_commit_time >= ? ";
            params = ObjectUtils.objectAdd(params, DateUtils.parseNormalDateToTimeStamp(accountStartTime));
        }
        if (!StringUtils.isNullOrEmpty(accountEndTime)) {
            sql += "and o.c_commit_time < ? ";
            params = ObjectUtils.objectAdd(params, DateUtils.parseNormalDateToTimeStamp(accountEndTime) + 24 * 60 * 60 * 1000);
        }
        sql += "order by o.c_commit_time desc ) a ";
        return findPageSqlObject(sql, params, pageNo, pageSize);
    }

    @Override
    public Page<OrderAccount> findAccountPage(String accountNo, String createSupplier,
        String accountStatus, String accountOpenInvoiceStatus,
            Long startCommitTime, Long endCommitTime, Long startAssessTime, Long endAssessTime,int pageNo,int pageSize) {
        String hql = "from OrderAccount where state = ? ";
        Object[] params = new Object[]{Constants.STATE_OK};
        if (StrUtil.isNotBlank(accountNo)) {
            hql+="and accountNo = ? ";
            params = ObjectUtils.objectAdd(params,accountNo);
        }
        if (StrUtil.isNotBlank(createSupplier)) {
            hql+="and createSupplier like ? ";
            params = ObjectUtils.objectAdd(params,StrUtil.wrap(createSupplier,"%"));
        }
        if (StrUtil.isNotBlank(accountStatus)) {
            hql+="and accountState = ? ";
            params = ObjectUtils.objectAdd(params,accountStatus);
        }
        if (StrUtil.isNotBlank(accountOpenInvoiceStatus)) {
            hql+="and supplierOpenInvoiceStatus = ? ";
            params = ObjectUtils.objectAdd(params,accountOpenInvoiceStatus);
        }
        if (startCommitTime!=null) {
            hql+="and commitTime >= ? ";
            params = ObjectUtils.objectAdd(params,startCommitTime);
        }
        if (endCommitTime!=null) {
            hql+="and commitTime <= ? ";
            params = ObjectUtils.objectAdd(params,endCommitTime);
        }
        if (startAssessTime!=null) {
            hql+="and assessTime >= ? ";
            params = ObjectUtils.objectAdd(params,startAssessTime);
        }
        if (endAssessTime!=null) {
            hql+="and assessTime <= ? ";
            params = ObjectUtils.objectAdd(params,endAssessTime);
        }
        hql += "order by commitTime ";
        return findPage(hql,params,pageNo,pageSize);
    }

  @Override
  public Page<String> findAccountPage(String accountNo, String createSupplier, String accountStatus,
      String accountOpenInvoiceStatus, List<String> platforms, Long startCommitTime, Long endCommitTime,
      Long startAssessTime, Long endAssessTime, int pageNo, int pageSize) {
      StringBuilder sql = new StringBuilder("select a.id from (select distinct(oa.id) from "
        + "t_order_account oa "+
        "left join t_order_account_to_order ao on oa.id=ao.order_account_id and ao.c_state=? "+
        "where oa.c_state = ? ");
    Object[] params = new Object[]{Constants.STATE_OK,Constants.STATE_OK};
    if (StrUtil.isNotBlank(accountNo)) {
      sql.append("and oa.c_account_no = ? ");
      params = ObjectUtils.objectAdd(params,accountNo);
    }
    if (StrUtil.isNotBlank(createSupplier)) {
      sql.append("and oa.c_create_supplier like ? ");
      params = ObjectUtils.objectAdd(params,StrUtil.wrap(createSupplier,"%"));
    }
    if (StrUtil.isNotBlank(accountStatus)) {
      sql.append("and oa.c_account_state = ? ");
      params = ObjectUtils.objectAdd(params,accountStatus);
    }
    if (StrUtil.isNotBlank(accountOpenInvoiceStatus)) {
      sql.append("and oa.c_account_open_invoice_status = ? ");
      params = ObjectUtils.objectAdd(params,accountOpenInvoiceStatus);
    }
    if (startCommitTime!=null) {
      sql.append("and oa.c_commit_time >= ? ");
      params = ObjectUtils.objectAdd(params,startCommitTime);
    }
    if (endCommitTime!=null) {
      sql.append("and oa.c_commit_time <= ? ");
      params = ObjectUtils.objectAdd(params,endCommitTime);
    }
    if (startAssessTime!=null) {
      sql.append("and oa.c_assess_time >= ? ");
      params = ObjectUtils.objectAdd(params,startAssessTime);
    }
    if (endAssessTime!=null) {
      sql.append("and oa.c_assess_time <= ? ");
      params = ObjectUtils.objectAdd(params,endAssessTime);
    }
    if (CollUtil.isNotEmpty(platforms)) {
      params = HqlUtil.appendFieldIn(sql, params, "oa.c_platform_code", platforms);
    }
    sql.append( "order by oa.c_commit_time )a");
    return findPageSqlObject(sql.toString(),params,pageNo,pageSize);
  }

  @Override
  public Page<OrderAccount> findAccountPageRef(Map<String, Object> queryMap) {
    StringBuilder sql = new StringBuilder("select distinct oa.* from t_order_account oa "+
        "left join t_order_account_to_order ao on oa.id=ao.order_account_id and ao.c_state = ? ");
    List<Object> params = new ArrayList<>();
    params.add(Constants.STATE_OK);
    buildWhereQuery(sql, params, queryMap);
    sql.append( "order by oa.c_commit_time ");
    return findPageSql(sql.toString(), params.toArray(), (Integer) queryMap.get("pageNo"),
        (Integer) queryMap.get("pageSize"));
  }

  @Override
  public List<OrderAccount> getAccountStatistics(Map<String, Object> queryMap) {
    StringBuilder sql = new StringBuilder("select distinct oa.id, oa.c_price, oa.c_order_count from "
        + "t_order_account oa "+
        "left join t_order_account_to_order ao on oa.id=ao.order_account_id and ao.c_state = ? ");
    List<Object> params = new ArrayList<>();
    params.add(Constants.STATE_OK);
    buildWhereQuery(sql, params, queryMap);
    List<Object[]> sqlObjList = getSqlObjList(sql.toString(), params.toArray());
    return sqlObjList.stream().map(obj -> {
      OrderAccount orderAccount = new OrderAccount();
      orderAccount.setId(Convert.toStr(obj[0]));
      orderAccount.setPrice(Convert.toBigDecimal(obj[1]));
      orderAccount.setOrderCount(Convert.toInt(obj[2]));
      return orderAccount;
    }).collect(Collectors.toList());
  }

  @Override
  public AccountStatistics getAccountStatistics2(Map<String, Object> queryMap) {
    StringBuilder sql = new StringBuilder();
    sql.append("select COALESCE(SUM(oa.c_price), 0), COALESCE(SUM(oa.c_order_count), 0) ")
        .append("from t_order_account oa ")
        .append("left join t_order_account_to_order ao on oa.id=ao.order_account_id and ao.c_state = ? ");
    List<Object> params = new ArrayList<>();
    params.add(Constants.STATE_OK);
    buildWhereQuery(sql, params, queryMap);
    sql.append("group by oa.id ");
    Object[] sqlObjList = (Object[]) getUniqueSqlObj(sql.toString(), params.toArray());
    AccountStatistics accountStatistics = new AccountStatistics();
    accountStatistics.setPrice(Convert.toBigDecimal(sqlObjList[0]));
    accountStatistics.setOrderCount(Convert.toInt(sqlObjList[1]));
    return accountStatistics;
  }

  private void buildWhereQuery(StringBuilder sql, List<Object> params, Map<String, Object> queryMap) {
    sql.append("where oa.c_state = ? ");
    params.add(Constants.STATE_OK);
    if (!StrUtil.isBlankIfStr(queryMap.get("accountNo"))) {
      sql.append("and oa.c_account_no = ? ");
      params.add(queryMap.get("accountNo"));
    }
    if (!StrUtil.isBlankIfStr(queryMap.get("createSupplier"))) {
      sql.append("and oa.c_create_supplier like ? ");
      params.add("%" + queryMap.get("createSupplier") + "%");
    }
    if (!StrUtil.isBlankIfStr(queryMap.get("accountStatus"))) {
      sql.append("and oa.c_account_state = ? ");
      params.add(queryMap.get("accountStatus"));
    }
    if (!StrUtil.isBlankIfStr(queryMap.get("accountOpenInvoiceStatus"))) {
      sql.append("and oa.c_account_open_invoice_status = ? ");
      params.add(queryMap.get("accountOpenInvoiceStatus"));
    }
    if (queryMap.get("startCommitTime") != null) {
      sql.append("and oa.c_commit_time >= ? ");
      params.add(queryMap.get("startCommitTime"));
    }
    if (queryMap.get("endCommitTime") != null) {
      sql.append("and oa.c_commit_time <= ? ");
      params.add(queryMap.get("endCommitTime"));
    }
    if (queryMap.get("startAssessTime") != null) {
      sql.append("and oa.c_assess_time >= ? ");
      params.add(queryMap.get("startAssessTime"));
    }
    if (queryMap.get("endAssessTime") != null) {
      sql.append("and oa.c_assess_time <= ? ");
      params.add(queryMap.get("endAssessTime"));
    }
    if (queryMap.get("platforms") != null) {
      List<String> platformList = Arrays.asList(StrUtil.split(queryMap.get("platforms").toString(), ","));
      if (CollUtil.isNotEmpty(platformList)) {
        sql.append("and oa.c_platform_code in ( ");
        for (int i = 0; i < platformList.size(); i++) {
          if (i == platformList.size() - 1) {
            sql.append(" ? ) ");
          } else {
            sql.append(" ? , ");
          }
          params.add(platformList.get(i));
        }
      }
    }
  }

  //导出查询
  @Override
  public List<String> findAccount(OrderAccountParam param) {
    StringBuilder sql = new StringBuilder("select a.id from (select distinct(oa.id) from "
        + "t_order_account oa "+
        "left join t_order_account_to_order ao on oa.id=ao.order_account_id and ao.c_state=? "+
        "left join t_order o on ao.order_id = o.id and o.c_state =? " +
        "where oa.c_state = ? ");

    Object[] params = new Object[]{Constants.STATE_OK,Constants.STATE_OK,Constants.STATE_OK};
    if (StrUtil.isNotBlank(param.getAccountNo())) {
      sql.append("and oa.c_account_no = ? ");
      params = ObjectUtils.objectAdd(params,param.getAccountNo());
    }
    if (StrUtil.isNotBlank(param.getCreateSupplier())) {
      sql.append("and oa.c_create_supplier like ? ");
      params = ObjectUtils.objectAdd(params,StrUtil.wrap(param.getCreateSupplier(),"%"));
    }
    if (StrUtil.isNotBlank(param.getAccountStatus())) {
      sql.append("and oa.c_account_state = ? ");
      params = ObjectUtils.objectAdd(params,param.getAccountStatus());
    }
    if (StrUtil.isNotBlank(param.getAccountOpenInvoiceStatus())) {
      sql.append("and oa.c_account_open_invoice_status = ? ");
      params = ObjectUtils.objectAdd(params,param.getAccountOpenInvoiceStatus());
    }
    if (param.getStartCommitTime()!=null) {
      sql.append("and oa.c_commit_time >= ? ");
      params = ObjectUtils.objectAdd(params,param.getStartCommitTime());
    }
    if (param.getEndCommitTime()!=null) {
      sql.append("and oa.c_commit_time <= ? ");
      params = ObjectUtils.objectAdd(params,param.getEndCommitTime());
    }
    if (param.getStartAssessTime()!=null) {
      sql.append("and oa.c_assess_time >= ? ");
      params = ObjectUtils.objectAdd(params,param.getStartAssessTime());
    }
    if (param.getEndAssessTime()!=null) {
      sql.append("and oa.c_assess_time <= ? ");
      params = ObjectUtils.objectAdd(params,param.getEndAssessTime());
    }
    if (StrUtil.isNotBlank(param.getPlatforms())) {
      List<String> platformList = Arrays.asList(StrUtil.split(param.getPlatforms(), ","));
      params = HqlUtil.appendFieldIn(sql, params, "o.c_type", platformList);
    }
    sql.append( "order by oa.c_commit_time )a");
    return getSqlObjList(sql.toString(), params);
  }

  @Override
  public long getOrderAccountTotal(String supplierId, String orderAccountStatus,
      String accountOpenInvoiceStatus) {
    String hql = "select count(id) from OrderAccount o where o.state <> ? ";
    Object[] params = new Object[]{Constants.COMMONSTATE_DELETE};
    if (StrUtil.isNotBlank(supplierId)) {
      hql += "and supplierId = ? ";
      params = ObjectUtils.objectAdd(params, supplierId);
    }
    if (StrUtil.isNotBlank(orderAccountStatus)) {
      hql += "and accountState = ? ";
      params = ObjectUtils.objectAdd(params, orderAccountStatus);
    }
    if (StrUtil.isNotBlank(accountOpenInvoiceStatus)) {
      hql += "and supplierOpenInvoiceStatus = ? ";
      params = ObjectUtils.objectAdd(params, accountOpenInvoiceStatus);
    }
    return count(hql, params);
  }

    @Override
    public List<OrderAccount> getListByOpenInvoiceStatus(String orderAccountOpenNotInvoiced) {
      String hql = "from OrderAccount where state = ? and accountOpenInvoiceStatus <> ?";
      Object[] params = new Object[]{Constants.STATE_OK,orderAccountOpenNotInvoiced};
      hql += "order by commitTime ";
      return getHqlList(hql,params);
    }

  @Override
  public OrderAccount getAccountByAccountNo(String accountNo) {
    String hql = "from OrderAccount where state = ? and accountNo = ?";
    Object[] params = new Object[]{Constants.STATE_OK,accountNo};
    hql += "order by commitTime ";
    return getFirstHqlEntity(hql,params);
  }
}
